{"version": 3, "sources": ["../../src/build/entries.ts"], "sourcesContent": ["import type { ClientPagesLoaderOptions } from './webpack/loaders/next-client-pages-loader'\nimport type { MiddlewareLoaderOptions } from './webpack/loaders/next-middleware-loader'\nimport type { EdgeSSRLoaderQuery } from './webpack/loaders/next-edge-ssr-loader'\nimport type { EdgeAppRouteLoaderQuery } from './webpack/loaders/next-edge-app-route-loader'\nimport type { NextConfigComplete } from '../server/config-shared'\nimport type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport type {\n  MiddlewareConfig,\n  MiddlewareMatcher,\n  PageStaticInfo,\n} from './analysis/get-page-static-info'\nimport type { LoadedEnvFiles } from '@next/env'\nimport type { AppLoaderOptions } from './webpack/loaders/next-app-loader'\n\nimport { posix, join, dirname, extname, normalize } from 'path'\nimport { stringify } from 'querystring'\nimport fs from 'fs'\nimport {\n  PAGES_DIR_ALIAS,\n  ROOT_DIR_ALIAS,\n  APP_DIR_ALIAS,\n  WEBPACK_LAYERS,\n  INSTRUMENTATION_HOOK_FILENAME,\n} from '../lib/constants'\nimport { isAPIRoute } from '../lib/is-api-route'\nimport { isEdgeRuntime } from '../lib/is-edge-runtime'\nimport {\n  APP_CLIENT_INTERNALS,\n  RSC_MODULE_TYPES,\n  UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n} from '../shared/lib/constants'\nimport {\n  CLIENT_STATIC_FILES_RUNTIME_AMP,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN_APP,\n  CLIENT_STATIC_FILES_RUNTIME_POLYFILLS,\n  CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH,\n  COMPILER_NAMES,\n  EDGE_RUNTIME_WEBPACK,\n} from '../shared/lib/constants'\nimport type { CompilerNameValues } from '../shared/lib/constants'\nimport type { __ApiPreviewProps } from '../server/api-utils'\nimport {\n  isMiddlewareFile,\n  isMiddlewareFilename,\n  isInstrumentationHookFile,\n  isInstrumentationHookFilename,\n  reduceAppConfig,\n} from './utils'\nimport {\n  getAppPageStaticInfo,\n  getPageStaticInfo,\n} from './analysis/get-page-static-info'\nimport { normalizePathSep } from '../shared/lib/page-path/normalize-path-sep'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport type { ServerRuntime } from '../types'\nimport { normalizeAppPath } from '../shared/lib/router/utils/app-paths'\nimport { encodeMatchers } from './webpack/loaders/next-middleware-loader'\nimport type { EdgeFunctionLoaderOptions } from './webpack/loaders/next-edge-function-loader'\nimport { isAppRouteRoute } from '../lib/is-app-route-route'\nimport {\n  normalizeMetadataPageToRoute,\n  normalizeMetadataRoute,\n} from '../lib/metadata/get-metadata-route'\nimport { getRouteLoaderEntry } from './webpack/loaders/next-route-loader'\nimport {\n  isInternalComponent,\n  isNonRoutePagesPage,\n} from '../lib/is-internal-component'\nimport { isMetadataRouteFile } from '../lib/metadata/is-metadata-route'\nimport { RouteKind } from '../server/route-kind'\nimport { encodeToBase64 } from './webpack/loaders/utils'\nimport { normalizeCatchAllRoutes } from './normalize-catchall-routes'\nimport type { PageExtensions } from './page-extensions-type'\nimport type { MappedPages } from './build-context'\nimport { PAGE_TYPES } from '../lib/page-types'\nimport { isAppPageRoute } from '../lib/is-app-page-route'\nimport { recursiveReadDir } from '../lib/recursive-readdir'\nimport type { createValidFileMatcher } from '../server/lib/find-page-file'\nimport { isReservedPage } from './utils'\nimport { isParallelRouteSegment } from '../shared/lib/segment'\nimport { ensureLeadingSlash } from '../shared/lib/page-path/ensure-leading-slash'\n\n/**\n * Collect app pages, layouts, and default files from the app directory\n * @param appDir - The app directory path\n * @param validFileMatcher - File matcher object\n * @returns Object containing appPaths, layoutPaths, and defaultPaths arrays\n */\nexport async function collectAppFiles(\n  appDir: string,\n  validFileMatcher: ReturnType<typeof createValidFileMatcher>\n): Promise<{\n  appPaths: string[]\n  layoutPaths: string[]\n  defaultPaths: string[]\n}> {\n  // Collect app pages, layouts, and default files in a single directory traversal\n  const allAppFiles = await recursiveReadDir(appDir, {\n    pathnameFilter: (absolutePath) =>\n      validFileMatcher.isAppRouterPage(absolutePath) ||\n      validFileMatcher.isRootNotFound(absolutePath) ||\n      validFileMatcher.isAppLayoutPage(absolutePath) ||\n      validFileMatcher.isAppDefaultPage(absolutePath),\n    ignorePartFilter: (part) => part.startsWith('_'),\n  })\n\n  // Separate app pages, layouts, and defaults\n  const appPaths = allAppFiles.filter(\n    (absolutePath) =>\n      validFileMatcher.isAppRouterPage(absolutePath) ||\n      validFileMatcher.isRootNotFound(absolutePath)\n  )\n  const layoutPaths = allAppFiles.filter((absolutePath) =>\n    validFileMatcher.isAppLayoutPage(absolutePath)\n  )\n  const defaultPaths = allAppFiles.filter((absolutePath) =>\n    validFileMatcher.isAppDefaultPage(absolutePath)\n  )\n\n  return { appPaths, layoutPaths, defaultPaths }\n}\n\n/**\n * Collect pages from the pages directory\n * @param pagesDir - The pages directory path\n * @param validFileMatcher - File matcher object\n * @returns Array of page file paths\n */\nexport async function collectPagesFiles(\n  pagesDir: string,\n  validFileMatcher: ReturnType<typeof createValidFileMatcher>\n): Promise<string[]> {\n  return recursiveReadDir(pagesDir, {\n    pathnameFilter: validFileMatcher.isPageFile,\n  })\n}\n\n// Types for route processing\nexport type RouteInfo = {\n  route: string\n  filePath: string\n}\n\nexport type SlotInfo = {\n  name: string\n  parent: string\n}\n\n/**\n * Create a relative file path from a mapped page path\n * @param baseDir - The base directory path\n * @param filePath - The mapped file path (with private prefix)\n * @param prefix - The directory prefix ('pages' or 'app')\n * @param isSrcDir - Whether the project uses src directory structure\n * @returns The relative file path\n */\nexport function createRelativeFilePath(\n  baseDir: string,\n  filePath: string,\n  prefix: 'pages' | 'app',\n  isSrcDir: boolean\n): string {\n  const privatePrefix =\n    prefix === 'pages' ? 'private-next-pages' : 'private-next-app-dir'\n  const srcPrefix = isSrcDir ? 'src/' : ''\n  return join(\n    baseDir,\n    filePath.replace(new RegExp(`^${privatePrefix}/`), `${srcPrefix}${prefix}/`)\n  )\n}\n\n/**\n * Process pages routes from mapped pages\n * @param mappedPages - The mapped pages object\n * @param baseDir - The base directory path\n * @param isSrcDir - Whether the project uses src directory structure\n * @returns Object containing pageRoutes and pageApiRoutes\n */\nexport function processPageRoutes(\n  mappedPages: { [page: string]: string },\n  baseDir: string,\n  isSrcDir: boolean\n): {\n  pageRoutes: RouteInfo[]\n  pageApiRoutes: RouteInfo[]\n} {\n  const pageRoutes: RouteInfo[] = []\n  const pageApiRoutes: RouteInfo[] = []\n\n  for (const [route, filePath] of Object.entries(mappedPages)) {\n    const relativeFilePath = createRelativeFilePath(\n      baseDir,\n      filePath,\n      'pages',\n      isSrcDir\n    )\n\n    if (route.startsWith('/api/')) {\n      pageApiRoutes.push({\n        route: normalizePathSep(route),\n        filePath: relativeFilePath,\n      })\n    } else {\n      // Filter out _app, _error, _document\n      if (isReservedPage(route)) continue\n\n      pageRoutes.push({\n        route: normalizePathSep(route),\n        filePath: relativeFilePath,\n      })\n    }\n  }\n\n  return { pageRoutes, pageApiRoutes }\n}\n\n/**\n * Extract slots from app routes\n * @param mappedAppPages - The mapped app pages object\n * @returns Array of slot information\n */\nexport function extractSlotsFromAppRoutes(mappedAppPages: {\n  [page: string]: string\n}): SlotInfo[] {\n  const slots: SlotInfo[] = []\n\n  for (const [route] of Object.entries(mappedAppPages)) {\n    if (route === '/_not-found/page') continue\n\n    const segments = route.split('/')\n    for (let i = segments.length - 1; i >= 0; i--) {\n      const segment = segments[i]\n      if (isParallelRouteSegment(segment)) {\n        const parentPath = normalizeAppPath(segments.slice(0, i).join('/'))\n        const slotName = segment.slice(1)\n\n        // Check if the slot already exists\n        if (slots.some((s) => s.name === slotName && s.parent === parentPath))\n          continue\n\n        slots.push({\n          name: slotName,\n          parent: parentPath,\n        })\n        break\n      }\n    }\n  }\n\n  return slots\n}\n\n/**\n * Extract slots from default files\n * @param mappedDefaultFiles - The mapped default files object\n * @returns Array of slot information\n */\nexport function extractSlotsFromDefaultFiles(mappedDefaultFiles: {\n  [page: string]: string\n}): SlotInfo[] {\n  const slots: SlotInfo[] = []\n\n  for (const [route] of Object.entries(mappedDefaultFiles)) {\n    const segments = route.split('/')\n    for (let i = segments.length - 1; i >= 0; i--) {\n      const segment = segments[i]\n      if (isParallelRouteSegment(segment)) {\n        const parentPath = normalizeAppPath(segments.slice(0, i).join('/'))\n        const slotName = segment.slice(1)\n\n        // Check if the slot already exists\n        if (slots.some((s) => s.name === slotName && s.parent === parentPath))\n          continue\n\n        slots.push({\n          name: slotName,\n          parent: parentPath,\n        })\n        break\n      }\n    }\n  }\n\n  return slots\n}\n\n/**\n * Combine and deduplicate slot arrays using a Set\n * @param slotArrays - Arrays of slot information to combine\n * @returns Deduplicated array of slots\n */\nexport function combineSlots(...slotArrays: SlotInfo[][]): SlotInfo[] {\n  const slotSet = new Set<string>()\n  const result: SlotInfo[] = []\n\n  for (const slots of slotArrays) {\n    for (const slot of slots) {\n      const key = `${slot.name}:${slot.parent}`\n      if (!slotSet.has(key)) {\n        slotSet.add(key)\n        result.push(slot)\n      }\n    }\n  }\n\n  return result\n}\n\n/**\n * Process app routes from mapped app pages\n * @param mappedAppPages - The mapped app pages object\n * @param validFileMatcher - File matcher object\n * @param baseDir - The base directory path\n * @param isSrcDir - Whether the project uses src directory structure\n * @returns Array of route information\n */\nexport function processAppRoutes(\n  mappedAppPages: { [page: string]: string },\n  validFileMatcher: ReturnType<typeof createValidFileMatcher>,\n  baseDir: string,\n  isSrcDir: boolean\n): {\n  appRoutes: RouteInfo[]\n  appRouteHandlers: RouteInfo[]\n} {\n  const appRoutes: RouteInfo[] = []\n  const appRouteHandlers: RouteInfo[] = []\n\n  for (const [route, filePath] of Object.entries(mappedAppPages)) {\n    if (route === '/_not-found/page') continue\n\n    const relativeFilePath = createRelativeFilePath(\n      baseDir,\n      filePath,\n      'app',\n      isSrcDir\n    )\n\n    if (validFileMatcher.isAppRouterRoute(filePath)) {\n      appRouteHandlers.push({\n        route: normalizeAppPath(normalizePathSep(route)),\n        filePath: relativeFilePath,\n      })\n    } else {\n      appRoutes.push({\n        route: normalizeAppPath(normalizePathSep(route)),\n        filePath: relativeFilePath,\n      })\n    }\n  }\n\n  return { appRoutes, appRouteHandlers }\n}\n\n/**\n * Process layout routes from mapped app layouts\n * @param mappedAppLayouts - The mapped app layouts object\n * @param baseDir - The base directory path\n * @param isSrcDir - Whether the project uses src directory structure\n * @returns Array of layout route information\n */\nexport function processLayoutRoutes(\n  mappedAppLayouts: { [page: string]: string },\n  baseDir: string,\n  isSrcDir: boolean\n): RouteInfo[] {\n  const layoutRoutes: RouteInfo[] = []\n\n  for (const [route, filePath] of Object.entries(mappedAppLayouts)) {\n    const relativeFilePath = createRelativeFilePath(\n      baseDir,\n      filePath,\n      'app',\n      isSrcDir\n    )\n    layoutRoutes.push({\n      route: ensureLeadingSlash(\n        normalizeAppPath(normalizePathSep(route)).replace(/\\/layout$/, '')\n      ),\n      filePath: relativeFilePath,\n    })\n  }\n\n  return layoutRoutes\n}\n\nexport function sortByPageExts(pageExtensions: PageExtensions) {\n  return (a: string, b: string) => {\n    // prioritize entries according to pageExtensions order\n    // for consistency as fs order can differ across systems\n    // NOTE: this is reversed so preferred comes last and\n    // overrides prior\n    const aExt = extname(a)\n    const bExt = extname(b)\n\n    const aNoExt = a.substring(0, a.length - aExt.length)\n    const bNoExt = a.substring(0, b.length - bExt.length)\n\n    if (aNoExt !== bNoExt) return 0\n\n    // find extension index (skip '.' as pageExtensions doesn't have it)\n    const aExtIndex = pageExtensions.indexOf(aExt.substring(1))\n    const bExtIndex = pageExtensions.indexOf(bExt.substring(1))\n\n    return bExtIndex - aExtIndex\n  }\n}\n\nexport async function getStaticInfoIncludingLayouts({\n  isInsideAppDir,\n  pageExtensions,\n  pageFilePath,\n  appDir,\n  config: nextConfig,\n  isDev,\n  page,\n}: {\n  isInsideAppDir: boolean\n  pageExtensions: PageExtensions\n  pageFilePath: string\n  appDir: string | undefined\n  config: NextConfigComplete\n  isDev: boolean | undefined\n  page: string\n}): Promise<PageStaticInfo> {\n  // TODO: sync types for pages: PAGE_TYPES, ROUTER_TYPE, 'app' | 'pages', etc.\n  const pageType = isInsideAppDir ? PAGE_TYPES.APP : PAGE_TYPES.PAGES\n\n  const pageStaticInfo = await getPageStaticInfo({\n    nextConfig,\n    pageFilePath,\n    isDev,\n    page,\n    pageType,\n  })\n\n  if (pageStaticInfo.type === PAGE_TYPES.PAGES || !appDir) {\n    return pageStaticInfo\n  }\n\n  const segments = [pageStaticInfo]\n\n  // inherit from layout files only if it's a page route\n  if (isAppPageRoute(page)) {\n    const layoutFiles = []\n    const potentialLayoutFiles = pageExtensions.map((ext) => 'layout.' + ext)\n    let dir = dirname(pageFilePath)\n\n    // Uses startsWith to not include directories further up.\n    while (dir.startsWith(appDir)) {\n      for (const potentialLayoutFile of potentialLayoutFiles) {\n        const layoutFile = join(dir, potentialLayoutFile)\n        if (!fs.existsSync(layoutFile)) {\n          continue\n        }\n        layoutFiles.push(layoutFile)\n      }\n      // Walk up the directory tree\n      dir = join(dir, '..')\n    }\n\n    for (const layoutFile of layoutFiles) {\n      const layoutStaticInfo = await getAppPageStaticInfo({\n        nextConfig,\n        pageFilePath: layoutFile,\n        isDev,\n        page,\n        pageType: isInsideAppDir ? PAGE_TYPES.APP : PAGE_TYPES.PAGES,\n      })\n\n      segments.unshift(layoutStaticInfo)\n    }\n  }\n\n  const config = reduceAppConfig(segments)\n\n  return {\n    ...pageStaticInfo,\n    config,\n    runtime: config.runtime,\n    preferredRegion: config.preferredRegion,\n    maxDuration: config.maxDuration,\n  }\n}\n\ntype ObjectValue<T> = T extends { [key: string]: infer V } ? V : never\n\n/**\n * For a given page path removes the provided extensions.\n */\nexport function getPageFromPath(\n  pagePath: string,\n  pageExtensions: PageExtensions\n) {\n  let page = normalizePathSep(\n    pagePath.replace(new RegExp(`\\\\.+(${pageExtensions.join('|')})$`), '')\n  )\n\n  page = page.replace(/\\/index$/, '')\n\n  return page === '' ? '/' : page\n}\n\nexport function getPageFilePath({\n  absolutePagePath,\n  pagesDir,\n  appDir,\n  rootDir,\n}: {\n  absolutePagePath: string\n  pagesDir: string | undefined\n  appDir: string | undefined\n  rootDir: string\n}) {\n  if (absolutePagePath.startsWith(PAGES_DIR_ALIAS) && pagesDir) {\n    return absolutePagePath.replace(PAGES_DIR_ALIAS, pagesDir)\n  }\n\n  if (absolutePagePath.startsWith(APP_DIR_ALIAS) && appDir) {\n    return absolutePagePath.replace(APP_DIR_ALIAS, appDir)\n  }\n\n  if (absolutePagePath.startsWith(ROOT_DIR_ALIAS)) {\n    return absolutePagePath.replace(ROOT_DIR_ALIAS, rootDir)\n  }\n\n  return require.resolve(absolutePagePath)\n}\n\n/**\n * Creates a mapping of route to page file path for a given list of page paths.\n * For example ['/middleware.ts'] is turned into  { '/middleware': `${ROOT_DIR_ALIAS}/middleware.ts` }\n */\nexport async function createPagesMapping({\n  isDev,\n  pageExtensions,\n  pagePaths,\n  pagesType,\n  pagesDir,\n  appDir,\n}: {\n  isDev: boolean\n  pageExtensions: PageExtensions\n  pagePaths: string[]\n  pagesType: PAGE_TYPES\n  pagesDir: string | undefined\n  appDir: string | undefined\n}): Promise<MappedPages> {\n  const isAppRoute = pagesType === 'app'\n  const pages: MappedPages = {}\n  const promises = pagePaths.map<Promise<void>>(async (pagePath) => {\n    // Do not process .d.ts files as routes\n    if (pagePath.endsWith('.d.ts') && pageExtensions.includes('ts')) {\n      return\n    }\n\n    let pageKey = getPageFromPath(pagePath, pageExtensions)\n    if (isAppRoute) {\n      pageKey = pageKey.replace(/%5F/g, '_')\n      if (pageKey === '/not-found') {\n        pageKey = UNDERSCORE_NOT_FOUND_ROUTE_ENTRY\n      }\n    }\n\n    const normalizedPath = normalizePathSep(\n      join(\n        pagesType === 'pages'\n          ? PAGES_DIR_ALIAS\n          : pagesType === 'app'\n            ? APP_DIR_ALIAS\n            : ROOT_DIR_ALIAS,\n        pagePath\n      )\n    )\n\n    let route = pagesType === 'app' ? normalizeMetadataRoute(pageKey) : pageKey\n\n    if (\n      pagesType === 'app' &&\n      isMetadataRouteFile(pagePath, pageExtensions, true)\n    ) {\n      const filePath = join(appDir!, pagePath)\n      const staticInfo = await getPageStaticInfo({\n        nextConfig: {},\n        pageFilePath: filePath,\n        isDev,\n        page: pageKey,\n        pageType: pagesType,\n      })\n\n      route = normalizeMetadataPageToRoute(\n        route,\n        !!(staticInfo.generateImageMetadata || staticInfo.generateSitemaps)\n      )\n    }\n\n    pages[route] = normalizedPath\n  })\n\n  await Promise.all(promises)\n\n  switch (pagesType) {\n    case PAGE_TYPES.ROOT: {\n      return pages\n    }\n    case PAGE_TYPES.APP: {\n      const hasAppPages = Object.keys(pages).some((page) =>\n        page.endsWith('/page')\n      )\n      return {\n        // If there's any app pages existed, add a default /_not-found route as 404.\n        // If there's any custom /_not-found page, it will override the default one.\n        ...(hasAppPages && {\n          [UNDERSCORE_NOT_FOUND_ROUTE_ENTRY]: require.resolve(\n            'next/dist/client/components/builtin/global-not-found'\n          ),\n        }),\n        ...pages,\n      }\n    }\n    case PAGE_TYPES.PAGES: {\n      if (isDev) {\n        delete pages['/_app']\n        delete pages['/_error']\n        delete pages['/_document']\n      }\n\n      // In development we always alias these to allow Webpack to fallback to\n      // the correct source file so that HMR can work properly when a file is\n      // added or removed.\n      const root = isDev && pagesDir ? PAGES_DIR_ALIAS : 'next/dist/pages'\n\n      return {\n        '/_app': `${root}/_app`,\n        '/_error': `${root}/_error`,\n        '/_document': `${root}/_document`,\n        ...pages,\n      }\n    }\n    default: {\n      return {}\n    }\n  }\n}\n\nexport interface CreateEntrypointsParams {\n  buildId: string\n  config: NextConfigComplete\n  envFiles: LoadedEnvFiles\n  isDev?: boolean\n  pages: MappedPages\n  pagesDir?: string\n  previewMode: __ApiPreviewProps\n  rootDir: string\n  rootPaths?: MappedPages\n  appDir?: string\n  appPaths?: MappedPages\n  pageExtensions: PageExtensions\n  hasInstrumentationHook?: boolean\n}\n\nexport function getEdgeServerEntry(opts: {\n  rootDir: string\n  absolutePagePath: string\n  buildId: string\n  bundlePath: string\n  config: NextConfigComplete\n  isDev: boolean\n  isServerComponent: boolean\n  page: string\n  pages: MappedPages\n  middleware?: Partial<MiddlewareConfig>\n  pagesType: PAGE_TYPES\n  appDirLoader?: string\n  hasInstrumentationHook?: boolean\n  preferredRegion: string | string[] | undefined\n  middlewareConfig?: MiddlewareConfig\n}) {\n  if (\n    opts.pagesType === 'app' &&\n    isAppRouteRoute(opts.page) &&\n    opts.appDirLoader\n  ) {\n    const loaderParams: EdgeAppRouteLoaderQuery = {\n      absolutePagePath: opts.absolutePagePath,\n      page: opts.page,\n      appDirLoader: Buffer.from(opts.appDirLoader || '').toString('base64'),\n      nextConfig: Buffer.from(JSON.stringify(opts.config)).toString('base64'),\n      preferredRegion: opts.preferredRegion,\n      middlewareConfig: Buffer.from(\n        JSON.stringify(opts.middlewareConfig || {})\n      ).toString('base64'),\n      cacheHandlers: JSON.stringify(\n        opts.config.experimental.cacheHandlers || {}\n      ),\n    }\n\n    return {\n      import: `next-edge-app-route-loader?${stringify(loaderParams)}!`,\n      layer: WEBPACK_LAYERS.reactServerComponents,\n    }\n  }\n\n  if (isMiddlewareFile(opts.page)) {\n    const loaderParams: MiddlewareLoaderOptions = {\n      absolutePagePath: opts.absolutePagePath,\n      page: opts.page,\n      rootDir: opts.rootDir,\n      matchers: opts.middleware?.matchers\n        ? encodeMatchers(opts.middleware.matchers)\n        : '',\n      preferredRegion: opts.preferredRegion,\n      middlewareConfig: Buffer.from(\n        JSON.stringify(opts.middlewareConfig || {})\n      ).toString('base64'),\n    }\n\n    return {\n      import: `next-middleware-loader?${stringify(loaderParams)}!`,\n      layer: WEBPACK_LAYERS.middleware,\n    }\n  }\n\n  if (isAPIRoute(opts.page)) {\n    const loaderParams: EdgeFunctionLoaderOptions = {\n      absolutePagePath: opts.absolutePagePath,\n      page: opts.page,\n      rootDir: opts.rootDir,\n      preferredRegion: opts.preferredRegion,\n      middlewareConfig: Buffer.from(\n        JSON.stringify(opts.middlewareConfig || {})\n      ).toString('base64'),\n    }\n\n    return {\n      import: `next-edge-function-loader?${stringify(loaderParams)}!`,\n      layer: WEBPACK_LAYERS.apiEdge,\n    }\n  }\n\n  const loaderParams: EdgeSSRLoaderQuery = {\n    absolute500Path: opts.pages['/500'] || '',\n    absoluteAppPath: opts.pages['/_app'],\n    absoluteDocumentPath: opts.pages['/_document'],\n    absoluteErrorPath: opts.pages['/_error'],\n    absolutePagePath: opts.absolutePagePath,\n    dev: opts.isDev,\n    isServerComponent: opts.isServerComponent,\n    page: opts.page,\n    stringifiedConfig: Buffer.from(JSON.stringify(opts.config)).toString(\n      'base64'\n    ),\n    pagesType: opts.pagesType,\n    appDirLoader: Buffer.from(opts.appDirLoader || '').toString('base64'),\n    sriEnabled: !opts.isDev && !!opts.config.experimental.sri?.algorithm,\n    cacheHandler: opts.config.cacheHandler,\n    preferredRegion: opts.preferredRegion,\n    middlewareConfig: Buffer.from(\n      JSON.stringify(opts.middlewareConfig || {})\n    ).toString('base64'),\n    serverActions: opts.config.experimental.serverActions,\n    cacheHandlers: JSON.stringify(opts.config.experimental.cacheHandlers || {}),\n  }\n\n  return {\n    import: `next-edge-ssr-loader?${JSON.stringify(loaderParams)}!`,\n    // The Edge bundle includes the server in its entrypoint, so it has to\n    // be in the SSR layer — we later convert the page request to the RSC layer\n    // via a webpack rule.\n    layer: opts.appDirLoader ? WEBPACK_LAYERS.serverSideRendering : undefined,\n  }\n}\n\nexport function getInstrumentationEntry(opts: {\n  absolutePagePath: string\n  isEdgeServer: boolean\n  isDev: boolean\n}) {\n  // the '../' is needed to make sure the file is not chunked\n  const filename = `${\n    opts.isEdgeServer ? 'edge-' : opts.isDev ? '' : '../'\n  }${INSTRUMENTATION_HOOK_FILENAME}.js`\n\n  return {\n    import: opts.absolutePagePath,\n    filename,\n    layer: WEBPACK_LAYERS.instrument,\n  }\n}\n\nexport function getAppLoader() {\n  return process.env.BUILTIN_APP_LOADER\n    ? `builtin:next-app-loader`\n    : 'next-app-loader'\n}\n\nexport function getAppEntry(opts: Readonly<AppLoaderOptions>) {\n  if (process.env.NEXT_RSPACK && process.env.BUILTIN_APP_LOADER) {\n    ;(opts as any).projectRoot = normalize(join(__dirname, '../../..'))\n  }\n  return {\n    import: `${getAppLoader()}?${stringify(opts)}!`,\n    layer: WEBPACK_LAYERS.reactServerComponents,\n  }\n}\n\nexport function getClientEntry(opts: {\n  absolutePagePath: string\n  page: string\n}) {\n  const loaderOptions: ClientPagesLoaderOptions = {\n    absolutePagePath: opts.absolutePagePath,\n    page: opts.page,\n  }\n\n  const pageLoader = `next-client-pages-loader?${stringify(loaderOptions)}!`\n\n  // Make sure next/router is a dependency of _app or else chunk splitting\n  // might cause the router to not be able to load causing hydration\n  // to fail\n  return opts.page === '/_app'\n    ? [pageLoader, require.resolve('../client/router')]\n    : pageLoader\n}\n\nexport function runDependingOnPageType<T>(params: {\n  onClient: () => T\n  onEdgeServer: () => T\n  onServer: () => T\n  page: string\n  pageRuntime: ServerRuntime\n  pageType?: PAGE_TYPES\n}): void {\n  if (\n    params.pageType === PAGE_TYPES.ROOT &&\n    isInstrumentationHookFile(params.page)\n  ) {\n    params.onServer()\n    params.onEdgeServer()\n    return\n  }\n\n  if (isMiddlewareFile(params.page)) {\n    if (params.pageRuntime === 'nodejs') {\n      params.onServer()\n      return\n    } else {\n      params.onEdgeServer()\n      return\n    }\n  }\n\n  if (isAPIRoute(params.page)) {\n    if (isEdgeRuntime(params.pageRuntime)) {\n      params.onEdgeServer()\n      return\n    }\n\n    params.onServer()\n    return\n  }\n  if (params.page === '/_document') {\n    params.onServer()\n    return\n  }\n  if (\n    params.page === '/_app' ||\n    params.page === '/_error' ||\n    params.page === '/404' ||\n    params.page === '/500'\n  ) {\n    params.onClient()\n    params.onServer()\n    return\n  }\n  if (isEdgeRuntime(params.pageRuntime)) {\n    params.onClient()\n    params.onEdgeServer()\n    return\n  }\n\n  params.onClient()\n  params.onServer()\n  return\n}\n\nexport async function createEntrypoints(\n  params: CreateEntrypointsParams\n): Promise<{\n  client: webpack.EntryObject\n  server: webpack.EntryObject\n  edgeServer: webpack.EntryObject\n  middlewareMatchers: undefined\n}> {\n  const {\n    config,\n    pages,\n    pagesDir,\n    isDev,\n    rootDir,\n    rootPaths,\n    appDir,\n    appPaths,\n    pageExtensions,\n  } = params\n  const edgeServer: webpack.EntryObject = {}\n  const server: webpack.EntryObject = {}\n  const client: webpack.EntryObject = {}\n  let middlewareMatchers: MiddlewareMatcher[] | undefined = undefined\n\n  let appPathsPerRoute: Record<string, string[]> = {}\n  if (appDir && appPaths) {\n    for (const pathname in appPaths) {\n      const normalizedPath = normalizeAppPath(pathname)\n      const actualPath = appPaths[pathname]\n      if (!appPathsPerRoute[normalizedPath]) {\n        appPathsPerRoute[normalizedPath] = []\n      }\n      appPathsPerRoute[normalizedPath].push(\n        // TODO-APP: refactor to pass the page path from createPagesMapping instead.\n        getPageFromPath(actualPath, pageExtensions).replace(APP_DIR_ALIAS, '')\n      )\n    }\n\n    // TODO: find a better place to do this\n    normalizeCatchAllRoutes(appPathsPerRoute)\n\n    // Make sure to sort parallel routes to make the result deterministic.\n    appPathsPerRoute = Object.fromEntries(\n      Object.entries(appPathsPerRoute).map(([k, v]) => [k, v.sort()])\n    )\n  }\n\n  const getEntryHandler =\n    (mappings: MappedPages, pagesType: PAGE_TYPES): ((page: string) => void) =>\n    async (page) => {\n      const bundleFile = normalizePagePath(page)\n      const clientBundlePath = posix.join(pagesType, bundleFile)\n      const serverBundlePath =\n        pagesType === PAGE_TYPES.PAGES\n          ? posix.join('pages', bundleFile)\n          : pagesType === PAGE_TYPES.APP\n            ? posix.join('app', bundleFile)\n            : bundleFile.slice(1)\n\n      const absolutePagePath = mappings[page]\n\n      // Handle paths that have aliases\n      const pageFilePath = getPageFilePath({\n        absolutePagePath,\n        pagesDir,\n        appDir,\n        rootDir,\n      })\n\n      const isInsideAppDir =\n        !!appDir &&\n        (absolutePagePath.startsWith(APP_DIR_ALIAS) ||\n          absolutePagePath.startsWith(appDir))\n\n      const staticInfo: PageStaticInfo = await getStaticInfoIncludingLayouts({\n        isInsideAppDir,\n        pageExtensions,\n        pageFilePath,\n        appDir,\n        config,\n        isDev,\n        page,\n      })\n\n      // TODO(timneutkens): remove this\n      const isServerComponent =\n        isInsideAppDir && staticInfo.rsc !== RSC_MODULE_TYPES.client\n\n      if (isMiddlewareFile(page)) {\n        middlewareMatchers = staticInfo.middleware?.matchers ?? [\n          { regexp: '.*', originalSource: '/:path*' },\n        ]\n      }\n\n      const isInstrumentation =\n        isInstrumentationHookFile(page) && pagesType === PAGE_TYPES.ROOT\n\n      runDependingOnPageType({\n        page,\n        pageRuntime: staticInfo.runtime,\n        pageType: pagesType,\n        onClient: () => {\n          if (isServerComponent || isInsideAppDir) {\n            // We skip the initial entries for server component pages and let the\n            // server compiler inject them instead.\n          } else {\n            client[clientBundlePath] = getClientEntry({\n              absolutePagePath,\n              page,\n            })\n          }\n        },\n        onServer: () => {\n          if (pagesType === 'app' && appDir) {\n            const matchedAppPaths = appPathsPerRoute[normalizeAppPath(page)]\n            server[serverBundlePath] = getAppEntry({\n              page,\n              name: serverBundlePath,\n              pagePath: absolutePagePath,\n              appDir,\n              appPaths: matchedAppPaths,\n              pageExtensions,\n              basePath: config.basePath,\n              assetPrefix: config.assetPrefix,\n              nextConfigOutput: config.output,\n              preferredRegion: staticInfo.preferredRegion,\n              middlewareConfig: encodeToBase64(staticInfo.middleware || {}),\n              isGlobalNotFoundEnabled: config.experimental.globalNotFound\n                ? true\n                : undefined,\n            })\n          } else if (isInstrumentation) {\n            server[serverBundlePath.replace('src/', '')] =\n              getInstrumentationEntry({\n                absolutePagePath,\n                isEdgeServer: false,\n                isDev: false,\n              })\n          } else if (isMiddlewareFile(page)) {\n            server[serverBundlePath.replace('src/', '')] = getEdgeServerEntry({\n              ...params,\n              rootDir,\n              absolutePagePath: absolutePagePath,\n              bundlePath: clientBundlePath,\n              isDev: false,\n              isServerComponent,\n              page,\n              middleware: staticInfo?.middleware,\n              pagesType,\n              preferredRegion: staticInfo.preferredRegion,\n              middlewareConfig: staticInfo.middleware,\n            })\n          } else if (isAPIRoute(page)) {\n            server[serverBundlePath] = [\n              getRouteLoaderEntry({\n                kind: RouteKind.PAGES_API,\n                page,\n                absolutePagePath,\n                preferredRegion: staticInfo.preferredRegion,\n                middlewareConfig: staticInfo.middleware || {},\n              }),\n            ]\n          } else if (\n            !isMiddlewareFile(page) &&\n            !isInternalComponent(absolutePagePath) &&\n            !isNonRoutePagesPage(page)\n          ) {\n            server[serverBundlePath] = [\n              getRouteLoaderEntry({\n                kind: RouteKind.PAGES,\n                page,\n                pages,\n                absolutePagePath,\n                preferredRegion: staticInfo.preferredRegion,\n                middlewareConfig: staticInfo.middleware ?? {},\n              }),\n            ]\n          } else {\n            server[serverBundlePath] = [absolutePagePath]\n          }\n        },\n        onEdgeServer: () => {\n          let appDirLoader: string = ''\n          if (isInstrumentation) {\n            edgeServer[serverBundlePath.replace('src/', '')] =\n              getInstrumentationEntry({\n                absolutePagePath,\n                isEdgeServer: true,\n                isDev: false,\n              })\n          } else {\n            if (pagesType === 'app') {\n              const matchedAppPaths = appPathsPerRoute[normalizeAppPath(page)]\n              appDirLoader = getAppEntry({\n                name: serverBundlePath,\n                page,\n                pagePath: absolutePagePath,\n                appDir: appDir!,\n                appPaths: matchedAppPaths,\n                pageExtensions,\n                basePath: config.basePath,\n                assetPrefix: config.assetPrefix,\n                nextConfigOutput: config.output,\n                // This isn't used with edge as it needs to be set on the entry module, which will be the `edgeServerEntry` instead.\n                // Still passing it here for consistency.\n                preferredRegion: staticInfo.preferredRegion,\n                middlewareConfig: Buffer.from(\n                  JSON.stringify(staticInfo.middleware || {})\n                ).toString('base64'),\n                isGlobalNotFoundEnabled: config.experimental.globalNotFound\n                  ? true\n                  : undefined,\n              }).import\n            }\n            edgeServer[serverBundlePath] = getEdgeServerEntry({\n              ...params,\n              rootDir,\n              absolutePagePath: absolutePagePath,\n              bundlePath: clientBundlePath,\n              isDev: false,\n              isServerComponent,\n              page,\n              middleware: staticInfo?.middleware,\n              pagesType,\n              appDirLoader,\n              preferredRegion: staticInfo.preferredRegion,\n              middlewareConfig: staticInfo.middleware,\n            })\n          }\n        },\n      })\n    }\n\n  const promises: Promise<void[]>[] = []\n\n  if (appPaths) {\n    const entryHandler = getEntryHandler(appPaths, PAGE_TYPES.APP)\n    promises.push(Promise.all(Object.keys(appPaths).map(entryHandler)))\n  }\n  if (rootPaths) {\n    promises.push(\n      Promise.all(\n        Object.keys(rootPaths).map(getEntryHandler(rootPaths, PAGE_TYPES.ROOT))\n      )\n    )\n  }\n  promises.push(\n    Promise.all(\n      Object.keys(pages).map(getEntryHandler(pages, PAGE_TYPES.PAGES))\n    )\n  )\n\n  await Promise.all(promises)\n\n  // Optimization: If there's only one instrumentation hook in edge compiler, which means there's no edge server entry.\n  // We remove the edge instrumentation entry from edge compiler as it can be pure server side.\n  if (edgeServer.instrumentation && Object.keys(edgeServer).length === 1) {\n    delete edgeServer.instrumentation\n  }\n\n  return {\n    client,\n    server,\n    edgeServer,\n    middlewareMatchers,\n  }\n}\n\nexport function finalizeEntrypoint({\n  name,\n  compilerType,\n  value,\n  isServerComponent,\n  hasAppDir,\n}: {\n  compilerType: CompilerNameValues\n  name: string\n  value: ObjectValue<webpack.EntryObject>\n  isServerComponent?: boolean\n  hasAppDir?: boolean\n}): ObjectValue<webpack.EntryObject> {\n  const entry =\n    typeof value !== 'object' || Array.isArray(value)\n      ? { import: value }\n      : value\n\n  const isApi = name.startsWith('pages/api/')\n  const isInstrumentation = isInstrumentationHookFilename(name)\n\n  switch (compilerType) {\n    case COMPILER_NAMES.server: {\n      const layer = isApi\n        ? WEBPACK_LAYERS.apiNode\n        : isInstrumentation\n          ? WEBPACK_LAYERS.instrument\n          : isServerComponent\n            ? WEBPACK_LAYERS.reactServerComponents\n            : name.startsWith('pages/')\n              ? WEBPACK_LAYERS.pagesDirNode\n              : undefined\n\n      return {\n        publicPath: isApi ? '' : undefined,\n        runtime: isApi ? 'webpack-api-runtime' : 'webpack-runtime',\n        layer,\n        ...entry,\n      }\n    }\n    case COMPILER_NAMES.edgeServer: {\n      return {\n        layer: isApi\n          ? WEBPACK_LAYERS.apiEdge\n          : isMiddlewareFilename(name) || isInstrumentation\n            ? WEBPACK_LAYERS.middleware\n            : name.startsWith('pages/')\n              ? WEBPACK_LAYERS.pagesDirEdge\n              : undefined,\n        library: { name: ['_ENTRIES', `middleware_[name]`], type: 'assign' },\n        runtime: EDGE_RUNTIME_WEBPACK,\n        asyncChunks: false,\n        ...entry,\n      }\n    }\n    case COMPILER_NAMES.client: {\n      const isAppLayer =\n        hasAppDir &&\n        (name === CLIENT_STATIC_FILES_RUNTIME_MAIN_APP ||\n          name === APP_CLIENT_INTERNALS ||\n          name.startsWith('app/'))\n\n      if (\n        // Client special cases\n        name !== CLIENT_STATIC_FILES_RUNTIME_POLYFILLS &&\n        name !== CLIENT_STATIC_FILES_RUNTIME_MAIN &&\n        name !== CLIENT_STATIC_FILES_RUNTIME_MAIN_APP &&\n        name !== CLIENT_STATIC_FILES_RUNTIME_AMP &&\n        name !== CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH\n      ) {\n        if (isAppLayer) {\n          return {\n            dependOn: CLIENT_STATIC_FILES_RUNTIME_MAIN_APP,\n            layer: WEBPACK_LAYERS.appPagesBrowser,\n            ...entry,\n          }\n        }\n\n        return {\n          dependOn:\n            name.startsWith('pages/') && name !== 'pages/_app'\n              ? 'pages/_app'\n              : CLIENT_STATIC_FILES_RUNTIME_MAIN,\n          layer: WEBPACK_LAYERS.pagesDirBrowser,\n          ...entry,\n        }\n      }\n\n      if (isAppLayer) {\n        return {\n          layer: WEBPACK_LAYERS.appPagesBrowser,\n          ...entry,\n        }\n      }\n\n      return {\n        layer: WEBPACK_LAYERS.pagesDirBrowser,\n        ...entry,\n      }\n    }\n    default:\n      return compilerType satisfies never\n  }\n}\n"], "names": ["posix", "join", "dirname", "extname", "normalize", "stringify", "fs", "PAGES_DIR_ALIAS", "ROOT_DIR_ALIAS", "APP_DIR_ALIAS", "WEBPACK_LAYERS", "INSTRUMENTATION_HOOK_FILENAME", "isAPIRoute", "isEdgeRuntime", "APP_CLIENT_INTERNALS", "RSC_MODULE_TYPES", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "CLIENT_STATIC_FILES_RUNTIME_AMP", "CLIENT_STATIC_FILES_RUNTIME_MAIN", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "CLIENT_STATIC_FILES_RUNTIME_POLYFILLS", "CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH", "COMPILER_NAMES", "EDGE_RUNTIME_WEBPACK", "isMiddlewareFile", "isMiddlewareFilename", "isInstrumentationHookFile", "isInstrumentationHookFilename", "reduceAppConfig", "getAppPageStaticInfo", "getPageStaticInfo", "normalizePathSep", "normalizePagePath", "normalizeAppPath", "encodeMatchers", "isAppRouteRoute", "normalizeMetadataPageToRoute", "normalizeMetadataRoute", "getRouteLoaderEntry", "isInternalComponent", "isNonRoutePagesPage", "isMetadataRouteFile", "RouteKind", "encodeToBase64", "normalizeCatchAllRoutes", "PAGE_TYPES", "isAppPageRoute", "recursiveReadDir", "isReservedPage", "isParallelRouteSegment", "ensureLeadingSlash", "collectAppFiles", "appDir", "validFile<PERSON><PERSON><PERSON>", "allAppFiles", "pathnameFilter", "absolutePath", "isAppRouterPage", "isRootNotFound", "isAppLayoutPage", "isAppDefaultPage", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "part", "startsWith", "appPaths", "filter", "layoutPaths", "defaultPaths", "collectPagesFiles", "pagesDir", "isPageFile", "createRelativeFilePath", "baseDir", "filePath", "prefix", "isSrcDir", "privatePrefix", "srcPrefix", "replace", "RegExp", "processPageRoutes", "mappedPages", "pageRoutes", "pageApiRoutes", "route", "Object", "entries", "relativeFilePath", "push", "extractSlotsFromAppRoutes", "mappedAppPages", "slots", "segments", "split", "i", "length", "segment", "parentPath", "slice", "slotName", "some", "s", "name", "parent", "extractSlotsFromDefaultFiles", "mappedDefaultFiles", "combineSlots", "slotArrays", "slotSet", "Set", "result", "slot", "key", "has", "add", "processAppRoutes", "appRoutes", "appRouteHandlers", "isAppRouterRoute", "processLayoutRoutes", "mappedAppLayouts", "layoutRoutes", "sortByPageExts", "pageExtensions", "a", "b", "aExt", "bExt", "aNoExt", "substring", "bNoExt", "aExtIndex", "indexOf", "bExtIndex", "getStaticInfoIncludingLayouts", "isInsideAppDir", "pageFilePath", "config", "nextConfig", "isDev", "page", "pageType", "APP", "PAGES", "pageStaticInfo", "type", "layoutFiles", "potentialLayoutFiles", "map", "ext", "dir", "potentialLayoutFile", "layoutFile", "existsSync", "layoutStaticInfo", "unshift", "runtime", "preferredRegion", "maxDuration", "getPageFromPath", "pagePath", "getPageFilePath", "absolutePagePath", "rootDir", "require", "resolve", "createPagesMapping", "pagePaths", "pagesType", "isAppRoute", "pages", "promises", "endsWith", "includes", "page<PERSON><PERSON>", "normalizedPath", "staticInfo", "generateImageMetadata", "generateSitemaps", "Promise", "all", "ROOT", "hasAppPages", "keys", "root", "getEdgeServerEntry", "opts", "appDirLoader", "loaderParams", "<PERSON><PERSON><PERSON>", "from", "toString", "JSON", "middlewareConfig", "cacheHandlers", "experimental", "import", "layer", "reactServerComponents", "matchers", "middleware", "apiEdge", "absolute500Path", "absoluteAppPath", "absoluteDocumentPath", "absoluteErrorPath", "dev", "isServerComponent", "stringifiedConfig", "sriEnabled", "sri", "algorithm", "cache<PERSON><PERSON><PERSON>", "serverActions", "serverSideRendering", "undefined", "getInstrumentationEntry", "filename", "isEdgeServer", "instrument", "getApp<PERSON><PERSON>der", "process", "env", "BUILTIN_APP_LOADER", "getAppEntry", "NEXT_RSPACK", "projectRoot", "__dirname", "getClientEntry", "loaderOptions", "page<PERSON><PERSON>der", "runDependingOnPageType", "params", "onServer", "onEdgeServer", "pageRuntime", "onClient", "createEntrypoints", "rootPaths", "edgeServer", "server", "client", "middlewareMatchers", "appPathsPerRoute", "pathname", "actualPath", "fromEntries", "k", "v", "sort", "getEntryHandler", "mappings", "bundleFile", "clientBundlePath", "serverBundlePath", "rsc", "regexp", "originalSource", "isInstrumentation", "matchedAppPaths", "basePath", "assetPrefix", "nextConfigOutput", "output", "isGlobalNotFoundEnabled", "globalNotFound", "bundlePath", "kind", "PAGES_API", "<PERSON><PERSON><PERSON><PERSON>", "instrumentation", "finalizeEntrypoint", "compilerType", "value", "hasAppDir", "entry", "Array", "isArray", "isApi", "apiNode", "pagesDirNode", "publicPath", "pagesDirEdge", "library", "asyncChunks", "isApp<PERSON><PERSON>er", "dependOn", "appPagesBrowser", "pagesDirBrowser"], "mappings": "AAcA,SAASA,KAAK,EAAEC,IAAI,EAAEC,OAAO,EAAEC,OAAO,EAAEC,SAAS,QAAQ,OAAM;AAC/D,SAASC,SAAS,QAAQ,cAAa;AACvC,OAAOC,QAAQ,KAAI;AACnB,SACEC,eAAe,EACfC,cAAc,EACdC,aAAa,EACbC,cAAc,EACdC,6BAA6B,QACxB,mBAAkB;AACzB,SAASC,UAAU,QAAQ,sBAAqB;AAChD,SAASC,aAAa,QAAQ,yBAAwB;AACtD,SACEC,oBAAoB,EACpBC,gBAAgB,EAChBC,gCAAgC,QAC3B,0BAAyB;AAChC,SACEC,+BAA+B,EAC/BC,gCAAgC,EAChCC,oCAAoC,EACpCC,qCAAqC,EACrCC,yCAAyC,EACzCC,cAAc,EACdC,oBAAoB,QACf,0BAAyB;AAGhC,SACEC,gBAAgB,EAChBC,oBAAoB,EACpBC,yBAAyB,EACzBC,6BAA6B,EAC7BC,eAAe,QACV,UAAS;AAChB,SACEC,oBAAoB,EACpBC,iBAAiB,QACZ,kCAAiC;AACxC,SAASC,gBAAgB,QAAQ,6CAA4C;AAC7E,SAASC,iBAAiB,QAAQ,8CAA6C;AAE/E,SAASC,gBAAgB,QAAQ,uCAAsC;AACvE,SAASC,cAAc,QAAQ,2CAA0C;AAEzE,SAASC,eAAe,QAAQ,4BAA2B;AAC3D,SACEC,4BAA4B,EAC5BC,sBAAsB,QACjB,qCAAoC;AAC3C,SAASC,mBAAmB,QAAQ,sCAAqC;AACzE,SACEC,mBAAmB,EACnBC,mBAAmB,QACd,+BAA8B;AACrC,SAASC,mBAAmB,QAAQ,oCAAmC;AACvE,SAASC,SAAS,QAAQ,uBAAsB;AAChD,SAASC,cAAc,QAAQ,0BAAyB;AACxD,SAASC,uBAAuB,QAAQ,8BAA6B;AAGrE,SAASC,UAAU,QAAQ,oBAAmB;AAC9C,SAASC,cAAc,QAAQ,2BAA0B;AACzD,SAASC,gBAAgB,QAAQ,2BAA0B;AAE3D,SAASC,cAAc,QAAQ,UAAS;AACxC,SAASC,sBAAsB,QAAQ,wBAAuB;AAC9D,SAASC,kBAAkB,QAAQ,+CAA8C;AAEjF;;;;;CAKC,GACD,OAAO,eAAeC,gBACpBC,MAAc,EACdC,gBAA2D;IAM3D,gFAAgF;IAChF,MAAMC,cAAc,MAAMP,iBAAiBK,QAAQ;QACjDG,gBAAgB,CAACC,eACfH,iBAAiBI,eAAe,CAACD,iBACjCH,iBAAiBK,cAAc,CAACF,iBAChCH,iBAAiBM,eAAe,CAACH,iBACjCH,iBAAiBO,gBAAgB,CAACJ;QACpCK,kBAAkB,CAACC,OAASA,KAAKC,UAAU,CAAC;IAC9C;IAEA,4CAA4C;IAC5C,MAAMC,WAAWV,YAAYW,MAAM,CACjC,CAACT,eACCH,iBAAiBI,eAAe,CAACD,iBACjCH,iBAAiBK,cAAc,CAACF;IAEpC,MAAMU,cAAcZ,YAAYW,MAAM,CAAC,CAACT,eACtCH,iBAAiBM,eAAe,CAACH;IAEnC,MAAMW,eAAeb,YAAYW,MAAM,CAAC,CAACT,eACvCH,iBAAiBO,gBAAgB,CAACJ;IAGpC,OAAO;QAAEQ;QAAUE;QAAaC;IAAa;AAC/C;AAEA;;;;;CAKC,GACD,OAAO,eAAeC,kBACpBC,QAAgB,EAChBhB,gBAA2D;IAE3D,OAAON,iBAAiBsB,UAAU;QAChCd,gBAAgBF,iBAAiBiB,UAAU;IAC7C;AACF;AAaA;;;;;;;CAOC,GACD,OAAO,SAASC,uBACdC,OAAe,EACfC,QAAgB,EAChBC,MAAuB,EACvBC,QAAiB;IAEjB,MAAMC,gBACJF,WAAW,UAAU,uBAAuB;IAC9C,MAAMG,YAAYF,WAAW,SAAS;IACtC,OAAO1E,KACLuE,SACAC,SAASK,OAAO,CAAC,IAAIC,OAAO,CAAC,CAAC,EAAEH,cAAc,CAAC,CAAC,GAAG,GAAGC,YAAYH,OAAO,CAAC,CAAC;AAE/E;AAEA;;;;;;CAMC,GACD,OAAO,SAASM,kBACdC,WAAuC,EACvCT,OAAe,EACfG,QAAiB;IAKjB,MAAMO,aAA0B,EAAE;IAClC,MAAMC,gBAA6B,EAAE;IAErC,KAAK,MAAM,CAACC,OAAOX,SAAS,IAAIY,OAAOC,OAAO,CAACL,aAAc;QAC3D,MAAMM,mBAAmBhB,uBACvBC,SACAC,UACA,SACAE;QAGF,IAAIS,MAAMrB,UAAU,CAAC,UAAU;YAC7BoB,cAAcK,IAAI,CAAC;gBACjBJ,OAAOrD,iBAAiBqD;gBACxBX,UAAUc;YACZ;QACF,OAAO;YACL,qCAAqC;YACrC,IAAIvC,eAAeoC,QAAQ;YAE3BF,WAAWM,IAAI,CAAC;gBACdJ,OAAOrD,iBAAiBqD;gBACxBX,UAAUc;YACZ;QACF;IACF;IAEA,OAAO;QAAEL;QAAYC;IAAc;AACrC;AAEA;;;;CAIC,GACD,OAAO,SAASM,0BAA0BC,cAEzC;IACC,MAAMC,QAAoB,EAAE;IAE5B,KAAK,MAAM,CAACP,MAAM,IAAIC,OAAOC,OAAO,CAACI,gBAAiB;QACpD,IAAIN,UAAU,oBAAoB;QAElC,MAAMQ,WAAWR,MAAMS,KAAK,CAAC;QAC7B,IAAK,IAAIC,IAAIF,SAASG,MAAM,GAAG,GAAGD,KAAK,GAAGA,IAAK;YAC7C,MAAME,UAAUJ,QAAQ,CAACE,EAAE;YAC3B,IAAI7C,uBAAuB+C,UAAU;gBACnC,MAAMC,aAAahE,iBAAiB2D,SAASM,KAAK,CAAC,GAAGJ,GAAG7F,IAAI,CAAC;gBAC9D,MAAMkG,WAAWH,QAAQE,KAAK,CAAC;gBAE/B,mCAAmC;gBACnC,IAAIP,MAAMS,IAAI,CAAC,CAACC,IAAMA,EAAEC,IAAI,KAAKH,YAAYE,EAAEE,MAAM,KAAKN,aACxD;gBAEFN,MAAMH,IAAI,CAAC;oBACTc,MAAMH;oBACNI,QAAQN;gBACV;gBACA;YACF;QACF;IACF;IAEA,OAAON;AACT;AAEA;;;;CAIC,GACD,OAAO,SAASa,6BAA6BC,kBAE5C;IACC,MAAMd,QAAoB,EAAE;IAE5B,KAAK,MAAM,CAACP,MAAM,IAAIC,OAAOC,OAAO,CAACmB,oBAAqB;QACxD,MAAMb,WAAWR,MAAMS,KAAK,CAAC;QAC7B,IAAK,IAAIC,IAAIF,SAASG,MAAM,GAAG,GAAGD,KAAK,GAAGA,IAAK;YAC7C,MAAME,UAAUJ,QAAQ,CAACE,EAAE;YAC3B,IAAI7C,uBAAuB+C,UAAU;gBACnC,MAAMC,aAAahE,iBAAiB2D,SAASM,KAAK,CAAC,GAAGJ,GAAG7F,IAAI,CAAC;gBAC9D,MAAMkG,WAAWH,QAAQE,KAAK,CAAC;gBAE/B,mCAAmC;gBACnC,IAAIP,MAAMS,IAAI,CAAC,CAACC,IAAMA,EAAEC,IAAI,KAAKH,YAAYE,EAAEE,MAAM,KAAKN,aACxD;gBAEFN,MAAMH,IAAI,CAAC;oBACTc,MAAMH;oBACNI,QAAQN;gBACV;gBACA;YACF;QACF;IACF;IAEA,OAAON;AACT;AAEA;;;;CAIC,GACD,OAAO,SAASe,aAAa,GAAGC,UAAwB;IACtD,MAAMC,UAAU,IAAIC;IACpB,MAAMC,SAAqB,EAAE;IAE7B,KAAK,MAAMnB,SAASgB,WAAY;QAC9B,KAAK,MAAMI,QAAQpB,MAAO;YACxB,MAAMqB,MAAM,GAAGD,KAAKT,IAAI,CAAC,CAAC,EAAES,KAAKR,MAAM,EAAE;YACzC,IAAI,CAACK,QAAQK,GAAG,CAACD,MAAM;gBACrBJ,QAAQM,GAAG,CAACF;gBACZF,OAAOtB,IAAI,CAACuB;YACd;QACF;IACF;IAEA,OAAOD;AACT;AAEA;;;;;;;CAOC,GACD,OAAO,SAASK,iBACdzB,cAA0C,EAC1CrC,gBAA2D,EAC3DmB,OAAe,EACfG,QAAiB;IAKjB,MAAMyC,YAAyB,EAAE;IACjC,MAAMC,mBAAgC,EAAE;IAExC,KAAK,MAAM,CAACjC,OAAOX,SAAS,IAAIY,OAAOC,OAAO,CAACI,gBAAiB;QAC9D,IAAIN,UAAU,oBAAoB;QAElC,MAAMG,mBAAmBhB,uBACvBC,SACAC,UACA,OACAE;QAGF,IAAItB,iBAAiBiE,gBAAgB,CAAC7C,WAAW;YAC/C4C,iBAAiB7B,IAAI,CAAC;gBACpBJ,OAAOnD,iBAAiBF,iBAAiBqD;gBACzCX,UAAUc;YACZ;QACF,OAAO;YACL6B,UAAU5B,IAAI,CAAC;gBACbJ,OAAOnD,iBAAiBF,iBAAiBqD;gBACzCX,UAAUc;YACZ;QACF;IACF;IAEA,OAAO;QAAE6B;QAAWC;IAAiB;AACvC;AAEA;;;;;;CAMC,GACD,OAAO,SAASE,oBACdC,gBAA4C,EAC5ChD,OAAe,EACfG,QAAiB;IAEjB,MAAM8C,eAA4B,EAAE;IAEpC,KAAK,MAAM,CAACrC,OAAOX,SAAS,IAAIY,OAAOC,OAAO,CAACkC,kBAAmB;QAChE,MAAMjC,mBAAmBhB,uBACvBC,SACAC,UACA,OACAE;QAEF8C,aAAajC,IAAI,CAAC;YAChBJ,OAAOlC,mBACLjB,iBAAiBF,iBAAiBqD,QAAQN,OAAO,CAAC,aAAa;YAEjEL,UAAUc;QACZ;IACF;IAEA,OAAOkC;AACT;AAEA,OAAO,SAASC,eAAeC,cAA8B;IAC3D,OAAO,CAACC,GAAWC;QACjB,uDAAuD;QACvD,wDAAwD;QACxD,qDAAqD;QACrD,kBAAkB;QAClB,MAAMC,OAAO3H,QAAQyH;QACrB,MAAMG,OAAO5H,QAAQ0H;QAErB,MAAMG,SAASJ,EAAEK,SAAS,CAAC,GAAGL,EAAE7B,MAAM,GAAG+B,KAAK/B,MAAM;QACpD,MAAMmC,SAASN,EAAEK,SAAS,CAAC,GAAGJ,EAAE9B,MAAM,GAAGgC,KAAKhC,MAAM;QAEpD,IAAIiC,WAAWE,QAAQ,OAAO;QAE9B,oEAAoE;QACpE,MAAMC,YAAYR,eAAeS,OAAO,CAACN,KAAKG,SAAS,CAAC;QACxD,MAAMI,YAAYV,eAAeS,OAAO,CAACL,KAAKE,SAAS,CAAC;QAExD,OAAOI,YAAYF;IACrB;AACF;AAEA,OAAO,eAAeG,8BAA8B,EAClDC,cAAc,EACdZ,cAAc,EACda,YAAY,EACZpF,MAAM,EACNqF,QAAQC,UAAU,EAClBC,KAAK,EACLC,IAAI,EASL;IACC,6EAA6E;IAC7E,MAAMC,WAAWN,iBAAiB1F,WAAWiG,GAAG,GAAGjG,WAAWkG,KAAK;IAEnE,MAAMC,iBAAiB,MAAMlH,kBAAkB;QAC7C4G;QACAF;QACAG;QACAC;QACAC;IACF;IAEA,IAAIG,eAAeC,IAAI,KAAKpG,WAAWkG,KAAK,IAAI,CAAC3F,QAAQ;QACvD,OAAO4F;IACT;IAEA,MAAMpD,WAAW;QAACoD;KAAe;IAEjC,sDAAsD;IACtD,IAAIlG,eAAe8F,OAAO;QACxB,MAAMM,cAAc,EAAE;QACtB,MAAMC,uBAAuBxB,eAAeyB,GAAG,CAAC,CAACC,MAAQ,YAAYA;QACrE,IAAIC,MAAMpJ,QAAQsI;QAElB,yDAAyD;QACzD,MAAOc,IAAIvF,UAAU,CAACX,QAAS;YAC7B,KAAK,MAAMmG,uBAAuBJ,qBAAsB;gBACtD,MAAMK,aAAavJ,KAAKqJ,KAAKC;gBAC7B,IAAI,CAACjJ,GAAGmJ,UAAU,CAACD,aAAa;oBAC9B;gBACF;gBACAN,YAAY1D,IAAI,CAACgE;YACnB;YACA,6BAA6B;YAC7BF,MAAMrJ,KAAKqJ,KAAK;QAClB;QAEA,KAAK,MAAME,cAAcN,YAAa;YACpC,MAAMQ,mBAAmB,MAAM7H,qBAAqB;gBAClD6G;gBACAF,cAAcgB;gBACdb;gBACAC;gBACAC,UAAUN,iBAAiB1F,WAAWiG,GAAG,GAAGjG,WAAWkG,KAAK;YAC9D;YAEAnD,SAAS+D,OAAO,CAACD;QACnB;IACF;IAEA,MAAMjB,SAAS7G,gBAAgBgE;IAE/B,OAAO;QACL,GAAGoD,cAAc;QACjBP;QACAmB,SAASnB,OAAOmB,OAAO;QACvBC,iBAAiBpB,OAAOoB,eAAe;QACvCC,aAAarB,OAAOqB,WAAW;IACjC;AACF;AAIA;;CAEC,GACD,OAAO,SAASC,gBACdC,QAAgB,EAChBrC,cAA8B;IAE9B,IAAIiB,OAAO7G,iBACTiI,SAASlF,OAAO,CAAC,IAAIC,OAAO,CAAC,KAAK,EAAE4C,eAAe1H,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG;IAGrE2I,OAAOA,KAAK9D,OAAO,CAAC,YAAY;IAEhC,OAAO8D,SAAS,KAAK,MAAMA;AAC7B;AAEA,OAAO,SAASqB,gBAAgB,EAC9BC,gBAAgB,EAChB7F,QAAQ,EACRjB,MAAM,EACN+G,OAAO,EAMR;IACC,IAAID,iBAAiBnG,UAAU,CAACxD,oBAAoB8D,UAAU;QAC5D,OAAO6F,iBAAiBpF,OAAO,CAACvE,iBAAiB8D;IACnD;IAEA,IAAI6F,iBAAiBnG,UAAU,CAACtD,kBAAkB2C,QAAQ;QACxD,OAAO8G,iBAAiBpF,OAAO,CAACrE,eAAe2C;IACjD;IAEA,IAAI8G,iBAAiBnG,UAAU,CAACvD,iBAAiB;QAC/C,OAAO0J,iBAAiBpF,OAAO,CAACtE,gBAAgB2J;IAClD;IAEA,OAAOC,QAAQC,OAAO,CAACH;AACzB;AAEA;;;CAGC,GACD,OAAO,eAAeI,mBAAmB,EACvC3B,KAAK,EACLhB,cAAc,EACd4C,SAAS,EACTC,SAAS,EACTnG,QAAQ,EACRjB,MAAM,EAQP;IACC,MAAMqH,aAAaD,cAAc;IACjC,MAAME,QAAqB,CAAC;IAC5B,MAAMC,WAAWJ,UAAUnB,GAAG,CAAgB,OAAOY;QACnD,uCAAuC;QACvC,IAAIA,SAASY,QAAQ,CAAC,YAAYjD,eAAekD,QAAQ,CAAC,OAAO;YAC/D;QACF;QAEA,IAAIC,UAAUf,gBAAgBC,UAAUrC;QACxC,IAAI8C,YAAY;YACdK,UAAUA,QAAQhG,OAAO,CAAC,QAAQ;YAClC,IAAIgG,YAAY,cAAc;gBAC5BA,UAAU9J;YACZ;QACF;QAEA,MAAM+J,iBAAiBhJ,iBACrB9B,KACEuK,cAAc,UACVjK,kBACAiK,cAAc,QACZ/J,gBACAD,gBACNwJ;QAIJ,IAAI5E,QAAQoF,cAAc,QAAQnI,uBAAuByI,WAAWA;QAEpE,IACEN,cAAc,SACd/H,oBAAoBuH,UAAUrC,gBAAgB,OAC9C;YACA,MAAMlD,WAAWxE,KAAKmD,QAAS4G;YAC/B,MAAMgB,aAAa,MAAMlJ,kBAAkB;gBACzC4G,YAAY,CAAC;gBACbF,cAAc/D;gBACdkE;gBACAC,MAAMkC;gBACNjC,UAAU2B;YACZ;YAEApF,QAAQhD,6BACNgD,OACA,CAAC,CAAE4F,CAAAA,WAAWC,qBAAqB,IAAID,WAAWE,gBAAgB,AAAD;QAErE;QAEAR,KAAK,CAACtF,MAAM,GAAG2F;IACjB;IAEA,MAAMI,QAAQC,GAAG,CAACT;IAElB,OAAQH;QACN,KAAK3H,WAAWwI,IAAI;YAAE;gBACpB,OAAOX;YACT;QACA,KAAK7H,WAAWiG,GAAG;YAAE;gBACnB,MAAMwC,cAAcjG,OAAOkG,IAAI,CAACb,OAAOtE,IAAI,CAAC,CAACwC,OAC3CA,KAAKgC,QAAQ,CAAC;gBAEhB,OAAO;oBACL,4EAA4E;oBAC5E,4EAA4E;oBAC5E,GAAIU,eAAe;wBACjB,CAACtK,iCAAiC,EAAEoJ,QAAQC,OAAO,CACjD;oBAEJ,CAAC;oBACD,GAAGK,KAAK;gBACV;YACF;QACA,KAAK7H,WAAWkG,KAAK;YAAE;gBACrB,IAAIJ,OAAO;oBACT,OAAO+B,KAAK,CAAC,QAAQ;oBACrB,OAAOA,KAAK,CAAC,UAAU;oBACvB,OAAOA,KAAK,CAAC,aAAa;gBAC5B;gBAEA,uEAAuE;gBACvE,uEAAuE;gBACvE,oBAAoB;gBACpB,MAAMc,OAAO7C,SAAStE,WAAW9D,kBAAkB;gBAEnD,OAAO;oBACL,SAAS,GAAGiL,KAAK,KAAK,CAAC;oBACvB,WAAW,GAAGA,KAAK,OAAO,CAAC;oBAC3B,cAAc,GAAGA,KAAK,UAAU,CAAC;oBACjC,GAAGd,KAAK;gBACV;YACF;QACA;YAAS;gBACP,OAAO,CAAC;YACV;IACF;AACF;AAkBA,OAAO,SAASe,mBAAmBC,IAgBlC;QA6EgCA;IA5E/B,IACEA,KAAKlB,SAAS,KAAK,SACnBrI,gBAAgBuJ,KAAK9C,IAAI,KACzB8C,KAAKC,YAAY,EACjB;QACA,MAAMC,eAAwC;YAC5C1B,kBAAkBwB,KAAKxB,gBAAgB;YACvCtB,MAAM8C,KAAK9C,IAAI;YACf+C,cAAcE,OAAOC,IAAI,CAACJ,KAAKC,YAAY,IAAI,IAAII,QAAQ,CAAC;YAC5DrD,YAAYmD,OAAOC,IAAI,CAACE,KAAK3L,SAAS,CAACqL,KAAKjD,MAAM,GAAGsD,QAAQ,CAAC;YAC9DlC,iBAAiB6B,KAAK7B,eAAe;YACrCoC,kBAAkBJ,OAAOC,IAAI,CAC3BE,KAAK3L,SAAS,CAACqL,KAAKO,gBAAgB,IAAI,CAAC,IACzCF,QAAQ,CAAC;YACXG,eAAeF,KAAK3L,SAAS,CAC3BqL,KAAKjD,MAAM,CAAC0D,YAAY,CAACD,aAAa,IAAI,CAAC;QAE/C;QAEA,OAAO;YACLE,QAAQ,CAAC,2BAA2B,EAAE/L,UAAUuL,cAAc,CAAC,CAAC;YAChES,OAAO3L,eAAe4L,qBAAqB;QAC7C;IACF;IAEA,IAAI9K,iBAAiBkK,KAAK9C,IAAI,GAAG;YAKnB8C;QAJZ,MAAME,eAAwC;YAC5C1B,kBAAkBwB,KAAKxB,gBAAgB;YACvCtB,MAAM8C,KAAK9C,IAAI;YACfuB,SAASuB,KAAKvB,OAAO;YACrBoC,UAAUb,EAAAA,mBAAAA,KAAKc,UAAU,qBAAfd,iBAAiBa,QAAQ,IAC/BrK,eAAewJ,KAAKc,UAAU,CAACD,QAAQ,IACvC;YACJ1C,iBAAiB6B,KAAK7B,eAAe;YACrCoC,kBAAkBJ,OAAOC,IAAI,CAC3BE,KAAK3L,SAAS,CAACqL,KAAKO,gBAAgB,IAAI,CAAC,IACzCF,QAAQ,CAAC;QACb;QAEA,OAAO;YACLK,QAAQ,CAAC,uBAAuB,EAAE/L,UAAUuL,cAAc,CAAC,CAAC;YAC5DS,OAAO3L,eAAe8L,UAAU;QAClC;IACF;IAEA,IAAI5L,WAAW8K,KAAK9C,IAAI,GAAG;QACzB,MAAMgD,eAA0C;YAC9C1B,kBAAkBwB,KAAKxB,gBAAgB;YACvCtB,MAAM8C,KAAK9C,IAAI;YACfuB,SAASuB,KAAKvB,OAAO;YACrBN,iBAAiB6B,KAAK7B,eAAe;YACrCoC,kBAAkBJ,OAAOC,IAAI,CAC3BE,KAAK3L,SAAS,CAACqL,KAAKO,gBAAgB,IAAI,CAAC,IACzCF,QAAQ,CAAC;QACb;QAEA,OAAO;YACLK,QAAQ,CAAC,0BAA0B,EAAE/L,UAAUuL,cAAc,CAAC,CAAC;YAC/DS,OAAO3L,eAAe+L,OAAO;QAC/B;IACF;IAEA,MAAMb,eAAmC;QACvCc,iBAAiBhB,KAAKhB,KAAK,CAAC,OAAO,IAAI;QACvCiC,iBAAiBjB,KAAKhB,KAAK,CAAC,QAAQ;QACpCkC,sBAAsBlB,KAAKhB,KAAK,CAAC,aAAa;QAC9CmC,mBAAmBnB,KAAKhB,KAAK,CAAC,UAAU;QACxCR,kBAAkBwB,KAAKxB,gBAAgB;QACvC4C,KAAKpB,KAAK/C,KAAK;QACfoE,mBAAmBrB,KAAKqB,iBAAiB;QACzCnE,MAAM8C,KAAK9C,IAAI;QACfoE,mBAAmBnB,OAAOC,IAAI,CAACE,KAAK3L,SAAS,CAACqL,KAAKjD,MAAM,GAAGsD,QAAQ,CAClE;QAEFvB,WAAWkB,KAAKlB,SAAS;QACzBmB,cAAcE,OAAOC,IAAI,CAACJ,KAAKC,YAAY,IAAI,IAAII,QAAQ,CAAC;QAC5DkB,YAAY,CAACvB,KAAK/C,KAAK,IAAI,CAAC,GAAC+C,gCAAAA,KAAKjD,MAAM,CAAC0D,YAAY,CAACe,GAAG,qBAA5BxB,8BAA8ByB,SAAS;QACpEC,cAAc1B,KAAKjD,MAAM,CAAC2E,YAAY;QACtCvD,iBAAiB6B,KAAK7B,eAAe;QACrCoC,kBAAkBJ,OAAOC,IAAI,CAC3BE,KAAK3L,SAAS,CAACqL,KAAKO,gBAAgB,IAAI,CAAC,IACzCF,QAAQ,CAAC;QACXsB,eAAe3B,KAAKjD,MAAM,CAAC0D,YAAY,CAACkB,aAAa;QACrDnB,eAAeF,KAAK3L,SAAS,CAACqL,KAAKjD,MAAM,CAAC0D,YAAY,CAACD,aAAa,IAAI,CAAC;IAC3E;IAEA,OAAO;QACLE,QAAQ,CAAC,qBAAqB,EAAEJ,KAAK3L,SAAS,CAACuL,cAAc,CAAC,CAAC;QAC/D,sEAAsE;QACtE,2EAA2E;QAC3E,sBAAsB;QACtBS,OAAOX,KAAKC,YAAY,GAAGjL,eAAe4M,mBAAmB,GAAGC;IAClE;AACF;AAEA,OAAO,SAASC,wBAAwB9B,IAIvC;IACC,2DAA2D;IAC3D,MAAM+B,WAAW,GACf/B,KAAKgC,YAAY,GAAG,UAAUhC,KAAK/C,KAAK,GAAG,KAAK,QAC/ChI,8BAA8B,GAAG,CAAC;IAErC,OAAO;QACLyL,QAAQV,KAAKxB,gBAAgB;QAC7BuD;QACApB,OAAO3L,eAAeiN,UAAU;IAClC;AACF;AAEA,OAAO,SAASC;IACd,OAAOC,QAAQC,GAAG,CAACC,kBAAkB,GACjC,CAAC,uBAAuB,CAAC,GACzB;AACN;AAEA,OAAO,SAASC,YAAYtC,IAAgC;IAC1D,IAAImC,QAAQC,GAAG,CAACG,WAAW,IAAIJ,QAAQC,GAAG,CAACC,kBAAkB,EAAE;;QAC3DrC,KAAawC,WAAW,GAAG9N,UAAUH,KAAKkO,WAAW;IACzD;IACA,OAAO;QACL/B,QAAQ,GAAGwB,eAAe,CAAC,EAAEvN,UAAUqL,MAAM,CAAC,CAAC;QAC/CW,OAAO3L,eAAe4L,qBAAqB;IAC7C;AACF;AAEA,OAAO,SAAS8B,eAAe1C,IAG9B;IACC,MAAM2C,gBAA0C;QAC9CnE,kBAAkBwB,KAAKxB,gBAAgB;QACvCtB,MAAM8C,KAAK9C,IAAI;IACjB;IAEA,MAAM0F,aAAa,CAAC,yBAAyB,EAAEjO,UAAUgO,eAAe,CAAC,CAAC;IAE1E,wEAAwE;IACxE,kEAAkE;IAClE,UAAU;IACV,OAAO3C,KAAK9C,IAAI,KAAK,UACjB;QAAC0F;QAAYlE,QAAQC,OAAO,CAAC;KAAoB,GACjDiE;AACN;AAEA,OAAO,SAASC,uBAA0BC,MAOzC;IACC,IACEA,OAAO3F,QAAQ,KAAKhG,WAAWwI,IAAI,IACnC3J,0BAA0B8M,OAAO5F,IAAI,GACrC;QACA4F,OAAOC,QAAQ;QACfD,OAAOE,YAAY;QACnB;IACF;IAEA,IAAIlN,iBAAiBgN,OAAO5F,IAAI,GAAG;QACjC,IAAI4F,OAAOG,WAAW,KAAK,UAAU;YACnCH,OAAOC,QAAQ;YACf;QACF,OAAO;YACLD,OAAOE,YAAY;YACnB;QACF;IACF;IAEA,IAAI9N,WAAW4N,OAAO5F,IAAI,GAAG;QAC3B,IAAI/H,cAAc2N,OAAOG,WAAW,GAAG;YACrCH,OAAOE,YAAY;YACnB;QACF;QAEAF,OAAOC,QAAQ;QACf;IACF;IACA,IAAID,OAAO5F,IAAI,KAAK,cAAc;QAChC4F,OAAOC,QAAQ;QACf;IACF;IACA,IACED,OAAO5F,IAAI,KAAK,WAChB4F,OAAO5F,IAAI,KAAK,aAChB4F,OAAO5F,IAAI,KAAK,UAChB4F,OAAO5F,IAAI,KAAK,QAChB;QACA4F,OAAOI,QAAQ;QACfJ,OAAOC,QAAQ;QACf;IACF;IACA,IAAI5N,cAAc2N,OAAOG,WAAW,GAAG;QACrCH,OAAOI,QAAQ;QACfJ,OAAOE,YAAY;QACnB;IACF;IAEAF,OAAOI,QAAQ;IACfJ,OAAOC,QAAQ;IACf;AACF;AAEA,OAAO,eAAeI,kBACpBL,MAA+B;IAO/B,MAAM,EACJ/F,MAAM,EACNiC,KAAK,EACLrG,QAAQ,EACRsE,KAAK,EACLwB,OAAO,EACP2E,SAAS,EACT1L,MAAM,EACNY,QAAQ,EACR2D,cAAc,EACf,GAAG6G;IACJ,MAAMO,aAAkC,CAAC;IACzC,MAAMC,SAA8B,CAAC;IACrC,MAAMC,SAA8B,CAAC;IACrC,IAAIC,qBAAsD3B;IAE1D,IAAI4B,mBAA6C,CAAC;IAClD,IAAI/L,UAAUY,UAAU;QACtB,IAAK,MAAMoL,YAAYpL,SAAU;YAC/B,MAAM+G,iBAAiB9I,iBAAiBmN;YACxC,MAAMC,aAAarL,QAAQ,CAACoL,SAAS;YACrC,IAAI,CAACD,gBAAgB,CAACpE,eAAe,EAAE;gBACrCoE,gBAAgB,CAACpE,eAAe,GAAG,EAAE;YACvC;YACAoE,gBAAgB,CAACpE,eAAe,CAACvF,IAAI,CACnC,4EAA4E;YAC5EuE,gBAAgBsF,YAAY1H,gBAAgB7C,OAAO,CAACrE,eAAe;QAEvE;QAEA,uCAAuC;QACvCmC,wBAAwBuM;QAExB,sEAAsE;QACtEA,mBAAmB9J,OAAOiK,WAAW,CACnCjK,OAAOC,OAAO,CAAC6J,kBAAkB/F,GAAG,CAAC,CAAC,CAACmG,GAAGC,EAAE,GAAK;gBAACD;gBAAGC,EAAEC,IAAI;aAAG;IAElE;IAEA,MAAMC,kBACJ,CAACC,UAAuBnF,YACxB,OAAO5B;YACL,MAAMgH,aAAa5N,kBAAkB4G;YACrC,MAAMiH,mBAAmB7P,MAAMC,IAAI,CAACuK,WAAWoF;YAC/C,MAAME,mBACJtF,cAAc3H,WAAWkG,KAAK,GAC1B/I,MAAMC,IAAI,CAAC,SAAS2P,cACpBpF,cAAc3H,WAAWiG,GAAG,GAC1B9I,MAAMC,IAAI,CAAC,OAAO2P,cAClBA,WAAW1J,KAAK,CAAC;YAEzB,MAAMgE,mBAAmByF,QAAQ,CAAC/G,KAAK;YAEvC,iCAAiC;YACjC,MAAMJ,eAAeyB,gBAAgB;gBACnCC;gBACA7F;gBACAjB;gBACA+G;YACF;YAEA,MAAM5B,iBACJ,CAAC,CAACnF,UACD8G,CAAAA,iBAAiBnG,UAAU,CAACtD,kBAC3ByJ,iBAAiBnG,UAAU,CAACX,OAAM;YAEtC,MAAM4H,aAA6B,MAAM1C,8BAA8B;gBACrEC;gBACAZ;gBACAa;gBACApF;gBACAqF;gBACAE;gBACAC;YACF;YAEA,iCAAiC;YACjC,MAAMmE,oBACJxE,kBAAkByC,WAAW+E,GAAG,KAAKhP,iBAAiBkO,MAAM;YAE9D,IAAIzN,iBAAiBoH,OAAO;oBACLoC;gBAArBkE,qBAAqBlE,EAAAA,yBAAAA,WAAWwB,UAAU,qBAArBxB,uBAAuBuB,QAAQ,KAAI;oBACtD;wBAAEyD,QAAQ;wBAAMC,gBAAgB;oBAAU;iBAC3C;YACH;YAEA,MAAMC,oBACJxO,0BAA0BkH,SAAS4B,cAAc3H,WAAWwI,IAAI;YAElEkD,uBAAuB;gBACrB3F;gBACA+F,aAAa3D,WAAWpB,OAAO;gBAC/Bf,UAAU2B;gBACVoE,UAAU;oBACR,IAAI7B,qBAAqBxE,gBAAgB;oBACvC,qEAAqE;oBACrE,uCAAuC;oBACzC,OAAO;wBACL0G,MAAM,CAACY,iBAAiB,GAAGzB,eAAe;4BACxClE;4BACAtB;wBACF;oBACF;gBACF;gBACA6F,UAAU;oBACR,IAAIjE,cAAc,SAASpH,QAAQ;wBACjC,MAAM+M,kBAAkBhB,gBAAgB,CAAClN,iBAAiB2G,MAAM;wBAChEoG,MAAM,CAACc,iBAAiB,GAAG9B,YAAY;4BACrCpF;4BACAtC,MAAMwJ;4BACN9F,UAAUE;4BACV9G;4BACAY,UAAUmM;4BACVxI;4BACAyI,UAAU3H,OAAO2H,QAAQ;4BACzBC,aAAa5H,OAAO4H,WAAW;4BAC/BC,kBAAkB7H,OAAO8H,MAAM;4BAC/B1G,iBAAiBmB,WAAWnB,eAAe;4BAC3CoC,kBAAkBtJ,eAAeqI,WAAWwB,UAAU,IAAI,CAAC;4BAC3DgE,yBAAyB/H,OAAO0D,YAAY,CAACsE,cAAc,GACvD,OACAlD;wBACN;oBACF,OAAO,IAAI2C,mBAAmB;wBAC5BlB,MAAM,CAACc,iBAAiBhL,OAAO,CAAC,QAAQ,IAAI,GAC1C0I,wBAAwB;4BACtBtD;4BACAwD,cAAc;4BACd/E,OAAO;wBACT;oBACJ,OAAO,IAAInH,iBAAiBoH,OAAO;wBACjCoG,MAAM,CAACc,iBAAiBhL,OAAO,CAAC,QAAQ,IAAI,GAAG2G,mBAAmB;4BAChE,GAAG+C,MAAM;4BACTrE;4BACAD,kBAAkBA;4BAClBwG,YAAYb;4BACZlH,OAAO;4BACPoE;4BACAnE;4BACA4D,UAAU,EAAExB,8BAAAA,WAAYwB,UAAU;4BAClChC;4BACAX,iBAAiBmB,WAAWnB,eAAe;4BAC3CoC,kBAAkBjB,WAAWwB,UAAU;wBACzC;oBACF,OAAO,IAAI5L,WAAWgI,OAAO;wBAC3BoG,MAAM,CAACc,iBAAiB,GAAG;4BACzBxN,oBAAoB;gCAClBqO,MAAMjO,UAAUkO,SAAS;gCACzBhI;gCACAsB;gCACAL,iBAAiBmB,WAAWnB,eAAe;gCAC3CoC,kBAAkBjB,WAAWwB,UAAU,IAAI,CAAC;4BAC9C;yBACD;oBACH,OAAO,IACL,CAAChL,iBAAiBoH,SAClB,CAACrG,oBAAoB2H,qBACrB,CAAC1H,oBAAoBoG,OACrB;wBACAoG,MAAM,CAACc,iBAAiB,GAAG;4BACzBxN,oBAAoB;gCAClBqO,MAAMjO,UAAUqG,KAAK;gCACrBH;gCACA8B;gCACAR;gCACAL,iBAAiBmB,WAAWnB,eAAe;gCAC3CoC,kBAAkBjB,WAAWwB,UAAU,IAAI,CAAC;4BAC9C;yBACD;oBACH,OAAO;wBACLwC,MAAM,CAACc,iBAAiB,GAAG;4BAAC5F;yBAAiB;oBAC/C;gBACF;gBACAwE,cAAc;oBACZ,IAAI/C,eAAuB;oBAC3B,IAAIuE,mBAAmB;wBACrBnB,UAAU,CAACe,iBAAiBhL,OAAO,CAAC,QAAQ,IAAI,GAC9C0I,wBAAwB;4BACtBtD;4BACAwD,cAAc;4BACd/E,OAAO;wBACT;oBACJ,OAAO;wBACL,IAAI6B,cAAc,OAAO;4BACvB,MAAM2F,kBAAkBhB,gBAAgB,CAAClN,iBAAiB2G,MAAM;4BAChE+C,eAAeqC,YAAY;gCACzB1H,MAAMwJ;gCACNlH;gCACAoB,UAAUE;gCACV9G,QAAQA;gCACRY,UAAUmM;gCACVxI;gCACAyI,UAAU3H,OAAO2H,QAAQ;gCACzBC,aAAa5H,OAAO4H,WAAW;gCAC/BC,kBAAkB7H,OAAO8H,MAAM;gCAC/B,oHAAoH;gCACpH,yCAAyC;gCACzC1G,iBAAiBmB,WAAWnB,eAAe;gCAC3CoC,kBAAkBJ,OAAOC,IAAI,CAC3BE,KAAK3L,SAAS,CAAC2K,WAAWwB,UAAU,IAAI,CAAC,IACzCT,QAAQ,CAAC;gCACXyE,yBAAyB/H,OAAO0D,YAAY,CAACsE,cAAc,GACvD,OACAlD;4BACN,GAAGnB,MAAM;wBACX;wBACA2C,UAAU,CAACe,iBAAiB,GAAGrE,mBAAmB;4BAChD,GAAG+C,MAAM;4BACTrE;4BACAD,kBAAkBA;4BAClBwG,YAAYb;4BACZlH,OAAO;4BACPoE;4BACAnE;4BACA4D,UAAU,EAAExB,8BAAAA,WAAYwB,UAAU;4BAClChC;4BACAmB;4BACA9B,iBAAiBmB,WAAWnB,eAAe;4BAC3CoC,kBAAkBjB,WAAWwB,UAAU;wBACzC;oBACF;gBACF;YACF;QACF;IAEF,MAAM7B,WAA8B,EAAE;IAEtC,IAAI3G,UAAU;QACZ,MAAM6M,eAAenB,gBAAgB1L,UAAUnB,WAAWiG,GAAG;QAC7D6B,SAASnF,IAAI,CAAC2F,QAAQC,GAAG,CAAC/F,OAAOkG,IAAI,CAACvH,UAAUoF,GAAG,CAACyH;IACtD;IACA,IAAI/B,WAAW;QACbnE,SAASnF,IAAI,CACX2F,QAAQC,GAAG,CACT/F,OAAOkG,IAAI,CAACuD,WAAW1F,GAAG,CAACsG,gBAAgBZ,WAAWjM,WAAWwI,IAAI;IAG3E;IACAV,SAASnF,IAAI,CACX2F,QAAQC,GAAG,CACT/F,OAAOkG,IAAI,CAACb,OAAOtB,GAAG,CAACsG,gBAAgBhF,OAAO7H,WAAWkG,KAAK;IAIlE,MAAMoC,QAAQC,GAAG,CAACT;IAElB,qHAAqH;IACrH,6FAA6F;IAC7F,IAAIoE,WAAW+B,eAAe,IAAIzL,OAAOkG,IAAI,CAACwD,YAAYhJ,MAAM,KAAK,GAAG;QACtE,OAAOgJ,WAAW+B,eAAe;IACnC;IAEA,OAAO;QACL7B;QACAD;QACAD;QACAG;IACF;AACF;AAEA,OAAO,SAAS6B,mBAAmB,EACjCzK,IAAI,EACJ0K,YAAY,EACZC,KAAK,EACLlE,iBAAiB,EACjBmE,SAAS,EAOV;IACC,MAAMC,QACJ,OAAOF,UAAU,YAAYG,MAAMC,OAAO,CAACJ,SACvC;QAAE7E,QAAQ6E;IAAM,IAChBA;IAEN,MAAMK,QAAQhL,KAAKvC,UAAU,CAAC;IAC9B,MAAMmM,oBAAoBvO,8BAA8B2E;IAExD,OAAQ0K;QACN,KAAK1P,eAAe0N,MAAM;YAAE;gBAC1B,MAAM3C,QAAQiF,QACV5Q,eAAe6Q,OAAO,GACtBrB,oBACExP,eAAeiN,UAAU,GACzBZ,oBACErM,eAAe4L,qBAAqB,GACpChG,KAAKvC,UAAU,CAAC,YACdrD,eAAe8Q,YAAY,GAC3BjE;gBAEV,OAAO;oBACLkE,YAAYH,QAAQ,KAAK/D;oBACzB3D,SAAS0H,QAAQ,wBAAwB;oBACzCjF;oBACA,GAAG8E,KAAK;gBACV;YACF;QACA,KAAK7P,eAAeyN,UAAU;YAAE;gBAC9B,OAAO;oBACL1C,OAAOiF,QACH5Q,eAAe+L,OAAO,GACtBhL,qBAAqB6E,SAAS4J,oBAC5BxP,eAAe8L,UAAU,GACzBlG,KAAKvC,UAAU,CAAC,YACdrD,eAAegR,YAAY,GAC3BnE;oBACRoE,SAAS;wBAAErL,MAAM;4BAAC;4BAAY,CAAC,iBAAiB,CAAC;yBAAC;wBAAE2C,MAAM;oBAAS;oBACnEW,SAASrI;oBACTqQ,aAAa;oBACb,GAAGT,KAAK;gBACV;YACF;QACA,KAAK7P,eAAe2N,MAAM;YAAE;gBAC1B,MAAM4C,aACJX,aACC5K,CAAAA,SAASnF,wCACRmF,SAASxF,wBACTwF,KAAKvC,UAAU,CAAC,OAAM;gBAE1B,IACE,uBAAuB;gBACvBuC,SAASlF,yCACTkF,SAASpF,oCACToF,SAASnF,wCACTmF,SAASrF,mCACTqF,SAASjF,2CACT;oBACA,IAAIwQ,YAAY;wBACd,OAAO;4BACLC,UAAU3Q;4BACVkL,OAAO3L,eAAeqR,eAAe;4BACrC,GAAGZ,KAAK;wBACV;oBACF;oBAEA,OAAO;wBACLW,UACExL,KAAKvC,UAAU,CAAC,aAAauC,SAAS,eAClC,eACApF;wBACNmL,OAAO3L,eAAesR,eAAe;wBACrC,GAAGb,KAAK;oBACV;gBACF;gBAEA,IAAIU,YAAY;oBACd,OAAO;wBACLxF,OAAO3L,eAAeqR,eAAe;wBACrC,GAAGZ,KAAK;oBACV;gBACF;gBAEA,OAAO;oBACL9E,OAAO3L,eAAesR,eAAe;oBACrC,GAAGb,KAAK;gBACV;YACF;QACA;YACE,OAAOH;IACX;AACF", "ignoreList": [0]}