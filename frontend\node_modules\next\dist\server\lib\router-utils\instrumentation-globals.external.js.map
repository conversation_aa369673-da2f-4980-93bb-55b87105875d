{"version": 3, "sources": ["../../../../src/server/lib/router-utils/instrumentation-globals.external.ts"], "sourcesContent": ["import path from 'node:path'\nimport isError from '../../../lib/is-error'\nimport { INSTRUMENTATION_HOOK_FILENAME } from '../../../lib/constants'\nimport type {\n  InstrumentationModule,\n  InstrumentationOnRequestError,\n} from '../../instrumentation/types'\nimport { interopDefault } from '../../../lib/interop-default'\nimport { afterRegistration as extendInstrumentationAfterRegistration } from './instrumentation-node-extensions'\n\nlet cachedInstrumentationModule: InstrumentationModule\n\nexport async function getInstrumentationModule(\n  projectDir: string,\n  distDir: string\n): Promise<InstrumentationModule | undefined> {\n  if (cachedInstrumentationModule) {\n    return cachedInstrumentationModule\n  }\n\n  try {\n    cachedInstrumentationModule = interopDefault(\n      await require(\n        path.join(\n          projectDir,\n          distDir,\n          'server',\n          `${INSTRUMENTATION_HOOK_FILENAME}.js`\n        )\n      )\n    )\n    return cachedInstrumentationModule\n  } catch (err: unknown) {\n    if (\n      isError(err) &&\n      err.code !== 'ENOENT' &&\n      err.code !== 'MODULE_NOT_FOUND' &&\n      err.code !== 'ERR_MODULE_NOT_FOUND'\n    ) {\n      throw err\n    }\n  }\n}\n\nlet instrumentationModulePromise: Promise<any> | null = null\n\nasync function registerInstrumentation(projectDir: string, distDir: string) {\n  // Ensure registerInstrumentation is not called in production build\n  if (process.env.NEXT_PHASE === 'phase-production-build') {\n    return\n  }\n  if (!instrumentationModulePromise) {\n    instrumentationModulePromise = getInstrumentationModule(projectDir, distDir)\n  }\n  const instrumentation = await instrumentationModulePromise\n  if (instrumentation?.register) {\n    try {\n      await instrumentation.register()\n      extendInstrumentationAfterRegistration()\n    } catch (err: any) {\n      err.message = `An error occurred while loading instrumentation hook: ${err.message}`\n      throw err\n    }\n  }\n}\n\nexport async function instrumentationOnRequestError(\n  projectDir: string,\n  distDir: string,\n  ...args: Parameters<InstrumentationOnRequestError>\n) {\n  const instrumentation = await getInstrumentationModule(projectDir, distDir)\n  try {\n    await instrumentation?.onRequestError?.(...args)\n  } catch (err) {\n    // Log the soft error and continue, since the original error has already been thrown\n    console.error('Error in instrumentation.onRequestError:', err)\n  }\n}\n\nlet registerInstrumentationPromise: Promise<void> | null = null\nexport function ensureInstrumentationRegistered(\n  projectDir: string,\n  distDir: string\n) {\n  if (!registerInstrumentationPromise) {\n    registerInstrumentationPromise = registerInstrumentation(\n      projectDir,\n      distDir\n    )\n  }\n  return registerInstrumentationPromise\n}\n"], "names": ["ensureInstrumentationRegistered", "getInstrumentationModule", "instrumentationOnRequestError", "cachedInstrumentationModule", "projectDir", "distDir", "interopDefault", "require", "path", "join", "INSTRUMENTATION_HOOK_FILENAME", "err", "isError", "code", "instrumentationModulePromise", "registerInstrumentation", "process", "env", "NEXT_PHASE", "instrumentation", "register", "extendInstrumentationAfterRegistration", "message", "args", "onRequestError", "console", "error", "registerInstrumentationPromise"], "mappings": ";;;;;;;;;;;;;;;;IAiFgBA,+BAA+B;eAA/BA;;IArEMC,wBAAwB;eAAxBA;;IAsDAC,6BAA6B;eAA7BA;;;iEAlEL;gEACG;2BAC0B;gCAKf;+CAC6C;;;;;;AAE5E,IAAIC;AAEG,eAAeF,yBACpBG,UAAkB,EAClBC,OAAe;IAEf,IAAIF,6BAA6B;QAC/B,OAAOA;IACT;IAEA,IAAI;QACFA,8BAA8BG,IAAAA,8BAAc,EAC1C,MAAMC,QACJC,iBAAI,CAACC,IAAI,CACPL,YACAC,SACA,UACA,GAAGK,wCAA6B,CAAC,GAAG,CAAC;QAI3C,OAAOP;IACT,EAAE,OAAOQ,KAAc;QACrB,IACEC,IAAAA,gBAAO,EAACD,QACRA,IAAIE,IAAI,KAAK,YACbF,IAAIE,IAAI,KAAK,sBACbF,IAAIE,IAAI,KAAK,wBACb;YACA,MAAMF;QACR;IACF;AACF;AAEA,IAAIG,+BAAoD;AAExD,eAAeC,wBAAwBX,UAAkB,EAAEC,OAAe;IACxE,mEAAmE;IACnE,IAAIW,QAAQC,GAAG,CAACC,UAAU,KAAK,0BAA0B;QACvD;IACF;IACA,IAAI,CAACJ,8BAA8B;QACjCA,+BAA+Bb,yBAAyBG,YAAYC;IACtE;IACA,MAAMc,kBAAkB,MAAML;IAC9B,IAAIK,mCAAAA,gBAAiBC,QAAQ,EAAE;QAC7B,IAAI;YACF,MAAMD,gBAAgBC,QAAQ;YAC9BC,IAAAA,gDAAsC;QACxC,EAAE,OAAOV,KAAU;YACjBA,IAAIW,OAAO,GAAG,CAAC,sDAAsD,EAAEX,IAAIW,OAAO,EAAE;YACpF,MAAMX;QACR;IACF;AACF;AAEO,eAAeT,8BACpBE,UAAkB,EAClBC,OAAe,EACf,GAAGkB,IAA+C;IAElD,MAAMJ,kBAAkB,MAAMlB,yBAAyBG,YAAYC;IACnE,IAAI;YACIc;QAAN,OAAMA,oCAAAA,kCAAAA,gBAAiBK,cAAc,qBAA/BL,qCAAAA,oBAAqCI;IAC7C,EAAE,OAAOZ,KAAK;QACZ,oFAAoF;QACpFc,QAAQC,KAAK,CAAC,4CAA4Cf;IAC5D;AACF;AAEA,IAAIgB,iCAAuD;AACpD,SAAS3B,gCACdI,UAAkB,EAClBC,OAAe;IAEf,IAAI,CAACsB,gCAAgC;QACnCA,iCAAiCZ,wBAC/BX,YACAC;IAEJ;IACA,OAAOsB;AACT", "ignoreList": [0]}