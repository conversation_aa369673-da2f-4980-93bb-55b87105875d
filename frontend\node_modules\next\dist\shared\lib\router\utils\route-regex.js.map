{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/route-regex.ts"], "sourcesContent": ["import {\n  NEXT_INTERCEPTION_MARKER_PREFIX,\n  NEXT_QUERY_PARAM_PREFIX,\n} from '../../../../lib/constants'\nimport { INTERCEPTION_ROUTE_MARKERS } from './interception-routes'\nimport { escapeStringRegexp } from '../../escape-regexp'\nimport { removeTrailingSlash } from './remove-trailing-slash'\nimport { PARAMETER_PATTERN, parseMatchedParameter } from './get-dynamic-param'\n\nexport interface Group {\n  pos: number\n  repeat: boolean\n  optional: boolean\n}\n\nexport interface RouteRegex {\n  groups: { [groupName: string]: Group }\n  re: RegExp\n}\n\ntype GetNamedRouteRegexOptions = {\n  /**\n   * Whether to prefix the route keys with the NEXT_INTERCEPTION_MARKER_PREFIX\n   * or NEXT_QUERY_PARAM_PREFIX. This is only relevant when creating the\n   * routes-manifest during the build.\n   */\n  prefixRouteKeys: boolean\n\n  /**\n   * Whether to include the suffix in the route regex. This means that when you\n   * have something like `/[...slug].json` the `.json` part will be included\n   * in the regex, yielding `/(.*).json` as the regex.\n   */\n  includeSuffix?: boolean\n\n  /**\n   * Whether to include the prefix in the route regex. This means that when you\n   * have something like `/[...slug].json` the `/` part will be included\n   * in the regex, yielding `^/(.*).json$` as the regex.\n   *\n   * Note that interception markers will already be included without the need\n   */\n  includePrefix?: boolean\n\n  /**\n   * Whether to exclude the optional trailing slash from the route regex.\n   */\n  excludeOptionalTrailingSlash?: boolean\n\n  /**\n   * Whether to backtrack duplicate keys. This is only relevant when creating\n   * the routes-manifest during the build.\n   */\n  backreferenceDuplicateKeys?: boolean\n}\n\ntype GetRouteRegexOptions = {\n  /**\n   * Whether to include extra parts in the route regex. This means that when you\n   * have something like `/[...slug].json` the `.json` part will be included\n   * in the regex, yielding `/(.*).json` as the regex.\n   */\n  includeSuffix?: boolean\n\n  /**\n   * Whether to include the prefix in the route regex. This means that when you\n   * have something like `/[...slug].json` the `/` part will be included\n   * in the regex, yielding `^/(.*).json$` as the regex.\n   *\n   * Note that interception markers will already be included without the need\n   * of adding this option.\n   */\n  includePrefix?: boolean\n\n  /**\n   * Whether to exclude the optional trailing slash from the route regex.\n   */\n  excludeOptionalTrailingSlash?: boolean\n}\n\nfunction getParametrizedRoute(\n  route: string,\n  includeSuffix: boolean,\n  includePrefix: boolean\n) {\n  const groups: { [groupName: string]: Group } = {}\n  let groupIndex = 1\n\n  const segments: string[] = []\n  for (const segment of removeTrailingSlash(route).slice(1).split('/')) {\n    const markerMatch = INTERCEPTION_ROUTE_MARKERS.find((m) =>\n      segment.startsWith(m)\n    )\n    const paramMatches = segment.match(PARAMETER_PATTERN) // Check for parameters\n\n    if (markerMatch && paramMatches && paramMatches[2]) {\n      const { key, optional, repeat } = parseMatchedParameter(paramMatches[2])\n      groups[key] = { pos: groupIndex++, repeat, optional }\n      segments.push(`/${escapeStringRegexp(markerMatch)}([^/]+?)`)\n    } else if (paramMatches && paramMatches[2]) {\n      const { key, repeat, optional } = parseMatchedParameter(paramMatches[2])\n      groups[key] = { pos: groupIndex++, repeat, optional }\n\n      if (includePrefix && paramMatches[1]) {\n        segments.push(`/${escapeStringRegexp(paramMatches[1])}`)\n      }\n\n      let s = repeat ? (optional ? '(?:/(.+?))?' : '/(.+?)') : '/([^/]+?)'\n\n      // Remove the leading slash if includePrefix already added it.\n      if (includePrefix && paramMatches[1]) {\n        s = s.substring(1)\n      }\n\n      segments.push(s)\n    } else {\n      segments.push(`/${escapeStringRegexp(segment)}`)\n    }\n\n    // If there's a suffix, add it to the segments if it's enabled.\n    if (includeSuffix && paramMatches && paramMatches[3]) {\n      segments.push(escapeStringRegexp(paramMatches[3]))\n    }\n  }\n\n  return {\n    parameterizedRoute: segments.join(''),\n    groups,\n  }\n}\n\n/**\n * From a normalized route this function generates a regular expression and\n * a corresponding groups object intended to be used to store matching groups\n * from the regular expression.\n */\nexport function getRouteRegex(\n  normalizedRoute: string,\n  {\n    includeSuffix = false,\n    includePrefix = false,\n    excludeOptionalTrailingSlash = false,\n  }: GetRouteRegexOptions = {}\n): RouteRegex {\n  const { parameterizedRoute, groups } = getParametrizedRoute(\n    normalizedRoute,\n    includeSuffix,\n    includePrefix\n  )\n\n  let re = parameterizedRoute\n  if (!excludeOptionalTrailingSlash) {\n    re += '(?:/)?'\n  }\n\n  return {\n    re: new RegExp(`^${re}$`),\n    groups: groups,\n  }\n}\n\n/**\n * Builds a function to generate a minimal routeKey using only a-z and minimal\n * number of characters.\n */\nfunction buildGetSafeRouteKey() {\n  let i = 0\n\n  return () => {\n    let routeKey = ''\n    let j = ++i\n    while (j > 0) {\n      routeKey += String.fromCharCode(97 + ((j - 1) % 26))\n      j = Math.floor((j - 1) / 26)\n    }\n    return routeKey\n  }\n}\n\nfunction getSafeKeyFromSegment({\n  interceptionMarker,\n  getSafeRouteKey,\n  segment,\n  routeKeys,\n  keyPrefix,\n  backreferenceDuplicateKeys,\n}: {\n  interceptionMarker?: string\n  getSafeRouteKey: () => string\n  segment: string\n  routeKeys: Record<string, string>\n  keyPrefix?: string\n  backreferenceDuplicateKeys: boolean\n}) {\n  const { key, optional, repeat } = parseMatchedParameter(segment)\n\n  // replace any non-word characters since they can break\n  // the named regex\n  let cleanedKey = key.replace(/\\W/g, '')\n\n  if (keyPrefix) {\n    cleanedKey = `${keyPrefix}${cleanedKey}`\n  }\n  let invalidKey = false\n\n  // check if the key is still invalid and fallback to using a known\n  // safe key\n  if (cleanedKey.length === 0 || cleanedKey.length > 30) {\n    invalidKey = true\n  }\n  if (!isNaN(parseInt(cleanedKey.slice(0, 1)))) {\n    invalidKey = true\n  }\n\n  if (invalidKey) {\n    cleanedKey = getSafeRouteKey()\n  }\n\n  const duplicateKey = cleanedKey in routeKeys\n\n  if (keyPrefix) {\n    routeKeys[cleanedKey] = `${keyPrefix}${key}`\n  } else {\n    routeKeys[cleanedKey] = key\n  }\n\n  // if the segment has an interception marker, make sure that's part of the regex pattern\n  // this is to ensure that the route with the interception marker doesn't incorrectly match\n  // the non-intercepted route (ie /app/(.)[username] should not match /app/[username])\n  const interceptionPrefix = interceptionMarker\n    ? escapeStringRegexp(interceptionMarker)\n    : ''\n\n  let pattern: string\n  if (duplicateKey && backreferenceDuplicateKeys) {\n    // Use a backreference to the key to ensure that the key is the same value\n    // in each of the placeholders.\n    pattern = `\\\\k<${cleanedKey}>`\n  } else if (repeat) {\n    pattern = `(?<${cleanedKey}>.+?)`\n  } else {\n    pattern = `(?<${cleanedKey}>[^/]+?)`\n  }\n\n  return optional\n    ? `(?:/${interceptionPrefix}${pattern})?`\n    : `/${interceptionPrefix}${pattern}`\n}\n\nfunction getNamedParametrizedRoute(\n  route: string,\n  prefixRouteKeys: boolean,\n  includeSuffix: boolean,\n  includePrefix: boolean,\n  backreferenceDuplicateKeys: boolean\n) {\n  const getSafeRouteKey = buildGetSafeRouteKey()\n  const routeKeys: { [named: string]: string } = {}\n\n  const segments: string[] = []\n  for (const segment of removeTrailingSlash(route).slice(1).split('/')) {\n    const hasInterceptionMarker = INTERCEPTION_ROUTE_MARKERS.some((m) =>\n      segment.startsWith(m)\n    )\n\n    const paramMatches = segment.match(PARAMETER_PATTERN) // Check for parameters\n\n    if (hasInterceptionMarker && paramMatches && paramMatches[2]) {\n      // If there's an interception marker, add it to the segments.\n      segments.push(\n        getSafeKeyFromSegment({\n          getSafeRouteKey,\n          interceptionMarker: paramMatches[1],\n          segment: paramMatches[2],\n          routeKeys,\n          keyPrefix: prefixRouteKeys\n            ? NEXT_INTERCEPTION_MARKER_PREFIX\n            : undefined,\n          backreferenceDuplicateKeys,\n        })\n      )\n    } else if (paramMatches && paramMatches[2]) {\n      // If there's a prefix, add it to the segments if it's enabled.\n      if (includePrefix && paramMatches[1]) {\n        segments.push(`/${escapeStringRegexp(paramMatches[1])}`)\n      }\n\n      let s = getSafeKeyFromSegment({\n        getSafeRouteKey,\n        segment: paramMatches[2],\n        routeKeys,\n        keyPrefix: prefixRouteKeys ? NEXT_QUERY_PARAM_PREFIX : undefined,\n        backreferenceDuplicateKeys,\n      })\n\n      // Remove the leading slash if includePrefix already added it.\n      if (includePrefix && paramMatches[1]) {\n        s = s.substring(1)\n      }\n\n      segments.push(s)\n    } else {\n      segments.push(`/${escapeStringRegexp(segment)}`)\n    }\n\n    // If there's a suffix, add it to the segments if it's enabled.\n    if (includeSuffix && paramMatches && paramMatches[3]) {\n      segments.push(escapeStringRegexp(paramMatches[3]))\n    }\n  }\n\n  return {\n    namedParameterizedRoute: segments.join(''),\n    routeKeys,\n  }\n}\n\n/**\n * This function extends `getRouteRegex` generating also a named regexp where\n * each group is named along with a routeKeys object that indexes the assigned\n * named group with its corresponding key. When the routeKeys need to be\n * prefixed to uniquely identify internally the \"prefixRouteKey\" arg should\n * be \"true\" currently this is only the case when creating the routes-manifest\n * during the build\n */\nexport function getNamedRouteRegex(\n  normalizedRoute: string,\n  options: GetNamedRouteRegexOptions\n) {\n  const result = getNamedParametrizedRoute(\n    normalizedRoute,\n    options.prefixRouteKeys,\n    options.includeSuffix ?? false,\n    options.includePrefix ?? false,\n    options.backreferenceDuplicateKeys ?? false\n  )\n\n  let namedRegex = result.namedParameterizedRoute\n  if (!options.excludeOptionalTrailingSlash) {\n    namedRegex += '(?:/)?'\n  }\n\n  return {\n    ...getRouteRegex(normalizedRoute, options),\n    namedRegex: `^${namedRegex}$`,\n    routeKeys: result.routeKeys,\n  }\n}\n\n/**\n * Generates a named regexp.\n * This is intended to be using for build time only.\n */\nexport function getNamedMiddlewareRegex(\n  normalizedRoute: string,\n  options: {\n    catchAll?: boolean\n  }\n) {\n  const { parameterizedRoute } = getParametrizedRoute(\n    normalizedRoute,\n    false,\n    false\n  )\n  const { catchAll = true } = options\n  if (parameterizedRoute === '/') {\n    let catchAllRegex = catchAll ? '.*' : ''\n    return {\n      namedRegex: `^/${catchAllRegex}$`,\n    }\n  }\n\n  const { namedParameterizedRoute } = getNamedParametrizedRoute(\n    normalizedRoute,\n    false,\n    false,\n    false,\n    false\n  )\n  let catchAllGroupedRegex = catchAll ? '(?:(/.*)?)' : ''\n  return {\n    namedRegex: `^${namedParameterizedRoute}${catchAllGroupedRegex}$`,\n  }\n}\n"], "names": ["getNamedMiddlewareRegex", "getNamedRouteRegex", "getRouteRegex", "getParametrizedRoute", "route", "includeSuffix", "includePrefix", "groups", "groupIndex", "segments", "segment", "removeTrailingSlash", "slice", "split", "markerMatch", "INTERCEPTION_ROUTE_MARKERS", "find", "m", "startsWith", "paramMatch<PERSON>", "match", "PARAMETER_PATTERN", "key", "optional", "repeat", "parseMatchedParameter", "pos", "push", "escapeStringRegexp", "s", "substring", "parameterizedRoute", "join", "normalizedRoute", "excludeOptionalTrailingSlash", "re", "RegExp", "buildGetSafeRouteKey", "i", "routeKey", "j", "String", "fromCharCode", "Math", "floor", "getSafeKeyFromSegment", "<PERSON><PERSON><PERSON><PERSON>", "getSafeRouteKey", "routeKeys", "keyPrefix", "backreferenceDuplicateKeys", "<PERSON><PERSON><PERSON>", "replace", "<PERSON><PERSON><PERSON>", "length", "isNaN", "parseInt", "duplicate<PERSON>ey", "interceptionPrefix", "pattern", "getNamedParametrizedRoute", "prefixRouteKeys", "hasInterceptionMarker", "some", "NEXT_INTERCEPTION_MARKER_PREFIX", "undefined", "NEXT_QUERY_PARAM_PREFIX", "namedParameterizedRoute", "options", "result", "namedRegex", "catchAll", "catchAllRegex", "catchAllGroupedRegex"], "mappings": ";;;;;;;;;;;;;;;;IAiWgBA,uBAAuB;eAAvBA;;IA5BAC,kBAAkB;eAAlBA;;IA7LAC,aAAa;eAAbA;;;2BArIT;oCACoC;8BACR;qCACC;iCACqB;AAyEzD,SAASC,qBACPC,KAAa,EACbC,aAAsB,EACtBC,aAAsB;IAEtB,MAAMC,SAAyC,CAAC;IAChD,IAAIC,aAAa;IAEjB,MAAMC,WAAqB,EAAE;IAC7B,KAAK,MAAMC,WAAWC,IAAAA,wCAAmB,EAACP,OAAOQ,KAAK,CAAC,GAAGC,KAAK,CAAC,KAAM;QACpE,MAAMC,cAAcC,8CAA0B,CAACC,IAAI,CAAC,CAACC,IACnDP,QAAQQ,UAAU,CAACD;QAErB,MAAME,eAAeT,QAAQU,KAAK,CAACC,kCAAiB,EAAE,uBAAuB;;QAE7E,IAAIP,eAAeK,gBAAgBA,YAAY,CAAC,EAAE,EAAE;YAClD,MAAM,EAAEG,GAAG,EAAEC,QAAQ,EAAEC,MAAM,EAAE,GAAGC,IAAAA,sCAAqB,EAACN,YAAY,CAAC,EAAE;YACvEZ,MAAM,CAACe,IAAI,GAAG;gBAAEI,KAAKlB;gBAAcgB;gBAAQD;YAAS;YACpDd,SAASkB,IAAI,CAAC,AAAC,MAAGC,IAAAA,gCAAkB,EAACd,eAAa;QACpD,OAAO,IAAIK,gBAAgBA,YAAY,CAAC,EAAE,EAAE;YAC1C,MAAM,EAAEG,GAAG,EAAEE,MAAM,EAAED,QAAQ,EAAE,GAAGE,IAAAA,sCAAqB,EAACN,YAAY,CAAC,EAAE;YACvEZ,MAAM,CAACe,IAAI,GAAG;gBAAEI,KAAKlB;gBAAcgB;gBAAQD;YAAS;YAEpD,IAAIjB,iBAAiBa,YAAY,CAAC,EAAE,EAAE;gBACpCV,SAASkB,IAAI,CAAC,AAAC,MAAGC,IAAAA,gCAAkB,EAACT,YAAY,CAAC,EAAE;YACtD;YAEA,IAAIU,IAAIL,SAAUD,WAAW,gBAAgB,WAAY;YAEzD,8DAA8D;YAC9D,IAAIjB,iBAAiBa,YAAY,CAAC,EAAE,EAAE;gBACpCU,IAAIA,EAAEC,SAAS,CAAC;YAClB;YAEArB,SAASkB,IAAI,CAACE;QAChB,OAAO;YACLpB,SAASkB,IAAI,CAAC,AAAC,MAAGC,IAAAA,gCAAkB,EAAClB;QACvC;QAEA,+DAA+D;QAC/D,IAAIL,iBAAiBc,gBAAgBA,YAAY,CAAC,EAAE,EAAE;YACpDV,SAASkB,IAAI,CAACC,IAAAA,gCAAkB,EAACT,YAAY,CAAC,EAAE;QAClD;IACF;IAEA,OAAO;QACLY,oBAAoBtB,SAASuB,IAAI,CAAC;QAClCzB;IACF;AACF;AAOO,SAASL,cACd+B,eAAuB,EACvB;IAAA,IAAA,EACE5B,gBAAgB,KAAK,EACrBC,gBAAgB,KAAK,EACrB4B,+BAA+B,KAAK,EACf,GAJvB,mBAI0B,CAAC,IAJ3B;IAMA,MAAM,EAAEH,kBAAkB,EAAExB,MAAM,EAAE,GAAGJ,qBACrC8B,iBACA5B,eACAC;IAGF,IAAI6B,KAAKJ;IACT,IAAI,CAACG,8BAA8B;QACjCC,MAAM;IACR;IAEA,OAAO;QACLA,IAAI,IAAIC,OAAO,AAAC,MAAGD,KAAG;QACtB5B,QAAQA;IACV;AACF;AAEA;;;CAGC,GACD,SAAS8B;IACP,IAAIC,IAAI;IAER,OAAO;QACL,IAAIC,WAAW;QACf,IAAIC,IAAI,EAAEF;QACV,MAAOE,IAAI,EAAG;YACZD,YAAYE,OAAOC,YAAY,CAAC,KAAM,AAACF,CAAAA,IAAI,CAAA,IAAK;YAChDA,IAAIG,KAAKC,KAAK,CAAC,AAACJ,CAAAA,IAAI,CAAA,IAAK;QAC3B;QACA,OAAOD;IACT;AACF;AAEA,SAASM,sBAAsB,KAc9B;IAd8B,IAAA,EAC7BC,kBAAkB,EAClBC,eAAe,EACfrC,OAAO,EACPsC,SAAS,EACTC,SAAS,EACTC,0BAA0B,EAQ3B,GAd8B;IAe7B,MAAM,EAAE5B,GAAG,EAAEC,QAAQ,EAAEC,MAAM,EAAE,GAAGC,IAAAA,sCAAqB,EAACf;IAExD,uDAAuD;IACvD,kBAAkB;IAClB,IAAIyC,aAAa7B,IAAI8B,OAAO,CAAC,OAAO;IAEpC,IAAIH,WAAW;QACbE,aAAa,AAAC,KAAEF,YAAYE;IAC9B;IACA,IAAIE,aAAa;IAEjB,kEAAkE;IAClE,WAAW;IACX,IAAIF,WAAWG,MAAM,KAAK,KAAKH,WAAWG,MAAM,GAAG,IAAI;QACrDD,aAAa;IACf;IACA,IAAI,CAACE,MAAMC,SAASL,WAAWvC,KAAK,CAAC,GAAG,MAAM;QAC5CyC,aAAa;IACf;IAEA,IAAIA,YAAY;QACdF,aAAaJ;IACf;IAEA,MAAMU,eAAeN,cAAcH;IAEnC,IAAIC,WAAW;QACbD,SAAS,CAACG,WAAW,GAAG,AAAC,KAAEF,YAAY3B;IACzC,OAAO;QACL0B,SAAS,CAACG,WAAW,GAAG7B;IAC1B;IAEA,wFAAwF;IACxF,0FAA0F;IAC1F,qFAAqF;IACrF,MAAMoC,qBAAqBZ,qBACvBlB,IAAAA,gCAAkB,EAACkB,sBACnB;IAEJ,IAAIa;IACJ,IAAIF,gBAAgBP,4BAA4B;QAC9C,0EAA0E;QAC1E,+BAA+B;QAC/BS,UAAU,AAAC,SAAMR,aAAW;IAC9B,OAAO,IAAI3B,QAAQ;QACjBmC,UAAU,AAAC,QAAKR,aAAW;IAC7B,OAAO;QACLQ,UAAU,AAAC,QAAKR,aAAW;IAC7B;IAEA,OAAO5B,WACH,AAAC,SAAMmC,qBAAqBC,UAAQ,OACpC,AAAC,MAAGD,qBAAqBC;AAC/B;AAEA,SAASC,0BACPxD,KAAa,EACbyD,eAAwB,EACxBxD,aAAsB,EACtBC,aAAsB,EACtB4C,0BAAmC;IAEnC,MAAMH,kBAAkBV;IACxB,MAAMW,YAAyC,CAAC;IAEhD,MAAMvC,WAAqB,EAAE;IAC7B,KAAK,MAAMC,WAAWC,IAAAA,wCAAmB,EAACP,OAAOQ,KAAK,CAAC,GAAGC,KAAK,CAAC,KAAM;QACpE,MAAMiD,wBAAwB/C,8CAA0B,CAACgD,IAAI,CAAC,CAAC9C,IAC7DP,QAAQQ,UAAU,CAACD;QAGrB,MAAME,eAAeT,QAAQU,KAAK,CAACC,kCAAiB,EAAE,uBAAuB;;QAE7E,IAAIyC,yBAAyB3C,gBAAgBA,YAAY,CAAC,EAAE,EAAE;YAC5D,6DAA6D;YAC7DV,SAASkB,IAAI,CACXkB,sBAAsB;gBACpBE;gBACAD,oBAAoB3B,YAAY,CAAC,EAAE;gBACnCT,SAASS,YAAY,CAAC,EAAE;gBACxB6B;gBACAC,WAAWY,kBACPG,0CAA+B,GAC/BC;gBACJf;YACF;QAEJ,OAAO,IAAI/B,gBAAgBA,YAAY,CAAC,EAAE,EAAE;YAC1C,+DAA+D;YAC/D,IAAIb,iBAAiBa,YAAY,CAAC,EAAE,EAAE;gBACpCV,SAASkB,IAAI,CAAC,AAAC,MAAGC,IAAAA,gCAAkB,EAACT,YAAY,CAAC,EAAE;YACtD;YAEA,IAAIU,IAAIgB,sBAAsB;gBAC5BE;gBACArC,SAASS,YAAY,CAAC,EAAE;gBACxB6B;gBACAC,WAAWY,kBAAkBK,kCAAuB,GAAGD;gBACvDf;YACF;YAEA,8DAA8D;YAC9D,IAAI5C,iBAAiBa,YAAY,CAAC,EAAE,EAAE;gBACpCU,IAAIA,EAAEC,SAAS,CAAC;YAClB;YAEArB,SAASkB,IAAI,CAACE;QAChB,OAAO;YACLpB,SAASkB,IAAI,CAAC,AAAC,MAAGC,IAAAA,gCAAkB,EAAClB;QACvC;QAEA,+DAA+D;QAC/D,IAAIL,iBAAiBc,gBAAgBA,YAAY,CAAC,EAAE,EAAE;YACpDV,SAASkB,IAAI,CAACC,IAAAA,gCAAkB,EAACT,YAAY,CAAC,EAAE;QAClD;IACF;IAEA,OAAO;QACLgD,yBAAyB1D,SAASuB,IAAI,CAAC;QACvCgB;IACF;AACF;AAUO,SAAS/C,mBACdgC,eAAuB,EACvBmC,OAAkC;QAKhCA,wBACAA,wBACAA;IALF,MAAMC,SAAST,0BACb3B,iBACAmC,QAAQP,eAAe,EACvBO,CAAAA,yBAAAA,QAAQ/D,aAAa,YAArB+D,yBAAyB,OACzBA,CAAAA,yBAAAA,QAAQ9D,aAAa,YAArB8D,yBAAyB,OACzBA,CAAAA,sCAAAA,QAAQlB,0BAA0B,YAAlCkB,sCAAsC;IAGxC,IAAIE,aAAaD,OAAOF,uBAAuB;IAC/C,IAAI,CAACC,QAAQlC,4BAA4B,EAAE;QACzCoC,cAAc;IAChB;IAEA,OAAO;QACL,GAAGpE,cAAc+B,iBAAiBmC,QAAQ;QAC1CE,YAAY,AAAC,MAAGA,aAAW;QAC3BtB,WAAWqB,OAAOrB,SAAS;IAC7B;AACF;AAMO,SAAShD,wBACdiC,eAAuB,EACvBmC,OAEC;IAED,MAAM,EAAErC,kBAAkB,EAAE,GAAG5B,qBAC7B8B,iBACA,OACA;IAEF,MAAM,EAAEsC,WAAW,IAAI,EAAE,GAAGH;IAC5B,IAAIrC,uBAAuB,KAAK;QAC9B,IAAIyC,gBAAgBD,WAAW,OAAO;QACtC,OAAO;YACLD,YAAY,AAAC,OAAIE,gBAAc;QACjC;IACF;IAEA,MAAM,EAAEL,uBAAuB,EAAE,GAAGP,0BAClC3B,iBACA,OACA,OACA,OACA;IAEF,IAAIwC,uBAAuBF,WAAW,eAAe;IACrD,OAAO;QACLD,YAAY,AAAC,MAAGH,0BAA0BM,uBAAqB;IACjE;AACF", "ignoreList": [0]}