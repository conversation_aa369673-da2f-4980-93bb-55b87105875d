{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-root-params-loader.ts"], "sourcesContent": ["import type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport * as path from 'node:path'\nimport * as fs from 'node:fs/promises'\nimport { normalizeAppPath } from '../../../shared/lib/router/utils/app-paths'\nimport { ensureLeadingSlash } from '../../../shared/lib/page-path/ensure-leading-slash'\nimport { getSegmentParam } from '../../../server/app-render/get-segment-param'\n\nexport type RootParamsLoaderOpts = {\n  appDir: string\n  pageExtensions: string[]\n}\n\ntype CollectedRootParams = Set<string>\n\nconst rootParamsLoader: webpack.LoaderDefinitionFunction<RootParamsLoaderOpts> =\n  async function () {\n    const { appDir, pageExtensions } = this.getOptions()\n\n    const allRootParams = await collectRootParamsFromFileSystem({\n      appDir,\n      pageExtensions,\n    })\n    // invalidate the result whenever a file/directory is added/removed inside the app dir or its subdirectories,\n    // because that might mean that a root layout has been moved.\n    this.addContextDependency(appDir)\n\n    // If there's no root params, there's nothing to generate.\n    if (allRootParams.size === 0) {\n      return 'export {}'\n    }\n\n    // Generate a getter for each root param we found.\n    const sortedRootParamNames = Array.from(allRootParams).sort()\n    const content = [\n      `import { getRootParam } from 'next/dist/server/request/root-params';`,\n      ...sortedRootParamNames.map((paramName) => {\n        return `export function ${paramName}() { return getRootParam('${paramName}'); }`\n      }),\n    ].join('\\n')\n\n    return content\n  }\n\nexport default rootParamsLoader\n\nasync function collectRootParamsFromFileSystem(\n  opts: Parameters<typeof findRootLayouts>[0]\n) {\n  return collectRootParams({\n    appDir: opts.appDir,\n    rootLayoutFilePaths: await findRootLayouts(opts),\n  })\n}\n\nfunction collectRootParams({\n  rootLayoutFilePaths,\n  appDir,\n}: {\n  rootLayoutFilePaths: string[]\n  appDir: string\n}): CollectedRootParams {\n  const allRootParams: CollectedRootParams = new Set()\n\n  for (const rootLayoutFilePath of rootLayoutFilePaths) {\n    const params = getParamsFromLayoutFilePath({\n      appDir,\n      layoutFilePath: rootLayoutFilePath,\n    })\n    for (const param of params) {\n      allRootParams.add(param)\n    }\n  }\n\n  return allRootParams\n}\n\nasync function findRootLayouts({\n  appDir,\n  pageExtensions,\n}: {\n  appDir: string\n  pageExtensions: string[]\n}) {\n  const layoutFilenameRegex = new RegExp(\n    `^layout\\\\.(?:${pageExtensions.join('|')})$`\n  )\n\n  async function visit(directory: string): Promise<string[]> {\n    let dir: Awaited<ReturnType<(typeof fs)['readdir']>>\n    try {\n      dir = await fs.readdir(directory, { withFileTypes: true })\n    } catch (err) {\n      // If the directory was removed before we managed to read it, just ignore it.\n      if (\n        err &&\n        typeof err === 'object' &&\n        'code' in err &&\n        err.code === 'ENOENT'\n      ) {\n        return []\n      }\n\n      throw err\n    }\n\n    const subdirectories: string[] = []\n    for (const entry of dir) {\n      if (entry.isDirectory()) {\n        // Directories that start with an underscore are excluded from routing, so we shouldn't look for layouts inside.\n        if (entry.name[0] === '_') {\n          continue\n        }\n        // Parallel routes cannot occur above a layout, so they can't contain a root layout.\n        if (entry.name[0] === '@') {\n          continue\n        }\n\n        const absolutePathname = path.join(directory, entry.name)\n        subdirectories.push(absolutePathname)\n      } else if (entry.isFile()) {\n        if (layoutFilenameRegex.test(entry.name)) {\n          // We found a root layout, so we're not going to recurse into subdirectories,\n          // meaning that we can skip the rest of the entries.\n          // Note that we don't need to track any of the subdirectories as dependencies --\n          // changes in the subdirectories will only become relevant if this root layout is (re)moved,\n          // in which case the loader will re-run, traverse deeper (because it no longer stops at this root layout)\n          // and then track those directories as needed.\n          const rootLayoutPath = path.join(directory, entry.name)\n          return [rootLayoutPath]\n        }\n      }\n    }\n\n    if (subdirectories.length === 0) {\n      return []\n    }\n\n    const subdirectoryRootLayouts = await Promise.all(\n      subdirectories.map((subdirectory) => visit(subdirectory))\n    )\n    return subdirectoryRootLayouts.flat(1)\n  }\n\n  return visit(appDir)\n}\n\nfunction getParamsFromLayoutFilePath({\n  appDir,\n  layoutFilePath,\n}: {\n  appDir: string\n  layoutFilePath: string\n}): string[] {\n  const rootLayoutPath = normalizeAppPath(\n    ensureLeadingSlash(path.dirname(path.relative(appDir, layoutFilePath)))\n  )\n  const segments = rootLayoutPath.split('/')\n  const paramNames: string[] = []\n  for (const segment of segments) {\n    const param = getSegmentParam(segment)\n    if (param !== null) {\n      paramNames.push(param.param)\n    }\n  }\n  return paramNames\n}\n"], "names": ["rootParamsLoader", "appDir", "pageExtensions", "getOptions", "allRootParams", "collectRootParamsFromFileSystem", "addContextDependency", "size", "sortedRootParamNames", "Array", "from", "sort", "content", "map", "paramName", "join", "opts", "collectRootParams", "rootLayoutFilePaths", "findRootLayouts", "Set", "rootLayoutFilePath", "params", "getParamsFromLayoutFilePath", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "param", "add", "layoutFilenameRegex", "RegExp", "visit", "directory", "dir", "fs", "readdir", "withFileTypes", "err", "code", "subdirectories", "entry", "isDirectory", "name", "absolutePathname", "path", "push", "isFile", "test", "rootLayoutPath", "length", "subdirectoryRootLayouts", "Promise", "all", "subdirectory", "flat", "normalizeAppPath", "ensureLeadingSlash", "dirname", "relative", "segments", "split", "paramNames", "segment", "getSegmentParam"], "mappings": ";;;;+BA2CA;;;eAAA;;;kEA1CsB;kEACF;0BACa;oCACE;iCACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShC,MAAMA,mBACJ;IACE,MAAM,EAAEC,MAAM,EAAEC,cAAc,EAAE,GAAG,IAAI,CAACC,UAAU;IAElD,MAAMC,gBAAgB,MAAMC,gCAAgC;QAC1DJ;QACAC;IACF;IACA,6GAA6G;IAC7G,6DAA6D;IAC7D,IAAI,CAACI,oBAAoB,CAACL;IAE1B,0DAA0D;IAC1D,IAAIG,cAAcG,IAAI,KAAK,GAAG;QAC5B,OAAO;IACT;IAEA,kDAAkD;IAClD,MAAMC,uBAAuBC,MAAMC,IAAI,CAACN,eAAeO,IAAI;IAC3D,MAAMC,UAAU;QACd,CAAC,oEAAoE,CAAC;WACnEJ,qBAAqBK,GAAG,CAAC,CAACC;YAC3B,OAAO,CAAC,gBAAgB,EAAEA,UAAU,0BAA0B,EAAEA,UAAU,KAAK,CAAC;QAClF;KACD,CAACC,IAAI,CAAC;IAEP,OAAOH;AACT;MAEF,WAAeZ;AAEf,eAAeK,gCACbW,IAA2C;IAE3C,OAAOC,kBAAkB;QACvBhB,QAAQe,KAAKf,MAAM;QACnBiB,qBAAqB,MAAMC,gBAAgBH;IAC7C;AACF;AAEA,SAASC,kBAAkB,EACzBC,mBAAmB,EACnBjB,MAAM,EAIP;IACC,MAAMG,gBAAqC,IAAIgB;IAE/C,KAAK,MAAMC,sBAAsBH,oBAAqB;QACpD,MAAMI,SAASC,4BAA4B;YACzCtB;YACAuB,gBAAgBH;QAClB;QACA,KAAK,MAAMI,SAASH,OAAQ;YAC1BlB,cAAcsB,GAAG,CAACD;QACpB;IACF;IAEA,OAAOrB;AACT;AAEA,eAAee,gBAAgB,EAC7BlB,MAAM,EACNC,cAAc,EAIf;IACC,MAAMyB,sBAAsB,IAAIC,OAC9B,CAAC,aAAa,EAAE1B,eAAea,IAAI,CAAC,KAAK,EAAE,CAAC;IAG9C,eAAec,MAAMC,SAAiB;QACpC,IAAIC;QACJ,IAAI;YACFA,MAAM,MAAMC,UAAGC,OAAO,CAACH,WAAW;gBAAEI,eAAe;YAAK;QAC1D,EAAE,OAAOC,KAAK;YACZ,6EAA6E;YAC7E,IACEA,OACA,OAAOA,QAAQ,YACf,UAAUA,OACVA,IAAIC,IAAI,KAAK,UACb;gBACA,OAAO,EAAE;YACX;YAEA,MAAMD;QACR;QAEA,MAAME,iBAA2B,EAAE;QACnC,KAAK,MAAMC,SAASP,IAAK;YACvB,IAAIO,MAAMC,WAAW,IAAI;gBACvB,gHAAgH;gBAChH,IAAID,MAAME,IAAI,CAAC,EAAE,KAAK,KAAK;oBACzB;gBACF;gBACA,oFAAoF;gBACpF,IAAIF,MAAME,IAAI,CAAC,EAAE,KAAK,KAAK;oBACzB;gBACF;gBAEA,MAAMC,mBAAmBC,UAAK3B,IAAI,CAACe,WAAWQ,MAAME,IAAI;gBACxDH,eAAeM,IAAI,CAACF;YACtB,OAAO,IAAIH,MAAMM,MAAM,IAAI;gBACzB,IAAIjB,oBAAoBkB,IAAI,CAACP,MAAME,IAAI,GAAG;oBACxC,6EAA6E;oBAC7E,oDAAoD;oBACpD,gFAAgF;oBAChF,4FAA4F;oBAC5F,yGAAyG;oBACzG,8CAA8C;oBAC9C,MAAMM,iBAAiBJ,UAAK3B,IAAI,CAACe,WAAWQ,MAAME,IAAI;oBACtD,OAAO;wBAACM;qBAAe;gBACzB;YACF;QACF;QAEA,IAAIT,eAAeU,MAAM,KAAK,GAAG;YAC/B,OAAO,EAAE;QACX;QAEA,MAAMC,0BAA0B,MAAMC,QAAQC,GAAG,CAC/Cb,eAAexB,GAAG,CAAC,CAACsC,eAAiBtB,MAAMsB;QAE7C,OAAOH,wBAAwBI,IAAI,CAAC;IACtC;IAEA,OAAOvB,MAAM5B;AACf;AAEA,SAASsB,4BAA4B,EACnCtB,MAAM,EACNuB,cAAc,EAIf;IACC,MAAMsB,iBAAiBO,IAAAA,0BAAgB,EACrCC,IAAAA,sCAAkB,EAACZ,UAAKa,OAAO,CAACb,UAAKc,QAAQ,CAACvD,QAAQuB;IAExD,MAAMiC,WAAWX,eAAeY,KAAK,CAAC;IACtC,MAAMC,aAAuB,EAAE;IAC/B,KAAK,MAAMC,WAAWH,SAAU;QAC9B,MAAMhC,QAAQoC,IAAAA,gCAAe,EAACD;QAC9B,IAAInC,UAAU,MAAM;YAClBkC,WAAWhB,IAAI,CAAClB,MAAMA,KAAK;QAC7B;IACF;IACA,OAAOkC;AACT", "ignoreList": [0]}