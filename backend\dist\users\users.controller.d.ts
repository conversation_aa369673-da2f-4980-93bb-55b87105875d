import { UsersService } from './users.service';
import { UpdateUserDto } from './dto/update-user.dto';
export declare class UsersController {
    private usersService;
    constructor(usersService: UsersService);
    getProfile(req: any): Promise<{
        firstName: string;
        lastName: string;
        email: string;
        id: string;
        profilePicture: string | null;
        createdAt: Date;
        updatedAt: Date;
    } | null>;
    updateProfile(req: any, updateUserDto: UpdateUserDto): Promise<{
        message: string;
        user: {
            firstName: string;
            lastName: string;
            email: string;
            id: string;
            profilePicture: string | null;
            updatedAt: Date;
        };
    }>;
}
