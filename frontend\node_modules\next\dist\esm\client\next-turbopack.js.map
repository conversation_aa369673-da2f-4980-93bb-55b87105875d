{"version": 3, "sources": ["../../src/client/next-turbopack.ts"], "sourcesContent": ["// A client-side entry point for Turbopack builds. Includes logic to load chunks,\n// but does not include development-time features like hot module reloading.\n\nimport '../lib/require-instrumentation-client'\n\n// TODO: Remove use of `any` type.\nimport { initialize, version, router, emitter, hydrate } from './'\n// TODO: This seems necessary, but is a module in the `dev` directory.\nimport { displayContent } from './dev/fouc'\n\nwindow.next = {\n  version,\n  turbopack: true,\n  // router is initialized later so it has to be live-binded\n  get router() {\n    return router\n  },\n  emitter,\n}\n;(self as any).__next_set_public_path__ = () => {}\n;(self as any).__webpack_hash__ = ''\n\n// for the page loader\ndeclare let __turbopack_load__: any\n\ninitialize({})\n  .then(() => {\n    // for the page loader\n    ;(self as any).__turbopack_load_page_chunks__ = (\n      page: string,\n      chunksData: any\n    ) => {\n      const chunkPromises = chunksData.map((c: unknown) =>\n        __turbopack_load__(c)\n      )\n\n      Promise.all(chunkPromises).catch((err) =>\n        console.error('failed to load chunks for page ' + page, err)\n      )\n    }\n\n    return hydrate({ beforeRender: displayContent })\n  })\n  .catch((err) => {\n    console.error('Error was not caught', err)\n  })\n"], "names": ["initialize", "version", "router", "emitter", "hydrate", "displayContent", "window", "next", "turbopack", "self", "__next_set_public_path__", "__webpack_hash__", "then", "__turbopack_load_page_chunks__", "page", "chunksData", "chunkPromises", "map", "c", "__turbopack_load__", "Promise", "all", "catch", "err", "console", "error", "beforeRender"], "mappings": "AAAA,iFAAiF;AACjF,4EAA4E;AAE5E,OAAO,wCAAuC;AAE9C,kCAAkC;AAClC,SAASA,UAAU,EAAEC,OAAO,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO,QAAQ,KAAI;AAClE,sEAAsE;AACtE,SAASC,cAAc,QAAQ,aAAY;AAE3CC,OAAOC,IAAI,GAAG;IACZN;IACAO,WAAW;IACX,0DAA0D;IAC1D,IAAIN,UAAS;QACX,OAAOA;IACT;IACAC;AACF;AACEM,KAAaC,wBAAwB,GAAG,KAAO;AAC/CD,KAAaE,gBAAgB,GAAG;AAKlCX,WAAW,CAAC,GACTY,IAAI,CAAC;IACJ,sBAAsB;;IACpBH,KAAaI,8BAA8B,GAAG,CAC9CC,MACAC;QAEA,MAAMC,gBAAgBD,WAAWE,GAAG,CAAC,CAACC,IACpCC,mBAAmBD;QAGrBE,QAAQC,GAAG,CAACL,eAAeM,KAAK,CAAC,CAACC,MAChCC,QAAQC,KAAK,CAAC,oCAAoCX,MAAMS;IAE5D;IAEA,OAAOnB,QAAQ;QAAEsB,cAAcrB;IAAe;AAChD,GACCiB,KAAK,CAAC,CAACC;IACNC,QAAQC,KAAK,CAAC,wBAAwBF;AACxC", "ignoreList": [0]}