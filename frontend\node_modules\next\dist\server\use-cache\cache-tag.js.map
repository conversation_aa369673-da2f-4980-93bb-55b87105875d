{"version": 3, "sources": ["../../../src/server/use-cache/cache-tag.ts"], "sourcesContent": ["import { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external'\nimport { validateTags } from '../lib/patch-fetch'\n\nexport function cacheTag(...tags: string[]): void {\n  if (!process.env.__NEXT_USE_CACHE) {\n    throw new Error(\n      'cacheTag() is only available with the experimental.useCache config.'\n    )\n  }\n\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  switch (workUnitStore?.type) {\n    case 'prerender':\n    case 'prerender-client':\n    case 'prerender-runtime':\n    case 'prerender-ppr':\n    case 'prerender-legacy':\n    case 'request':\n    case 'unstable-cache':\n    case undefined:\n      throw new Error(\n        'cacheTag() can only be called inside a \"use cache\" function.'\n      )\n    case 'cache':\n    case 'private-cache':\n      break\n    default:\n      workUnitStore satisfies never\n  }\n\n  const validTags = validateTags(tags, 'cacheTag()')\n\n  if (!workUnitStore.tags) {\n    workUnitStore.tags = validTags\n  } else {\n    workUnitStore.tags.push(...validTags)\n  }\n}\n"], "names": ["cacheTag", "tags", "process", "env", "__NEXT_USE_CACHE", "Error", "workUnitStore", "workUnitAsyncStorage", "getStore", "type", "undefined", "validTags", "validateTags", "push"], "mappings": ";;;;+BAGgBA;;;eAAAA;;;8CAHqB;4BACR;AAEtB,SAASA,SAAS,GAAGC,IAAc;IACxC,IAAI,CAACC,QAAQC,GAAG,CAACC,gBAAgB,EAAE;QACjC,MAAM,qBAEL,CAFK,IAAIC,MACR,wEADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMC,gBAAgBC,kDAAoB,CAACC,QAAQ;IAEnD,OAAQF,iCAAAA,cAAeG,IAAI;QACzB,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAKC;YACH,MAAM,qBAEL,CAFK,IAAIL,MACR,iEADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,KAAK;QACL,KAAK;YACH;QACF;YACEC;IACJ;IAEA,MAAMK,YAAYC,IAAAA,wBAAY,EAACX,MAAM;IAErC,IAAI,CAACK,cAAcL,IAAI,EAAE;QACvBK,cAAcL,IAAI,GAAGU;IACvB,OAAO;QACLL,cAAcL,IAAI,CAACY,IAAI,IAAIF;IAC7B;AACF", "ignoreList": [0]}