{"version": 3, "sources": ["../../../../src/shared/lib/turbopack/utils.ts"], "sourcesContent": ["import type {\n  Issue,\n  PlainTraceItem,\n  StyledString,\n  TurbopackResult,\n} from '../../../build/swc/types'\n\nimport { bold, green, magenta, red } from '../../../lib/picocolors'\nimport isInternal from '../is-internal'\nimport {\n  decodeMagicIdentifier,\n  MAGIC_IDENTIFIER_REGEX,\n} from '../magic-identifier'\nimport type { EntryKey } from './entry-key'\nimport * as Log from '../../../build/output/log'\nimport type { NextConfigComplete } from '../../../server/config-shared'\nimport loadJsConfig from '../../../build/load-jsconfig'\n\ntype IssueKey = `${Issue['severity']}-${Issue['filePath']}-${string}-${string}`\nexport type IssuesMap = Map<IssueKey, Issue>\nexport type EntryIssuesMap = Map<EntryKey, IssuesMap>\nexport type TopLevelIssuesMap = IssuesMap\n\n/**\n * An error generated from emitted Turbopack issues. This can include build\n * errors caused by issues with user code.\n */\nexport class ModuleBuildError extends Error {\n  name = 'ModuleBuildError'\n}\n\n/**\n * Thin stopgap workaround layer to mimic existing wellknown-errors-plugin in webpack's build\n * to emit certain type of errors into cli.\n */\nexport function isWellKnownError(issue: Issue): boolean {\n  const { title } = issue\n  const formattedTitle = renderStyledStringToErrorAnsi(title)\n  // TODO: add more well known errors\n  if (\n    formattedTitle.includes('Module not found') ||\n    formattedTitle.includes('Unknown module type')\n  ) {\n    return true\n  }\n\n  return false\n}\n\nexport function getIssueKey(issue: Issue): IssueKey {\n  return `${issue.severity}-${issue.filePath}-${JSON.stringify(\n    issue.title\n  )}-${JSON.stringify(issue.description)}`\n}\n\nexport async function getTurbopackJsConfig(\n  dir: string,\n  nextConfig: NextConfigComplete\n) {\n  const { jsConfig } = await loadJsConfig(dir, nextConfig)\n  return jsConfig ?? { compilerOptions: {} }\n}\n\nexport function processIssues(\n  currentEntryIssues: EntryIssuesMap,\n  key: EntryKey,\n  result: TurbopackResult,\n  throwIssue: boolean,\n  logErrors: boolean\n) {\n  const newIssues = new Map<IssueKey, Issue>()\n  currentEntryIssues.set(key, newIssues)\n\n  const relevantIssues = new Set()\n\n  for (const issue of result.issues) {\n    if (\n      issue.severity !== 'error' &&\n      issue.severity !== 'fatal' &&\n      issue.severity !== 'warning'\n    )\n      continue\n\n    const issueKey = getIssueKey(issue)\n    newIssues.set(issueKey, issue)\n\n    if (issue.severity !== 'warning') {\n      if (throwIssue) {\n        const formatted = formatIssue(issue)\n        relevantIssues.add(formatted)\n      }\n      // if we throw the issue it will most likely get handed and logged elsewhere\n      else if (logErrors && isWellKnownError(issue)) {\n        const formatted = formatIssue(issue)\n        Log.error(formatted)\n      }\n    }\n  }\n\n  if (relevantIssues.size && throwIssue) {\n    throw new ModuleBuildError([...relevantIssues].join('\\n\\n'))\n  }\n}\n\nexport function formatIssue(issue: Issue) {\n  const { filePath, title, description, source, importTraces } = issue\n  let { documentationLink } = issue\n  const formattedTitle = renderStyledStringToErrorAnsi(title).replace(\n    /\\n/g,\n    '\\n    '\n  )\n\n  // TODO: Use error codes to identify these\n  // TODO: Generalize adapting Turbopack errors to Next.js errors\n  if (formattedTitle.includes('Module not found')) {\n    // For compatiblity with webpack\n    // TODO: include columns in webpack errors.\n    documentationLink = 'https://nextjs.org/docs/messages/module-not-found'\n  }\n\n  const formattedFilePath = filePath\n    .replace('[project]/', './')\n    .replaceAll('/./', '/')\n    .replace('\\\\\\\\?\\\\', '')\n\n  let message = ''\n\n  if (source?.range) {\n    const { start } = source.range\n    message = `${formattedFilePath}:${start.line + 1}:${\n      start.column + 1\n    }\\n${formattedTitle}`\n  } else if (formattedFilePath) {\n    message = `${formattedFilePath}\\n${formattedTitle}`\n  } else {\n    message = formattedTitle\n  }\n  message += '\\n'\n\n  if (\n    source?.range &&\n    source.source.content &&\n    // ignore Next.js/React internals, as these can often be huge bundled files.\n    !isInternal(filePath)\n  ) {\n    const { start, end } = source.range\n    const { codeFrameColumns } =\n      require('next/dist/compiled/babel/code-frame') as typeof import('next/dist/compiled/babel/code-frame')\n\n    message +=\n      codeFrameColumns(\n        source.source.content,\n        {\n          start: {\n            line: start.line + 1,\n            column: start.column + 1,\n          },\n          end: {\n            line: end.line + 1,\n            column: end.column + 1,\n          },\n        },\n        { forceColor: true }\n      ).trim() + '\\n\\n'\n  }\n\n  if (description) {\n    if (\n      description.type === 'text' &&\n      description.value.includes(`Cannot find module 'sass'`)\n    ) {\n      message +=\n        \"To use Next.js' built-in Sass support, you first need to install `sass`.\\n\"\n      message += 'Run `npm i sass` or `yarn add sass` inside your workspace.\\n'\n      message += '\\nLearn more: https://nextjs.org/docs/messages/install-sass'\n    } else {\n      message += renderStyledStringToErrorAnsi(description) + '\\n\\n'\n    }\n  }\n\n  // TODO: make it possible to enable this for debugging, but not in tests.\n  // if (detail) {\n  //   message += renderStyledStringToErrorAnsi(detail) + '\\n\\n'\n  // }\n\n  if (importTraces?.length) {\n    // This is the same logic as in turbopack/crates/turbopack-cli-utils/src/issue.rs\n    // We end up with multiple traces when the file with the error is reachable from multiple\n    // different entry points (e.g. ssr, client)\n    message += `Import trace${importTraces.length > 1 ? 's' : ''}:\\n`\n    const everyTraceHasADistinctRootLayer =\n      new Set(importTraces.map(leafLayerName).filter((l) => l != null)).size ===\n      importTraces.length\n    for (let i = 0; i < importTraces.length; i++) {\n      const trace = importTraces[i]\n      const layer = leafLayerName(trace)\n      let traceIndent = '    '\n      // If this is true, layer must be present\n      if (everyTraceHasADistinctRootLayer) {\n        message += `  ${layer}:\\n`\n      } else {\n        if (importTraces.length > 1) {\n          // Otherwise use simple 1 based indices to disambiguate\n          message += `  #${i + 1}`\n          if (layer) {\n            message += ` [${layer}]`\n          }\n          message += ':\\n'\n        } else if (layer) {\n          message += ` [${layer}]:\\n`\n        } else {\n          // If there is a single trace and no layer name just don't indent it.\n          traceIndent = '  '\n        }\n      }\n      message += formatIssueTrace(trace, traceIndent, !identicalLayers(trace))\n    }\n  }\n  if (documentationLink) {\n    message += documentationLink + '\\n\\n'\n  }\n  return message\n}\n\n/** Returns the first present layer name in the trace */\nfunction leafLayerName(items: PlainTraceItem[]): string | undefined {\n  for (const item of items) {\n    const layer = item.layer\n    if (layer != null) return layer\n  }\n  return undefined\n}\n\n/**\n * Returns whether or not all items share the same layer.\n * If a layer is absent we ignore it in this analysis\n */\nfunction identicalLayers(items: PlainTraceItem[]): boolean {\n  const firstPresentLayer = items.findIndex((t) => t.layer != null)\n  if (firstPresentLayer === -1) return true // all layers are absent\n  const layer = items[firstPresentLayer].layer\n  for (let i = firstPresentLayer + 1; i < items.length; i++) {\n    const itemLayer = items[i].layer\n    if (itemLayer == null || itemLayer !== layer) {\n      return false\n    }\n  }\n  return true\n}\n\nfunction formatIssueTrace(\n  items: PlainTraceItem[],\n  indent: string,\n  printLayers: boolean\n): string {\n  return `${items\n    .map((item) => {\n      let r = indent\n      if (item.fsName !== 'project') {\n        r += `[${item.fsName}]/`\n      } else {\n        // This is consistent with webpack's output\n        r += './'\n      }\n      r += item.path\n      if (printLayers && item.layer) {\n        r += ` [${item.layer}]`\n      }\n      return r\n    })\n    .join('\\n')}\\n\\n`\n}\n\nexport function isRelevantWarning(issue: Issue): boolean {\n  return issue.severity === 'warning' && !isNodeModulesIssue(issue)\n}\n\nfunction isNodeModulesIssue(issue: Issue): boolean {\n  if (issue.severity === 'warning' && issue.stage === 'config') {\n    // Override for the externalize issue\n    // `Package foo (serverExternalPackages or default list) can't be external`\n    if (\n      renderStyledStringToErrorAnsi(issue.title).includes(\"can't be external\")\n    ) {\n      return false\n    }\n  }\n\n  return (\n    issue.severity === 'warning' &&\n    (issue.filePath.match(/^(?:.*[\\\\/])?node_modules(?:[\\\\/].*)?$/) !== null ||\n      // Ignore Next.js itself when running next directly in the monorepo where it is not inside\n      // node_modules anyway.\n      // TODO(mischnic) prevent matches when this is published to npm\n      issue.filePath.startsWith('[project]/packages/next/'))\n  )\n}\n\nexport function renderStyledStringToErrorAnsi(string: StyledString): string {\n  function decodeMagicIdentifiers(str: string): string {\n    return str.replaceAll(MAGIC_IDENTIFIER_REGEX, (ident) => {\n      try {\n        return magenta(`{${decodeMagicIdentifier(ident)}}`)\n      } catch (e) {\n        return magenta(`{${ident} (decoding failed: ${e})}`)\n      }\n    })\n  }\n\n  switch (string.type) {\n    case 'text':\n      return decodeMagicIdentifiers(string.value)\n    case 'strong':\n      return bold(red(decodeMagicIdentifiers(string.value)))\n    case 'code':\n      return green(decodeMagicIdentifiers(string.value))\n    case 'line':\n      return string.value.map(renderStyledStringToErrorAnsi).join('')\n    case 'stack':\n      return string.value.map(renderStyledStringToErrorAnsi).join('\\n')\n    default:\n      throw new Error('Unknown StyledString type', string)\n  }\n}\n\nexport function isPersistentCachingEnabled(\n  config: NextConfigComplete\n): boolean {\n  return config.experimental?.turbopackPersistentCaching || false\n}\n"], "names": ["bold", "green", "magenta", "red", "isInternal", "decodeMagicIdentifier", "MAGIC_IDENTIFIER_REGEX", "Log", "loadJsConfig", "ModuleBuildError", "Error", "name", "isWellKnownError", "issue", "title", "formattedTitle", "renderStyledStringToErrorAnsi", "includes", "getIssueKey", "severity", "filePath", "JSON", "stringify", "description", "getTurbopackJsConfig", "dir", "nextConfig", "jsConfig", "compilerOptions", "processIssues", "currentEntryIssues", "key", "result", "throwIssue", "logErrors", "newIssues", "Map", "set", "relevantIssues", "Set", "issues", "issue<PERSON><PERSON>", "formatted", "formatIssue", "add", "error", "size", "join", "source", "importTraces", "documentationLink", "replace", "formattedFilePath", "replaceAll", "message", "range", "start", "line", "column", "content", "end", "codeFrameColumns", "require", "forceColor", "trim", "type", "value", "length", "everyTraceHasADistinctRootLayer", "map", "leafLayerName", "filter", "l", "i", "trace", "layer", "traceIndent", "formatIssueTrace", "identicalLayers", "items", "item", "undefined", "firstPresentLayer", "findIndex", "t", "itemLayer", "indent", "printLayers", "r", "fsName", "path", "isRelevantWarning", "isNodeModulesIssue", "stage", "match", "startsWith", "string", "decodeMagicIdentifiers", "str", "ident", "e", "isPersistentCachingEnabled", "config", "experimental", "turbopackPersistentCaching"], "mappings": "AAOA,SAASA,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAEC,GAAG,QAAQ,0BAAyB;AACnE,OAAOC,gBAAgB,iBAAgB;AACvC,SACEC,qBAAqB,EACrBC,sBAAsB,QACjB,sBAAqB;AAE5B,YAAYC,SAAS,4BAA2B;AAEhD,OAAOC,kBAAkB,+BAA8B;AAOvD;;;CAGC,GACD,OAAO,MAAMC,yBAAyBC;;QAA/B,qBACLC,OAAO;;AACT;AAEA;;;CAGC,GACD,OAAO,SAASC,iBAAiBC,KAAY;IAC3C,MAAM,EAAEC,KAAK,EAAE,GAAGD;IAClB,MAAME,iBAAiBC,8BAA8BF;IACrD,mCAAmC;IACnC,IACEC,eAAeE,QAAQ,CAAC,uBACxBF,eAAeE,QAAQ,CAAC,wBACxB;QACA,OAAO;IACT;IAEA,OAAO;AACT;AAEA,OAAO,SAASC,YAAYL,KAAY;IACtC,OAAO,AAAGA,MAAMM,QAAQ,GAAC,MAAGN,MAAMO,QAAQ,GAAC,MAAGC,KAAKC,SAAS,CAC1DT,MAAMC,KAAK,IACX,MAAGO,KAAKC,SAAS,CAACT,MAAMU,WAAW;AACvC;AAEA,OAAO,eAAeC,qBACpBC,GAAW,EACXC,UAA8B;IAE9B,MAAM,EAAEC,QAAQ,EAAE,GAAG,MAAMnB,aAAaiB,KAAKC;IAC7C,OAAOC,mBAAAA,WAAY;QAAEC,iBAAiB,CAAC;IAAE;AAC3C;AAEA,OAAO,SAASC,cACdC,kBAAkC,EAClCC,GAAa,EACbC,MAAuB,EACvBC,UAAmB,EACnBC,SAAkB;IAElB,MAAMC,YAAY,IAAIC;IACtBN,mBAAmBO,GAAG,CAACN,KAAKI;IAE5B,MAAMG,iBAAiB,IAAIC;IAE3B,KAAK,MAAM1B,SAASmB,OAAOQ,MAAM,CAAE;QACjC,IACE3B,MAAMM,QAAQ,KAAK,WACnBN,MAAMM,QAAQ,KAAK,WACnBN,MAAMM,QAAQ,KAAK,WAEnB;QAEF,MAAMsB,WAAWvB,YAAYL;QAC7BsB,UAAUE,GAAG,CAACI,UAAU5B;QAExB,IAAIA,MAAMM,QAAQ,KAAK,WAAW;YAChC,IAAIc,YAAY;gBACd,MAAMS,YAAYC,YAAY9B;gBAC9ByB,eAAeM,GAAG,CAACF;YACrB,OAEK,IAAIR,aAAatB,iBAAiBC,QAAQ;gBAC7C,MAAM6B,YAAYC,YAAY9B;gBAC9BN,IAAIsC,KAAK,CAACH;YACZ;QACF;IACF;IAEA,IAAIJ,eAAeQ,IAAI,IAAIb,YAAY;QACrC,MAAM,qBAAsD,CAAtD,IAAIxB,iBAAiB;eAAI6B;SAAe,CAACS,IAAI,CAAC,UAA9C,qBAAA;mBAAA;wBAAA;0BAAA;QAAqD;IAC7D;AACF;AAEA,OAAO,SAASJ,YAAY9B,KAAY;IACtC,MAAM,EAAEO,QAAQ,EAAEN,KAAK,EAAES,WAAW,EAAEyB,MAAM,EAAEC,YAAY,EAAE,GAAGpC;IAC/D,IAAI,EAAEqC,iBAAiB,EAAE,GAAGrC;IAC5B,MAAME,iBAAiBC,8BAA8BF,OAAOqC,OAAO,CACjE,OACA;IAGF,0CAA0C;IAC1C,+DAA+D;IAC/D,IAAIpC,eAAeE,QAAQ,CAAC,qBAAqB;QAC/C,gCAAgC;QAChC,2CAA2C;QAC3CiC,oBAAoB;IACtB;IAEA,MAAME,oBAAoBhC,SACvB+B,OAAO,CAAC,cAAc,MACtBE,UAAU,CAAC,OAAO,KAClBF,OAAO,CAAC,WAAW;IAEtB,IAAIG,UAAU;IAEd,IAAIN,0BAAAA,OAAQO,KAAK,EAAE;QACjB,MAAM,EAAEC,KAAK,EAAE,GAAGR,OAAOO,KAAK;QAC9BD,UAAU,AAAGF,oBAAkB,MAAGI,CAAAA,MAAMC,IAAI,GAAG,CAAA,IAAE,MAC/CD,CAAAA,MAAME,MAAM,GAAG,CAAA,IAChB,OAAI3C;IACP,OAAO,IAAIqC,mBAAmB;QAC5BE,UAAU,AAAGF,oBAAkB,OAAIrC;IACrC,OAAO;QACLuC,UAAUvC;IACZ;IACAuC,WAAW;IAEX,IACEN,CAAAA,0BAAAA,OAAQO,KAAK,KACbP,OAAOA,MAAM,CAACW,OAAO,IACrB,4EAA4E;IAC5E,CAACvD,WAAWgB,WACZ;QACA,MAAM,EAAEoC,KAAK,EAAEI,GAAG,EAAE,GAAGZ,OAAOO,KAAK;QACnC,MAAM,EAAEM,gBAAgB,EAAE,GACxBC,QAAQ;QAEVR,WACEO,iBACEb,OAAOA,MAAM,CAACW,OAAO,EACrB;YACEH,OAAO;gBACLC,MAAMD,MAAMC,IAAI,GAAG;gBACnBC,QAAQF,MAAME,MAAM,GAAG;YACzB;YACAE,KAAK;gBACHH,MAAMG,IAAIH,IAAI,GAAG;gBACjBC,QAAQE,IAAIF,MAAM,GAAG;YACvB;QACF,GACA;YAAEK,YAAY;QAAK,GACnBC,IAAI,KAAK;IACf;IAEA,IAAIzC,aAAa;QACf,IACEA,YAAY0C,IAAI,KAAK,UACrB1C,YAAY2C,KAAK,CAACjD,QAAQ,CAAE,8BAC5B;YACAqC,WACE;YACFA,WAAW;YACXA,WAAW;QACb,OAAO;YACLA,WAAWtC,8BAA8BO,eAAe;QAC1D;IACF;IAEA,yEAAyE;IACzE,gBAAgB;IAChB,8DAA8D;IAC9D,IAAI;IAEJ,IAAI0B,gCAAAA,aAAckB,MAAM,EAAE;QACxB,iFAAiF;QACjF,yFAAyF;QACzF,4CAA4C;QAC5Cb,WAAW,AAAC,iBAAcL,CAAAA,aAAakB,MAAM,GAAG,IAAI,MAAM,EAAC,IAAE;QAC7D,MAAMC,kCACJ,IAAI7B,IAAIU,aAAaoB,GAAG,CAACC,eAAeC,MAAM,CAAC,CAACC,IAAMA,KAAK,OAAO1B,IAAI,KACtEG,aAAakB,MAAM;QACrB,IAAK,IAAIM,IAAI,GAAGA,IAAIxB,aAAakB,MAAM,EAAEM,IAAK;YAC5C,MAAMC,QAAQzB,YAAY,CAACwB,EAAE;YAC7B,MAAME,QAAQL,cAAcI;YAC5B,IAAIE,cAAc;YAClB,yCAAyC;YACzC,IAAIR,iCAAiC;gBACnCd,WAAW,AAAC,OAAIqB,QAAM;YACxB,OAAO;gBACL,IAAI1B,aAAakB,MAAM,GAAG,GAAG;oBAC3B,uDAAuD;oBACvDb,WAAW,AAAC,QAAKmB,CAAAA,IAAI,CAAA;oBACrB,IAAIE,OAAO;wBACTrB,WAAW,AAAC,OAAIqB,QAAM;oBACxB;oBACArB,WAAW;gBACb,OAAO,IAAIqB,OAAO;oBAChBrB,WAAW,AAAC,OAAIqB,QAAM;gBACxB,OAAO;oBACL,qEAAqE;oBACrEC,cAAc;gBAChB;YACF;YACAtB,WAAWuB,iBAAiBH,OAAOE,aAAa,CAACE,gBAAgBJ;QACnE;IACF;IACA,IAAIxB,mBAAmB;QACrBI,WAAWJ,oBAAoB;IACjC;IACA,OAAOI;AACT;AAEA,sDAAsD,GACtD,SAASgB,cAAcS,KAAuB;IAC5C,KAAK,MAAMC,QAAQD,MAAO;QACxB,MAAMJ,QAAQK,KAAKL,KAAK;QACxB,IAAIA,SAAS,MAAM,OAAOA;IAC5B;IACA,OAAOM;AACT;AAEA;;;CAGC,GACD,SAASH,gBAAgBC,KAAuB;IAC9C,MAAMG,oBAAoBH,MAAMI,SAAS,CAAC,CAACC,IAAMA,EAAET,KAAK,IAAI;IAC5D,IAAIO,sBAAsB,CAAC,GAAG,OAAO,KAAK,wBAAwB;;IAClE,MAAMP,QAAQI,KAAK,CAACG,kBAAkB,CAACP,KAAK;IAC5C,IAAK,IAAIF,IAAIS,oBAAoB,GAAGT,IAAIM,MAAMZ,MAAM,EAAEM,IAAK;QACzD,MAAMY,YAAYN,KAAK,CAACN,EAAE,CAACE,KAAK;QAChC,IAAIU,aAAa,QAAQA,cAAcV,OAAO;YAC5C,OAAO;QACT;IACF;IACA,OAAO;AACT;AAEA,SAASE,iBACPE,KAAuB,EACvBO,MAAc,EACdC,WAAoB;IAEpB,OAAO,AAAC,KAAER,MACPV,GAAG,CAAC,CAACW;QACJ,IAAIQ,IAAIF;QACR,IAAIN,KAAKS,MAAM,KAAK,WAAW;YAC7BD,KAAK,AAAC,MAAGR,KAAKS,MAAM,GAAC;QACvB,OAAO;YACL,2CAA2C;YAC3CD,KAAK;QACP;QACAA,KAAKR,KAAKU,IAAI;QACd,IAAIH,eAAeP,KAAKL,KAAK,EAAE;YAC7Ba,KAAK,AAAC,OAAIR,KAAKL,KAAK,GAAC;QACvB;QACA,OAAOa;IACT,GACCzC,IAAI,CAAC,QAAM;AAChB;AAEA,OAAO,SAAS4C,kBAAkB9E,KAAY;IAC5C,OAAOA,MAAMM,QAAQ,KAAK,aAAa,CAACyE,mBAAmB/E;AAC7D;AAEA,SAAS+E,mBAAmB/E,KAAY;IACtC,IAAIA,MAAMM,QAAQ,KAAK,aAAaN,MAAMgF,KAAK,KAAK,UAAU;QAC5D,qCAAqC;QACrC,2EAA2E;QAC3E,IACE7E,8BAA8BH,MAAMC,KAAK,EAAEG,QAAQ,CAAC,sBACpD;YACA,OAAO;QACT;IACF;IAEA,OACEJ,MAAMM,QAAQ,KAAK,aAClBN,CAAAA,MAAMO,QAAQ,CAAC0E,KAAK,CAAC,8CAA8C,QAClE,0FAA0F;IAC1F,uBAAuB;IACvB,+DAA+D;IAC/DjF,MAAMO,QAAQ,CAAC2E,UAAU,CAAC,2BAA0B;AAE1D;AAEA,OAAO,SAAS/E,8BAA8BgF,MAAoB;IAChE,SAASC,uBAAuBC,GAAW;QACzC,OAAOA,IAAI7C,UAAU,CAAC/C,wBAAwB,CAAC6F;YAC7C,IAAI;gBACF,OAAOjG,QAAQ,AAAC,MAAGG,sBAAsB8F,SAAO;YAClD,EAAE,OAAOC,GAAG;gBACV,OAAOlG,QAAQ,AAAC,MAAGiG,QAAM,wBAAqBC,IAAE;YAClD;QACF;IACF;IAEA,OAAQJ,OAAO/B,IAAI;QACjB,KAAK;YACH,OAAOgC,uBAAuBD,OAAO9B,KAAK;QAC5C,KAAK;YACH,OAAOlE,KAAKG,IAAI8F,uBAAuBD,OAAO9B,KAAK;QACrD,KAAK;YACH,OAAOjE,MAAMgG,uBAAuBD,OAAO9B,KAAK;QAClD,KAAK;YACH,OAAO8B,OAAO9B,KAAK,CAACG,GAAG,CAACrD,+BAA+B+B,IAAI,CAAC;QAC9D,KAAK;YACH,OAAOiD,OAAO9B,KAAK,CAACG,GAAG,CAACrD,+BAA+B+B,IAAI,CAAC;QAC9D;YACE,MAAM,qBAA8C,CAA9C,IAAIrC,MAAM,6BAA6BsF,SAAvC,qBAAA;uBAAA;4BAAA;8BAAA;YAA6C;IACvD;AACF;AAEA,OAAO,SAASK,2BACdC,MAA0B;QAEnBA;IAAP,OAAOA,EAAAA,uBAAAA,OAAOC,YAAY,qBAAnBD,qBAAqBE,0BAA0B,KAAI;AAC5D", "ignoreList": [0]}