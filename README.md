# Authentication Web App

A full-stack web application with authentication features built with Next.js frontend and NestJS backend.

## Features

- **Register Page**: firstName, lastName, email, password with validation
- **Login Page**: email & password with JWT-based authentication
- **Home Page**: motivational quotes that change every 5 seconds with countdown timer
- **Profile Page**: user details display and editing with profile picture upload

## Tech Stack

- **Frontend**: Next.js (TypeScript, TailwindCSS)
- **Backend**: NestJS (TypeScript, Prisma ORM)
- **Database**: PostgreSQL

## Project Structure

```
├── frontend/          # Next.js application
│   ├── src/
│   │   ├── app/       # App router pages
│   │   ├── components/
│   │   └── services/  # API clients
│   └── ...
├── backend/           # NestJS application
│   ├── src/
│   │   ├── auth/      # Authentication module
│   │   ├── users/     # User management module
│   │   ├── quotes/    # Quotes module
│   │   └── common/    # Shared utilities
│   └── ...
└── README.md
```

## Setup Instructions

### Prerequisites

1. **Node.js** (v18 or higher)
2. **PostgreSQL** database running locally or remotely
3. **npm** or **yarn** package manager

### Backend Setup

1. Navigate to backend directory:
```bash
cd backend
```

2. Install dependencies:
```bash
npm install
```

3. Configure environment variables:
   - Update `DATABASE_URL` in `.env` file with your PostgreSQL connection string
   - Example: `DATABASE_URL="postgresql://username:password@localhost:5432/auth_app"`
   - Make sure `JWT_SECRET` is set to a secure random string

4. Set up database:
```bash
# Generate Prisma client
npx prisma generate

# Run database migrations (make sure PostgreSQL is running)
npx prisma migrate dev --name init
```

5. Start the backend server:
```bash
npm run start:dev
```

The backend will run on `http://localhost:3001`

### Frontend Setup

1. Navigate to frontend directory:
```bash
cd frontend
```

2. Install dependencies:
```bash
npm install
```

3. Start the frontend development server:
```bash
npm run dev
```

The frontend will run on `http://localhost:3000`

### Database Setup (PostgreSQL)

If you don't have PostgreSQL installed:

**Windows:**
- Download and install from https://www.postgresql.org/download/windows/
- Or use Docker: `docker run --name postgres -e POSTGRES_PASSWORD=password -p 5432:5432 -d postgres`

**macOS:**
- Install via Homebrew: `brew install postgresql`
- Start service: `brew services start postgresql`

**Linux:**
- Install: `sudo apt-get install postgresql postgresql-contrib`
- Start service: `sudo systemctl start postgresql`

Create database:
```sql
CREATE DATABASE auth_app;
```

## User Flow

1. **Register** → Create new account with firstName, lastName, email, password
2. **Login** → Authenticate with email/password and get JWT token
3. **Home** → View motivational quotes that change every 5 seconds with countdown timer
4. **Profile** → View and edit user details (firstName, lastName, email, password, profile picture)

## API Endpoints

### Authentication
- `POST /auth/register` - Register new user
- `POST /auth/login` - Login user and get JWT token

### Users (Protected)
- `GET /users/me` - Get current user profile
- `PUT /users/update` - Update user profile

### Quotes
- `GET /quotes/random` - Get random motivational quote

## Environment Variables

### Backend (.env)
```
DATABASE_URL="postgresql://username:password@localhost:5432/auth_app"
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
PORT=3001
```

### Frontend (.env.local)
```
NEXT_PUBLIC_API_URL=http://localhost:3001
```

## Features Implemented

### Backend (NestJS)
- ✅ JWT-based authentication with Passport.js
- ✅ Password hashing with bcrypt
- ✅ User registration and login
- ✅ Protected routes with JWT guards
- ✅ User profile management
- ✅ Random motivational quotes API
- ✅ Input validation with class-validator
- ✅ Prisma ORM with PostgreSQL
- ✅ CORS configuration
- ✅ Global exception handling

### Frontend (Next.js)
- ✅ TypeScript configuration
- ✅ TailwindCSS for styling
- ✅ Authentication context with React Context API
- ✅ Protected routes
- ✅ Form validation
- ✅ Responsive design
- ✅ Auto-refreshing quotes with countdown timer
- ✅ Profile editing functionality
- ✅ Axios API client with interceptors

## Troubleshooting

### Backend won't start
1. Make sure PostgreSQL is running
2. Check DATABASE_URL in .env file
3. Run `npx prisma migrate dev` to create database tables
4. Verify all dependencies are installed: `npm install`

### Frontend won't connect to backend
1. Make sure backend is running on port 3001
2. Check NEXT_PUBLIC_API_URL in .env.local
3. Verify CORS is properly configured in backend

### Database connection issues
1. Ensure PostgreSQL service is running
2. Create the database: `CREATE DATABASE auth_app;`
3. Update DATABASE_URL with correct credentials
4. Test connection: `npx prisma db push`

## Development Notes

- Backend runs on port 3001
- Frontend runs on port 3000
- JWT tokens expire in 24 hours
- Quotes refresh every 5 seconds
- Passwords must be at least 6 characters
- Profile pictures are stored as URLs (not file uploads)
