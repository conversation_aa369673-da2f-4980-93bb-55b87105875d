{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/prepare-destination.ts"], "sourcesContent": ["import type { IncomingMessage } from 'http'\nimport type { Key } from 'next/dist/compiled/path-to-regexp'\nimport type { NextParsedUrlQuery } from '../../../../server/request-meta'\nimport type { RouteHas } from '../../../../lib/load-custom-routes'\nimport type { BaseNextRequest } from '../../../../server/base-http'\n\nimport { escapeStringRegexp } from '../../escape-regexp'\nimport { parseUrl } from './parse-url'\nimport {\n  INTERCEPTION_ROUTE_MARKERS,\n  isInterceptionRouteAppPath,\n} from './interception-routes'\nimport { getCookieParser } from '../../../../server/api-utils/get-cookie-parser'\nimport type { Params } from '../../../../server/request/params'\nimport { safePathToRegexp, safeCompile } from './route-match-utils'\n\n/**\n * Ensure only a-zA-Z are used for param names for proper interpolating\n * with path-to-regexp\n */\nfunction getSafeParamName(paramName: string) {\n  let newParamName = ''\n\n  for (let i = 0; i < paramName.length; i++) {\n    const charCode = paramName.charCodeAt(i)\n\n    if (\n      (charCode > 64 && charCode < 91) || // A-Z\n      (charCode > 96 && charCode < 123) // a-z\n    ) {\n      newParamName += paramName[i]\n    }\n  }\n  return newParamName\n}\n\nfunction escapeSegment(str: string, segmentName: string) {\n  return str.replace(\n    new RegExp(`:${escapeStringRegexp(segmentName)}`, 'g'),\n    `__ESC_COLON_${segmentName}`\n  )\n}\n\nfunction unescapeSegments(str: string) {\n  return str.replace(/__ESC_COLON_/gi, ':')\n}\n\nexport function matchHas(\n  req: BaseNextRequest | IncomingMessage,\n  query: Params,\n  has: RouteHas[] = [],\n  missing: RouteHas[] = []\n): false | Params {\n  const params: Params = {}\n\n  const hasMatch = (hasItem: RouteHas) => {\n    let value\n    let key = hasItem.key\n\n    switch (hasItem.type) {\n      case 'header': {\n        key = key!.toLowerCase()\n        value = req.headers[key] as string\n        break\n      }\n      case 'cookie': {\n        if ('cookies' in req) {\n          value = req.cookies[hasItem.key]\n        } else {\n          const cookies = getCookieParser(req.headers)()\n          value = cookies[hasItem.key]\n        }\n\n        break\n      }\n      case 'query': {\n        value = query[key!]\n        break\n      }\n      case 'host': {\n        const { host } = req?.headers || {}\n        // remove port from host if present\n        const hostname = host?.split(':', 1)[0].toLowerCase()\n        value = hostname\n        break\n      }\n      default: {\n        break\n      }\n    }\n\n    if (!hasItem.value && value) {\n      params[getSafeParamName(key!)] = value\n      return true\n    } else if (value) {\n      const matcher = new RegExp(`^${hasItem.value}$`)\n      const matches = Array.isArray(value)\n        ? value.slice(-1)[0].match(matcher)\n        : value.match(matcher)\n\n      if (matches) {\n        if (Array.isArray(matches)) {\n          if (matches.groups) {\n            Object.keys(matches.groups).forEach((groupKey) => {\n              params[groupKey] = matches.groups![groupKey]\n            })\n          } else if (hasItem.type === 'host' && matches[0]) {\n            params.host = matches[0]\n          }\n        }\n        return true\n      }\n    }\n    return false\n  }\n\n  const allMatch =\n    has.every((item) => hasMatch(item)) &&\n    !missing.some((item) => hasMatch(item))\n\n  if (allMatch) {\n    return params\n  }\n  return false\n}\n\nexport function compileNonPath(value: string, params: Params): string {\n  if (!value.includes(':')) {\n    return value\n  }\n\n  for (const key of Object.keys(params)) {\n    if (value.includes(`:${key}`)) {\n      value = value\n        .replace(\n          new RegExp(`:${key}\\\\*`, 'g'),\n          `:${key}--ESCAPED_PARAM_ASTERISKS`\n        )\n        .replace(\n          new RegExp(`:${key}\\\\?`, 'g'),\n          `:${key}--ESCAPED_PARAM_QUESTION`\n        )\n        .replace(new RegExp(`:${key}\\\\+`, 'g'), `:${key}--ESCAPED_PARAM_PLUS`)\n        .replace(\n          new RegExp(`:${key}(?!\\\\w)`, 'g'),\n          `--ESCAPED_PARAM_COLON${key}`\n        )\n    }\n  }\n  value = value\n    .replace(/(:|\\*|\\?|\\+|\\(|\\)|\\{|\\})/g, '\\\\$1')\n    .replace(/--ESCAPED_PARAM_PLUS/g, '+')\n    .replace(/--ESCAPED_PARAM_COLON/g, ':')\n    .replace(/--ESCAPED_PARAM_QUESTION/g, '?')\n    .replace(/--ESCAPED_PARAM_ASTERISKS/g, '*')\n\n  // the value needs to start with a forward-slash to be compiled\n  // correctly\n  return safeCompile(`/${value}`, { validate: false })(params).slice(1)\n}\n\nexport function parseDestination(args: {\n  destination: string\n  params: Readonly<Params>\n  query: Readonly<NextParsedUrlQuery>\n}) {\n  let escaped = args.destination\n  for (const param of Object.keys({ ...args.params, ...args.query })) {\n    if (!param) continue\n\n    escaped = escapeSegment(escaped, param)\n  }\n\n  const parsed = parseUrl(escaped)\n\n  let pathname = parsed.pathname\n  if (pathname) {\n    pathname = unescapeSegments(pathname)\n  }\n\n  let href = parsed.href\n  if (href) {\n    href = unescapeSegments(href)\n  }\n\n  let hostname = parsed.hostname\n  if (hostname) {\n    hostname = unescapeSegments(hostname)\n  }\n\n  let hash = parsed.hash\n  if (hash) {\n    hash = unescapeSegments(hash)\n  }\n\n  let search = parsed.search\n  if (search) {\n    search = unescapeSegments(search)\n  }\n\n  return {\n    ...parsed,\n    pathname,\n    hostname,\n    href,\n    hash,\n    search,\n  }\n}\n\nexport function prepareDestination(args: {\n  appendParamsToQuery: boolean\n  destination: string\n  params: Params\n  query: NextParsedUrlQuery\n}) {\n  const parsedDestination = parseDestination(args)\n\n  const {\n    hostname: destHostname,\n    query: destQuery,\n    search: destSearch,\n  } = parsedDestination\n\n  // The following code assumes that the pathname here includes the hash if it's\n  // present.\n  let destPath = parsedDestination.pathname\n  if (parsedDestination.hash) {\n    destPath = `${destPath}${parsedDestination.hash}`\n  }\n\n  const destParams: (string | number)[] = []\n\n  const destPathParamKeys: Key[] = []\n  safePathToRegexp(destPath, destPathParamKeys)\n  for (const key of destPathParamKeys) {\n    destParams.push(key.name)\n  }\n\n  if (destHostname) {\n    const destHostnameParamKeys: Key[] = []\n    safePathToRegexp(destHostname, destHostnameParamKeys)\n    for (const key of destHostnameParamKeys) {\n      destParams.push(key.name)\n    }\n  }\n\n  const destPathCompiler = safeCompile(\n    destPath,\n    // we don't validate while compiling the destination since we should\n    // have already validated before we got to this point and validating\n    // breaks compiling destinations with named pattern params from the source\n    // e.g. /something:hello(.*) -> /another/:hello is broken with validation\n    // since compile validation is meant for reversing and not for inserting\n    // params from a separate path-regex into another\n    { validate: false }\n  )\n\n  let destHostnameCompiler\n  if (destHostname) {\n    destHostnameCompiler = safeCompile(destHostname, { validate: false })\n  }\n\n  // update any params in query values\n  for (const [key, strOrArray] of Object.entries(destQuery)) {\n    // the value needs to start with a forward-slash to be compiled\n    // correctly\n    if (Array.isArray(strOrArray)) {\n      destQuery[key] = strOrArray.map((value) =>\n        compileNonPath(unescapeSegments(value), args.params)\n      )\n    } else if (typeof strOrArray === 'string') {\n      destQuery[key] = compileNonPath(unescapeSegments(strOrArray), args.params)\n    }\n  }\n\n  // add path params to query if it's not a redirect and not\n  // already defined in destination query or path\n  let paramKeys = Object.keys(args.params).filter(\n    (name) => name !== 'nextInternalLocale'\n  )\n\n  if (\n    args.appendParamsToQuery &&\n    !paramKeys.some((key) => destParams.includes(key))\n  ) {\n    for (const key of paramKeys) {\n      if (!(key in destQuery)) {\n        destQuery[key] = args.params[key]\n      }\n    }\n  }\n\n  let newUrl\n\n  // The compiler also that the interception route marker is an unnamed param, hence '0',\n  // so we need to add it to the params object.\n  if (isInterceptionRouteAppPath(destPath)) {\n    for (const segment of destPath.split('/')) {\n      const marker = INTERCEPTION_ROUTE_MARKERS.find((m) =>\n        segment.startsWith(m)\n      )\n      if (marker) {\n        if (marker === '(..)(..)') {\n          args.params['0'] = '(..)'\n          args.params['1'] = '(..)'\n        } else {\n          args.params['0'] = marker\n        }\n        break\n      }\n    }\n  }\n\n  try {\n    newUrl = destPathCompiler(args.params)\n\n    const [pathname, hash] = newUrl.split('#', 2)\n    if (destHostnameCompiler) {\n      parsedDestination.hostname = destHostnameCompiler(args.params)\n    }\n    parsedDestination.pathname = pathname\n    parsedDestination.hash = `${hash ? '#' : ''}${hash || ''}`\n    parsedDestination.search = destSearch\n      ? compileNonPath(destSearch, args.params)\n      : ''\n  } catch (err: any) {\n    if (err.message.match(/Expected .*? to not repeat, but got an array/)) {\n      throw new Error(\n        `To use a multi-match in the destination you must add \\`*\\` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match`\n      )\n    }\n    throw err\n  }\n\n  // Query merge order lowest priority to highest\n  // 1. initial URL query values\n  // 2. path segment values\n  // 3. destination specified query values\n  parsedDestination.query = {\n    ...args.query,\n    ...parsedDestination.query,\n  }\n\n  return {\n    newUrl,\n    destQuery,\n    parsedDestination,\n  }\n}\n"], "names": ["escapeStringRegexp", "parseUrl", "INTERCEPTION_ROUTE_MARKERS", "isInterceptionRouteAppPath", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "safePathToRegexp", "safeCompile", "getSafeParamName", "paramName", "newParamName", "i", "length", "charCode", "charCodeAt", "escapeSegment", "str", "segmentName", "replace", "RegExp", "unescapeSegments", "matchHas", "req", "query", "has", "missing", "params", "hasMatch", "hasItem", "value", "key", "type", "toLowerCase", "headers", "cookies", "host", "hostname", "split", "matcher", "matches", "Array", "isArray", "slice", "match", "groups", "Object", "keys", "for<PERSON>ach", "groupKey", "allMatch", "every", "item", "some", "compileNonPath", "includes", "validate", "parseDestination", "args", "escaped", "destination", "param", "parsed", "pathname", "href", "hash", "search", "prepareDestination", "parsedDestination", "destHostname", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "destSearch", "destPath", "destParams", "destPathPara<PERSON><PERSON><PERSON>s", "push", "name", "destHostnameParamKeys", "destPathCompiler", "destHostnameCompiler", "strOrArray", "entries", "map", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "appendParamsToQuery", "newUrl", "segment", "marker", "find", "m", "startsWith", "err", "message", "Error"], "mappings": "AAMA,SAASA,kBAAkB,QAAQ,sBAAqB;AACxD,SAASC,QAAQ,QAAQ,cAAa;AACtC,SACEC,0BAA0B,EAC1BC,0BAA0B,QACrB,wBAAuB;AAC9B,SAASC,eAAe,QAAQ,iDAAgD;AAEhF,SAASC,gBAAgB,EAAEC,WAAW,QAAQ,sBAAqB;AAEnE;;;CAGC,GACD,SAASC,iBAAiBC,SAAiB;IACzC,IAAIC,eAAe;IAEnB,IAAK,IAAIC,IAAI,GAAGA,IAAIF,UAAUG,MAAM,EAAED,IAAK;QACzC,MAAME,WAAWJ,UAAUK,UAAU,CAACH;QAEtC,IACE,AAACE,WAAW,MAAMA,WAAW,MAAO,MAAM;QACzCA,WAAW,MAAMA,WAAW,IAAK,MAAM;UACxC;YACAH,gBAAgBD,SAAS,CAACE,EAAE;QAC9B;IACF;IACA,OAAOD;AACT;AAEA,SAASK,cAAcC,GAAW,EAAEC,WAAmB;IACrD,OAAOD,IAAIE,OAAO,CAChB,IAAIC,OAAO,AAAC,MAAGlB,mBAAmBgB,cAAgB,MAClD,AAAC,iBAAcA;AAEnB;AAEA,SAASG,iBAAiBJ,GAAW;IACnC,OAAOA,IAAIE,OAAO,CAAC,kBAAkB;AACvC;AAEA,OAAO,SAASG,SACdC,GAAsC,EACtCC,KAAa,EACbC,GAAoB,EACpBC,OAAwB;IADxBD,IAAAA,gBAAAA,MAAkB,EAAE;IACpBC,IAAAA,oBAAAA,UAAsB,EAAE;IAExB,MAAMC,SAAiB,CAAC;IAExB,MAAMC,WAAW,CAACC;QAChB,IAAIC;QACJ,IAAIC,MAAMF,QAAQE,GAAG;QAErB,OAAQF,QAAQG,IAAI;YAClB,KAAK;gBAAU;oBACbD,MAAMA,IAAKE,WAAW;oBACtBH,QAAQP,IAAIW,OAAO,CAACH,IAAI;oBACxB;gBACF;YACA,KAAK;gBAAU;oBACb,IAAI,aAAaR,KAAK;wBACpBO,QAAQP,IAAIY,OAAO,CAACN,QAAQE,GAAG,CAAC;oBAClC,OAAO;wBACL,MAAMI,UAAU7B,gBAAgBiB,IAAIW,OAAO;wBAC3CJ,QAAQK,OAAO,CAACN,QAAQE,GAAG,CAAC;oBAC9B;oBAEA;gBACF;YACA,KAAK;gBAAS;oBACZD,QAAQN,KAAK,CAACO,IAAK;oBACnB;gBACF;YACA,KAAK;gBAAQ;oBACX,MAAM,EAAEK,IAAI,EAAE,GAAGb,CAAAA,uBAAAA,IAAKW,OAAO,KAAI,CAAC;oBAClC,mCAAmC;oBACnC,MAAMG,WAAWD,wBAAAA,KAAME,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,CAACL,WAAW;oBACnDH,QAAQO;oBACR;gBACF;YACA;gBAAS;oBACP;gBACF;QACF;QAEA,IAAI,CAACR,QAAQC,KAAK,IAAIA,OAAO;YAC3BH,MAAM,CAAClB,iBAAiBsB,KAAM,GAAGD;YACjC,OAAO;QACT,OAAO,IAAIA,OAAO;YAChB,MAAMS,UAAU,IAAInB,OAAO,AAAC,MAAGS,QAAQC,KAAK,GAAC;YAC7C,MAAMU,UAAUC,MAAMC,OAAO,CAACZ,SAC1BA,MAAMa,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAACC,KAAK,CAACL,WACzBT,MAAMc,KAAK,CAACL;YAEhB,IAAIC,SAAS;gBACX,IAAIC,MAAMC,OAAO,CAACF,UAAU;oBAC1B,IAAIA,QAAQK,MAAM,EAAE;wBAClBC,OAAOC,IAAI,CAACP,QAAQK,MAAM,EAAEG,OAAO,CAAC,CAACC;4BACnCtB,MAAM,CAACsB,SAAS,GAAGT,QAAQK,MAAM,AAAC,CAACI,SAAS;wBAC9C;oBACF,OAAO,IAAIpB,QAAQG,IAAI,KAAK,UAAUQ,OAAO,CAAC,EAAE,EAAE;wBAChDb,OAAOS,IAAI,GAAGI,OAAO,CAAC,EAAE;oBAC1B;gBACF;gBACA,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEA,MAAMU,WACJzB,IAAI0B,KAAK,CAAC,CAACC,OAASxB,SAASwB,UAC7B,CAAC1B,QAAQ2B,IAAI,CAAC,CAACD,OAASxB,SAASwB;IAEnC,IAAIF,UAAU;QACZ,OAAOvB;IACT;IACA,OAAO;AACT;AAEA,OAAO,SAAS2B,eAAexB,KAAa,EAAEH,MAAc;IAC1D,IAAI,CAACG,MAAMyB,QAAQ,CAAC,MAAM;QACxB,OAAOzB;IACT;IAEA,KAAK,MAAMC,OAAOe,OAAOC,IAAI,CAACpB,QAAS;QACrC,IAAIG,MAAMyB,QAAQ,CAAC,AAAC,MAAGxB,MAAQ;YAC7BD,QAAQA,MACLX,OAAO,CACN,IAAIC,OAAO,AAAC,MAAGW,MAAI,OAAM,MACzB,AAAC,MAAGA,MAAI,6BAETZ,OAAO,CACN,IAAIC,OAAO,AAAC,MAAGW,MAAI,OAAM,MACzB,AAAC,MAAGA,MAAI,4BAETZ,OAAO,CAAC,IAAIC,OAAO,AAAC,MAAGW,MAAI,OAAM,MAAM,AAAC,MAAGA,MAAI,wBAC/CZ,OAAO,CACN,IAAIC,OAAO,AAAC,MAAGW,MAAI,WAAU,MAC7B,AAAC,0BAAuBA;QAE9B;IACF;IACAD,QAAQA,MACLX,OAAO,CAAC,6BAA6B,QACrCA,OAAO,CAAC,yBAAyB,KACjCA,OAAO,CAAC,0BAA0B,KAClCA,OAAO,CAAC,6BAA6B,KACrCA,OAAO,CAAC,8BAA8B;IAEzC,+DAA+D;IAC/D,YAAY;IACZ,OAAOX,YAAY,AAAC,MAAGsB,OAAS;QAAE0B,UAAU;IAAM,GAAG7B,QAAQgB,KAAK,CAAC;AACrE;AAEA,OAAO,SAASc,iBAAiBC,IAIhC;IACC,IAAIC,UAAUD,KAAKE,WAAW;IAC9B,KAAK,MAAMC,SAASf,OAAOC,IAAI,CAAC;QAAE,GAAGW,KAAK/B,MAAM;QAAE,GAAG+B,KAAKlC,KAAK;IAAC,GAAI;QAClE,IAAI,CAACqC,OAAO;QAEZF,UAAU3C,cAAc2C,SAASE;IACnC;IAEA,MAAMC,SAAS3D,SAASwD;IAExB,IAAII,WAAWD,OAAOC,QAAQ;IAC9B,IAAIA,UAAU;QACZA,WAAW1C,iBAAiB0C;IAC9B;IAEA,IAAIC,OAAOF,OAAOE,IAAI;IACtB,IAAIA,MAAM;QACRA,OAAO3C,iBAAiB2C;IAC1B;IAEA,IAAI3B,WAAWyB,OAAOzB,QAAQ;IAC9B,IAAIA,UAAU;QACZA,WAAWhB,iBAAiBgB;IAC9B;IAEA,IAAI4B,OAAOH,OAAOG,IAAI;IACtB,IAAIA,MAAM;QACRA,OAAO5C,iBAAiB4C;IAC1B;IAEA,IAAIC,SAASJ,OAAOI,MAAM;IAC1B,IAAIA,QAAQ;QACVA,SAAS7C,iBAAiB6C;IAC5B;IAEA,OAAO;QACL,GAAGJ,MAAM;QACTC;QACA1B;QACA2B;QACAC;QACAC;IACF;AACF;AAEA,OAAO,SAASC,mBAAmBT,IAKlC;IACC,MAAMU,oBAAoBX,iBAAiBC;IAE3C,MAAM,EACJrB,UAAUgC,YAAY,EACtB7C,OAAO8C,SAAS,EAChBJ,QAAQK,UAAU,EACnB,GAAGH;IAEJ,8EAA8E;IAC9E,WAAW;IACX,IAAII,WAAWJ,kBAAkBL,QAAQ;IACzC,IAAIK,kBAAkBH,IAAI,EAAE;QAC1BO,WAAW,AAAC,KAAEA,WAAWJ,kBAAkBH,IAAI;IACjD;IAEA,MAAMQ,aAAkC,EAAE;IAE1C,MAAMC,oBAA2B,EAAE;IACnCnE,iBAAiBiE,UAAUE;IAC3B,KAAK,MAAM3C,OAAO2C,kBAAmB;QACnCD,WAAWE,IAAI,CAAC5C,IAAI6C,IAAI;IAC1B;IAEA,IAAIP,cAAc;QAChB,MAAMQ,wBAA+B,EAAE;QACvCtE,iBAAiB8D,cAAcQ;QAC/B,KAAK,MAAM9C,OAAO8C,sBAAuB;YACvCJ,WAAWE,IAAI,CAAC5C,IAAI6C,IAAI;QAC1B;IACF;IAEA,MAAME,mBAAmBtE,YACvBgE,UACA,oEAAoE;IACpE,oEAAoE;IACpE,0EAA0E;IAC1E,yEAAyE;IACzE,wEAAwE;IACxE,iDAAiD;IACjD;QAAEhB,UAAU;IAAM;IAGpB,IAAIuB;IACJ,IAAIV,cAAc;QAChBU,uBAAuBvE,YAAY6D,cAAc;YAAEb,UAAU;QAAM;IACrE;IAEA,oCAAoC;IACpC,KAAK,MAAM,CAACzB,KAAKiD,WAAW,IAAIlC,OAAOmC,OAAO,CAACX,WAAY;QACzD,+DAA+D;QAC/D,YAAY;QACZ,IAAI7B,MAAMC,OAAO,CAACsC,aAAa;YAC7BV,SAAS,CAACvC,IAAI,GAAGiD,WAAWE,GAAG,CAAC,CAACpD,QAC/BwB,eAAejC,iBAAiBS,QAAQ4B,KAAK/B,MAAM;QAEvD,OAAO,IAAI,OAAOqD,eAAe,UAAU;YACzCV,SAAS,CAACvC,IAAI,GAAGuB,eAAejC,iBAAiB2D,aAAatB,KAAK/B,MAAM;QAC3E;IACF;IAEA,0DAA0D;IAC1D,+CAA+C;IAC/C,IAAIwD,YAAYrC,OAAOC,IAAI,CAACW,KAAK/B,MAAM,EAAEyD,MAAM,CAC7C,CAACR,OAASA,SAAS;IAGrB,IACElB,KAAK2B,mBAAmB,IACxB,CAACF,UAAU9B,IAAI,CAAC,CAACtB,MAAQ0C,WAAWlB,QAAQ,CAACxB,OAC7C;QACA,KAAK,MAAMA,OAAOoD,UAAW;YAC3B,IAAI,CAAEpD,CAAAA,OAAOuC,SAAQ,GAAI;gBACvBA,SAAS,CAACvC,IAAI,GAAG2B,KAAK/B,MAAM,CAACI,IAAI;YACnC;QACF;IACF;IAEA,IAAIuD;IAEJ,uFAAuF;IACvF,6CAA6C;IAC7C,IAAIjF,2BAA2BmE,WAAW;QACxC,KAAK,MAAMe,WAAWf,SAASlC,KAAK,CAAC,KAAM;YACzC,MAAMkD,SAASpF,2BAA2BqF,IAAI,CAAC,CAACC,IAC9CH,QAAQI,UAAU,CAACD;YAErB,IAAIF,QAAQ;gBACV,IAAIA,WAAW,YAAY;oBACzB9B,KAAK/B,MAAM,CAAC,IAAI,GAAG;oBACnB+B,KAAK/B,MAAM,CAAC,IAAI,GAAG;gBACrB,OAAO;oBACL+B,KAAK/B,MAAM,CAAC,IAAI,GAAG6D;gBACrB;gBACA;YACF;QACF;IACF;IAEA,IAAI;QACFF,SAASR,iBAAiBpB,KAAK/B,MAAM;QAErC,MAAM,CAACoC,UAAUE,KAAK,GAAGqB,OAAOhD,KAAK,CAAC,KAAK;QAC3C,IAAIyC,sBAAsB;YACxBX,kBAAkB/B,QAAQ,GAAG0C,qBAAqBrB,KAAK/B,MAAM;QAC/D;QACAyC,kBAAkBL,QAAQ,GAAGA;QAC7BK,kBAAkBH,IAAI,GAAG,AAAC,KAAEA,CAAAA,OAAO,MAAM,EAAC,IAAIA,CAAAA,QAAQ,EAAC;QACvDG,kBAAkBF,MAAM,GAAGK,aACvBjB,eAAeiB,YAAYb,KAAK/B,MAAM,IACtC;IACN,EAAE,OAAOiE,KAAU;QACjB,IAAIA,IAAIC,OAAO,CAACjD,KAAK,CAAC,iDAAiD;YACrE,MAAM,qBAEL,CAFK,IAAIkD,MACP,4KADG,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,MAAMF;IACR;IAEA,+CAA+C;IAC/C,8BAA8B;IAC9B,yBAAyB;IACzB,wCAAwC;IACxCxB,kBAAkB5C,KAAK,GAAG;QACxB,GAAGkC,KAAKlC,KAAK;QACb,GAAG4C,kBAAkB5C,KAAK;IAC5B;IAEA,OAAO;QACL8D;QACAhB;QACAF;IACF;AACF", "ignoreList": [0]}