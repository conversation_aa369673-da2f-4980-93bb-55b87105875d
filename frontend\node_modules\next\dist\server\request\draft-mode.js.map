{"version": 3, "sources": ["../../../src/server/request/draft-mode.ts"], "sourcesContent": ["import {\n  getDraftModeProviderForCacheScope,\n  throwForMissingRequestStore,\n} from '../app-render/work-unit-async-storage.external'\n\nimport type { DraftModeProvider } from '../async-storage/draft-mode-provider'\n\nimport {\n  workAsyncStorage,\n  type WorkStore,\n} from '../app-render/work-async-storage.external'\nimport { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external'\nimport {\n  abortAndThrowOnSynchronousRequestDataAccess,\n  delayUntilRuntimeStage,\n  postponeWithTracking,\n  trackDynamicDataInDynamicRender,\n  trackSynchronousRequestDataAccessInDev,\n} from '../app-render/dynamic-rendering'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { DynamicServerError } from '../../client/components/hooks-server-context'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { ReflectAdapter } from '../web/spec-extension/adapters/reflect'\n\n/**\n * In this version of Next.js `draftMode()` returns a Promise however you can still reference the properties of the underlying draftMode object\n * synchronously to facilitate migration. The `UnsafeUnwrappedDraftMode` type is added to your code by a codemod that attempts to automatically\n * updates callsites to reflect the new Promise return type. There are some cases where `draftMode()` cannot be automatically converted, namely\n * when it is used inside a synchronous function and we can't be sure the function can be made async automatically. In these cases we add an\n * explicit type case to `UnsafeUnwrappedDraftMode` to enable typescript to allow for the synchronous usage only where it is actually necessary.\n *\n * You should should update these callsites to either be async functions where the `draftMode()` value can be awaited or you should call `draftMode()`\n * from outside and await the return value before passing it into this function.\n *\n * You can find instances that require manual migration by searching for `UnsafeUnwrappedDraftMode` in your codebase or by search for a comment that\n * starts with `@next-codemod-error`.\n *\n * In a future version of Next.js `draftMode()` will only return a Promise and you will not be able to access the underlying draftMode object directly\n * without awaiting the return value first. When this change happens the type `UnsafeUnwrappedDraftMode` will be updated to reflect that is it no longer\n * usable.\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedDraftMode = DraftMode\n\nexport function draftMode(): Promise<DraftMode> {\n  const callingExpression = 'draftMode'\n  const workStore = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (!workStore || !workUnitStore) {\n    throwForMissingRequestStore(callingExpression)\n  }\n\n  switch (workUnitStore.type) {\n    case 'prerender-runtime':\n      // TODO(runtime-ppr): does it make sense to delay this? normally it's always microtasky\n      return delayUntilRuntimeStage(\n        workUnitStore,\n        createOrGetCachedDraftMode(workUnitStore.draftMode, workStore)\n      )\n    case 'request':\n      return createOrGetCachedDraftMode(workUnitStore.draftMode, workStore)\n\n    case 'cache':\n    case 'private-cache':\n    case 'unstable-cache':\n      // Inside of `\"use cache\"` or `unstable_cache`, draft mode is available if\n      // the outmost work unit store is a request store (or a runtime prerender),\n      // and if draft mode is enabled.\n      const draftModeProvider = getDraftModeProviderForCacheScope(\n        workStore,\n        workUnitStore\n      )\n\n      if (draftModeProvider) {\n        return createOrGetCachedDraftMode(draftModeProvider, workStore)\n      }\n\n    // Otherwise, we fall through to providing an empty draft mode.\n    // eslint-disable-next-line no-fallthrough\n    case 'prerender':\n    case 'prerender-client':\n    case 'prerender-ppr':\n    case 'prerender-legacy':\n      // Return empty draft mode\n      return createOrGetCachedDraftMode(null, workStore)\n\n    default:\n      return workUnitStore satisfies never\n  }\n}\n\nfunction createOrGetCachedDraftMode(\n  draftModeProvider: DraftModeProvider | null,\n  workStore: WorkStore | undefined\n): Promise<DraftMode> {\n  const cacheKey = draftModeProvider ?? NullDraftMode\n  const cachedDraftMode = CachedDraftModes.get(cacheKey)\n\n  if (cachedDraftMode) {\n    return cachedDraftMode\n  }\n\n  let promise: Promise<DraftMode>\n\n  if (process.env.NODE_ENV === 'development' && !workStore?.isPrefetchRequest) {\n    const route = workStore?.route\n\n    if (process.env.__NEXT_CACHE_COMPONENTS) {\n      return createDraftModeWithDevWarnings(draftModeProvider, route)\n    }\n\n    promise = createExoticDraftModeWithDevWarnings(draftModeProvider, route)\n  } else {\n    if (process.env.__NEXT_CACHE_COMPONENTS) {\n      return Promise.resolve(new DraftMode(draftModeProvider))\n    }\n\n    promise = createExoticDraftMode(draftModeProvider)\n  }\n\n  CachedDraftModes.set(cacheKey, promise)\n\n  return promise\n}\n\ninterface CacheLifetime {}\nconst NullDraftMode = {}\nconst CachedDraftModes = new WeakMap<CacheLifetime, Promise<DraftMode>>()\n\nfunction createExoticDraftMode(\n  underlyingProvider: null | DraftModeProvider\n): Promise<DraftMode> {\n  const instance = new DraftMode(underlyingProvider)\n  const promise = Promise.resolve(instance)\n\n  Object.defineProperty(promise, 'isEnabled', {\n    get() {\n      return instance.isEnabled\n    },\n    enumerable: true,\n    configurable: true,\n  })\n  ;(promise as any).enable = instance.enable.bind(instance)\n  ;(promise as any).disable = instance.disable.bind(instance)\n\n  return promise\n}\n\nfunction createExoticDraftModeWithDevWarnings(\n  underlyingProvider: null | DraftModeProvider,\n  route: undefined | string\n): Promise<DraftMode> {\n  const instance = new DraftMode(underlyingProvider)\n  const promise = Promise.resolve(instance)\n\n  Object.defineProperty(promise, 'isEnabled', {\n    get() {\n      const expression = '`draftMode().isEnabled`'\n      syncIODev(route, expression)\n      return instance.isEnabled\n    },\n    enumerable: true,\n    configurable: true,\n  })\n\n  Object.defineProperty(promise, 'enable', {\n    value: function get() {\n      const expression = '`draftMode().enable()`'\n      syncIODev(route, expression)\n      return instance.enable.apply(instance, arguments as any)\n    },\n  })\n\n  Object.defineProperty(promise, 'disable', {\n    value: function get() {\n      const expression = '`draftMode().disable()`'\n      syncIODev(route, expression)\n      return instance.disable.apply(instance, arguments as any)\n    },\n  })\n\n  return promise\n}\n\n// Similar to `createExoticDraftModeWithDevWarnings`, but just logging the sync\n// access without actually defining the draftMode properties on the promise.\nfunction createDraftModeWithDevWarnings(\n  underlyingProvider: null | DraftModeProvider,\n  route: undefined | string\n): Promise<DraftMode> {\n  const instance = new DraftMode(underlyingProvider)\n  const promise = Promise.resolve(instance)\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      switch (prop) {\n        case 'isEnabled':\n          warnForSyncAccess(route, `\\`draftMode().${prop}\\``)\n          break\n        case 'enable':\n        case 'disable': {\n          warnForSyncAccess(route, `\\`draftMode().${prop}()\\``)\n          break\n        }\n        default: {\n          // We only warn for well-defined properties of the draftMode object.\n        }\n      }\n\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n  })\n\n  return proxiedPromise\n}\n\nclass DraftMode {\n  /**\n   * @internal - this declaration is stripped via `tsc --stripInternal`\n   */\n  private readonly _provider: null | DraftModeProvider\n\n  constructor(provider: null | DraftModeProvider) {\n    this._provider = provider\n  }\n  get isEnabled() {\n    if (this._provider !== null) {\n      return this._provider.isEnabled\n    }\n    return false\n  }\n  public enable() {\n    // We have a store we want to track dynamic data access to ensure we\n    // don't statically generate routes that manipulate draft mode.\n    trackDynamicDraftMode('draftMode().enable()', this.enable)\n    if (this._provider !== null) {\n      this._provider.enable()\n    }\n  }\n  public disable() {\n    trackDynamicDraftMode('draftMode().disable()', this.disable)\n    if (this._provider !== null) {\n      this._provider.disable()\n    }\n  }\n}\n\nfunction syncIODev(route: string | undefined, expression: string) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'request':\n        if (workUnitStore.prerenderPhase === true) {\n          // When we're rendering dynamically in dev, we need to advance out of\n          // the Prerender environment when we read Request data synchronously.\n          trackSynchronousRequestDataAccessInDev(workUnitStore)\n        }\n        break\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-runtime':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n\n  // In all cases we warn normally\n  warnForSyncAccess(route, expression)\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createDraftModeAccessError\n)\n\nfunction createDraftModeAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`draftMode()\\` should be awaited before using its value. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction trackDynamicDraftMode(expression: string, constructorOpt: Function) {\n  const workStore = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (workStore) {\n    // We have a store we want to track dynamic data access to ensure we\n    // don't statically generate routes that manipulate draft mode.\n    if (workUnitStore?.phase === 'after') {\n      throw new Error(\n        `Route ${workStore.route} used \"${expression}\" inside \\`after\\`. The enabled status of draftMode can be read inside \\`after\\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`\n      )\n    }\n\n    if (workStore.dynamicShouldError) {\n      throw new StaticGenBailoutError(\n        `Route ${workStore.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n      )\n    }\n\n    if (workUnitStore) {\n      switch (workUnitStore.type) {\n        case 'cache':\n        case 'private-cache': {\n          const error = new Error(\n            `Route ${workStore.route} used \"${expression}\" inside \"use cache\". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n          )\n          Error.captureStackTrace(error, constructorOpt)\n          workStore.invalidDynamicUsageError ??= error\n          throw error\n        }\n        case 'unstable-cache':\n          throw new Error(\n            `Route ${workStore.route} used \"${expression}\" inside a function cached with \"unstable_cache(...)\". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`\n          )\n\n        case 'prerender':\n        case 'prerender-runtime': {\n          const error = new Error(\n            `Route ${workStore.route} used ${expression} without first calling \\`await connection()\\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`\n          )\n          return abortAndThrowOnSynchronousRequestDataAccess(\n            workStore.route,\n            expression,\n            error,\n            workUnitStore\n          )\n        }\n        case 'prerender-client':\n          const exportName = '`draftMode`'\n          throw new InvariantError(\n            `${exportName} must not be used within a client component. Next.js should be preventing ${exportName} from being included in client components statically, but did not in this case.`\n          )\n        case 'prerender-ppr':\n          return postponeWithTracking(\n            workStore.route,\n            expression,\n            workUnitStore.dynamicTracking\n          )\n        case 'prerender-legacy':\n          workUnitStore.revalidate = 0\n\n          const err = new DynamicServerError(\n            `Route ${workStore.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n          )\n          workStore.dynamicUsageDescription = expression\n          workStore.dynamicUsageStack = err.stack\n\n          throw err\n        case 'request':\n          trackDynamicDataInDynamicRender(workUnitStore)\n          break\n        default:\n          workUnitStore satisfies never\n      }\n    }\n  }\n}\n"], "names": ["draftMode", "callingExpression", "workStore", "workAsyncStorage", "getStore", "workUnitStore", "workUnitAsyncStorage", "throwForMissingRequestStore", "type", "delayUntilRuntimeStage", "createOrGetCachedDraftMode", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getDraftModeProviderForCacheScope", "cache<PERSON>ey", "NullDraftMode", "cachedDraftMode", "CachedDraftModes", "get", "promise", "process", "env", "NODE_ENV", "isPrefetchRequest", "route", "__NEXT_CACHE_COMPONENTS", "createDraftModeWithDevWarnings", "createExoticDraftModeWithDevWarnings", "Promise", "resolve", "DraftMode", "createExoticDraftMode", "set", "WeakMap", "underlyingProvider", "instance", "Object", "defineProperty", "isEnabled", "enumerable", "configurable", "enable", "bind", "disable", "expression", "syncIODev", "value", "apply", "arguments", "proxiedPromise", "Proxy", "target", "prop", "receiver", "warnForSyncAccess", "ReflectAdapter", "constructor", "provider", "_provider", "trackDynamicDraftMode", "prerenderPhase", "trackSynchronousRequestDataAccessInDev", "createDedupedByCallsiteServerErrorLoggerDev", "createDraftModeAccessError", "prefix", "Error", "constructorOpt", "phase", "dynamicShouldError", "StaticGenBailoutError", "error", "captureStackTrace", "invalidDynamicUsageError", "abortAndThrowOnSynchronousRequestDataAccess", "exportName", "InvariantError", "postponeWithTracking", "dynamicTracking", "revalidate", "err", "DynamicServerError", "dynamicUsageDescription", "dynamicUsageStack", "stack", "trackDynamicDataInDynamicRender"], "mappings": ";;;;+BAgDgBA;;;eAAAA;;;8CA7CT;0CAOA;kCAQA;0DACqD;yCACtB;oCACH;gCACJ;yBACA;AAyBxB,SAASA;IACd,MAAMC,oBAAoB;IAC1B,MAAMC,YAAYC,0CAAgB,CAACC,QAAQ;IAC3C,MAAMC,gBAAgBC,kDAAoB,CAACF,QAAQ;IAEnD,IAAI,CAACF,aAAa,CAACG,eAAe;QAChCE,IAAAA,yDAA2B,EAACN;IAC9B;IAEA,OAAQI,cAAcG,IAAI;QACxB,KAAK;YACH,uFAAuF;YACvF,OAAOC,IAAAA,wCAAsB,EAC3BJ,eACAK,2BAA2BL,cAAcL,SAAS,EAAEE;QAExD,KAAK;YACH,OAAOQ,2BAA2BL,cAAcL,SAAS,EAAEE;QAE7D,KAAK;QACL,KAAK;QACL,KAAK;YACH,0EAA0E;YAC1E,2EAA2E;YAC3E,gCAAgC;YAChC,MAAMS,oBAAoBC,IAAAA,+DAAiC,EACzDV,WACAG;YAGF,IAAIM,mBAAmB;gBACrB,OAAOD,2BAA2BC,mBAAmBT;YACvD;QAEF,+DAA+D;QAC/D,0CAA0C;QAC1C,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,0BAA0B;YAC1B,OAAOQ,2BAA2B,MAAMR;QAE1C;YACE,OAAOG;IACX;AACF;AAEA,SAASK,2BACPC,iBAA2C,EAC3CT,SAAgC;IAEhC,MAAMW,WAAWF,qBAAqBG;IACtC,MAAMC,kBAAkBC,iBAAiBC,GAAG,CAACJ;IAE7C,IAAIE,iBAAiB;QACnB,OAAOA;IACT;IAEA,IAAIG;IAEJ,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBAAiB,EAACnB,6BAAAA,UAAWoB,iBAAiB,GAAE;QAC3E,MAAMC,QAAQrB,6BAAAA,UAAWqB,KAAK;QAE9B,IAAIJ,QAAQC,GAAG,CAACI,uBAAuB,EAAE;YACvC,OAAOC,+BAA+Bd,mBAAmBY;QAC3D;QAEAL,UAAUQ,qCAAqCf,mBAAmBY;IACpE,OAAO;QACL,IAAIJ,QAAQC,GAAG,CAACI,uBAAuB,EAAE;YACvC,OAAOG,QAAQC,OAAO,CAAC,IAAIC,UAAUlB;QACvC;QAEAO,UAAUY,sBAAsBnB;IAClC;IAEAK,iBAAiBe,GAAG,CAAClB,UAAUK;IAE/B,OAAOA;AACT;AAGA,MAAMJ,gBAAgB,CAAC;AACvB,MAAME,mBAAmB,IAAIgB;AAE7B,SAASF,sBACPG,kBAA4C;IAE5C,MAAMC,WAAW,IAAIL,UAAUI;IAC/B,MAAMf,UAAUS,QAAQC,OAAO,CAACM;IAEhCC,OAAOC,cAAc,CAAClB,SAAS,aAAa;QAC1CD;YACE,OAAOiB,SAASG,SAAS;QAC3B;QACAC,YAAY;QACZC,cAAc;IAChB;IACErB,QAAgBsB,MAAM,GAAGN,SAASM,MAAM,CAACC,IAAI,CAACP;IAC9ChB,QAAgBwB,OAAO,GAAGR,SAASQ,OAAO,CAACD,IAAI,CAACP;IAElD,OAAOhB;AACT;AAEA,SAASQ,qCACPO,kBAA4C,EAC5CV,KAAyB;IAEzB,MAAMW,WAAW,IAAIL,UAAUI;IAC/B,MAAMf,UAAUS,QAAQC,OAAO,CAACM;IAEhCC,OAAOC,cAAc,CAAClB,SAAS,aAAa;QAC1CD;YACE,MAAM0B,aAAa;YACnBC,UAAUrB,OAAOoB;YACjB,OAAOT,SAASG,SAAS;QAC3B;QACAC,YAAY;QACZC,cAAc;IAChB;IAEAJ,OAAOC,cAAc,CAAClB,SAAS,UAAU;QACvC2B,OAAO,SAAS5B;YACd,MAAM0B,aAAa;YACnBC,UAAUrB,OAAOoB;YACjB,OAAOT,SAASM,MAAM,CAACM,KAAK,CAACZ,UAAUa;QACzC;IACF;IAEAZ,OAAOC,cAAc,CAAClB,SAAS,WAAW;QACxC2B,OAAO,SAAS5B;YACd,MAAM0B,aAAa;YACnBC,UAAUrB,OAAOoB;YACjB,OAAOT,SAASQ,OAAO,CAACI,KAAK,CAACZ,UAAUa;QAC1C;IACF;IAEA,OAAO7B;AACT;AAEA,+EAA+E;AAC/E,4EAA4E;AAC5E,SAASO,+BACPQ,kBAA4C,EAC5CV,KAAyB;IAEzB,MAAMW,WAAW,IAAIL,UAAUI;IAC/B,MAAMf,UAAUS,QAAQC,OAAO,CAACM;IAEhC,MAAMc,iBAAiB,IAAIC,MAAM/B,SAAS;QACxCD,KAAIiC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,OAAQD;gBACN,KAAK;oBACHE,kBAAkB9B,OAAO,CAAC,cAAc,EAAE4B,KAAK,EAAE,CAAC;oBAClD;gBACF,KAAK;gBACL,KAAK;oBAAW;wBACdE,kBAAkB9B,OAAO,CAAC,cAAc,EAAE4B,KAAK,IAAI,CAAC;wBACpD;oBACF;gBACA;oBAAS;oBACP,oEAAoE;oBACtE;YACF;YAEA,OAAOG,uBAAc,CAACrC,GAAG,CAACiC,QAAQC,MAAMC;QAC1C;IACF;IAEA,OAAOJ;AACT;AAEA,MAAMnB;IAMJ0B,YAAYC,QAAkC,CAAE;QAC9C,IAAI,CAACC,SAAS,GAAGD;IACnB;IACA,IAAInB,YAAY;QACd,IAAI,IAAI,CAACoB,SAAS,KAAK,MAAM;YAC3B,OAAO,IAAI,CAACA,SAAS,CAACpB,SAAS;QACjC;QACA,OAAO;IACT;IACOG,SAAS;QACd,oEAAoE;QACpE,+DAA+D;QAC/DkB,sBAAsB,wBAAwB,IAAI,CAAClB,MAAM;QACzD,IAAI,IAAI,CAACiB,SAAS,KAAK,MAAM;YAC3B,IAAI,CAACA,SAAS,CAACjB,MAAM;QACvB;IACF;IACOE,UAAU;QACfgB,sBAAsB,yBAAyB,IAAI,CAAChB,OAAO;QAC3D,IAAI,IAAI,CAACe,SAAS,KAAK,MAAM;YAC3B,IAAI,CAACA,SAAS,CAACf,OAAO;QACxB;IACF;AACF;AAEA,SAASE,UAAUrB,KAAyB,EAAEoB,UAAkB;IAC9D,MAAMtC,gBAAgBC,kDAAoB,CAACF,QAAQ;IAEnD,IAAIC,eAAe;QACjB,OAAQA,cAAcG,IAAI;YACxB,KAAK;gBACH,IAAIH,cAAcsD,cAAc,KAAK,MAAM;oBACzC,qEAAqE;oBACrE,qEAAqE;oBACrEC,IAAAA,wDAAsC,EAACvD;gBACzC;gBACA;YACF,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH;YACF;gBACEA;QACJ;IACF;IAEA,gCAAgC;IAChCgD,kBAAkB9B,OAAOoB;AAC3B;AAEA,MAAMU,oBAAoBQ,IAAAA,qFAA2C,EACnEC;AAGF,SAASA,2BACPvC,KAAyB,EACzBoB,UAAkB;IAElB,MAAMoB,SAASxC,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,qBAIN,CAJM,IAAIyC,MACT,GAAGD,OAAO,KAAK,EAAEpB,WAAW,EAAE,CAAC,GAC7B,CAAC,0DAA0D,CAAC,GAC5D,CAAC,8DAA8D,CAAC,GAH7D,qBAAA;eAAA;oBAAA;sBAAA;IAIP;AACF;AAEA,SAASe,sBAAsBf,UAAkB,EAAEsB,cAAwB;IACzE,MAAM/D,YAAYC,0CAAgB,CAACC,QAAQ;IAC3C,MAAMC,gBAAgBC,kDAAoB,CAACF,QAAQ;IAEnD,IAAIF,WAAW;QACb,oEAAoE;QACpE,+DAA+D;QAC/D,IAAIG,CAAAA,iCAAAA,cAAe6D,KAAK,MAAK,SAAS;YACpC,MAAM,qBAEL,CAFK,IAAIF,MACR,CAAC,MAAM,EAAE9D,UAAUqB,KAAK,CAAC,OAAO,EAAEoB,WAAW,0MAA0M,CAAC,GADpP,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIzC,UAAUiE,kBAAkB,EAAE;YAChC,MAAM,qBAEL,CAFK,IAAIC,8CAAqB,CAC7B,CAAC,MAAM,EAAElE,UAAUqB,KAAK,CAAC,8EAA8E,EAAEoB,WAAW,4HAA4H,CAAC,GAD7O,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAItC,eAAe;YACjB,OAAQA,cAAcG,IAAI;gBACxB,KAAK;gBACL,KAAK;oBAAiB;wBACpB,MAAM6D,QAAQ,qBAEb,CAFa,IAAIL,MAChB,CAAC,MAAM,EAAE9D,UAAUqB,KAAK,CAAC,OAAO,EAAEoB,WAAW,uNAAuN,CAAC,GADzP,qBAAA;mCAAA;wCAAA;0CAAA;wBAEd;wBACAqB,MAAMM,iBAAiB,CAACD,OAAOJ;wBAC/B/D,UAAUqE,wBAAwB,KAAKF;wBACvC,MAAMA;oBACR;gBACA,KAAK;oBACH,MAAM,qBAEL,CAFK,IAAIL,MACR,CAAC,MAAM,EAAE9D,UAAUqB,KAAK,CAAC,OAAO,EAAEoB,WAAW,gQAAgQ,CAAC,GAD1S,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBAEF,KAAK;gBACL,KAAK;oBAAqB;wBACxB,MAAM0B,QAAQ,qBAEb,CAFa,IAAIL,MAChB,CAAC,MAAM,EAAE9D,UAAUqB,KAAK,CAAC,MAAM,EAAEoB,WAAW,+HAA+H,CAAC,GADhK,qBAAA;mCAAA;wCAAA;0CAAA;wBAEd;wBACA,OAAO6B,IAAAA,6DAA2C,EAChDtE,UAAUqB,KAAK,EACfoB,YACA0B,OACAhE;oBAEJ;gBACA,KAAK;oBACH,MAAMoE,aAAa;oBACnB,MAAM,qBAEL,CAFK,IAAIC,8BAAc,CACtB,GAAGD,WAAW,0EAA0E,EAAEA,WAAW,+EAA+E,CAAC,GADjL,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF,KAAK;oBACH,OAAOE,IAAAA,sCAAoB,EACzBzE,UAAUqB,KAAK,EACfoB,YACAtC,cAAcuE,eAAe;gBAEjC,KAAK;oBACHvE,cAAcwE,UAAU,GAAG;oBAE3B,MAAMC,MAAM,qBAEX,CAFW,IAAIC,sCAAkB,CAChC,CAAC,MAAM,EAAE7E,UAAUqB,KAAK,CAAC,mDAAmD,EAAEoB,WAAW,6EAA6E,CAAC,GAD7J,qBAAA;+BAAA;oCAAA;sCAAA;oBAEZ;oBACAzC,UAAU8E,uBAAuB,GAAGrC;oBACpCzC,UAAU+E,iBAAiB,GAAGH,IAAII,KAAK;oBAEvC,MAAMJ;gBACR,KAAK;oBACHK,IAAAA,iDAA+B,EAAC9E;oBAChC;gBACF;oBACEA;YACJ;QACF;IACF;AACF", "ignoreList": [0]}