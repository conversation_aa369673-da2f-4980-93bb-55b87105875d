{"version": 3, "sources": ["../../src/server/config.ts"], "sourcesContent": ["import { existsSync } from 'fs'\nimport { basename, extname, join, relative, isAbsolute, resolve } from 'path'\nimport { pathToFileURL } from 'url'\nimport findUp from 'next/dist/compiled/find-up'\nimport * as Log from '../build/output/log'\nimport * as ciEnvironment from '../server/ci-info'\nimport {\n  CONFIG_FILES,\n  PHASE_DEVELOPMENT_SERVER,\n  PHASE_EXPORT,\n  PHASE_PRODUCTION_BUILD,\n  PHASE_PRODUCTION_SERVER,\n} from '../shared/lib/constants'\nimport { defaultConfig, normalizeConfig } from './config-shared'\nimport type {\n  ExperimentalConfig,\n  NextConfigComplete,\n  NextConfig,\n  TurbopackLoaderItem,\n  NextAdapter,\n} from './config-shared'\n\nimport { loadWebpackHook } from './config-utils'\nimport { imageConfigDefault } from '../shared/lib/image-config'\nimport type { ImageConfig } from '../shared/lib/image-config'\nimport { loadEnvConfig, updateInitialEnv } from '@next/env'\nimport { flushAndExit } from '../telemetry/flush-and-exit'\nimport { findRootDir } from '../lib/find-root'\nimport { setHttpClientAndAgentOptions } from './setup-http-agent-env'\nimport { pathHasPrefix } from '../shared/lib/router/utils/path-has-prefix'\nimport { matchRemotePattern } from '../shared/lib/match-remote-pattern'\n\nimport type { ZodError } from 'next/dist/compiled/zod'\nimport { hasNextSupport } from '../server/ci-info'\nimport { transpileConfig } from '../build/next-config-ts/transpile-config'\nimport { dset } from '../shared/lib/dset'\nimport { normalizeZodErrors } from '../shared/lib/zod'\nimport { HTML_LIMITED_BOT_UA_RE_STRING } from '../shared/lib/router/utils/is-bot'\nimport { findDir } from '../lib/find-pages-dir'\nimport { CanaryOnlyError, isStableBuild } from '../shared/lib/canary-only'\nimport { interopDefault } from '../lib/interop-default'\nimport { djb2Hash } from '../shared/lib/hash'\n\nexport { normalizeConfig } from './config-shared'\nexport type { DomainLocale, NextConfig } from './config-shared'\n\nfunction normalizeNextConfigZodErrors(\n  error: ZodError<NextConfig>\n): [errorMessages: string[], shouldExit: boolean] {\n  let shouldExit = false\n  const issues = normalizeZodErrors(error)\n  return [\n    issues.flatMap(({ issue, message }) => {\n      if (issue.path[0] === 'images') {\n        // We exit the build when encountering an error in the images config\n        shouldExit = true\n      }\n\n      return message\n    }),\n    shouldExit,\n  ]\n}\n\nexport function warnOptionHasBeenDeprecated(\n  config: NextConfig,\n  nestedPropertyKey: string,\n  reason: string,\n  silent: boolean\n): boolean {\n  let hasWarned = false\n  if (!silent) {\n    let current = config\n    let found = true\n    const nestedPropertyKeys = nestedPropertyKey.split('.')\n    for (const key of nestedPropertyKeys) {\n      if (current[key] !== undefined) {\n        current = current[key]\n      } else {\n        found = false\n        break\n      }\n    }\n    if (found) {\n      Log.warnOnce(reason)\n      hasWarned = true\n    }\n  }\n  return hasWarned\n}\n\nfunction checkDeprecations(\n  userConfig: NextConfig,\n  configFileName: string,\n  silent: boolean,\n  dir: string\n) {\n  warnOptionHasBeenDeprecated(\n    userConfig,\n    'amp',\n    `Built-in amp support is deprecated and the \\`amp\\` configuration option will be removed in Next.js 16.`,\n    silent\n  )\n\n  warnOptionHasBeenDeprecated(\n    userConfig,\n    'experimental.amp',\n    `Built-in amp support is deprecated and the \\`experimental.amp\\` configuration option will be removed in Next.js 16.`,\n    silent\n  )\n\n  if (userConfig.experimental?.dynamicIO !== undefined) {\n    warnOptionHasBeenDeprecated(\n      userConfig,\n      'experimental.dynamicIO',\n      `\\`experimental.dynamicIO\\` has been renamed to \\`experimental.cacheComponents\\`. Please update your ${configFileName} file accordingly.`,\n      silent\n    )\n  }\n\n  warnOptionHasBeenDeprecated(\n    userConfig,\n    'experimental.instrumentationHook',\n    `\\`experimental.instrumentationHook\\` is no longer needed, because \\`instrumentation.js\\` is available by default. You can remove it from ${configFileName}.`,\n    silent\n  )\n\n  warnOptionHasBeenDeprecated(\n    userConfig,\n    'experimental.after',\n    `\\`experimental.after\\` is no longer needed, because \\`after\\` is available by default. You can remove it from ${configFileName}.`,\n    silent\n  )\n\n  warnOptionHasBeenDeprecated(\n    userConfig,\n    'devIndicators.appIsrStatus',\n    `\\`devIndicators.appIsrStatus\\` is deprecated and no longer configurable. Please remove it from ${configFileName}.`,\n    silent\n  )\n\n  warnOptionHasBeenDeprecated(\n    userConfig,\n    'devIndicators.buildActivity',\n    `\\`devIndicators.buildActivity\\` is deprecated and no longer configurable. Please remove it from ${configFileName}.`,\n    silent\n  )\n\n  warnOptionHasBeenDeprecated(\n    userConfig,\n    'devIndicators.buildActivityPosition',\n    `\\`devIndicators.buildActivityPosition\\` has been renamed to \\`devIndicators.position\\`. Please update your ${configFileName} file accordingly.`,\n    silent\n  )\n\n  // i18n deprecation for App Router\n  if (userConfig.i18n) {\n    const hasAppDir = Boolean(findDir(dir, 'app'))\n    if (hasAppDir) {\n      warnOptionHasBeenDeprecated(\n        userConfig,\n        'i18n',\n        `i18n configuration in ${configFileName} is unsupported in App Router.\\nLearn more about internationalization in App Router: https://nextjs.org/docs/app/building-your-application/routing/internationalization`,\n        silent\n      )\n    }\n  }\n}\n\nexport function warnOptionHasBeenMovedOutOfExperimental(\n  config: NextConfig,\n  oldExperimentalKey: string,\n  newKey: string,\n  configFileName: string,\n  silent: boolean\n) {\n  if (config.experimental && oldExperimentalKey in config.experimental) {\n    if (!silent) {\n      Log.warn(\n        `\\`experimental.${oldExperimentalKey}\\` has been moved to \\`${newKey}\\`. ` +\n          `Please update your ${configFileName} file accordingly.`\n      )\n    }\n\n    let current = config\n    const newKeys = newKey.split('.')\n    while (newKeys.length > 1) {\n      const key = newKeys.shift()!\n      current[key] = current[key] || {}\n      current = current[key]\n    }\n    current[newKeys.shift()!] = (config.experimental as any)[oldExperimentalKey]\n  }\n\n  return config\n}\n\nfunction warnCustomizedOption(\n  config: NextConfig,\n  key: string,\n  defaultValue: any,\n  customMessage: string,\n  configFileName: string,\n  silent: boolean\n) {\n  const segs = key.split('.')\n  let current = config\n\n  while (segs.length >= 1) {\n    const seg = segs.shift()!\n    if (!(seg in current)) {\n      return\n    }\n    current = current[seg]\n  }\n\n  if (!silent && current !== defaultValue) {\n    Log.warn(\n      `The \"${key}\" option has been modified. ${customMessage ? customMessage + '. ' : ''}It should be removed from your ${configFileName}.`\n    )\n  }\n}\n\nfunction assignDefaults(\n  dir: string,\n  userConfig: NextConfig & { configFileName: string },\n  silent: boolean\n): NextConfigComplete {\n  const configFileName = userConfig.configFileName\n  if (typeof userConfig.exportTrailingSlash !== 'undefined') {\n    if (!silent) {\n      Log.warn(\n        `The \"exportTrailingSlash\" option has been renamed to \"trailingSlash\". Please update your ${configFileName}.`\n      )\n    }\n    if (typeof userConfig.trailingSlash === 'undefined') {\n      userConfig.trailingSlash = userConfig.exportTrailingSlash\n    }\n    delete userConfig.exportTrailingSlash\n  }\n\n  // Handle migration of experimental.dynamicIO to experimental.cacheComponents\n  if (userConfig.experimental?.dynamicIO !== undefined) {\n    // If cacheComponents was not explicitly set by the user (i.e., it's still the default value),\n    // use the dynamicIO value. We check against the user config, not the merged result.\n    if (userConfig.experimental?.cacheComponents === undefined) {\n      userConfig.experimental.cacheComponents =\n        userConfig.experimental.dynamicIO\n    }\n\n    // Remove the deprecated property\n    delete userConfig.experimental.dynamicIO\n  }\n\n  const config = Object.keys(userConfig).reduce<{ [key: string]: any }>(\n    (currentConfig, key) => {\n      const value = userConfig[key]\n\n      if (value === undefined || value === null) {\n        return currentConfig\n      }\n\n      if (key === 'distDir') {\n        if (typeof value !== 'string') {\n          throw new Error(\n            `Specified distDir is not a string, found type \"${typeof value}\"`\n          )\n        }\n        const userDistDir = value.trim()\n\n        // don't allow public as the distDir as this is a reserved folder for\n        // public files\n        if (userDistDir === 'public') {\n          throw new Error(\n            `The 'public' directory is reserved in Next.js and can not be set as the 'distDir'. https://nextjs.org/docs/messages/can-not-output-to-public`\n          )\n        }\n        // make sure distDir isn't an empty string as it can result in the provided\n        // directory being deleted in development mode\n        if (userDistDir.length === 0) {\n          throw new Error(\n            `Invalid distDir provided, distDir can not be an empty string. Please remove this config or set it to undefined`\n          )\n        }\n      }\n\n      if (key === 'pageExtensions') {\n        if (!Array.isArray(value)) {\n          throw new Error(\n            `Specified pageExtensions is not an array of strings, found \"${value}\". Please update this config or remove it.`\n          )\n        }\n\n        if (!value.length) {\n          throw new Error(\n            `Specified pageExtensions is an empty array. Please update it with the relevant extensions or remove it.`\n          )\n        }\n\n        value.forEach((ext) => {\n          if (typeof ext !== 'string') {\n            throw new Error(\n              `Specified pageExtensions is not an array of strings, found \"${ext}\" of type \"${typeof ext}\". Please update this config or remove it.`\n            )\n          }\n        })\n      }\n\n      const defaultValue = (defaultConfig as Record<string, unknown>)[key]\n\n      if (\n        !!value &&\n        value.constructor === Object &&\n        typeof defaultValue === 'object'\n      ) {\n        currentConfig[key] = {\n          ...defaultValue,\n          ...Object.keys(value).reduce<any>((c, k) => {\n            const v = value[k]\n            if (v !== undefined && v !== null) {\n              c[k] = v\n            }\n            return c\n          }, {}),\n        }\n      } else {\n        currentConfig[key] = value\n      }\n\n      return currentConfig\n    },\n    {}\n  ) as NextConfig & { configFileName: string }\n\n  const result = {\n    ...defaultConfig,\n    ...config,\n    experimental: {\n      ...defaultConfig.experimental,\n      ...config.experimental,\n    },\n  }\n\n  // ensure correct default is set for api-resolver revalidate handling\n  if (!result.experimental?.trustHostHeader && ciEnvironment.hasNextSupport) {\n    result.experimental.trustHostHeader = true\n  }\n\n  if (\n    result.experimental?.allowDevelopmentBuild &&\n    process.env.NODE_ENV !== 'development'\n  ) {\n    throw new Error(\n      `The experimental.allowDevelopmentBuild option requires NODE_ENV to be explicitly set to 'development'.`\n    )\n  }\n\n  if (isStableBuild()) {\n    // Prevents usage of certain experimental features outside of canary\n    if (result.experimental?.ppr) {\n      throw new CanaryOnlyError({ feature: 'experimental.ppr' })\n    } else if (result.experimental?.cacheComponents) {\n      throw new CanaryOnlyError({ feature: 'experimental.cacheComponents' })\n    } else if (result.experimental?.turbopackPersistentCaching) {\n      throw new CanaryOnlyError({\n        feature: 'experimental.turbopackPersistentCaching',\n      })\n    }\n  }\n\n  if (result.output === 'export') {\n    if (result.i18n) {\n      throw new Error(\n        'Specified \"i18n\" cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/messages/export-no-i18n'\n      )\n    }\n\n    if (!hasNextSupport) {\n      if (result.rewrites) {\n        Log.warn(\n          'Specified \"rewrites\" will not automatically work with \"output: export\". See more info here: https://nextjs.org/docs/messages/export-no-custom-routes'\n        )\n      }\n      if (result.redirects) {\n        Log.warn(\n          'Specified \"redirects\" will not automatically work with \"output: export\". See more info here: https://nextjs.org/docs/messages/export-no-custom-routes'\n        )\n      }\n      if (result.headers) {\n        Log.warn(\n          'Specified \"headers\" will not automatically work with \"output: export\". See more info here: https://nextjs.org/docs/messages/export-no-custom-routes'\n        )\n      }\n    }\n  }\n\n  if (typeof result.assetPrefix !== 'string') {\n    throw new Error(\n      `Specified assetPrefix is not a string, found type \"${typeof result.assetPrefix}\" https://nextjs.org/docs/messages/invalid-assetprefix`\n    )\n  }\n\n  if (typeof result.basePath !== 'string') {\n    throw new Error(\n      `Specified basePath is not a string, found type \"${typeof result.basePath}\"`\n    )\n  }\n\n  if (result.basePath !== '') {\n    if (result.basePath === '/') {\n      throw new Error(\n        `Specified basePath /. basePath has to be either an empty string or a path prefix\"`\n      )\n    }\n\n    if (!result.basePath.startsWith('/')) {\n      throw new Error(\n        `Specified basePath has to start with a /, found \"${result.basePath}\"`\n      )\n    }\n\n    if (result.basePath !== '/') {\n      if (result.basePath.endsWith('/')) {\n        throw new Error(\n          `Specified basePath should not end with /, found \"${result.basePath}\"`\n        )\n      }\n\n      if (result.assetPrefix === '') {\n        result.assetPrefix = result.basePath\n      }\n\n      if (result.amp?.canonicalBase === '') {\n        result.amp.canonicalBase = result.basePath\n      }\n    }\n  }\n\n  if (result?.images) {\n    const images: ImageConfig = result.images\n\n    if (typeof images !== 'object') {\n      throw new Error(\n        `Specified images should be an object received ${typeof images}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-images-config`\n      )\n    }\n\n    if (images.localPatterns) {\n      if (!Array.isArray(images.localPatterns)) {\n        throw new Error(\n          `Specified images.localPatterns should be an Array received ${typeof images.localPatterns}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-images-config`\n        )\n      }\n      // avoid double-pushing the same pattern if it already exists\n      const hasMatch = images.localPatterns.some(\n        (pattern) =>\n          pattern.pathname === '/_next/static/media/**' && pattern.search === ''\n      )\n      if (!hasMatch) {\n        // static import images are automatically allowed\n        images.localPatterns.push({\n          pathname: '/_next/static/media/**',\n          search: '',\n        })\n      }\n    }\n\n    if (images.remotePatterns) {\n      if (!Array.isArray(images.remotePatterns)) {\n        throw new Error(\n          `Specified images.remotePatterns should be an Array received ${typeof images.remotePatterns}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-images-config`\n        )\n      }\n\n      // We must convert URL to RemotePattern since URL has a colon in the protocol\n      // and also has additional properties we want to filter out. Also, new URL()\n      // accepts any protocol so we need manual validation here.\n      images.remotePatterns = images.remotePatterns.map(\n        ({ protocol, hostname, port, pathname, search }) => {\n          const proto = protocol?.replace(/:$/, '')\n          if (!['http', 'https', undefined].includes(proto)) {\n            throw new Error(\n              `Specified images.remotePatterns must have protocol \"http\" or \"https\" received \"${proto}\".`\n            )\n          }\n          return {\n            protocol: proto as 'http' | 'https' | undefined,\n            hostname,\n            port,\n            pathname,\n            search,\n          }\n        }\n      )\n\n      // static images are automatically prefixed with assetPrefix\n      // so we need to ensure _next/image allows downloading from\n      // this resource\n      if (config.assetPrefix?.startsWith('http')) {\n        try {\n          const url = new URL(config.assetPrefix)\n          const hasMatchForAssetPrefix = images.remotePatterns.some((pattern) =>\n            matchRemotePattern(pattern, url)\n          )\n\n          // avoid double-pushing the same pattern if it already can be matched\n          if (!hasMatchForAssetPrefix) {\n            images.remotePatterns.push({\n              hostname: url.hostname,\n              protocol: url.protocol.replace(/:$/, '') as 'http' | 'https',\n              port: url.port,\n            })\n          }\n        } catch (error) {\n          throw new Error(\n            `Invalid assetPrefix provided. Original error: ${error}`\n          )\n        }\n      }\n    }\n\n    if (images.domains) {\n      if (!Array.isArray(images.domains)) {\n        throw new Error(\n          `Specified images.domains should be an Array received ${typeof images.domains}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-images-config`\n        )\n      }\n    }\n\n    if (!images.loader) {\n      images.loader = 'default'\n    }\n\n    if (\n      images.loader !== 'default' &&\n      images.loader !== 'custom' &&\n      images.path === imageConfigDefault.path\n    ) {\n      throw new Error(\n        `Specified images.loader property (${images.loader}) also requires images.path property to be assigned to a URL prefix.\\nSee more info here: https://nextjs.org/docs/api-reference/next/legacy/image#loader-configuration`\n      )\n    }\n\n    if (\n      images.path === imageConfigDefault.path &&\n      result.basePath &&\n      !pathHasPrefix(images.path, result.basePath)\n    ) {\n      images.path = `${result.basePath}${images.path}`\n    }\n\n    // Append trailing slash for non-default loaders and when trailingSlash is set\n    if (\n      images.path &&\n      !images.path.endsWith('/') &&\n      (images.loader !== 'default' || result.trailingSlash)\n    ) {\n      images.path += '/'\n    }\n\n    if (images.loaderFile) {\n      if (images.loader !== 'default' && images.loader !== 'custom') {\n        throw new Error(\n          `Specified images.loader property (${images.loader}) cannot be used with images.loaderFile property. Please set images.loader to \"custom\".`\n        )\n      }\n      const absolutePath = join(dir, images.loaderFile)\n      if (!existsSync(absolutePath)) {\n        throw new Error(\n          `Specified images.loaderFile does not exist at \"${absolutePath}\".`\n        )\n      }\n      images.loaderFile = absolutePath\n    }\n  }\n\n  warnCustomizedOption(\n    result,\n    'experimental.esmExternals',\n    true,\n    'experimental.esmExternals is not recommended to be modified as it may disrupt module resolution',\n    configFileName,\n    silent\n  )\n\n  // Handle buildActivityPosition migration (needs to be done after merging with defaults)\n  if (\n    result.devIndicators &&\n    typeof result.devIndicators === 'object' &&\n    'buildActivityPosition' in result.devIndicators &&\n    result.devIndicators.buildActivityPosition !== result.devIndicators.position\n  ) {\n    if (!silent) {\n      Log.warnOnce(\n        `The \\`devIndicators\\` option \\`buildActivityPosition\\` (\"${result.devIndicators.buildActivityPosition}\") conflicts with \\`position\\` (\"${result.devIndicators.position}\"). Using \\`buildActivityPosition\\` (\"${result.devIndicators.buildActivityPosition}\") for backward compatibility.`\n      )\n    }\n    result.devIndicators.position = result.devIndicators.buildActivityPosition\n  }\n\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'bundlePagesExternals',\n    'bundlePagesRouterDependencies',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'serverComponentsExternalPackages',\n    'serverExternalPackages',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'relay',\n    'compiler.relay',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'styledComponents',\n    'compiler.styledComponents',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'emotion',\n    'compiler.emotion',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'reactRemoveProperties',\n    'compiler.reactRemoveProperties',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'removeConsole',\n    'compiler.removeConsole',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'swrDelta',\n    'expireTime',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'typedRoutes',\n    'typedRoutes',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'outputFileTracingRoot',\n    'outputFileTracingRoot',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'outputFileTracingIncludes',\n    'outputFileTracingIncludes',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'outputFileTracingExcludes',\n    'outputFileTracingExcludes',\n    configFileName,\n    silent\n  )\n\n  if ((result.experimental as any).outputStandalone) {\n    if (!silent) {\n      Log.warn(\n        `experimental.outputStandalone has been renamed to \"output: 'standalone'\", please move the config.`\n      )\n    }\n    result.output = 'standalone'\n  }\n\n  if (\n    typeof result.experimental?.serverActions?.bodySizeLimit !== 'undefined'\n  ) {\n    const value = parseInt(\n      result.experimental.serverActions?.bodySizeLimit.toString()\n    )\n    if (isNaN(value) || value < 1) {\n      throw new Error(\n        'Server Actions Size Limit must be a valid number or filesize format larger than 1MB: https://nextjs.org/docs/app/api-reference/next-config-js/serverActions#bodysizelimit'\n      )\n    }\n  }\n\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'transpilePackages',\n    'transpilePackages',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'skipMiddlewareUrlNormalize',\n    'skipMiddlewareUrlNormalize',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'skipTrailingSlashRedirect',\n    'skipTrailingSlashRedirect',\n    configFileName,\n    silent\n  )\n\n  if (\n    result?.outputFileTracingRoot &&\n    !isAbsolute(result.outputFileTracingRoot)\n  ) {\n    result.outputFileTracingRoot = resolve(result.outputFileTracingRoot)\n    if (!silent) {\n      Log.warn(\n        `outputFileTracingRoot should be absolute, using: ${result.outputFileTracingRoot}`\n      )\n    }\n  }\n\n  if (result?.turbopack?.root && !isAbsolute(result.turbopack.root)) {\n    result.turbopack.root = resolve(result.turbopack.root)\n    if (!silent) {\n      Log.warn(\n        `turbopack.root should be absolute, using: ${result.turbopack.root}`\n      )\n    }\n  }\n\n  // only leverage deploymentId\n  if (process.env.NEXT_DEPLOYMENT_ID) {\n    result.deploymentId = process.env.NEXT_DEPLOYMENT_ID\n  }\n\n  const tracingRoot = result?.outputFileTracingRoot\n  const turbopackRoot = result?.turbopack?.root\n\n  // If both provided, validate they match. If not, use outputFileTracingRoot.\n  if (tracingRoot && turbopackRoot && tracingRoot !== turbopackRoot) {\n    Log.warn(\n      `Both \\`outputFileTracingRoot\\` and \\`turbopack.root\\` are set, but they must have the same value.\\n` +\n        `Using \\`outputFileTracingRoot\\` value: ${tracingRoot}.`\n    )\n  }\n\n  const rootDir = tracingRoot || turbopackRoot || findRootDir(dir)\n\n  if (!rootDir) {\n    throw new Error(\n      'Failed to find the root directory of the project. This is a bug in Next.js.'\n    )\n  }\n\n  // Ensure both properties are set to the same value\n  result.outputFileTracingRoot = rootDir\n  dset(result, ['turbopack', 'root'], rootDir)\n\n  setHttpClientAndAgentOptions(result || defaultConfig)\n\n  if (result.i18n) {\n    const { i18n } = result\n    const i18nType = typeof i18n\n\n    if (i18nType !== 'object') {\n      throw new Error(\n        `Specified i18n should be an object received ${i18nType}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n\n    if (!Array.isArray(i18n.locales)) {\n      throw new Error(\n        `Specified i18n.locales should be an Array received ${typeof i18n.locales}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n\n    if (i18n.locales.length > 100 && !silent) {\n      Log.warn(\n        `Received ${i18n.locales.length} i18n.locales items which exceeds the recommended max of 100.\\nSee more info here: https://nextjs.org/docs/advanced-features/i18n-routing#how-does-this-work-with-static-generation`\n      )\n    }\n\n    const defaultLocaleType = typeof i18n.defaultLocale\n\n    if (!i18n.defaultLocale || defaultLocaleType !== 'string') {\n      throw new Error(\n        `Specified i18n.defaultLocale should be a string.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n\n    if (typeof i18n.domains !== 'undefined' && !Array.isArray(i18n.domains)) {\n      throw new Error(\n        `Specified i18n.domains must be an array of domain objects e.g. [ { domain: 'example.fr', defaultLocale: 'fr', locales: ['fr'] } ] received ${typeof i18n.domains}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n\n    if (i18n.domains) {\n      const invalidDomainItems = i18n.domains.filter((item) => {\n        if (!item || typeof item !== 'object') return true\n        if (!item.defaultLocale) return true\n        if (!item.domain || typeof item.domain !== 'string') return true\n\n        if (item.domain.includes(':')) {\n          console.warn(\n            `i18n domain: \"${item.domain}\" is invalid it should be a valid domain without protocol (https://) or port (:3000) e.g. example.vercel.sh`\n          )\n          return true\n        }\n\n        const defaultLocaleDuplicate = i18n.domains?.find(\n          (altItem) =>\n            altItem.defaultLocale === item.defaultLocale &&\n            altItem.domain !== item.domain\n        )\n\n        if (!silent && defaultLocaleDuplicate) {\n          console.warn(\n            `Both ${item.domain} and ${defaultLocaleDuplicate.domain} configured the defaultLocale ${item.defaultLocale} but only one can. Change one item's default locale to continue`\n          )\n          return true\n        }\n\n        let hasInvalidLocale = false\n\n        if (Array.isArray(item.locales)) {\n          for (const locale of item.locales) {\n            if (typeof locale !== 'string') hasInvalidLocale = true\n\n            for (const domainItem of i18n.domains || []) {\n              if (domainItem === item) continue\n              if (domainItem.locales && domainItem.locales.includes(locale)) {\n                console.warn(\n                  `Both ${item.domain} and ${domainItem.domain} configured the locale (${locale}) but only one can. Remove it from one i18n.domains config to continue`\n                )\n                hasInvalidLocale = true\n                break\n              }\n            }\n          }\n        }\n\n        return hasInvalidLocale\n      })\n\n      if (invalidDomainItems.length > 0) {\n        throw new Error(\n          `Invalid i18n.domains values:\\n${invalidDomainItems\n            .map((item: any) => JSON.stringify(item))\n            .join(\n              '\\n'\n            )}\\n\\ndomains value must follow format { domain: 'example.fr', defaultLocale: 'fr', locales: ['fr'] }.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n        )\n      }\n    }\n\n    if (!Array.isArray(i18n.locales)) {\n      throw new Error(\n        `Specified i18n.locales must be an array of locale strings e.g. [\"en-US\", \"nl-NL\"] received ${typeof i18n.locales}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n\n    const invalidLocales = i18n.locales.filter(\n      (locale: any) => typeof locale !== 'string'\n    )\n\n    if (invalidLocales.length > 0) {\n      throw new Error(\n        `Specified i18n.locales contains invalid values (${invalidLocales\n          .map(String)\n          .join(\n            ', '\n          )}), locales must be valid locale tags provided as strings e.g. \"en-US\".\\n` +\n          `See here for list of valid language sub-tags: http://www.iana.org/assignments/language-subtag-registry/language-subtag-registry`\n      )\n    }\n\n    if (!i18n.locales.includes(i18n.defaultLocale)) {\n      throw new Error(\n        `Specified i18n.defaultLocale should be included in i18n.locales.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n\n    const normalizedLocales = new Set()\n    const duplicateLocales = new Set()\n\n    i18n.locales.forEach((locale) => {\n      const localeLower = locale.toLowerCase()\n      if (normalizedLocales.has(localeLower)) {\n        duplicateLocales.add(locale)\n      }\n      normalizedLocales.add(localeLower)\n    })\n\n    if (duplicateLocales.size > 0) {\n      throw new Error(\n        `Specified i18n.locales contains the following duplicate locales:\\n` +\n          `${[...duplicateLocales].join(', ')}\\n` +\n          `Each locale should be listed only once.\\n` +\n          `See more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n\n    // make sure default Locale is at the front\n    i18n.locales = [\n      i18n.defaultLocale,\n      ...i18n.locales.filter((locale) => locale !== i18n.defaultLocale),\n    ]\n\n    const localeDetectionType = typeof i18n.localeDetection\n\n    if (\n      localeDetectionType !== 'boolean' &&\n      localeDetectionType !== 'undefined'\n    ) {\n      throw new Error(\n        `Specified i18n.localeDetection should be undefined or a boolean received ${localeDetectionType}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n  }\n\n  if (result.devIndicators !== false && result.devIndicators?.position) {\n    const { position } = result.devIndicators\n    const allowedValues = [\n      'top-left',\n      'top-right',\n      'bottom-left',\n      'bottom-right',\n    ]\n\n    if (!allowedValues.includes(position)) {\n      throw new Error(\n        `Invalid \"devIndicator.position\" provided, expected one of ${allowedValues.join(\n          ', '\n        )}, received ${position}`\n      )\n    }\n  }\n\n  if (result.experimental) {\n    result.experimental.cacheLife = {\n      ...defaultConfig.experimental?.cacheLife,\n      ...result.experimental.cacheLife,\n    }\n    const defaultDefault = defaultConfig.experimental?.cacheLife?.['default']\n    if (\n      !defaultDefault ||\n      defaultDefault.revalidate === undefined ||\n      defaultDefault.expire === undefined ||\n      !defaultConfig.experimental?.staleTimes?.static\n    ) {\n      throw new Error('No default cacheLife profile.')\n    }\n    const defaultCacheLifeProfile = result.experimental.cacheLife['default']\n    if (!defaultCacheLifeProfile) {\n      result.experimental.cacheLife['default'] = defaultDefault\n    } else {\n      if (defaultCacheLifeProfile.stale === undefined) {\n        const staticStaleTime = result.experimental.staleTimes?.static\n        defaultCacheLifeProfile.stale =\n          staticStaleTime ?? defaultConfig.experimental?.staleTimes?.static\n      }\n      if (defaultCacheLifeProfile.revalidate === undefined) {\n        defaultCacheLifeProfile.revalidate = defaultDefault.revalidate\n      }\n      if (defaultCacheLifeProfile.expire === undefined) {\n        defaultCacheLifeProfile.expire =\n          result.expireTime ?? defaultDefault.expire\n      }\n    }\n  }\n\n  if (result.experimental?.cacheHandlers) {\n    const allowedHandlerNameRegex = /[a-z-]/\n\n    if (typeof result.experimental.cacheHandlers !== 'object') {\n      throw new Error(\n        `Invalid \"experimental.cacheHandlers\" provided, expected an object e.g. { default: '/my-handler.js' }, received ${JSON.stringify(result.experimental.cacheHandlers)}`\n      )\n    }\n\n    const handlerKeys = Object.keys(result.experimental.cacheHandlers)\n    const invalidHandlerItems: Array<{ key: string; reason: string }> = []\n\n    for (const key of handlerKeys) {\n      if (key === 'private') {\n        invalidHandlerItems.push({\n          key,\n          reason:\n            'The cache handler for \"use cache: private\" cannot be customized.',\n        })\n      } else if (!allowedHandlerNameRegex.test(key)) {\n        invalidHandlerItems.push({\n          key,\n          reason: 'key must only use characters a-z and -',\n        })\n      } else {\n        const handlerPath = (\n          result.experimental.cacheHandlers as {\n            [handlerName: string]: string | undefined\n          }\n        )[key]\n\n        if (handlerPath && !existsSync(handlerPath)) {\n          invalidHandlerItems.push({\n            key,\n            reason: `cache handler path provided does not exist, received ${handlerPath}`,\n          })\n        }\n      }\n      if (invalidHandlerItems.length) {\n        throw new Error(\n          `Invalid handler fields configured for \"experimental.cacheHandler\":\\n${invalidHandlerItems.map((item) => `${key}: ${item.reason}`).join('\\n')}`\n        )\n      }\n    }\n  }\n\n  const userProvidedModularizeImports = result.modularizeImports\n  // Unfortunately these packages end up re-exporting 10600 modules, for example: https://unpkg.com/browse/@mui/icons-material@5.11.16/esm/index.js.\n  // Leveraging modularizeImports tremendously reduces compile times for these.\n  result.modularizeImports = {\n    ...(userProvidedModularizeImports || {}),\n    // This is intentionally added after the user-provided modularizeImports config.\n    '@mui/icons-material': {\n      transform: '@mui/icons-material/{{member}}',\n    },\n    lodash: {\n      transform: 'lodash/{{member}}',\n    },\n  }\n\n  const userProvidedOptimizePackageImports =\n    result.experimental?.optimizePackageImports || []\n\n  result.experimental.optimizePackageImports = [\n    ...new Set([\n      ...userProvidedOptimizePackageImports,\n      'lucide-react',\n      'date-fns',\n      'lodash-es',\n      'ramda',\n      'antd',\n      'react-bootstrap',\n      'ahooks',\n      '@ant-design/icons',\n      '@headlessui/react',\n      '@headlessui-float/react',\n      '@heroicons/react/20/solid',\n      '@heroicons/react/24/solid',\n      '@heroicons/react/24/outline',\n      '@visx/visx',\n      '@tremor/react',\n      'rxjs',\n      '@mui/material',\n      '@mui/icons-material',\n      'recharts',\n      'react-use',\n      'effect',\n      '@effect/schema',\n      '@effect/platform',\n      '@effect/platform-node',\n      '@effect/platform-browser',\n      '@effect/platform-bun',\n      '@effect/sql',\n      '@effect/sql-mssql',\n      '@effect/sql-mysql2',\n      '@effect/sql-pg',\n      '@effect/sql-sqlite-node',\n      '@effect/sql-sqlite-bun',\n      '@effect/sql-sqlite-wasm',\n      '@effect/sql-sqlite-react-native',\n      '@effect/rpc',\n      '@effect/rpc-http',\n      '@effect/typeclass',\n      '@effect/experimental',\n      '@effect/opentelemetry',\n      '@material-ui/core',\n      '@material-ui/icons',\n      '@tabler/icons-react',\n      'mui-core',\n      // We don't support wildcard imports for these configs, e.g. `react-icons/*`\n      // so we need to add them manually.\n      // In the future, we should consider automatically detecting packages that\n      // need to be optimized.\n      'react-icons/ai',\n      'react-icons/bi',\n      'react-icons/bs',\n      'react-icons/cg',\n      'react-icons/ci',\n      'react-icons/di',\n      'react-icons/fa',\n      'react-icons/fa6',\n      'react-icons/fc',\n      'react-icons/fi',\n      'react-icons/gi',\n      'react-icons/go',\n      'react-icons/gr',\n      'react-icons/hi',\n      'react-icons/hi2',\n      'react-icons/im',\n      'react-icons/io',\n      'react-icons/io5',\n      'react-icons/lia',\n      'react-icons/lib',\n      'react-icons/lu',\n      'react-icons/md',\n      'react-icons/pi',\n      'react-icons/ri',\n      'react-icons/rx',\n      'react-icons/si',\n      'react-icons/sl',\n      'react-icons/tb',\n      'react-icons/tfi',\n      'react-icons/ti',\n      'react-icons/vsc',\n      'react-icons/wi',\n    ]),\n  ]\n\n  if (!result.htmlLimitedBots) {\n    // @ts-expect-error: override the htmlLimitedBots with default string, type covert: RegExp -> string\n    result.htmlLimitedBots = HTML_LIMITED_BOT_UA_RE_STRING\n  }\n\n  // \"use cache\" was originally implicitly enabled with the cacheComponents flag, so\n  // we transfer the value for cacheComponents to the explicit useCache flag to ensure\n  // backwards compatibility.\n  if (result.experimental.useCache === undefined) {\n    result.experimental.useCache = result.experimental.cacheComponents\n  }\n\n  // If cacheComponents is enabled, we also enable PPR.\n  if (result.experimental.cacheComponents) {\n    if (\n      userConfig.experimental?.ppr === false ||\n      userConfig.experimental?.ppr === 'incremental'\n    ) {\n      throw new Error(\n        `\\`experimental.ppr\\` can not be \\`${JSON.stringify(userConfig.experimental?.ppr)}\\` when \\`experimental.cacheComponents\\` is \\`true\\`. PPR is implicitly enabled when Cache Components is enabled.`\n      )\n    }\n\n    result.experimental.ppr = true\n  }\n\n  return result as NextConfigComplete\n}\n\nasync function applyModifyConfig(\n  config: NextConfigComplete,\n  phase: string,\n  silent: boolean\n): Promise<NextConfigComplete> {\n  if (\n    // TODO: should this be called for server start as\n    // adapters shouldn't be relying on \"next start\"\n    [PHASE_PRODUCTION_BUILD, PHASE_PRODUCTION_SERVER].includes(phase) &&\n    config.experimental?.adapterPath\n  ) {\n    const adapterMod = interopDefault(\n      await import(\n        pathToFileURL(require.resolve(config.experimental.adapterPath)).href\n      )\n    ) as NextAdapter\n\n    if (typeof adapterMod.modifyConfig === 'function') {\n      if (!silent) {\n        Log.info(`Applying modifyConfig from ${adapterMod.name}`)\n      }\n      config = await adapterMod.modifyConfig(config)\n    }\n  }\n  return config\n}\n\n// Cache config with keys to handle multiple configurations (e.g., multi-zone)\nconst configCache = new Map<\n  string,\n  {\n    rawConfig: any\n    config: NextConfigComplete\n    configuredExperimentalFeatures: ConfiguredExperimentalFeature[]\n  }\n>()\n\n// Generate cache key based on parameters that affect config output\n// We need a unique key for cache because there can be multiple values\nfunction getCacheKey(\n  phase: string,\n  dir: string,\n  customConfig?: object | null,\n  reactProductionProfiling?: boolean,\n  debugPrerender?: boolean\n): string {\n  // The next.config.js is unique per project, so we can use the dir as the major key\n  // to generate the unique config key.\n  const keyData = JSON.stringify({\n    dir,\n    phase,\n    hasCustomConfig: Boolean(customConfig),\n    reactProductionProfiling: Boolean(reactProductionProfiling),\n    debugPrerender: Boolean(debugPrerender),\n  })\n\n  return djb2Hash(keyData).toString(36)\n}\n\nexport default async function loadConfig(\n  phase: string,\n  dir: string,\n  {\n    customConfig,\n    rawConfig,\n    silent = true,\n    reportExperimentalFeatures,\n    reactProductionProfiling,\n    debugPrerender,\n  }: {\n    customConfig?: object | null\n    rawConfig?: boolean\n    silent?: boolean\n    reportExperimentalFeatures?: (\n      configuredExperimentalFeatures: ConfiguredExperimentalFeature[]\n    ) => void\n    reactProductionProfiling?: boolean\n    debugPrerender?: boolean\n  } = {}\n): Promise<NextConfigComplete> {\n  // Generate cache key based on parameters that affect config output\n  const cacheKey = getCacheKey(\n    phase,\n    dir,\n    customConfig,\n    reactProductionProfiling,\n    debugPrerender\n  )\n\n  // Check if we have a cached result\n  const cachedResult = configCache.get(cacheKey)\n  if (cachedResult) {\n    // Call the experimental features callback if provided\n    if (reportExperimentalFeatures) {\n      reportExperimentalFeatures(cachedResult.configuredExperimentalFeatures)\n    }\n\n    // Return raw config if requested and available\n    if (rawConfig && cachedResult.rawConfig) {\n      return cachedResult.rawConfig\n    }\n\n    return cachedResult.config\n  }\n\n  // Original implementation continues below...\n  if (!process.env.__NEXT_PRIVATE_RENDER_WORKER) {\n    try {\n      loadWebpackHook()\n    } catch (err) {\n      // this can fail in standalone mode as the files\n      // aren't traced/included\n      if (!process.env.__NEXT_PRIVATE_STANDALONE_CONFIG) {\n        throw err\n      }\n    }\n  }\n\n  if (process.env.__NEXT_PRIVATE_STANDALONE_CONFIG) {\n    // we don't apply assignDefaults or modifyConfig here as it\n    // has already been applied\n    const standaloneConfig = JSON.parse(\n      process.env.__NEXT_PRIVATE_STANDALONE_CONFIG\n    )\n\n    // Cache the standalone config\n    configCache.set(cacheKey, {\n      config: standaloneConfig,\n      rawConfig: standaloneConfig,\n      configuredExperimentalFeatures: [],\n    })\n\n    return standaloneConfig\n  }\n\n  const curLog = silent\n    ? {\n        warn: () => {},\n        info: () => {},\n        error: () => {},\n      }\n    : Log\n\n  loadEnvConfig(dir, phase === PHASE_DEVELOPMENT_SERVER, curLog)\n\n  let configFileName = 'next.config.js'\n  const configuredExperimentalFeatures: ConfiguredExperimentalFeature[] = []\n\n  if (customConfig) {\n    // Check deprecation warnings on the custom config before merging with defaults\n    checkDeprecations(customConfig as NextConfig, configFileName, silent, dir)\n\n    const config = await applyModifyConfig(\n      assignDefaults(\n        dir,\n        {\n          configOrigin: 'server',\n          configFileName,\n          ...customConfig,\n        },\n        silent\n      ) as NextConfigComplete,\n      phase,\n      silent\n    )\n\n    // Cache the custom config result\n    configCache.set(cacheKey, {\n      config,\n      rawConfig: customConfig,\n      configuredExperimentalFeatures,\n    })\n\n    reportExperimentalFeatures?.(configuredExperimentalFeatures)\n\n    return config\n  }\n\n  const path = await findUp(CONFIG_FILES, { cwd: dir })\n\n  // If config file was found\n  if (path?.length) {\n    configFileName = basename(path)\n\n    let userConfigModule: any\n    try {\n      const envBefore = Object.assign({}, process.env)\n\n      // `import()` expects url-encoded strings, so the path must be properly\n      // escaped and (especially on Windows) absolute paths must pe prefixed\n      // with the `file://` protocol\n      if (process.env.__NEXT_TEST_MODE === 'jest') {\n        // dynamic import does not currently work inside of vm which\n        // jest relies on so we fall back to require for this case\n        // https://github.com/nodejs/node/issues/35889\n        userConfigModule = require(path)\n      } else if (configFileName === 'next.config.ts') {\n        userConfigModule = await transpileConfig({\n          nextConfigPath: path,\n          configFileName,\n          cwd: dir,\n        })\n      } else {\n        userConfigModule = await import(pathToFileURL(path).href)\n      }\n      const newEnv: typeof process.env = {} as any\n\n      for (const key of Object.keys(process.env)) {\n        if (envBefore[key] !== process.env[key]) {\n          newEnv[key] = process.env[key]\n        }\n      }\n      updateInitialEnv(newEnv)\n\n      if (rawConfig) {\n        // Cache the raw config\n        configCache.set(cacheKey, {\n          config: userConfigModule as NextConfigComplete,\n          rawConfig: userConfigModule,\n          configuredExperimentalFeatures,\n        })\n\n        reportExperimentalFeatures?.(configuredExperimentalFeatures)\n\n        return userConfigModule\n      }\n    } catch (err) {\n      // TODO: Modify docs to add cases of failing next.config.ts transformation\n      curLog.error(\n        `Failed to load ${configFileName}, see more info here https://nextjs.org/docs/messages/next-config-error`\n      )\n      throw err\n    }\n\n    const loadedConfig = Object.freeze(\n      (await normalizeConfig(\n        phase,\n        interopDefault(userConfigModule)\n      )) as NextConfig\n    )\n\n    if (loadedConfig.experimental) {\n      for (const name of Object.keys(\n        loadedConfig.experimental\n      ) as (keyof ExperimentalConfig)[]) {\n        const value = loadedConfig.experimental[name]\n\n        if (name === 'turbo' && !process.env.TURBOPACK) {\n          // Ignore any Turbopack config if Turbopack is not enabled\n          continue\n        }\n\n        addConfiguredExperimentalFeature(\n          configuredExperimentalFeatures,\n          name,\n          value\n        )\n      }\n    }\n\n    // Clone a new userConfig each time to avoid mutating the original\n    const userConfig = cloneObject(loadedConfig) as NextConfig\n\n    // Check deprecation warnings on the actual user config before merging with defaults\n    checkDeprecations(userConfig, configFileName, silent, dir)\n\n    // Always validate the config against schema in non minimal mode.\n    // Only validate once in the root Next.js process, not in forked processes.\n    const isRootProcess = typeof process.send !== 'function'\n    if (!process.env.NEXT_MINIMAL && isRootProcess) {\n      // We only validate the config against schema in non minimal mode\n      const { configSchema } =\n        require('./config-schema') as typeof import('./config-schema')\n      const state = configSchema.safeParse(userConfig)\n\n      if (!state.success) {\n        // error message header\n        const messages = [`Invalid ${configFileName} options detected: `]\n\n        const [errorMessages, shouldExit] = normalizeNextConfigZodErrors(\n          state.error\n        )\n        // ident list item\n        for (const error of errorMessages) {\n          messages.push(`    ${error}`)\n        }\n\n        // error message footer\n        messages.push(\n          'See more info here: https://nextjs.org/docs/messages/invalid-next-config'\n        )\n\n        if (shouldExit) {\n          for (const message of messages) {\n            console.error(message)\n          }\n          await flushAndExit(1)\n        } else {\n          for (const message of messages) {\n            curLog.warn(message)\n          }\n        }\n      }\n    }\n\n    if (userConfig.target && userConfig.target !== 'server') {\n      throw new Error(\n        `The \"target\" property is no longer supported in ${configFileName}.\\n` +\n          'See more info here https://nextjs.org/docs/messages/deprecated-target-config'\n      )\n    }\n\n    if (userConfig.amp?.canonicalBase) {\n      const { canonicalBase } = userConfig.amp || ({} as any)\n      userConfig.amp = userConfig.amp || {}\n      userConfig.amp.canonicalBase =\n        (canonicalBase?.endsWith('/')\n          ? canonicalBase.slice(0, -1)\n          : canonicalBase) || ''\n    }\n\n    if (reactProductionProfiling) {\n      userConfig.reactProductionProfiling = reactProductionProfiling\n    }\n\n    if (\n      userConfig.experimental?.turbo?.loaders &&\n      !userConfig.experimental?.turbo?.rules\n    ) {\n      curLog.warn(\n        'experimental.turbo.loaders is now deprecated. Please update next.config.js to use experimental.turbo.rules as soon as possible.\\n' +\n          'The new option is similar, but the key should be a glob instead of an extension.\\n' +\n          'Example: loaders: { \".mdx\": [\"mdx-loader\"] } -> rules: { \"*.mdx\": [\"mdx-loader\"] }\" }\\n' +\n          'See more info here https://nextjs.org/docs/app/api-reference/next-config-js/turbo'\n      )\n\n      const rules: Record<string, TurbopackLoaderItem[]> = {}\n      for (const [ext, loaders] of Object.entries(\n        userConfig.experimental.turbo.loaders\n      )) {\n        rules['*' + ext] = loaders as TurbopackLoaderItem[]\n      }\n\n      userConfig.experimental.turbo.rules = rules\n    }\n\n    if (userConfig.experimental?.turbo) {\n      curLog.warn(\n        'The config property `experimental.turbo` is deprecated. Move this setting to `config.turbopack` or run `npx @next/codemod@latest next-experimental-turbo-to-turbopack .`'\n      )\n\n      // Merge the two configs, preferring values in `config.turbopack`.\n      userConfig.turbopack = {\n        ...userConfig.experimental.turbo,\n        ...userConfig.turbopack,\n      }\n      userConfig.experimental.turbopackMemoryLimit ??=\n        userConfig.experimental.turbo.memoryLimit\n      userConfig.experimental.turbopackMinify ??=\n        userConfig.experimental.turbo.minify\n      userConfig.experimental.turbopackTreeShaking ??=\n        userConfig.experimental.turbo.treeShaking\n      userConfig.experimental.turbopackSourceMaps ??=\n        userConfig.experimental.turbo.sourceMaps\n    }\n\n    if (userConfig.experimental?.useLightningcss) {\n      const { loadBindings } =\n        require('../build/swc') as typeof import('../build/swc')\n      const isLightningSupported = (await loadBindings())?.css?.lightning\n\n      if (!isLightningSupported) {\n        curLog.warn(\n          `experimental.useLightningcss is set, but the setting is disabled because next-swc/wasm does not support it yet.`\n        )\n        userConfig.experimental.useLightningcss = false\n      }\n    }\n\n    // serialize the regex config into string\n    if (userConfig?.htmlLimitedBots instanceof RegExp) {\n      // @ts-expect-error: override the htmlLimitedBots with default string, type covert: RegExp -> string\n      userConfig.htmlLimitedBots = userConfig.htmlLimitedBots.source\n    }\n\n    enforceExperimentalFeatures(userConfig, {\n      isDefaultConfig: false,\n      configuredExperimentalFeatures,\n      debugPrerender,\n      phase,\n    })\n\n    const completeConfig = assignDefaults(\n      dir,\n      {\n        configOrigin: relative(dir, path),\n        configFile: path,\n        configFileName,\n        ...userConfig,\n      },\n      silent\n    ) as NextConfigComplete\n\n    const finalConfig = await applyModifyConfig(completeConfig, phase, silent)\n\n    // Cache the final result\n    configCache.set(cacheKey, {\n      config: finalConfig,\n      rawConfig: userConfigModule, // Store the original user config module\n      configuredExperimentalFeatures,\n    })\n\n    if (reportExperimentalFeatures) {\n      reportExperimentalFeatures(configuredExperimentalFeatures)\n    }\n\n    return finalConfig\n  } else {\n    const configBaseName = basename(CONFIG_FILES[0], extname(CONFIG_FILES[0]))\n    const unsupportedConfig = findUp.sync(\n      [\n        `${configBaseName}.cjs`,\n        `${configBaseName}.cts`,\n        `${configBaseName}.mts`,\n        `${configBaseName}.json`,\n        `${configBaseName}.jsx`,\n        `${configBaseName}.tsx`,\n      ],\n      { cwd: dir }\n    )\n    if (unsupportedConfig?.length) {\n      throw new Error(\n        `Configuring Next.js via '${basename(\n          unsupportedConfig\n        )}' is not supported. Please replace the file with 'next.config.js', 'next.config.mjs', or 'next.config.ts'.`\n      )\n    }\n  }\n\n  const clonedDefaultConfig = cloneObject(defaultConfig) as NextConfig\n\n  enforceExperimentalFeatures(clonedDefaultConfig, {\n    isDefaultConfig: true,\n    configuredExperimentalFeatures,\n    debugPrerender,\n    phase,\n  })\n\n  // always call assignDefaults to ensure settings like\n  // reactRoot can be updated correctly even with no next.config.js\n  const completeConfig = assignDefaults(\n    dir,\n    { ...clonedDefaultConfig, configFileName },\n    silent\n  ) as NextConfigComplete\n\n  setHttpClientAndAgentOptions(completeConfig)\n\n  const finalConfig = await applyModifyConfig(completeConfig, phase, silent)\n\n  // Cache the default config result\n  configCache.set(cacheKey, {\n    config: finalConfig,\n    rawConfig: clonedDefaultConfig,\n    configuredExperimentalFeatures,\n  })\n\n  if (reportExperimentalFeatures) {\n    reportExperimentalFeatures(configuredExperimentalFeatures)\n  }\n\n  return finalConfig\n}\n\nexport type ConfiguredExperimentalFeature = {\n  key: keyof ExperimentalConfig\n  value: ExperimentalConfig[keyof ExperimentalConfig]\n  reason?: string\n}\n\nfunction enforceExperimentalFeatures(\n  config: NextConfig,\n  options: {\n    isDefaultConfig: boolean\n    configuredExperimentalFeatures: ConfiguredExperimentalFeature[] | undefined\n    debugPrerender: boolean | undefined\n    phase: string\n  }\n) {\n  const {\n    configuredExperimentalFeatures,\n    debugPrerender,\n    isDefaultConfig,\n    phase,\n  } = options\n\n  config.experimental ??= {}\n\n  if (\n    debugPrerender &&\n    (phase === PHASE_PRODUCTION_BUILD || phase === PHASE_EXPORT)\n  ) {\n    setExperimentalFeatureForDebugPrerender(\n      config.experimental,\n      'serverSourceMaps',\n      true,\n      configuredExperimentalFeatures\n    )\n\n    setExperimentalFeatureForDebugPrerender(\n      config.experimental,\n      process.env.TURBOPACK ? 'turbopackMinify' : 'serverMinification',\n      false,\n      configuredExperimentalFeatures\n    )\n\n    setExperimentalFeatureForDebugPrerender(\n      config.experimental,\n      'enablePrerenderSourceMaps',\n      true,\n      configuredExperimentalFeatures\n    )\n\n    setExperimentalFeatureForDebugPrerender(\n      config.experimental,\n      'prerenderEarlyExit',\n      false,\n      configuredExperimentalFeatures\n    )\n  }\n\n  // TODO: Remove this once we've made Cache Components the default.\n  if (\n    process.env.__NEXT_EXPERIMENTAL_CACHE_COMPONENTS === 'true' &&\n    // We do respect an explicit value in the user config.\n    (config.experimental.ppr === undefined ||\n      (isDefaultConfig && !config.experimental.ppr))\n  ) {\n    config.experimental.ppr = true\n\n    if (configuredExperimentalFeatures) {\n      addConfiguredExperimentalFeature(\n        configuredExperimentalFeatures,\n        'ppr',\n        true,\n        'enabled by `__NEXT_EXPERIMENTAL_CACHE_COMPONENTS`'\n      )\n    }\n  }\n\n  // TODO: Remove this once we've made Cache Components the default.\n  if (\n    process.env.__NEXT_EXPERIMENTAL_PPR === 'true' &&\n    // We do respect an explicit value in the user config.\n    (config.experimental.ppr === undefined ||\n      (isDefaultConfig && !config.experimental.ppr))\n  ) {\n    config.experimental.ppr = true\n\n    if (configuredExperimentalFeatures) {\n      addConfiguredExperimentalFeature(\n        configuredExperimentalFeatures,\n        'ppr',\n        true,\n        'enabled by `__NEXT_EXPERIMENTAL_PPR`'\n      )\n    }\n  }\n\n  // TODO: Remove this once we've made Client Segment Cache the default.\n  if (\n    process.env.__NEXT_EXPERIMENTAL_PPR === 'true' &&\n    // We do respect an explicit value in the user config.\n    (config.experimental.clientSegmentCache === undefined ||\n      (isDefaultConfig && !config.experimental.clientSegmentCache))\n  ) {\n    config.experimental.clientSegmentCache = true\n\n    if (configuredExperimentalFeatures) {\n      addConfiguredExperimentalFeature(\n        configuredExperimentalFeatures,\n        'clientSegmentCache',\n        true,\n        'enabled by `__NEXT_EXPERIMENTAL_PPR`'\n      )\n    }\n  }\n\n  // TODO: Remove this once we've made Client Param Parsing the default.\n  if (\n    process.env.__NEXT_EXPERIMENTAL_PPR === 'true' &&\n    // We do respect an explicit value in the user config.\n    (config.experimental.clientParamParsing === undefined ||\n      (isDefaultConfig && !config.experimental.clientParamParsing))\n  ) {\n    config.experimental.clientParamParsing = true\n\n    if (configuredExperimentalFeatures) {\n      addConfiguredExperimentalFeature(\n        configuredExperimentalFeatures,\n        'clientParamParsing',\n        true,\n        'enabled by `__NEXT_EXPERIMENTAL_PPR`'\n      )\n    }\n  }\n\n  // TODO: Remove this once we've made Cache Components the default.\n  if (\n    process.env.__NEXT_EXPERIMENTAL_CACHE_COMPONENTS === 'true' &&\n    // We do respect an explicit value in the user config.\n    (config.experimental.cacheComponents === undefined ||\n      (isDefaultConfig && !config.experimental.cacheComponents))\n  ) {\n    config.experimental.cacheComponents = true\n\n    if (configuredExperimentalFeatures) {\n      addConfiguredExperimentalFeature(\n        configuredExperimentalFeatures,\n        'cacheComponents',\n        true,\n        'enabled by `__NEXT_EXPERIMENTAL_CACHE_COMPONENTS`'\n      )\n    }\n  }\n\n  if (\n    config.experimental.enablePrerenderSourceMaps === undefined &&\n    config.experimental.cacheComponents === true\n  ) {\n    config.experimental.enablePrerenderSourceMaps = true\n\n    if (configuredExperimentalFeatures) {\n      addConfiguredExperimentalFeature(\n        configuredExperimentalFeatures,\n        'enablePrerenderSourceMaps',\n        true,\n        'enabled by `experimental.cacheComponents`'\n      )\n    }\n  }\n}\n\nfunction addConfiguredExperimentalFeature<\n  KeyType extends keyof ExperimentalConfig,\n>(\n  configuredExperimentalFeatures: ConfiguredExperimentalFeature[],\n  key: KeyType,\n  value: ExperimentalConfig[KeyType],\n  reason?: string\n) {\n  if (value !== (defaultConfig.experimental as Record<string, unknown>)[key]) {\n    configuredExperimentalFeatures.push({ key, value, reason })\n  }\n}\n\nfunction setExperimentalFeatureForDebugPrerender<\n  KeyType extends keyof ExperimentalConfig,\n>(\n  experimentalConfig: ExperimentalConfig,\n  key: KeyType,\n  value: ExperimentalConfig[KeyType],\n  configuredExperimentalFeatures: ConfiguredExperimentalFeature[] | undefined\n) {\n  if (experimentalConfig[key] !== value) {\n    experimentalConfig[key] = value\n\n    if (configuredExperimentalFeatures) {\n      const action =\n        value === true ? 'enabled' : value === false ? 'disabled' : 'set'\n\n      const reason = `${action} by \\`--debug-prerender\\``\n\n      addConfiguredExperimentalFeature(\n        configuredExperimentalFeatures,\n        key,\n        value,\n        reason\n      )\n    }\n  }\n}\n\nfunction cloneObject(obj: any): any {\n  // Primitives & null\n  if (obj === null || typeof obj !== 'object') {\n    return obj\n  }\n\n  // RegExp → clone via constructor\n  if (obj instanceof RegExp) {\n    return new RegExp(obj.source, obj.flags)\n  }\n\n  // Function → just reuse the function reference\n  if (typeof obj === 'function') {\n    return obj\n  }\n\n  // Arrays → map each element\n  if (Array.isArray(obj)) {\n    return obj.map(cloneObject)\n  }\n\n  // Detect non‑plain objects (class instances)\n  const proto = Object.getPrototypeOf(obj)\n  const isPlainObject = proto === Object.prototype || proto === null\n\n  // If it's not a plain object, just return the original\n  if (!isPlainObject) {\n    return obj\n  }\n\n  // Plain object → create a new object with the same prototype\n  // and copy all properties, cloning data properties and keeping\n  // accessor properties (getters/setters) as‑is.\n  const result = Object.create(proto)\n  for (const key of Reflect.ownKeys(obj)) {\n    const descriptor = Object.getOwnPropertyDescriptor(obj, key)\n\n    if (descriptor && (descriptor.get || descriptor.set)) {\n      // Accessor property → copy descriptor as‑is (get/set functions)\n      Object.defineProperty(result, key, descriptor)\n    } else {\n      // Data property → clone the value\n      result[key] = cloneObject(obj[key])\n    }\n  }\n\n  return result\n}\n"], "names": ["loadConfig", "normalizeConfig", "warnOptionHasBeenDeprecated", "warnOptionHasBeenMovedOutOfExperimental", "normalizeNextConfigZodErrors", "error", "shouldExit", "issues", "normalizeZodErrors", "flatMap", "issue", "message", "path", "config", "nested<PERSON><PERSON><PERSON><PERSON><PERSON>", "reason", "silent", "hasWarned", "current", "found", "nestedPropertyKeys", "split", "key", "undefined", "Log", "warnOnce", "checkDeprecations", "userConfig", "configFileName", "dir", "experimental", "dynamicIO", "i18n", "hasAppDir", "Boolean", "findDir", "oldExperimentalKey", "new<PERSON>ey", "warn", "newKeys", "length", "shift", "warnCustomizedOption", "defaultValue", "customMessage", "segs", "seg", "assignDefaults", "result", "exportTrailingSlash", "trailingSlash", "cacheComponents", "Object", "keys", "reduce", "currentConfig", "value", "Error", "userDistDir", "trim", "Array", "isArray", "for<PERSON>ach", "ext", "defaultConfig", "constructor", "c", "k", "v", "trustHostHeader", "ciEnvironment", "hasNextSupport", "allowDevelopmentBuild", "process", "env", "NODE_ENV", "isStableBuild", "ppr", "CanaryOnlyError", "feature", "turbopackPersistentCaching", "output", "rewrites", "redirects", "headers", "assetPrefix", "basePath", "startsWith", "endsWith", "amp", "canonicalBase", "images", "localPatterns", "hasMatch", "some", "pattern", "pathname", "search", "push", "remotePatterns", "map", "protocol", "hostname", "port", "proto", "replace", "includes", "url", "URL", "hasMatchForAssetPrefix", "matchRemotePattern", "domains", "loader", "imageConfigDefault", "pathHasPrefix", "loaderFile", "absolutePath", "join", "existsSync", "devIndicators", "buildActivityPosition", "position", "outputStandalone", "serverActions", "bodySizeLimit", "parseInt", "toString", "isNaN", "outputFileTracingRoot", "isAbsolute", "resolve", "turbopack", "root", "NEXT_DEPLOYMENT_ID", "deploymentId", "tracingRoot", "turbopackRoot", "rootDir", "findRootDir", "dset", "setHttpClientAndAgentOptions", "i18nType", "locales", "defaultLocaleType", "defaultLocale", "invalidDomainItems", "filter", "item", "domain", "console", "defaultLocaleDuplicate", "find", "altItem", "hasInvalidLocale", "locale", "domainItem", "JSON", "stringify", "invalidLocales", "String", "normalizedLocales", "Set", "duplicateLocales", "localeLower", "toLowerCase", "has", "add", "size", "localeDetectionType", "localeDetection", "<PERSON><PERSON><PERSON><PERSON>", "cacheLife", "defaultDefault", "revalidate", "expire", "staleTimes", "static", "defaultCacheLifeProfile", "stale", "staticStaleTime", "expireTime", "cacheHandlers", "allowedHandlerNameRegex", "handler<PERSON>eys", "invalidHandlerItems", "test", "handler<PERSON><PERSON>", "userProvidedModularizeImports", "modularizeImports", "transform", "lodash", "userProvidedOptimizePackageImports", "optimizePackageImports", "htmlLimitedBots", "HTML_LIMITED_BOT_UA_RE_STRING", "useCache", "applyModifyConfig", "phase", "PHASE_PRODUCTION_BUILD", "PHASE_PRODUCTION_SERVER", "adapterPath", "adapterMod", "interopDefault", "pathToFileURL", "require", "href", "modifyConfig", "info", "name", "config<PERSON><PERSON>", "Map", "get<PERSON><PERSON><PERSON><PERSON>", "customConfig", "reactProductionProfiling", "debugPrerender", "keyData", "hasCustomConfig", "djb2Hash", "rawConfig", "reportExperimentalFeatures", "cache<PERSON>ey", "cachedResult", "get", "configuredExperimentalFeatures", "__NEXT_PRIVATE_RENDER_WORKER", "loadWebpackHook", "err", "__NEXT_PRIVATE_STANDALONE_CONFIG", "standaloneConfig", "parse", "set", "curLog", "loadEnvConfig", "PHASE_DEVELOPMENT_SERVER", "config<PERSON><PERSON><PERSON>", "findUp", "CONFIG_FILES", "cwd", "basename", "userConfigModule", "envBefore", "assign", "__NEXT_TEST_MODE", "transpileConfig", "nextConfigPath", "newEnv", "updateInitialEnv", "loadedConfig", "freeze", "TURBOPACK", "addConfiguredExperimentalFeature", "cloneObject", "isRootProcess", "send", "NEXT_MINIMAL", "configSchema", "state", "safeParse", "success", "messages", "errorMessages", "flushAndExit", "target", "slice", "turbo", "loaders", "rules", "entries", "turbopackMemoryLimit", "memoryLimit", "turbopackMinify", "minify", "turbopackTreeShaking", "treeShaking", "turbopackSourceMaps", "sourceMaps", "useLightningcss", "loadBindings", "isLightningSupported", "css", "lightning", "RegExp", "source", "enforceExperimentalFeatures", "isDefaultConfig", "completeConfig", "relative", "configFile", "finalConfig", "configBaseName", "extname", "unsupportedConfig", "sync", "clonedDefaultConfig", "options", "PHASE_EXPORT", "setExperimentalFeatureForDebugPrerender", "__NEXT_EXPERIMENTAL_CACHE_COMPONENTS", "__NEXT_EXPERIMENTAL_PPR", "clientSegmentCache", "clientParamParsing", "enablePrerenderSourceMaps", "experimentalConfig", "action", "obj", "flags", "getPrototypeOf", "isPlainObject", "prototype", "create", "Reflect", "ownKeys", "descriptor", "getOwnPropertyDescriptor", "defineProperty"], "mappings": ";;;;;;;;;;;;;;;;;IA2sCA,OA6ZC;eA7Z6BA;;IAhqCrBC,eAAe;eAAfA,6BAAe;;IAqBRC,2BAA2B;eAA3BA;;IAyGAC,uCAAuC;eAAvCA;;;oBAzKW;sBAC4C;qBACzC;+DACX;6DACE;gEACU;2BAOxB;8BACwC;6BASf;6BACG;qBAEa;8BACnB;0BACD;mCACiB;+BACf;oCACK;iCAIH;sBACX;qBACc;uBACW;8BACtB;4BACuB;gCAChB;sBACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKzB,SAASC,6BACPC,KAA2B;IAE3B,IAAIC,aAAa;IACjB,MAAMC,SAASC,IAAAA,uBAAkB,EAACH;IAClC,OAAO;QACLE,OAAOE,OAAO,CAAC,CAAC,EAAEC,KAAK,EAAEC,OAAO,EAAE;YAChC,IAAID,MAAME,IAAI,CAAC,EAAE,KAAK,UAAU;gBAC9B,oEAAoE;gBACpEN,aAAa;YACf;YAEA,OAAOK;QACT;QACAL;KACD;AACH;AAEO,SAASJ,4BACdW,MAAkB,EAClBC,iBAAyB,EACzBC,MAAc,EACdC,MAAe;IAEf,IAAIC,YAAY;IAChB,IAAI,CAACD,QAAQ;QACX,IAAIE,UAAUL;QACd,IAAIM,QAAQ;QACZ,MAAMC,qBAAqBN,kBAAkBO,KAAK,CAAC;QACnD,KAAK,MAAMC,OAAOF,mBAAoB;YACpC,IAAIF,OAAO,CAACI,IAAI,KAAKC,WAAW;gBAC9BL,UAAUA,OAAO,CAACI,IAAI;YACxB,OAAO;gBACLH,QAAQ;gBACR;YACF;QACF;QACA,IAAIA,OAAO;YACTK,KAAIC,QAAQ,CAACV;YACbE,YAAY;QACd;IACF;IACA,OAAOA;AACT;AAEA,SAASS,kBACPC,UAAsB,EACtBC,cAAsB,EACtBZ,MAAe,EACfa,GAAW;QAgBPF;IAdJzB,4BACEyB,YACA,OACA,CAAC,sGAAsG,CAAC,EACxGX;IAGFd,4BACEyB,YACA,oBACA,CAAC,mHAAmH,CAAC,EACrHX;IAGF,IAAIW,EAAAA,2BAAAA,WAAWG,YAAY,qBAAvBH,yBAAyBI,SAAS,MAAKR,WAAW;QACpDrB,4BACEyB,YACA,0BACA,CAAC,oGAAoG,EAAEC,eAAe,kBAAkB,CAAC,EACzIZ;IAEJ;IAEAd,4BACEyB,YACA,oCACA,CAAC,yIAAyI,EAAEC,eAAe,CAAC,CAAC,EAC7JZ;IAGFd,4BACEyB,YACA,sBACA,CAAC,8GAA8G,EAAEC,eAAe,CAAC,CAAC,EAClIZ;IAGFd,4BACEyB,YACA,8BACA,CAAC,+FAA+F,EAAEC,eAAe,CAAC,CAAC,EACnHZ;IAGFd,4BACEyB,YACA,+BACA,CAAC,gGAAgG,EAAEC,eAAe,CAAC,CAAC,EACpHZ;IAGFd,4BACEyB,YACA,uCACA,CAAC,2GAA2G,EAAEC,eAAe,kBAAkB,CAAC,EAChJZ;IAGF,kCAAkC;IAClC,IAAIW,WAAWK,IAAI,EAAE;QACnB,MAAMC,YAAYC,QAAQC,IAAAA,qBAAO,EAACN,KAAK;QACvC,IAAII,WAAW;YACb/B,4BACEyB,YACA,QACA,CAAC,sBAAsB,EAAEC,eAAe,uKAAuK,CAAC,EAChNZ;QAEJ;IACF;AACF;AAEO,SAASb,wCACdU,MAAkB,EAClBuB,kBAA0B,EAC1BC,MAAc,EACdT,cAAsB,EACtBZ,MAAe;IAEf,IAAIH,OAAOiB,YAAY,IAAIM,sBAAsBvB,OAAOiB,YAAY,EAAE;QACpE,IAAI,CAACd,QAAQ;YACXQ,KAAIc,IAAI,CACN,CAAC,eAAe,EAAEF,mBAAmB,uBAAuB,EAAEC,OAAO,IAAI,CAAC,GACxE,CAAC,mBAAmB,EAAET,eAAe,kBAAkB,CAAC;QAE9D;QAEA,IAAIV,UAAUL;QACd,MAAM0B,UAAUF,OAAOhB,KAAK,CAAC;QAC7B,MAAOkB,QAAQC,MAAM,GAAG,EAAG;YACzB,MAAMlB,MAAMiB,QAAQE,KAAK;YACzBvB,OAAO,CAACI,IAAI,GAAGJ,OAAO,CAACI,IAAI,IAAI,CAAC;YAChCJ,UAAUA,OAAO,CAACI,IAAI;QACxB;QACAJ,OAAO,CAACqB,QAAQE,KAAK,GAAI,GAAG,AAAC5B,OAAOiB,YAAY,AAAQ,CAACM,mBAAmB;IAC9E;IAEA,OAAOvB;AACT;AAEA,SAAS6B,qBACP7B,MAAkB,EAClBS,GAAW,EACXqB,YAAiB,EACjBC,aAAqB,EACrBhB,cAAsB,EACtBZ,MAAe;IAEf,MAAM6B,OAAOvB,IAAID,KAAK,CAAC;IACvB,IAAIH,UAAUL;IAEd,MAAOgC,KAAKL,MAAM,IAAI,EAAG;QACvB,MAAMM,MAAMD,KAAKJ,KAAK;QACtB,IAAI,CAAEK,CAAAA,OAAO5B,OAAM,GAAI;YACrB;QACF;QACAA,UAAUA,OAAO,CAAC4B,IAAI;IACxB;IAEA,IAAI,CAAC9B,UAAUE,YAAYyB,cAAc;QACvCnB,KAAIc,IAAI,CACN,CAAC,KAAK,EAAEhB,IAAI,4BAA4B,EAAEsB,gBAAgBA,gBAAgB,OAAO,GAAG,+BAA+B,EAAEhB,eAAe,CAAC,CAAC;IAE1I;AACF;AAEA,SAASmB,eACPlB,GAAW,EACXF,UAAmD,EACnDX,MAAe;QAgBXW,0BAsGCqB,sBAKHA,uBA0VOA,oCAAAA,uBA8CLA,mBAekBA,oBAwLgBA,uBAmDlCA,uBA6DFA;IAxzBF,MAAMpB,iBAAiBD,WAAWC,cAAc;IAChD,IAAI,OAAOD,WAAWsB,mBAAmB,KAAK,aAAa;QACzD,IAAI,CAACjC,QAAQ;YACXQ,KAAIc,IAAI,CACN,CAAC,yFAAyF,EAAEV,eAAe,CAAC,CAAC;QAEjH;QACA,IAAI,OAAOD,WAAWuB,aAAa,KAAK,aAAa;YACnDvB,WAAWuB,aAAa,GAAGvB,WAAWsB,mBAAmB;QAC3D;QACA,OAAOtB,WAAWsB,mBAAmB;IACvC;IAEA,6EAA6E;IAC7E,IAAItB,EAAAA,2BAAAA,WAAWG,YAAY,qBAAvBH,yBAAyBI,SAAS,MAAKR,WAAW;YAGhDI;QAFJ,8FAA8F;QAC9F,oFAAoF;QACpF,IAAIA,EAAAA,4BAAAA,WAAWG,YAAY,qBAAvBH,0BAAyBwB,eAAe,MAAK5B,WAAW;YAC1DI,WAAWG,YAAY,CAACqB,eAAe,GACrCxB,WAAWG,YAAY,CAACC,SAAS;QACrC;QAEA,iCAAiC;QACjC,OAAOJ,WAAWG,YAAY,CAACC,SAAS;IAC1C;IAEA,MAAMlB,SAASuC,OAAOC,IAAI,CAAC1B,YAAY2B,MAAM,CAC3C,CAACC,eAAejC;QACd,MAAMkC,QAAQ7B,UAAU,CAACL,IAAI;QAE7B,IAAIkC,UAAUjC,aAAaiC,UAAU,MAAM;YACzC,OAAOD;QACT;QAEA,IAAIjC,QAAQ,WAAW;YACrB,IAAI,OAAOkC,UAAU,UAAU;gBAC7B,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,+CAA+C,EAAE,OAAOD,MAAM,CAAC,CAAC,GAD7D,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,MAAME,cAAcF,MAAMG,IAAI;YAE9B,qEAAqE;YACrE,eAAe;YACf,IAAID,gBAAgB,UAAU;gBAC5B,MAAM,qBAEL,CAFK,IAAID,MACR,CAAC,4IAA4I,CAAC,GAD1I,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,2EAA2E;YAC3E,8CAA8C;YAC9C,IAAIC,YAAYlB,MAAM,KAAK,GAAG;gBAC5B,MAAM,qBAEL,CAFK,IAAIiB,MACR,CAAC,8GAA8G,CAAC,GAD5G,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;QAEA,IAAInC,QAAQ,kBAAkB;YAC5B,IAAI,CAACsC,MAAMC,OAAO,CAACL,QAAQ;gBACzB,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,4DAA4D,EAAED,MAAM,0CAA0C,CAAC,GAD5G,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAI,CAACA,MAAMhB,MAAM,EAAE;gBACjB,MAAM,qBAEL,CAFK,IAAIiB,MACR,CAAC,uGAAuG,CAAC,GADrG,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEAD,MAAMM,OAAO,CAAC,CAACC;gBACb,IAAI,OAAOA,QAAQ,UAAU;oBAC3B,MAAM,qBAEL,CAFK,IAAIN,MACR,CAAC,4DAA4D,EAAEM,IAAI,WAAW,EAAE,OAAOA,IAAI,0CAA0C,CAAC,GADlI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;QACF;QAEA,MAAMpB,eAAe,AAACqB,2BAAa,AAA4B,CAAC1C,IAAI;QAEpE,IACE,CAAC,CAACkC,SACFA,MAAMS,WAAW,KAAKb,UACtB,OAAOT,iBAAiB,UACxB;YACAY,aAAa,CAACjC,IAAI,GAAG;gBACnB,GAAGqB,YAAY;gBACf,GAAGS,OAAOC,IAAI,CAACG,OAAOF,MAAM,CAAM,CAACY,GAAGC;oBACpC,MAAMC,IAAIZ,KAAK,CAACW,EAAE;oBAClB,IAAIC,MAAM7C,aAAa6C,MAAM,MAAM;wBACjCF,CAAC,CAACC,EAAE,GAAGC;oBACT;oBACA,OAAOF;gBACT,GAAG,CAAC,EAAE;YACR;QACF,OAAO;YACLX,aAAa,CAACjC,IAAI,GAAGkC;QACvB;QAEA,OAAOD;IACT,GACA,CAAC;IAGH,MAAMP,SAAS;QACb,GAAGgB,2BAAa;QAChB,GAAGnD,MAAM;QACTiB,cAAc;YACZ,GAAGkC,2BAAa,CAAClC,YAAY;YAC7B,GAAGjB,OAAOiB,YAAY;QACxB;IACF;IAEA,qEAAqE;IACrE,IAAI,GAACkB,uBAAAA,OAAOlB,YAAY,qBAAnBkB,qBAAqBqB,eAAe,KAAIC,QAAcC,cAAc,EAAE;QACzEvB,OAAOlB,YAAY,CAACuC,eAAe,GAAG;IACxC;IAEA,IACErB,EAAAA,wBAAAA,OAAOlB,YAAY,qBAAnBkB,sBAAqBwB,qBAAqB,KAC1CC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACzB;QACA,MAAM,qBAEL,CAFK,IAAIlB,MACR,CAAC,sGAAsG,CAAC,GADpG,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAImB,IAAAA,yBAAa,KAAI;YAEf5B,uBAEOA,uBAEAA;QALX,oEAAoE;QACpE,KAAIA,wBAAAA,OAAOlB,YAAY,qBAAnBkB,sBAAqB6B,GAAG,EAAE;YAC5B,MAAM,qBAAoD,CAApD,IAAIC,2BAAe,CAAC;gBAAEC,SAAS;YAAmB,IAAlD,qBAAA;uBAAA;4BAAA;8BAAA;YAAmD;QAC3D,OAAO,KAAI/B,wBAAAA,OAAOlB,YAAY,qBAAnBkB,sBAAqBG,eAAe,EAAE;YAC/C,MAAM,qBAAgE,CAAhE,IAAI2B,2BAAe,CAAC;gBAAEC,SAAS;YAA+B,IAA9D,qBAAA;uBAAA;4BAAA;8BAAA;YAA+D;QACvE,OAAO,KAAI/B,wBAAAA,OAAOlB,YAAY,qBAAnBkB,sBAAqBgC,0BAA0B,EAAE;YAC1D,MAAM,qBAEJ,CAFI,IAAIF,2BAAe,CAAC;gBACxBC,SAAS;YACX,IAFM,qBAAA;uBAAA;4BAAA;8BAAA;YAEL;QACH;IACF;IAEA,IAAI/B,OAAOiC,MAAM,KAAK,UAAU;QAC9B,IAAIjC,OAAOhB,IAAI,EAAE;YACf,MAAM,qBAEL,CAFK,IAAIyB,MACR,+HADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI,CAACc,sBAAc,EAAE;YACnB,IAAIvB,OAAOkC,QAAQ,EAAE;gBACnB1D,KAAIc,IAAI,CACN;YAEJ;YACA,IAAIU,OAAOmC,SAAS,EAAE;gBACpB3D,KAAIc,IAAI,CACN;YAEJ;YACA,IAAIU,OAAOoC,OAAO,EAAE;gBAClB5D,KAAIc,IAAI,CACN;YAEJ;QACF;IACF;IAEA,IAAI,OAAOU,OAAOqC,WAAW,KAAK,UAAU;QAC1C,MAAM,qBAEL,CAFK,IAAI5B,MACR,CAAC,mDAAmD,EAAE,OAAOT,OAAOqC,WAAW,CAAC,sDAAsD,CAAC,GADnI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAI,OAAOrC,OAAOsC,QAAQ,KAAK,UAAU;QACvC,MAAM,qBAEL,CAFK,IAAI7B,MACR,CAAC,gDAAgD,EAAE,OAAOT,OAAOsC,QAAQ,CAAC,CAAC,CAAC,GADxE,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAItC,OAAOsC,QAAQ,KAAK,IAAI;QAC1B,IAAItC,OAAOsC,QAAQ,KAAK,KAAK;YAC3B,MAAM,qBAEL,CAFK,IAAI7B,MACR,CAAC,iFAAiF,CAAC,GAD/E,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI,CAACT,OAAOsC,QAAQ,CAACC,UAAU,CAAC,MAAM;YACpC,MAAM,qBAEL,CAFK,IAAI9B,MACR,CAAC,iDAAiD,EAAET,OAAOsC,QAAQ,CAAC,CAAC,CAAC,GADlE,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAItC,OAAOsC,QAAQ,KAAK,KAAK;gBAWvBtC;YAVJ,IAAIA,OAAOsC,QAAQ,CAACE,QAAQ,CAAC,MAAM;gBACjC,MAAM,qBAEL,CAFK,IAAI/B,MACR,CAAC,iDAAiD,EAAET,OAAOsC,QAAQ,CAAC,CAAC,CAAC,GADlE,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAItC,OAAOqC,WAAW,KAAK,IAAI;gBAC7BrC,OAAOqC,WAAW,GAAGrC,OAAOsC,QAAQ;YACtC;YAEA,IAAItC,EAAAA,cAAAA,OAAOyC,GAAG,qBAAVzC,YAAY0C,aAAa,MAAK,IAAI;gBACpC1C,OAAOyC,GAAG,CAACC,aAAa,GAAG1C,OAAOsC,QAAQ;YAC5C;QACF;IACF;IAEA,IAAItC,0BAAAA,OAAQ2C,MAAM,EAAE;QAClB,MAAMA,SAAsB3C,OAAO2C,MAAM;QAEzC,IAAI,OAAOA,WAAW,UAAU;YAC9B,MAAM,qBAEL,CAFK,IAAIlC,MACR,CAAC,8CAA8C,EAAE,OAAOkC,OAAO,6EAA6E,CAAC,GADzI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIA,OAAOC,aAAa,EAAE;YACxB,IAAI,CAAChC,MAAMC,OAAO,CAAC8B,OAAOC,aAAa,GAAG;gBACxC,MAAM,qBAEL,CAFK,IAAInC,MACR,CAAC,2DAA2D,EAAE,OAAOkC,OAAOC,aAAa,CAAC,6EAA6E,CAAC,GADpK,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,6DAA6D;YAC7D,MAAMC,WAAWF,OAAOC,aAAa,CAACE,IAAI,CACxC,CAACC,UACCA,QAAQC,QAAQ,KAAK,4BAA4BD,QAAQE,MAAM,KAAK;YAExE,IAAI,CAACJ,UAAU;gBACb,iDAAiD;gBACjDF,OAAOC,aAAa,CAACM,IAAI,CAAC;oBACxBF,UAAU;oBACVC,QAAQ;gBACV;YACF;QACF;QAEA,IAAIN,OAAOQ,cAAc,EAAE;gBA+BrBtF;YA9BJ,IAAI,CAAC+C,MAAMC,OAAO,CAAC8B,OAAOQ,cAAc,GAAG;gBACzC,MAAM,qBAEL,CAFK,IAAI1C,MACR,CAAC,4DAA4D,EAAE,OAAOkC,OAAOQ,cAAc,CAAC,6EAA6E,CAAC,GADtK,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,6EAA6E;YAC7E,4EAA4E;YAC5E,0DAA0D;YAC1DR,OAAOQ,cAAc,GAAGR,OAAOQ,cAAc,CAACC,GAAG,CAC/C,CAAC,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAEP,QAAQ,EAAEC,MAAM,EAAE;gBAC7C,MAAMO,QAAQH,4BAAAA,SAAUI,OAAO,CAAC,MAAM;gBACtC,IAAI,CAAC;oBAAC;oBAAQ;oBAASlF;iBAAU,CAACmF,QAAQ,CAACF,QAAQ;oBACjD,MAAM,qBAEL,CAFK,IAAI/C,MACR,CAAC,+EAA+E,EAAE+C,MAAM,EAAE,CAAC,GADvF,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBACA,OAAO;oBACLH,UAAUG;oBACVF;oBACAC;oBACAP;oBACAC;gBACF;YACF;YAGF,4DAA4D;YAC5D,2DAA2D;YAC3D,gBAAgB;YAChB,KAAIpF,sBAAAA,OAAOwE,WAAW,qBAAlBxE,oBAAoB0E,UAAU,CAAC,SAAS;gBAC1C,IAAI;oBACF,MAAMoB,MAAM,IAAIC,IAAI/F,OAAOwE,WAAW;oBACtC,MAAMwB,yBAAyBlB,OAAOQ,cAAc,CAACL,IAAI,CAAC,CAACC,UACzDe,IAAAA,sCAAkB,EAACf,SAASY;oBAG9B,qEAAqE;oBACrE,IAAI,CAACE,wBAAwB;wBAC3BlB,OAAOQ,cAAc,CAACD,IAAI,CAAC;4BACzBI,UAAUK,IAAIL,QAAQ;4BACtBD,UAAUM,IAAIN,QAAQ,CAACI,OAAO,CAAC,MAAM;4BACrCF,MAAMI,IAAIJ,IAAI;wBAChB;oBACF;gBACF,EAAE,OAAOlG,OAAO;oBACd,MAAM,qBAEL,CAFK,IAAIoD,MACR,CAAC,8CAA8C,EAAEpD,OAAO,GADpD,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;QACF;QAEA,IAAIsF,OAAOoB,OAAO,EAAE;YAClB,IAAI,CAACnD,MAAMC,OAAO,CAAC8B,OAAOoB,OAAO,GAAG;gBAClC,MAAM,qBAEL,CAFK,IAAItD,MACR,CAAC,qDAAqD,EAAE,OAAOkC,OAAOoB,OAAO,CAAC,6EAA6E,CAAC,GADxJ,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;QAEA,IAAI,CAACpB,OAAOqB,MAAM,EAAE;YAClBrB,OAAOqB,MAAM,GAAG;QAClB;QAEA,IACErB,OAAOqB,MAAM,KAAK,aAClBrB,OAAOqB,MAAM,KAAK,YAClBrB,OAAO/E,IAAI,KAAKqG,+BAAkB,CAACrG,IAAI,EACvC;YACA,MAAM,qBAEL,CAFK,IAAI6C,MACR,CAAC,kCAAkC,EAAEkC,OAAOqB,MAAM,CAAC,sKAAsK,CAAC,GADtN,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IACErB,OAAO/E,IAAI,KAAKqG,+BAAkB,CAACrG,IAAI,IACvCoC,OAAOsC,QAAQ,IACf,CAAC4B,IAAAA,4BAAa,EAACvB,OAAO/E,IAAI,EAAEoC,OAAOsC,QAAQ,GAC3C;YACAK,OAAO/E,IAAI,GAAG,GAAGoC,OAAOsC,QAAQ,GAAGK,OAAO/E,IAAI,EAAE;QAClD;QAEA,8EAA8E;QAC9E,IACE+E,OAAO/E,IAAI,IACX,CAAC+E,OAAO/E,IAAI,CAAC4E,QAAQ,CAAC,QACrBG,CAAAA,OAAOqB,MAAM,KAAK,aAAahE,OAAOE,aAAa,AAAD,GACnD;YACAyC,OAAO/E,IAAI,IAAI;QACjB;QAEA,IAAI+E,OAAOwB,UAAU,EAAE;YACrB,IAAIxB,OAAOqB,MAAM,KAAK,aAAarB,OAAOqB,MAAM,KAAK,UAAU;gBAC7D,MAAM,qBAEL,CAFK,IAAIvD,MACR,CAAC,kCAAkC,EAAEkC,OAAOqB,MAAM,CAAC,uFAAuF,CAAC,GADvI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,MAAMI,eAAeC,IAAAA,UAAI,EAACxF,KAAK8D,OAAOwB,UAAU;YAChD,IAAI,CAACG,IAAAA,cAAU,EAACF,eAAe;gBAC7B,MAAM,qBAEL,CAFK,IAAI3D,MACR,CAAC,+CAA+C,EAAE2D,aAAa,EAAE,CAAC,GAD9D,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACAzB,OAAOwB,UAAU,GAAGC;QACtB;IACF;IAEA1E,qBACEM,QACA,6BACA,MACA,mGACApB,gBACAZ;IAGF,wFAAwF;IACxF,IACEgC,OAAOuE,aAAa,IACpB,OAAOvE,OAAOuE,aAAa,KAAK,YAChC,2BAA2BvE,OAAOuE,aAAa,IAC/CvE,OAAOuE,aAAa,CAACC,qBAAqB,KAAKxE,OAAOuE,aAAa,CAACE,QAAQ,EAC5E;QACA,IAAI,CAACzG,QAAQ;YACXQ,KAAIC,QAAQ,CACV,CAAC,yDAAyD,EAAEuB,OAAOuE,aAAa,CAACC,qBAAqB,CAAC,iCAAiC,EAAExE,OAAOuE,aAAa,CAACE,QAAQ,CAAC,sCAAsC,EAAEzE,OAAOuE,aAAa,CAACC,qBAAqB,CAAC,8BAA8B,CAAC;QAE9R;QACAxE,OAAOuE,aAAa,CAACE,QAAQ,GAAGzE,OAAOuE,aAAa,CAACC,qBAAqB;IAC5E;IAEArH,wCACE6C,QACA,wBACA,iCACApB,gBACAZ;IAEFb,wCACE6C,QACA,oCACA,0BACApB,gBACAZ;IAEFb,wCACE6C,QACA,SACA,kBACApB,gBACAZ;IAEFb,wCACE6C,QACA,oBACA,6BACApB,gBACAZ;IAEFb,wCACE6C,QACA,WACA,oBACApB,gBACAZ;IAEFb,wCACE6C,QACA,yBACA,kCACApB,gBACAZ;IAEFb,wCACE6C,QACA,iBACA,0BACApB,gBACAZ;IAEFb,wCACE6C,QACA,YACA,cACApB,gBACAZ;IAEFb,wCACE6C,QACA,eACA,eACApB,gBACAZ;IAEFb,wCACE6C,QACA,yBACA,yBACApB,gBACAZ;IAEFb,wCACE6C,QACA,6BACA,6BACApB,gBACAZ;IAEFb,wCACE6C,QACA,6BACA,6BACApB,gBACAZ;IAGF,IAAI,AAACgC,OAAOlB,YAAY,CAAS4F,gBAAgB,EAAE;QACjD,IAAI,CAAC1G,QAAQ;YACXQ,KAAIc,IAAI,CACN,CAAC,iGAAiG,CAAC;QAEvG;QACAU,OAAOiC,MAAM,GAAG;IAClB;IAEA,IACE,SAAOjC,wBAAAA,OAAOlB,YAAY,sBAAnBkB,qCAAAA,sBAAqB2E,aAAa,qBAAlC3E,mCAAoC4E,aAAa,MAAK,aAC7D;YAEE5E;QADF,MAAMQ,QAAQqE,UACZ7E,sCAAAA,OAAOlB,YAAY,CAAC6F,aAAa,qBAAjC3E,oCAAmC4E,aAAa,CAACE,QAAQ;QAE3D,IAAIC,MAAMvE,UAAUA,QAAQ,GAAG;YAC7B,MAAM,qBAEL,CAFK,IAAIC,MACR,8KADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;IAEAtD,wCACE6C,QACA,qBACA,qBACApB,gBACAZ;IAEFb,wCACE6C,QACA,8BACA,8BACApB,gBACAZ;IAEFb,wCACE6C,QACA,6BACA,6BACApB,gBACAZ;IAGF,IACEgC,CAAAA,0BAAAA,OAAQgF,qBAAqB,KAC7B,CAACC,IAAAA,gBAAU,EAACjF,OAAOgF,qBAAqB,GACxC;QACAhF,OAAOgF,qBAAqB,GAAGE,IAAAA,aAAO,EAAClF,OAAOgF,qBAAqB;QACnE,IAAI,CAAChH,QAAQ;YACXQ,KAAIc,IAAI,CACN,CAAC,iDAAiD,EAAEU,OAAOgF,qBAAqB,EAAE;QAEtF;IACF;IAEA,IAAIhF,CAAAA,2BAAAA,oBAAAA,OAAQmF,SAAS,qBAAjBnF,kBAAmBoF,IAAI,KAAI,CAACH,IAAAA,gBAAU,EAACjF,OAAOmF,SAAS,CAACC,IAAI,GAAG;QACjEpF,OAAOmF,SAAS,CAACC,IAAI,GAAGF,IAAAA,aAAO,EAAClF,OAAOmF,SAAS,CAACC,IAAI;QACrD,IAAI,CAACpH,QAAQ;YACXQ,KAAIc,IAAI,CACN,CAAC,0CAA0C,EAAEU,OAAOmF,SAAS,CAACC,IAAI,EAAE;QAExE;IACF;IAEA,6BAA6B;IAC7B,IAAI3D,QAAQC,GAAG,CAAC2D,kBAAkB,EAAE;QAClCrF,OAAOsF,YAAY,GAAG7D,QAAQC,GAAG,CAAC2D,kBAAkB;IACtD;IAEA,MAAME,cAAcvF,0BAAAA,OAAQgF,qBAAqB;IACjD,MAAMQ,gBAAgBxF,2BAAAA,qBAAAA,OAAQmF,SAAS,qBAAjBnF,mBAAmBoF,IAAI;IAE7C,4EAA4E;IAC5E,IAAIG,eAAeC,iBAAiBD,gBAAgBC,eAAe;QACjEhH,KAAIc,IAAI,CACN,CAAC,mGAAmG,CAAC,GACnG,CAAC,uCAAuC,EAAEiG,YAAY,CAAC,CAAC;IAE9D;IAEA,MAAME,UAAUF,eAAeC,iBAAiBE,IAAAA,qBAAW,EAAC7G;IAE5D,IAAI,CAAC4G,SAAS;QACZ,MAAM,qBAEL,CAFK,IAAIhF,MACR,gFADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,mDAAmD;IACnDT,OAAOgF,qBAAqB,GAAGS;IAC/BE,IAAAA,UAAI,EAAC3F,QAAQ;QAAC;QAAa;KAAO,EAAEyF;IAEpCG,IAAAA,+CAA4B,EAAC5F,UAAUgB,2BAAa;IAEpD,IAAIhB,OAAOhB,IAAI,EAAE;QACf,MAAM,EAAEA,IAAI,EAAE,GAAGgB;QACjB,MAAM6F,WAAW,OAAO7G;QAExB,IAAI6G,aAAa,UAAU;YACzB,MAAM,qBAEL,CAFK,IAAIpF,MACR,CAAC,4CAA4C,EAAEoF,SAAS,2EAA2E,CAAC,GADhI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI,CAACjF,MAAMC,OAAO,CAAC7B,KAAK8G,OAAO,GAAG;YAChC,MAAM,qBAEL,CAFK,IAAIrF,MACR,CAAC,mDAAmD,EAAE,OAAOzB,KAAK8G,OAAO,CAAC,2EAA2E,CAAC,GADlJ,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI9G,KAAK8G,OAAO,CAACtG,MAAM,GAAG,OAAO,CAACxB,QAAQ;YACxCQ,KAAIc,IAAI,CACN,CAAC,SAAS,EAAEN,KAAK8G,OAAO,CAACtG,MAAM,CAAC,mLAAmL,CAAC;QAExN;QAEA,MAAMuG,oBAAoB,OAAO/G,KAAKgH,aAAa;QAEnD,IAAI,CAAChH,KAAKgH,aAAa,IAAID,sBAAsB,UAAU;YACzD,MAAM,qBAEL,CAFK,IAAItF,MACR,CAAC,0HAA0H,CAAC,GADxH,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI,OAAOzB,KAAK+E,OAAO,KAAK,eAAe,CAACnD,MAAMC,OAAO,CAAC7B,KAAK+E,OAAO,GAAG;YACvE,MAAM,qBAEL,CAFK,IAAItD,MACR,CAAC,2IAA2I,EAAE,OAAOzB,KAAK+E,OAAO,CAAC,2EAA2E,CAAC,GAD1O,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI/E,KAAK+E,OAAO,EAAE;YAChB,MAAMkC,qBAAqBjH,KAAK+E,OAAO,CAACmC,MAAM,CAAC,CAACC;oBAYfnH;gBAX/B,IAAI,CAACmH,QAAQ,OAAOA,SAAS,UAAU,OAAO;gBAC9C,IAAI,CAACA,KAAKH,aAAa,EAAE,OAAO;gBAChC,IAAI,CAACG,KAAKC,MAAM,IAAI,OAAOD,KAAKC,MAAM,KAAK,UAAU,OAAO;gBAE5D,IAAID,KAAKC,MAAM,CAAC1C,QAAQ,CAAC,MAAM;oBAC7B2C,QAAQ/G,IAAI,CACV,CAAC,cAAc,EAAE6G,KAAKC,MAAM,CAAC,2GAA2G,CAAC;oBAE3I,OAAO;gBACT;gBAEA,MAAME,0BAAyBtH,gBAAAA,KAAK+E,OAAO,qBAAZ/E,cAAcuH,IAAI,CAC/C,CAACC,UACCA,QAAQR,aAAa,KAAKG,KAAKH,aAAa,IAC5CQ,QAAQJ,MAAM,KAAKD,KAAKC,MAAM;gBAGlC,IAAI,CAACpI,UAAUsI,wBAAwB;oBACrCD,QAAQ/G,IAAI,CACV,CAAC,KAAK,EAAE6G,KAAKC,MAAM,CAAC,KAAK,EAAEE,uBAAuBF,MAAM,CAAC,8BAA8B,EAAED,KAAKH,aAAa,CAAC,+DAA+D,CAAC;oBAE9K,OAAO;gBACT;gBAEA,IAAIS,mBAAmB;gBAEvB,IAAI7F,MAAMC,OAAO,CAACsF,KAAKL,OAAO,GAAG;oBAC/B,KAAK,MAAMY,UAAUP,KAAKL,OAAO,CAAE;wBACjC,IAAI,OAAOY,WAAW,UAAUD,mBAAmB;wBAEnD,KAAK,MAAME,cAAc3H,KAAK+E,OAAO,IAAI,EAAE,CAAE;4BAC3C,IAAI4C,eAAeR,MAAM;4BACzB,IAAIQ,WAAWb,OAAO,IAAIa,WAAWb,OAAO,CAACpC,QAAQ,CAACgD,SAAS;gCAC7DL,QAAQ/G,IAAI,CACV,CAAC,KAAK,EAAE6G,KAAKC,MAAM,CAAC,KAAK,EAAEO,WAAWP,MAAM,CAAC,wBAAwB,EAAEM,OAAO,sEAAsE,CAAC;gCAEvJD,mBAAmB;gCACnB;4BACF;wBACF;oBACF;gBACF;gBAEA,OAAOA;YACT;YAEA,IAAIR,mBAAmBzG,MAAM,GAAG,GAAG;gBACjC,MAAM,qBAML,CANK,IAAIiB,MACR,CAAC,8BAA8B,EAAEwF,mBAC9B7C,GAAG,CAAC,CAAC+C,OAAcS,KAAKC,SAAS,CAACV,OAClC9B,IAAI,CACH,MACA,8KAA8K,CAAC,GAL/K,qBAAA;2BAAA;gCAAA;kCAAA;gBAMN;YACF;QACF;QAEA,IAAI,CAACzD,MAAMC,OAAO,CAAC7B,KAAK8G,OAAO,GAAG;YAChC,MAAM,qBAEL,CAFK,IAAIrF,MACR,CAAC,2FAA2F,EAAE,OAAOzB,KAAK8G,OAAO,CAAC,2EAA2E,CAAC,GAD1L,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAMgB,iBAAiB9H,KAAK8G,OAAO,CAACI,MAAM,CACxC,CAACQ,SAAgB,OAAOA,WAAW;QAGrC,IAAII,eAAetH,MAAM,GAAG,GAAG;YAC7B,MAAM,qBAOL,CAPK,IAAIiB,MACR,CAAC,gDAAgD,EAAEqG,eAChD1D,GAAG,CAAC2D,QACJ1C,IAAI,CACH,MACA,wEAAwE,CAAC,GAC3E,CAAC,+HAA+H,CAAC,GAN/H,qBAAA;uBAAA;4BAAA;8BAAA;YAON;QACF;QAEA,IAAI,CAACrF,KAAK8G,OAAO,CAACpC,QAAQ,CAAC1E,KAAKgH,aAAa,GAAG;YAC9C,MAAM,qBAEL,CAFK,IAAIvF,MACR,CAAC,0IAA0I,CAAC,GADxI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAMuG,oBAAoB,IAAIC;QAC9B,MAAMC,mBAAmB,IAAID;QAE7BjI,KAAK8G,OAAO,CAAChF,OAAO,CAAC,CAAC4F;YACpB,MAAMS,cAAcT,OAAOU,WAAW;YACtC,IAAIJ,kBAAkBK,GAAG,CAACF,cAAc;gBACtCD,iBAAiBI,GAAG,CAACZ;YACvB;YACAM,kBAAkBM,GAAG,CAACH;QACxB;QAEA,IAAID,iBAAiBK,IAAI,GAAG,GAAG;YAC7B,MAAM,qBAKL,CALK,IAAI9G,MACR,CAAC,kEAAkE,CAAC,GAClE,GAAG;mBAAIyG;aAAiB,CAAC7C,IAAI,CAAC,MAAM,EAAE,CAAC,GACvC,CAAC,yCAAyC,CAAC,GAC3C,CAAC,wEAAwE,CAAC,GAJxE,qBAAA;uBAAA;4BAAA;8BAAA;YAKN;QACF;QAEA,2CAA2C;QAC3CrF,KAAK8G,OAAO,GAAG;YACb9G,KAAKgH,aAAa;eACfhH,KAAK8G,OAAO,CAACI,MAAM,CAAC,CAACQ,SAAWA,WAAW1H,KAAKgH,aAAa;SACjE;QAED,MAAMwB,sBAAsB,OAAOxI,KAAKyI,eAAe;QAEvD,IACED,wBAAwB,aACxBA,wBAAwB,aACxB;YACA,MAAM,qBAEL,CAFK,IAAI/G,MACR,CAAC,yEAAyE,EAAE+G,oBAAoB,2EAA2E,CAAC,GADxK,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;IAEA,IAAIxH,OAAOuE,aAAa,KAAK,WAASvE,wBAAAA,OAAOuE,aAAa,qBAApBvE,sBAAsByE,QAAQ,GAAE;QACpE,MAAM,EAAEA,QAAQ,EAAE,GAAGzE,OAAOuE,aAAa;QACzC,MAAMmD,gBAAgB;YACpB;YACA;YACA;YACA;SACD;QAED,IAAI,CAACA,cAAchE,QAAQ,CAACe,WAAW;YACrC,MAAM,qBAIL,CAJK,IAAIhE,MACR,CAAC,0DAA0D,EAAEiH,cAAcrD,IAAI,CAC7E,MACA,WAAW,EAAEI,UAAU,GAHrB,qBAAA;uBAAA;4BAAA;8BAAA;YAIN;QACF;IACF;IAEA,IAAIzE,OAAOlB,YAAY,EAAE;YAElBkC,6BAGkBA,uCAAAA,8BAKpBA,wCAAAA;QATHhB,OAAOlB,YAAY,CAAC6I,SAAS,GAAG;gBAC3B3G,8BAAAA,2BAAa,CAAClC,YAAY,qBAA1BkC,4BAA4B2G,SAAS,AAAxC;YACA,GAAG3H,OAAOlB,YAAY,CAAC6I,SAAS;QAClC;QACA,MAAMC,kBAAiB5G,+BAAAA,2BAAa,CAAClC,YAAY,sBAA1BkC,wCAAAA,6BAA4B2G,SAAS,qBAArC3G,qCAAuC,CAAC,UAAU;QACzE,IACE,CAAC4G,kBACDA,eAAeC,UAAU,KAAKtJ,aAC9BqJ,eAAeE,MAAM,KAAKvJ,aAC1B,GAACyC,+BAAAA,2BAAa,CAAClC,YAAY,sBAA1BkC,yCAAAA,6BAA4B+G,UAAU,qBAAtC/G,uCAAwCgH,MAAM,GAC/C;YACA,MAAM,qBAA0C,CAA1C,IAAIvH,MAAM,kCAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAyC;QACjD;QACA,MAAMwH,0BAA0BjI,OAAOlB,YAAY,CAAC6I,SAAS,CAAC,UAAU;QACxE,IAAI,CAACM,yBAAyB;YAC5BjI,OAAOlB,YAAY,CAAC6I,SAAS,CAAC,UAAU,GAAGC;QAC7C,OAAO;YACL,IAAIK,wBAAwBC,KAAK,KAAK3J,WAAW;oBACvByB,iCAEHgB,yCAAAA;gBAFrB,MAAMmH,mBAAkBnI,kCAAAA,OAAOlB,YAAY,CAACiJ,UAAU,qBAA9B/H,gCAAgCgI,MAAM;gBAC9DC,wBAAwBC,KAAK,GAC3BC,qBAAmBnH,+BAAAA,2BAAa,CAAClC,YAAY,sBAA1BkC,0CAAAA,6BAA4B+G,UAAU,qBAAtC/G,wCAAwCgH,MAAM;YACrE;YACA,IAAIC,wBAAwBJ,UAAU,KAAKtJ,WAAW;gBACpD0J,wBAAwBJ,UAAU,GAAGD,eAAeC,UAAU;YAChE;YACA,IAAII,wBAAwBH,MAAM,KAAKvJ,WAAW;gBAChD0J,wBAAwBH,MAAM,GAC5B9H,OAAOoI,UAAU,IAAIR,eAAeE,MAAM;YAC9C;QACF;IACF;IAEA,KAAI9H,wBAAAA,OAAOlB,YAAY,qBAAnBkB,sBAAqBqI,aAAa,EAAE;QACtC,MAAMC,0BAA0B;QAEhC,IAAI,OAAOtI,OAAOlB,YAAY,CAACuJ,aAAa,KAAK,UAAU;YACzD,MAAM,qBAEL,CAFK,IAAI5H,MACR,CAAC,+GAA+G,EAAEmG,KAAKC,SAAS,CAAC7G,OAAOlB,YAAY,CAACuJ,aAAa,GAAG,GADjK,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAME,cAAcnI,OAAOC,IAAI,CAACL,OAAOlB,YAAY,CAACuJ,aAAa;QACjE,MAAMG,sBAA8D,EAAE;QAEtE,KAAK,MAAMlK,OAAOiK,YAAa;YAC7B,IAAIjK,QAAQ,WAAW;gBACrBkK,oBAAoBtF,IAAI,CAAC;oBACvB5E;oBACAP,QACE;gBACJ;YACF,OAAO,IAAI,CAACuK,wBAAwBG,IAAI,CAACnK,MAAM;gBAC7CkK,oBAAoBtF,IAAI,CAAC;oBACvB5E;oBACAP,QAAQ;gBACV;YACF,OAAO;gBACL,MAAM2K,cAAc,AAClB1I,OAAOlB,YAAY,CAACuJ,aAAa,AAGlC,CAAC/J,IAAI;gBAEN,IAAIoK,eAAe,CAACpE,IAAAA,cAAU,EAACoE,cAAc;oBAC3CF,oBAAoBtF,IAAI,CAAC;wBACvB5E;wBACAP,QAAQ,CAAC,qDAAqD,EAAE2K,aAAa;oBAC/E;gBACF;YACF;YACA,IAAIF,oBAAoBhJ,MAAM,EAAE;gBAC9B,MAAM,qBAEL,CAFK,IAAIiB,MACR,CAAC,oEAAoE,EAAE+H,oBAAoBpF,GAAG,CAAC,CAAC+C,OAAS,GAAG7H,IAAI,EAAE,EAAE6H,KAAKpI,MAAM,EAAE,EAAEsG,IAAI,CAAC,OAAO,GAD3I,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;IACF;IAEA,MAAMsE,gCAAgC3I,OAAO4I,iBAAiB;IAC9D,kJAAkJ;IAClJ,6EAA6E;IAC7E5I,OAAO4I,iBAAiB,GAAG;QACzB,GAAID,iCAAiC,CAAC,CAAC;QACvC,gFAAgF;QAChF,uBAAuB;YACrBE,WAAW;QACb;QACAC,QAAQ;YACND,WAAW;QACb;IACF;IAEA,MAAME,qCACJ/I,EAAAA,wBAAAA,OAAOlB,YAAY,qBAAnBkB,sBAAqBgJ,sBAAsB,KAAI,EAAE;IAEnDhJ,OAAOlB,YAAY,CAACkK,sBAAsB,GAAG;WACxC,IAAI/B,IAAI;eACN8B;YACH;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,4EAA4E;YAC5E,mCAAmC;YACnC,0EAA0E;YAC1E,wBAAwB;YACxB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;KACF;IAED,IAAI,CAAC/I,OAAOiJ,eAAe,EAAE;QAC3B,oGAAoG;QACpGjJ,OAAOiJ,eAAe,GAAGC,oCAA6B;IACxD;IAEA,kFAAkF;IAClF,oFAAoF;IACpF,2BAA2B;IAC3B,IAAIlJ,OAAOlB,YAAY,CAACqK,QAAQ,KAAK5K,WAAW;QAC9CyB,OAAOlB,YAAY,CAACqK,QAAQ,GAAGnJ,OAAOlB,YAAY,CAACqB,eAAe;IACpE;IAEA,qDAAqD;IACrD,IAAIH,OAAOlB,YAAY,CAACqB,eAAe,EAAE;YAErCxB,2BACAA;QAFF,IACEA,EAAAA,4BAAAA,WAAWG,YAAY,qBAAvBH,0BAAyBkD,GAAG,MAAK,SACjClD,EAAAA,4BAAAA,WAAWG,YAAY,qBAAvBH,0BAAyBkD,GAAG,MAAK,eACjC;gBAEsDlD;YADtD,MAAM,qBAEL,CAFK,IAAI8B,MACR,CAAC,kCAAkC,EAAEmG,KAAKC,SAAS,EAAClI,4BAAAA,WAAWG,YAAY,qBAAvBH,0BAAyBkD,GAAG,EAAE,iHAAiH,CAAC,GADhM,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA7B,OAAOlB,YAAY,CAAC+C,GAAG,GAAG;IAC5B;IAEA,OAAO7B;AACT;AAEA,eAAeoJ,kBACbvL,MAA0B,EAC1BwL,KAAa,EACbrL,MAAe;QAMbH;IAJF,IACE,kDAAkD;IAClD,gDAAgD;IAChD;QAACyL,iCAAsB;QAAEC,kCAAuB;KAAC,CAAC7F,QAAQ,CAAC2F,YAC3DxL,uBAAAA,OAAOiB,YAAY,qBAAnBjB,qBAAqB2L,WAAW,GAChC;QACA,MAAMC,aAAaC,IAAAA,8BAAc,EAC/B,MAAM,MAAM,CACVC,IAAAA,kBAAa,EAACC,QAAQ1E,OAAO,CAACrH,OAAOiB,YAAY,CAAC0K,WAAW,GAAGK,IAAI;QAIxE,IAAI,OAAOJ,WAAWK,YAAY,KAAK,YAAY;YACjD,IAAI,CAAC9L,QAAQ;gBACXQ,KAAIuL,IAAI,CAAC,CAAC,2BAA2B,EAAEN,WAAWO,IAAI,EAAE;YAC1D;YACAnM,SAAS,MAAM4L,WAAWK,YAAY,CAACjM;QACzC;IACF;IACA,OAAOA;AACT;AAEA,8EAA8E;AAC9E,MAAMoM,cAAc,IAAIC;AASxB,mEAAmE;AACnE,sEAAsE;AACtE,SAASC,YACPd,KAAa,EACbxK,GAAW,EACXuL,YAA4B,EAC5BC,wBAAkC,EAClCC,cAAwB;IAExB,mFAAmF;IACnF,qCAAqC;IACrC,MAAMC,UAAU3D,KAAKC,SAAS,CAAC;QAC7BhI;QACAwK;QACAmB,iBAAiBtL,QAAQkL;QACzBC,0BAA0BnL,QAAQmL;QAClCC,gBAAgBpL,QAAQoL;IAC1B;IAEA,OAAOG,IAAAA,cAAQ,EAACF,SAASzF,QAAQ,CAAC;AACpC;AAEe,eAAe9H,WAC5BqM,KAAa,EACbxK,GAAW,EACX,EACEuL,YAAY,EACZM,SAAS,EACT1M,SAAS,IAAI,EACb2M,0BAA0B,EAC1BN,wBAAwB,EACxBC,cAAc,EAUf,GAAG,CAAC,CAAC;IAEN,mEAAmE;IACnE,MAAMM,WAAWT,YACfd,OACAxK,KACAuL,cACAC,0BACAC;IAGF,mCAAmC;IACnC,MAAMO,eAAeZ,YAAYa,GAAG,CAACF;IACrC,IAAIC,cAAc;QAChB,sDAAsD;QACtD,IAAIF,4BAA4B;YAC9BA,2BAA2BE,aAAaE,8BAA8B;QACxE;QAEA,+CAA+C;QAC/C,IAAIL,aAAaG,aAAaH,SAAS,EAAE;YACvC,OAAOG,aAAaH,SAAS;QAC/B;QAEA,OAAOG,aAAahN,MAAM;IAC5B;IAEA,6CAA6C;IAC7C,IAAI,CAAC4D,QAAQC,GAAG,CAACsJ,4BAA4B,EAAE;QAC7C,IAAI;YACFC,IAAAA,4BAAe;QACjB,EAAE,OAAOC,KAAK;YACZ,gDAAgD;YAChD,yBAAyB;YACzB,IAAI,CAACzJ,QAAQC,GAAG,CAACyJ,gCAAgC,EAAE;gBACjD,MAAMD;YACR;QACF;IACF;IAEA,IAAIzJ,QAAQC,GAAG,CAACyJ,gCAAgC,EAAE;QAChD,2DAA2D;QAC3D,2BAA2B;QAC3B,MAAMC,mBAAmBxE,KAAKyE,KAAK,CACjC5J,QAAQC,GAAG,CAACyJ,gCAAgC;QAG9C,8BAA8B;QAC9BlB,YAAYqB,GAAG,CAACV,UAAU;YACxB/M,QAAQuN;YACRV,WAAWU;YACXL,gCAAgC,EAAE;QACpC;QAEA,OAAOK;IACT;IAEA,MAAMG,SAASvN,SACX;QACEsB,MAAM,KAAO;QACbyK,MAAM,KAAO;QACb1M,OAAO,KAAO;IAChB,IACAmB;IAEJgN,IAAAA,kBAAa,EAAC3M,KAAKwK,UAAUoC,mCAAwB,EAAEF;IAEvD,IAAI3M,iBAAiB;IACrB,MAAMmM,iCAAkE,EAAE;IAE1E,IAAIX,cAAc;QAChB,+EAA+E;QAC/E1L,kBAAkB0L,cAA4BxL,gBAAgBZ,QAAQa;QAEtE,MAAMhB,SAAS,MAAMuL,kBACnBrJ,eACElB,KACA;YACE6M,cAAc;YACd9M;YACA,GAAGwL,YAAY;QACjB,GACApM,SAEFqL,OACArL;QAGF,iCAAiC;QACjCiM,YAAYqB,GAAG,CAACV,UAAU;YACxB/M;YACA6M,WAAWN;YACXW;QACF;QAEAJ,8CAAAA,2BAA6BI;QAE7B,OAAOlN;IACT;IAEA,MAAMD,OAAO,MAAM+N,IAAAA,eAAM,EAACC,uBAAY,EAAE;QAAEC,KAAKhN;IAAI;IAEnD,2BAA2B;IAC3B,IAAIjB,wBAAAA,KAAM4B,MAAM,EAAE;YAmIZb,iBAcFA,gCAAAA,0BACCA,iCAAAA,2BAmBCA,2BAoBAA;QAxLJC,iBAAiBkN,IAAAA,cAAQ,EAAClO;QAE1B,IAAImO;QACJ,IAAI;YACF,MAAMC,YAAY5L,OAAO6L,MAAM,CAAC,CAAC,GAAGxK,QAAQC,GAAG;YAE/C,uEAAuE;YACvE,sEAAsE;YACtE,8BAA8B;YAC9B,IAAID,QAAQC,GAAG,CAACwK,gBAAgB,KAAK,QAAQ;gBAC3C,4DAA4D;gBAC5D,0DAA0D;gBAC1D,8CAA8C;gBAC9CH,mBAAmBnC,QAAQhM;YAC7B,OAAO,IAAIgB,mBAAmB,kBAAkB;gBAC9CmN,mBAAmB,MAAMI,IAAAA,gCAAe,EAAC;oBACvCC,gBAAgBxO;oBAChBgB;oBACAiN,KAAKhN;gBACP;YACF,OAAO;gBACLkN,mBAAmB,MAAM,MAAM,CAACpC,IAAAA,kBAAa,EAAC/L,MAAMiM,IAAI;YAC1D;YACA,MAAMwC,SAA6B,CAAC;YAEpC,KAAK,MAAM/N,OAAO8B,OAAOC,IAAI,CAACoB,QAAQC,GAAG,EAAG;gBAC1C,IAAIsK,SAAS,CAAC1N,IAAI,KAAKmD,QAAQC,GAAG,CAACpD,IAAI,EAAE;oBACvC+N,MAAM,CAAC/N,IAAI,GAAGmD,QAAQC,GAAG,CAACpD,IAAI;gBAChC;YACF;YACAgO,IAAAA,qBAAgB,EAACD;YAEjB,IAAI3B,WAAW;gBACb,uBAAuB;gBACvBT,YAAYqB,GAAG,CAACV,UAAU;oBACxB/M,QAAQkO;oBACRrB,WAAWqB;oBACXhB;gBACF;gBAEAJ,8CAAAA,2BAA6BI;gBAE7B,OAAOgB;YACT;QACF,EAAE,OAAOb,KAAK;YACZ,0EAA0E;YAC1EK,OAAOlO,KAAK,CACV,CAAC,eAAe,EAAEuB,eAAe,uEAAuE,CAAC;YAE3G,MAAMsM;QACR;QAEA,MAAMqB,eAAenM,OAAOoM,MAAM,CAC/B,MAAMvP,IAAAA,6BAAe,EACpBoM,OACAK,IAAAA,8BAAc,EAACqC;QAInB,IAAIQ,aAAazN,YAAY,EAAE;YAC7B,KAAK,MAAMkL,QAAQ5J,OAAOC,IAAI,CAC5BkM,aAAazN,YAAY,EACQ;gBACjC,MAAM0B,QAAQ+L,aAAazN,YAAY,CAACkL,KAAK;gBAE7C,IAAIA,SAAS,WAAW,CAACvI,QAAQC,GAAG,CAAC+K,SAAS,EAAE;oBAE9C;gBACF;gBAEAC,iCACE3B,gCACAf,MACAxJ;YAEJ;QACF;QAEA,kEAAkE;QAClE,MAAM7B,aAAagO,YAAYJ;QAE/B,oFAAoF;QACpF7N,kBAAkBC,YAAYC,gBAAgBZ,QAAQa;QAEtD,iEAAiE;QACjE,2EAA2E;QAC3E,MAAM+N,gBAAgB,OAAOnL,QAAQoL,IAAI,KAAK;QAC9C,IAAI,CAACpL,QAAQC,GAAG,CAACoL,YAAY,IAAIF,eAAe;YAC9C,iEAAiE;YACjE,MAAM,EAAEG,YAAY,EAAE,GACpBnD,QAAQ;YACV,MAAMoD,QAAQD,aAAaE,SAAS,CAACtO;YAErC,IAAI,CAACqO,MAAME,OAAO,EAAE;gBAClB,uBAAuB;gBACvB,MAAMC,WAAW;oBAAC,CAAC,QAAQ,EAAEvO,eAAe,mBAAmB,CAAC;iBAAC;gBAEjE,MAAM,CAACwO,eAAe9P,WAAW,GAAGF,6BAClC4P,MAAM3P,KAAK;gBAEb,kBAAkB;gBAClB,KAAK,MAAMA,SAAS+P,cAAe;oBACjCD,SAASjK,IAAI,CAAC,CAAC,IAAI,EAAE7F,OAAO;gBAC9B;gBAEA,uBAAuB;gBACvB8P,SAASjK,IAAI,CACX;gBAGF,IAAI5F,YAAY;oBACd,KAAK,MAAMK,WAAWwP,SAAU;wBAC9B9G,QAAQhJ,KAAK,CAACM;oBAChB;oBACA,MAAM0P,IAAAA,0BAAY,EAAC;gBACrB,OAAO;oBACL,KAAK,MAAM1P,WAAWwP,SAAU;wBAC9B5B,OAAOjM,IAAI,CAAC3B;oBACd;gBACF;YACF;QACF;QAEA,IAAIgB,WAAW2O,MAAM,IAAI3O,WAAW2O,MAAM,KAAK,UAAU;YACvD,MAAM,qBAGL,CAHK,IAAI7M,MACR,CAAC,gDAAgD,EAAE7B,eAAe,GAAG,CAAC,GACpE,iFAFE,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QAEA,KAAID,kBAAAA,WAAW8D,GAAG,qBAAd9D,gBAAgB+D,aAAa,EAAE;YACjC,MAAM,EAAEA,aAAa,EAAE,GAAG/D,WAAW8D,GAAG,IAAK,CAAC;YAC9C9D,WAAW8D,GAAG,GAAG9D,WAAW8D,GAAG,IAAI,CAAC;YACpC9D,WAAW8D,GAAG,CAACC,aAAa,GAC1B,AAACA,CAAAA,CAAAA,iCAAAA,cAAeF,QAAQ,CAAC,QACrBE,cAAc6K,KAAK,CAAC,GAAG,CAAC,KACxB7K,aAAY,KAAM;QAC1B;QAEA,IAAI2H,0BAA0B;YAC5B1L,WAAW0L,wBAAwB,GAAGA;QACxC;QAEA,IACE1L,EAAAA,2BAAAA,WAAWG,YAAY,sBAAvBH,iCAAAA,yBAAyB6O,KAAK,qBAA9B7O,+BAAgC8O,OAAO,KACvC,GAAC9O,4BAAAA,WAAWG,YAAY,sBAAvBH,kCAAAA,0BAAyB6O,KAAK,qBAA9B7O,gCAAgC+O,KAAK,GACtC;YACAnC,OAAOjM,IAAI,CACT,sIACE,uFACA,4FACA;YAGJ,MAAMoO,QAA+C,CAAC;YACtD,KAAK,MAAM,CAAC3M,KAAK0M,QAAQ,IAAIrN,OAAOuN,OAAO,CACzChP,WAAWG,YAAY,CAAC0O,KAAK,CAACC,OAAO,EACpC;gBACDC,KAAK,CAAC,MAAM3M,IAAI,GAAG0M;YACrB;YAEA9O,WAAWG,YAAY,CAAC0O,KAAK,CAACE,KAAK,GAAGA;QACxC;QAEA,KAAI/O,4BAAAA,WAAWG,YAAY,qBAAvBH,0BAAyB6O,KAAK,EAAE;YAClCjC,OAAOjM,IAAI,CACT;YAGF,kEAAkE;YAClEX,WAAWwG,SAAS,GAAG;gBACrB,GAAGxG,WAAWG,YAAY,CAAC0O,KAAK;gBAChC,GAAG7O,WAAWwG,SAAS;YACzB;YACAxG,WAAWG,YAAY,CAAC8O,oBAAoB,KAC1CjP,WAAWG,YAAY,CAAC0O,KAAK,CAACK,WAAW;YAC3ClP,WAAWG,YAAY,CAACgP,eAAe,KACrCnP,WAAWG,YAAY,CAAC0O,KAAK,CAACO,MAAM;YACtCpP,WAAWG,YAAY,CAACkP,oBAAoB,KAC1CrP,WAAWG,YAAY,CAAC0O,KAAK,CAACS,WAAW;YAC3CtP,WAAWG,YAAY,CAACoP,mBAAmB,KACzCvP,WAAWG,YAAY,CAAC0O,KAAK,CAACW,UAAU;QAC5C;QAEA,KAAIxP,4BAAAA,WAAWG,YAAY,qBAAvBH,0BAAyByP,eAAe,EAAE;gBAGf,MAAC;YAF9B,MAAM,EAAEC,YAAY,EAAE,GACpBzE,QAAQ;YACV,MAAM0E,wBAAwB,QAAA,MAAMD,oCAAP,OAAA,AAAC,MAAuBE,GAAG,qBAA3B,KAA6BC,SAAS;YAEnE,IAAI,CAACF,sBAAsB;gBACzB/C,OAAOjM,IAAI,CACT,CAAC,+GAA+G,CAAC;gBAEnHX,WAAWG,YAAY,CAACsP,eAAe,GAAG;YAC5C;QACF;QAEA,yCAAyC;QACzC,IAAIzP,CAAAA,8BAAAA,WAAYsK,eAAe,aAAYwF,QAAQ;YACjD,oGAAoG;YACpG9P,WAAWsK,eAAe,GAAGtK,WAAWsK,eAAe,CAACyF,MAAM;QAChE;QAEAC,4BAA4BhQ,YAAY;YACtCiQ,iBAAiB;YACjB7D;YACAT;YACAjB;QACF;QAEA,MAAMwF,iBAAiB9O,eACrBlB,KACA;YACE6M,cAAcoD,IAAAA,cAAQ,EAACjQ,KAAKjB;YAC5BmR,YAAYnR;YACZgB;YACA,GAAGD,UAAU;QACf,GACAX;QAGF,MAAMgR,cAAc,MAAM5F,kBAAkByF,gBAAgBxF,OAAOrL;QAEnE,yBAAyB;QACzBiM,YAAYqB,GAAG,CAACV,UAAU;YACxB/M,QAAQmR;YACRtE,WAAWqB;YACXhB;QACF;QAEA,IAAIJ,4BAA4B;YAC9BA,2BAA2BI;QAC7B;QAEA,OAAOiE;IACT,OAAO;QACL,MAAMC,iBAAiBnD,IAAAA,cAAQ,EAACF,uBAAY,CAAC,EAAE,EAAEsD,IAAAA,aAAO,EAACtD,uBAAY,CAAC,EAAE;QACxE,MAAMuD,oBAAoBxD,eAAM,CAACyD,IAAI,CACnC;YACE,GAAGH,eAAe,IAAI,CAAC;YACvB,GAAGA,eAAe,IAAI,CAAC;YACvB,GAAGA,eAAe,IAAI,CAAC;YACvB,GAAGA,eAAe,KAAK,CAAC;YACxB,GAAGA,eAAe,IAAI,CAAC;YACvB,GAAGA,eAAe,IAAI,CAAC;SACxB,EACD;YAAEpD,KAAKhN;QAAI;QAEb,IAAIsQ,qCAAAA,kBAAmB3P,MAAM,EAAE;YAC7B,MAAM,qBAIL,CAJK,IAAIiB,MACR,CAAC,yBAAyB,EAAEqL,IAAAA,cAAQ,EAClCqD,mBACA,0GAA0G,CAAC,GAHzG,qBAAA;uBAAA;4BAAA;8BAAA;YAIN;QACF;IACF;IAEA,MAAME,sBAAsB1C,YAAY3L,2BAAa;IAErD2N,4BAA4BU,qBAAqB;QAC/CT,iBAAiB;QACjB7D;QACAT;QACAjB;IACF;IAEA,qDAAqD;IACrD,iEAAiE;IACjE,MAAMwF,iBAAiB9O,eACrBlB,KACA;QAAE,GAAGwQ,mBAAmB;QAAEzQ;IAAe,GACzCZ;IAGF4H,IAAAA,+CAA4B,EAACiJ;IAE7B,MAAMG,cAAc,MAAM5F,kBAAkByF,gBAAgBxF,OAAOrL;IAEnE,kCAAkC;IAClCiM,YAAYqB,GAAG,CAACV,UAAU;QACxB/M,QAAQmR;QACRtE,WAAW2E;QACXtE;IACF;IAEA,IAAIJ,4BAA4B;QAC9BA,2BAA2BI;IAC7B;IAEA,OAAOiE;AACT;AAQA,SAASL,4BACP9Q,MAAkB,EAClByR,OAKC;IAED,MAAM,EACJvE,8BAA8B,EAC9BT,cAAc,EACdsE,eAAe,EACfvF,KAAK,EACN,GAAGiG;IAEJzR,OAAOiB,YAAY,KAAK,CAAC;IAEzB,IACEwL,kBACCjB,CAAAA,UAAUC,iCAAsB,IAAID,UAAUkG,uBAAY,AAAD,GAC1D;QACAC,wCACE3R,OAAOiB,YAAY,EACnB,oBACA,MACAiM;QAGFyE,wCACE3R,OAAOiB,YAAY,EACnB2C,QAAQC,GAAG,CAAC+K,SAAS,GAAG,oBAAoB,sBAC5C,OACA1B;QAGFyE,wCACE3R,OAAOiB,YAAY,EACnB,6BACA,MACAiM;QAGFyE,wCACE3R,OAAOiB,YAAY,EACnB,sBACA,OACAiM;IAEJ;IAEA,kEAAkE;IAClE,IACEtJ,QAAQC,GAAG,CAAC+N,oCAAoC,KAAK,UACrD,sDAAsD;IACrD5R,CAAAA,OAAOiB,YAAY,CAAC+C,GAAG,KAAKtD,aAC1BqQ,mBAAmB,CAAC/Q,OAAOiB,YAAY,CAAC+C,GAAG,GAC9C;QACAhE,OAAOiB,YAAY,CAAC+C,GAAG,GAAG;QAE1B,IAAIkJ,gCAAgC;YAClC2B,iCACE3B,gCACA,OACA,MACA;QAEJ;IACF;IAEA,kEAAkE;IAClE,IACEtJ,QAAQC,GAAG,CAACgO,uBAAuB,KAAK,UACxC,sDAAsD;IACrD7R,CAAAA,OAAOiB,YAAY,CAAC+C,GAAG,KAAKtD,aAC1BqQ,mBAAmB,CAAC/Q,OAAOiB,YAAY,CAAC+C,GAAG,GAC9C;QACAhE,OAAOiB,YAAY,CAAC+C,GAAG,GAAG;QAE1B,IAAIkJ,gCAAgC;YAClC2B,iCACE3B,gCACA,OACA,MACA;QAEJ;IACF;IAEA,sEAAsE;IACtE,IACEtJ,QAAQC,GAAG,CAACgO,uBAAuB,KAAK,UACxC,sDAAsD;IACrD7R,CAAAA,OAAOiB,YAAY,CAAC6Q,kBAAkB,KAAKpR,aACzCqQ,mBAAmB,CAAC/Q,OAAOiB,YAAY,CAAC6Q,kBAAkB,GAC7D;QACA9R,OAAOiB,YAAY,CAAC6Q,kBAAkB,GAAG;QAEzC,IAAI5E,gCAAgC;YAClC2B,iCACE3B,gCACA,sBACA,MACA;QAEJ;IACF;IAEA,sEAAsE;IACtE,IACEtJ,QAAQC,GAAG,CAACgO,uBAAuB,KAAK,UACxC,sDAAsD;IACrD7R,CAAAA,OAAOiB,YAAY,CAAC8Q,kBAAkB,KAAKrR,aACzCqQ,mBAAmB,CAAC/Q,OAAOiB,YAAY,CAAC8Q,kBAAkB,GAC7D;QACA/R,OAAOiB,YAAY,CAAC8Q,kBAAkB,GAAG;QAEzC,IAAI7E,gCAAgC;YAClC2B,iCACE3B,gCACA,sBACA,MACA;QAEJ;IACF;IAEA,kEAAkE;IAClE,IACEtJ,QAAQC,GAAG,CAAC+N,oCAAoC,KAAK,UACrD,sDAAsD;IACrD5R,CAAAA,OAAOiB,YAAY,CAACqB,eAAe,KAAK5B,aACtCqQ,mBAAmB,CAAC/Q,OAAOiB,YAAY,CAACqB,eAAe,GAC1D;QACAtC,OAAOiB,YAAY,CAACqB,eAAe,GAAG;QAEtC,IAAI4K,gCAAgC;YAClC2B,iCACE3B,gCACA,mBACA,MACA;QAEJ;IACF;IAEA,IACElN,OAAOiB,YAAY,CAAC+Q,yBAAyB,KAAKtR,aAClDV,OAAOiB,YAAY,CAACqB,eAAe,KAAK,MACxC;QACAtC,OAAOiB,YAAY,CAAC+Q,yBAAyB,GAAG;QAEhD,IAAI9E,gCAAgC;YAClC2B,iCACE3B,gCACA,6BACA,MACA;QAEJ;IACF;AACF;AAEA,SAAS2B,iCAGP3B,8BAA+D,EAC/DzM,GAAY,EACZkC,KAAkC,EAClCzC,MAAe;IAEf,IAAIyC,UAAU,AAACQ,2BAAa,CAAClC,YAAY,AAA4B,CAACR,IAAI,EAAE;QAC1EyM,+BAA+B7H,IAAI,CAAC;YAAE5E;YAAKkC;YAAOzC;QAAO;IAC3D;AACF;AAEA,SAASyR,wCAGPM,kBAAsC,EACtCxR,GAAY,EACZkC,KAAkC,EAClCuK,8BAA2E;IAE3E,IAAI+E,kBAAkB,CAACxR,IAAI,KAAKkC,OAAO;QACrCsP,kBAAkB,CAACxR,IAAI,GAAGkC;QAE1B,IAAIuK,gCAAgC;YAClC,MAAMgF,SACJvP,UAAU,OAAO,YAAYA,UAAU,QAAQ,aAAa;YAE9D,MAAMzC,SAAS,GAAGgS,OAAO,yBAAyB,CAAC;YAEnDrD,iCACE3B,gCACAzM,KACAkC,OACAzC;QAEJ;IACF;AACF;AAEA,SAAS4O,YAAYqD,GAAQ;IAC3B,oBAAoB;IACpB,IAAIA,QAAQ,QAAQ,OAAOA,QAAQ,UAAU;QAC3C,OAAOA;IACT;IAEA,iCAAiC;IACjC,IAAIA,eAAevB,QAAQ;QACzB,OAAO,IAAIA,OAAOuB,IAAItB,MAAM,EAAEsB,IAAIC,KAAK;IACzC;IAEA,+CAA+C;IAC/C,IAAI,OAAOD,QAAQ,YAAY;QAC7B,OAAOA;IACT;IAEA,4BAA4B;IAC5B,IAAIpP,MAAMC,OAAO,CAACmP,MAAM;QACtB,OAAOA,IAAI5M,GAAG,CAACuJ;IACjB;IAEA,6CAA6C;IAC7C,MAAMnJ,QAAQpD,OAAO8P,cAAc,CAACF;IACpC,MAAMG,gBAAgB3M,UAAUpD,OAAOgQ,SAAS,IAAI5M,UAAU;IAE9D,uDAAuD;IACvD,IAAI,CAAC2M,eAAe;QAClB,OAAOH;IACT;IAEA,6DAA6D;IAC7D,+DAA+D;IAC/D,+CAA+C;IAC/C,MAAMhQ,SAASI,OAAOiQ,MAAM,CAAC7M;IAC7B,KAAK,MAAMlF,OAAOgS,QAAQC,OAAO,CAACP,KAAM;QACtC,MAAMQ,aAAapQ,OAAOqQ,wBAAwB,CAACT,KAAK1R;QAExD,IAAIkS,cAAeA,CAAAA,WAAW1F,GAAG,IAAI0F,WAAWlF,GAAG,AAAD,GAAI;YACpD,gEAAgE;YAChElL,OAAOsQ,cAAc,CAAC1Q,QAAQ1B,KAAKkS;QACrC,OAAO;YACL,kCAAkC;YAClCxQ,MAAM,CAAC1B,IAAI,GAAGqO,YAAYqD,GAAG,CAAC1R,IAAI;QACpC;IACF;IAEA,OAAO0B;AACT", "ignoreList": [0]}