"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuotesService = void 0;
const common_1 = require("@nestjs/common");
let QuotesService = class QuotesService {
    quotes = [
        "The only way to do great work is to love what you do. - <PERSON>",
        "Innovation distinguishes between a leader and a follower. - <PERSON>",
        "Life is what happens to you while you're busy making other plans. - <PERSON>",
        "The future belongs to those who believe in the beauty of their dreams. - <PERSON>",
        "It is during our darkest moments that we must focus to see the light. - <PERSON>",
        "The way to get started is to quit talking and begin doing. - <PERSON> <PERSON>",
        "Don't let yesterday take up too much of today. - Will <PERSON>",
        "You learn more from failure than from success. Don't let it stop you. Failure builds character. - Unknown",
        "If you are working on something that you really care about, you don't have to be pushed. The vision pulls you. - <PERSON> Jobs",
        "People who are crazy enough to think they can change the world, are the ones who do. - Rob Siltanen",
        "Believe you can and you're halfway there. - Theodore Roosevelt",
        "The only impossible journey is the one you never begin. - Tony Robbins",
        "Success is not final, failure is not fatal: it is the courage to continue that counts. - Winston Churchill",
        "The greatest glory in living lies not in never falling, but in rising every time we fall. - Nelson Mandela",
        "Your time is limited, don't waste it living someone else's life. - Steve Jobs",
        "If life were predictable it would cease to be life, and be without flavor. - Eleanor Roosevelt",
        "In the end, we will remember not the words of our enemies, but the silence of our friends. - Martin Luther King Jr.",
        "The purpose of our lives is to be happy. - Dalai Lama",
        "Life is really simple, but we insist on making it complicated. - Confucius",
        "May you live all the days of your life. - Jonathan Swift"
    ];
    getRandomQuote() {
        const randomIndex = Math.floor(Math.random() * this.quotes.length);
        return {
            quote: this.quotes[randomIndex],
            timestamp: new Date().toISOString(),
        };
    }
};
exports.QuotesService = QuotesService;
exports.QuotesService = QuotesService = __decorate([
    (0, common_1.Injectable)()
], QuotesService);
//# sourceMappingURL=quotes.service.js.map