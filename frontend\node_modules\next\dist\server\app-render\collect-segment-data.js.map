{"version": 3, "sources": ["../../../src/server/app-render/collect-segment-data.tsx"], "sourcesContent": ["import type {\n  CacheNodeSeedData,\n  FlightRouterState,\n  InitialRSCPayload,\n  DynamicParamTypesShort,\n} from './types'\nimport type { ManifestNode } from '../../build/webpack/plugins/flight-manifest-plugin'\n\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport { createFromReadableStream } from 'react-server-dom-webpack/client'\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport { unstable_prerender as prerender } from 'react-server-dom-webpack/static'\n\nimport {\n  streamFromBuffer,\n  streamToBuffer,\n} from '../stream-utils/node-web-streams-helper'\nimport { waitAtLeastOneReactRenderTask } from '../../lib/scheduler'\nimport type {\n  HeadData,\n  LoadingModuleData,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport {\n  type SegmentRequestKey,\n  createSegmentRequestKeyPart,\n  appendSegmentRequestKeyPart,\n  ROOT_SEGMENT_REQUEST_KEY,\n} from '../../shared/lib/segment-cache/segment-value-encoding'\nimport { getDigestForWellKnownError } from './create-error-handler'\n\n// Contains metadata about the route tree. The client must fetch this before\n// it can fetch any actual segment data.\nexport type RootTreePrefetch = {\n  buildId: string\n  tree: TreePrefetch\n  head: HeadData\n  isHeadPartial: boolean\n  staleTime: number\n}\n\nexport type TreePrefetch = {\n  name: string\n  paramType: DynamicParamTypesShort | null\n  // TODO: When clientParamParsing is enabled, this field is always null.\n  // Instead we parse the param on the client, allowing us to omit it from\n  // the prefetch response and increase its cacheability. Remove this field\n  // once clientParamParsing is enabled everywhere.\n  paramKey: string | null\n\n  // Child segments.\n  slots: null | {\n    [parallelRouteKey: string]: TreePrefetch\n  }\n\n  // Extra fields that only exist so we can reconstruct a FlightRouterState on\n  // the client. We may be able to unify TreePrefetch and FlightRouterState\n  // after some refactoring, but in the meantime it would be wasteful to add a\n  // bunch of new prefetch-only fields to FlightRouterState. So think of\n  // TreePrefetch as a superset of FlightRouterState.\n  isRootLayout: boolean\n}\n\nexport type SegmentPrefetch = {\n  buildId: string\n  rsc: React.ReactNode | null\n  loading: LoadingModuleData | Promise<LoadingModuleData>\n  isPartial: boolean\n}\n\nconst filterStackFrame =\n  process.env.NODE_ENV !== 'production'\n    ? (require('../lib/source-maps') as typeof import('../lib/source-maps'))\n        .filterStackFrameDEV\n    : undefined\nconst findSourceMapURL =\n  process.env.NODE_ENV !== 'production'\n    ? (require('../lib/source-maps') as typeof import('../lib/source-maps'))\n        .findSourceMapURLDEV\n    : undefined\n\nfunction onSegmentPrerenderError(error: unknown) {\n  const digest = getDigestForWellKnownError(error)\n  if (digest) {\n    return digest\n  }\n  // We don't need to log the errors because we would have already done that\n  // when generating the original Flight stream for the whole page.\n}\n\nexport async function collectSegmentData(\n  isClientParamParsingEnabled: boolean,\n  fullPageDataBuffer: Buffer,\n  staleTime: number,\n  clientModules: ManifestNode,\n  serverConsumerManifest: any\n): Promise<Map<SegmentRequestKey, Buffer>> {\n  // Traverse the router tree and generate a prefetch response for each segment.\n\n  // A mutable map to collect the results as we traverse the route tree.\n  const resultMap = new Map<SegmentRequestKey, Buffer>()\n\n  // Before we start, warm up the module cache by decoding the page data once.\n  // Then we can assume that any remaining async tasks that occur the next time\n  // are due to hanging promises caused by dynamic data access. Note we only\n  // have to do this once per page, not per individual segment.\n  //\n  try {\n    await createFromReadableStream(streamFromBuffer(fullPageDataBuffer), {\n      findSourceMapURL,\n      serverConsumerManifest,\n    })\n    await waitAtLeastOneReactRenderTask()\n  } catch {}\n\n  // Create an abort controller that we'll use to stop the stream.\n  const abortController = new AbortController()\n  const onCompletedProcessingRouteTree = async () => {\n    // Since all we're doing is decoding and re-encoding a cached prerender, if\n    // serializing the stream takes longer than a microtask, it must because of\n    // hanging promises caused by dynamic data.\n    await waitAtLeastOneReactRenderTask()\n    abortController.abort()\n  }\n\n  // Generate a stream for the route tree prefetch. While we're walking the\n  // tree, we'll also spawn additional tasks to generate the segment prefetches.\n  // The promises for these tasks are pushed to a mutable array that we will\n  // await once the route tree is fully rendered.\n  const segmentTasks: Array<Promise<[SegmentRequestKey, Buffer]>> = []\n  const { prelude: treeStream } = await prerender(\n    // RootTreePrefetch is not a valid return type for a React component, but\n    // we need to use a component so that when we decode the original stream\n    // inside of it, the side effects are transferred to the new stream.\n    // @ts-expect-error\n    <PrefetchTreeData\n      isClientParamParsingEnabled={isClientParamParsingEnabled}\n      fullPageDataBuffer={fullPageDataBuffer}\n      serverConsumerManifest={serverConsumerManifest}\n      clientModules={clientModules}\n      staleTime={staleTime}\n      segmentTasks={segmentTasks}\n      onCompletedProcessingRouteTree={onCompletedProcessingRouteTree}\n    />,\n    clientModules,\n    {\n      filterStackFrame,\n      signal: abortController.signal,\n      onError: onSegmentPrerenderError,\n    }\n  )\n\n  // Write the route tree to a special `/_tree` segment.\n  const treeBuffer = await streamToBuffer(treeStream)\n  resultMap.set('/_tree' as SegmentRequestKey, treeBuffer)\n\n  // Now that we've finished rendering the route tree, all the segment tasks\n  // should have been spawned. Await them in parallel and write the segment\n  // prefetches to the result map.\n  for (const [segmentPath, buffer] of await Promise.all(segmentTasks)) {\n    resultMap.set(segmentPath, buffer)\n  }\n\n  return resultMap\n}\n\nasync function PrefetchTreeData({\n  isClientParamParsingEnabled,\n  fullPageDataBuffer,\n  serverConsumerManifest,\n  clientModules,\n  staleTime,\n  segmentTasks,\n  onCompletedProcessingRouteTree,\n}: {\n  isClientParamParsingEnabled: boolean\n  fullPageDataBuffer: Buffer\n  serverConsumerManifest: any\n  clientModules: ManifestNode\n  staleTime: number\n  segmentTasks: Array<Promise<[SegmentRequestKey, Buffer]>>\n  onCompletedProcessingRouteTree: () => void\n}): Promise<RootTreePrefetch | null> {\n  // We're currently rendering a Flight response for the route tree prefetch.\n  // Inside this component, decode the Flight stream for the whole page. This is\n  // a hack to transfer the side effects from the original Flight stream (e.g.\n  // Float preloads) onto the Flight stream for the tree prefetch.\n  // TODO: React needs a better way to do this. Needed for Server Actions, too.\n  const initialRSCPayload: InitialRSCPayload = await createFromReadableStream(\n    createUnclosingPrefetchStream(streamFromBuffer(fullPageDataBuffer)),\n    {\n      findSourceMapURL,\n      serverConsumerManifest,\n    }\n  )\n\n  const buildId = initialRSCPayload.b\n\n  // FlightDataPath is an unsound type, hence the additional checks.\n  const flightDataPaths = initialRSCPayload.f\n  if (flightDataPaths.length !== 1 && flightDataPaths[0].length !== 3) {\n    console.error(\n      'Internal Next.js error: InitialRSCPayload does not match the expected ' +\n        'shape for a prerendered page during segment prefetch generation.'\n    )\n    return null\n  }\n  const flightRouterState: FlightRouterState = flightDataPaths[0][0]\n  const seedData: CacheNodeSeedData = flightDataPaths[0][1]\n  const head: HeadData = flightDataPaths[0][2]\n\n  // Compute the route metadata tree by traversing the FlightRouterState. As we\n  // walk the tree, we will also spawn a task to produce a prefetch response for\n  // each segment.\n  const tree = collectSegmentDataImpl(\n    isClientParamParsingEnabled,\n    flightRouterState,\n    buildId,\n    seedData,\n    clientModules,\n    ROOT_SEGMENT_REQUEST_KEY,\n    segmentTasks\n  )\n\n  const isHeadPartial = await isPartialRSCData(head, clientModules)\n\n  // Notify the abort controller that we're done processing the route tree.\n  // Anything async that happens after this point must be due to hanging\n  // promises in the original stream.\n  onCompletedProcessingRouteTree()\n\n  // Render the route tree to a special `/_tree` segment.\n  const treePrefetch: RootTreePrefetch = {\n    buildId,\n    tree,\n    head,\n    isHeadPartial,\n    staleTime,\n  }\n  return treePrefetch\n}\n\nfunction collectSegmentDataImpl(\n  isClientParamParsingEnabled: boolean,\n  route: FlightRouterState,\n  buildId: string,\n  seedData: CacheNodeSeedData | null,\n  clientModules: ManifestNode,\n  requestKey: SegmentRequestKey,\n  segmentTasks: Array<Promise<[string, Buffer]>>\n): TreePrefetch {\n  // Metadata about the segment. Sent as part of the tree prefetch. Null if\n  // there are no children.\n  let slotMetadata: { [parallelRouteKey: string]: TreePrefetch } | null = null\n\n  const children = route[1]\n  const seedDataChildren = seedData !== null ? seedData[2] : null\n  for (const parallelRouteKey in children) {\n    const childRoute = children[parallelRouteKey]\n    const childSegment = childRoute[0]\n    const childSeedData =\n      seedDataChildren !== null ? seedDataChildren[parallelRouteKey] : null\n\n    const childRequestKey = appendSegmentRequestKeyPart(\n      requestKey,\n      parallelRouteKey,\n      createSegmentRequestKeyPart(childSegment)\n    )\n    const childTree = collectSegmentDataImpl(\n      isClientParamParsingEnabled,\n      childRoute,\n      buildId,\n      childSeedData,\n      clientModules,\n      childRequestKey,\n      segmentTasks\n    )\n    if (slotMetadata === null) {\n      slotMetadata = {}\n    }\n    slotMetadata[parallelRouteKey] = childTree\n  }\n\n  if (seedData !== null) {\n    // Spawn a task to write the segment data to a new Flight stream.\n    segmentTasks.push(\n      // Since we're already in the middle of a render, wait until after the\n      // current task to escape the current rendering context.\n      waitAtLeastOneReactRenderTask().then(() =>\n        renderSegmentPrefetch(buildId, seedData, requestKey, clientModules)\n      )\n    )\n  } else {\n    // This segment does not have any seed data. Skip generating a prefetch\n    // response for it. We'll still include it in the route tree, though.\n    // TODO: We should encode in the route tree whether a segment is missing\n    // so we don't attempt to fetch it for no reason. As of now this shouldn't\n    // ever happen in practice, though.\n  }\n\n  const segment = route[0]\n  let name\n  let paramType: DynamicParamTypesShort | null = null\n  let paramKey: string | null = null\n  if (typeof segment === 'string') {\n    name = segment\n    paramKey = segment\n    paramType = null\n  } else {\n    name = segment[0]\n    paramKey = segment[1]\n    paramType = segment[2] as DynamicParamTypesShort\n  }\n\n  // Metadata about the segment. Sent to the client as part of the\n  // tree prefetch.\n  return {\n    name,\n    paramType,\n    // This value is ommitted from the prefetch response when clientParamParsing\n    // is enabled. The flag only exists while we're testing the feature, in\n    // case there's a bug and we need to revert.\n    // TODO: Remove once clientParamParsing is enabled everywhere.\n    paramKey: isClientParamParsingEnabled ? null : paramKey,\n    slots: slotMetadata,\n    isRootLayout: route[4] === true,\n  }\n}\n\nasync function renderSegmentPrefetch(\n  buildId: string,\n  seedData: CacheNodeSeedData,\n  requestKey: SegmentRequestKey,\n  clientModules: ManifestNode\n): Promise<[SegmentRequestKey, Buffer]> {\n  // Render the segment data to a stream.\n  // In the future, this is where we can include additional metadata, like the\n  // stale time and cache tags.\n  const rsc = seedData[1]\n  const loading = seedData[3]\n  const segmentPrefetch: SegmentPrefetch = {\n    buildId,\n    rsc,\n    loading,\n    isPartial: await isPartialRSCData(rsc, clientModules),\n  }\n  // Since all we're doing is decoding and re-encoding a cached prerender, if\n  // it takes longer than a microtask, it must because of hanging promises\n  // caused by dynamic data. Abort the stream at the end of the current task.\n  const abortController = new AbortController()\n  waitAtLeastOneReactRenderTask().then(() => abortController.abort())\n  const { prelude: segmentStream } = await prerender(\n    segmentPrefetch,\n    clientModules,\n    {\n      filterStackFrame,\n      signal: abortController.signal,\n      onError: onSegmentPrerenderError,\n    }\n  )\n  const segmentBuffer = await streamToBuffer(segmentStream)\n  if (requestKey === ROOT_SEGMENT_REQUEST_KEY) {\n    return ['/_index' as SegmentRequestKey, segmentBuffer]\n  } else {\n    return [requestKey, segmentBuffer]\n  }\n}\n\nasync function isPartialRSCData(\n  rsc: React.ReactNode,\n  clientModules: ManifestNode\n): Promise<boolean> {\n  // We can determine if a segment contains only partial data if it takes longer\n  // than a task to encode, because dynamic data is encoded as an infinite\n  // promise. We must do this in a separate Flight prerender from the one that\n  // actually generates the prefetch stream because we need to include\n  // `isPartial` in the stream itself.\n  let isPartial = false\n  const abortController = new AbortController()\n  waitAtLeastOneReactRenderTask().then(() => {\n    // If we haven't yet finished the outer task, then it must be because we\n    // accessed dynamic data.\n    isPartial = true\n    abortController.abort()\n  })\n  await prerender(rsc, clientModules, {\n    filterStackFrame,\n    signal: abortController.signal,\n    onError() {},\n    onPostpone() {\n      // If something postponed, i.e. when Cache Components is not enabled, we can\n      // infer that the RSC data is partial.\n      isPartial = true\n    },\n  })\n  return isPartial\n}\n\nfunction createUnclosingPrefetchStream(\n  originalFlightStream: ReadableStream<Uint8Array>\n): ReadableStream<Uint8Array> {\n  // When PPR is enabled, prefetch streams may contain references that never\n  // resolve, because that's how we encode dynamic data access. In the decoded\n  // object returned by the Flight client, these are reified into hanging\n  // promises that suspend during render, which is effectively what we want.\n  // The UI resolves when it switches to the dynamic data stream\n  // (via useDeferredValue(dynamic, static)).\n  //\n  // However, the Flight implementation currently errors if the server closes\n  // the response before all the references are resolved. As a cheat to work\n  // around this, we wrap the original stream in a new stream that never closes,\n  // and therefore doesn't error.\n  const reader = originalFlightStream.getReader()\n  return new ReadableStream({\n    async pull(controller) {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (!done) {\n          // Pass to the target stream and keep consuming the Flight response\n          // from the server.\n          controller.enqueue(value)\n          continue\n        }\n        // The server stream has closed. Exit, but intentionally do not close\n        // the target stream.\n        return\n      }\n    },\n  })\n}\n"], "names": ["collectSegmentData", "filterStackFrame", "process", "env", "NODE_ENV", "require", "filterStackFrameDEV", "undefined", "findSourceMapURL", "findSourceMapURLDEV", "onSegmentPrerenderError", "error", "digest", "getDigestForWellKnownError", "isClientParamParsingEnabled", "fullPageDataBuffer", "staleTime", "clientModules", "serverConsumerManifest", "resultMap", "Map", "createFromReadableStream", "streamFromBuffer", "waitAtLeastOneReactRenderTask", "abortController", "AbortController", "onCompletedProcessingRouteTree", "abort", "segmentTasks", "prelude", "treeStream", "prerender", "PrefetchTreeData", "signal", "onError", "treeBuffer", "streamToBuffer", "set", "segmentPath", "buffer", "Promise", "all", "initialRSCPayload", "createUnclosingPrefetchStream", "buildId", "b", "flightDataPaths", "f", "length", "console", "flightRouterState", "seedData", "head", "tree", "collectSegmentDataImpl", "ROOT_SEGMENT_REQUEST_KEY", "isHeadPartial", "isPartialRSCData", "treePrefetch", "route", "request<PERSON>ey", "slotMetadata", "children", "seedDataChildren", "parallelRouteKey", "childRoute", "childSegment", "childSeedData", "childRequest<PERSON>ey", "appendSegmentRequestKeyPart", "createSegmentRequestKeyPart", "childTree", "push", "then", "renderSegmentPrefetch", "segment", "name", "paramType", "<PERSON><PERSON><PERSON><PERSON>", "slots", "isRootLayout", "rsc", "loading", "segmentPrefetch", "isPartial", "segmentStream", "segmentBuffer", "onPostpone", "originalFlightStream", "reader", "<PERSON><PERSON><PERSON><PERSON>", "ReadableStream", "pull", "controller", "done", "value", "read", "enqueue"], "mappings": ";;;;+BAyFsBA;;;eAAAA;;;;wBAhFmB;wBAEO;sCAKzC;2BACuC;sCAUvC;oCACoC;AAyC3C,MAAMC,mBACJC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACrB,AAACC,QAAQ,sBACNC,mBAAmB,GACtBC;AACN,MAAMC,mBACJN,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACrB,AAACC,QAAQ,sBACNI,mBAAmB,GACtBF;AAEN,SAASG,wBAAwBC,KAAc;IAC7C,MAAMC,SAASC,IAAAA,8CAA0B,EAACF;IAC1C,IAAIC,QAAQ;QACV,OAAOA;IACT;AACA,0EAA0E;AAC1E,iEAAiE;AACnE;AAEO,eAAeZ,mBACpBc,2BAAoC,EACpCC,kBAA0B,EAC1BC,SAAiB,EACjBC,aAA2B,EAC3BC,sBAA2B;IAE3B,8EAA8E;IAE9E,sEAAsE;IACtE,MAAMC,YAAY,IAAIC;IAEtB,4EAA4E;IAC5E,6EAA6E;IAC7E,0EAA0E;IAC1E,6DAA6D;IAC7D,EAAE;IACF,IAAI;QACF,MAAMC,IAAAA,gCAAwB,EAACC,IAAAA,sCAAgB,EAACP,qBAAqB;YACnEP;YACAU;QACF;QACA,MAAMK,IAAAA,wCAA6B;IACrC,EAAE,OAAM,CAAC;IAET,gEAAgE;IAChE,MAAMC,kBAAkB,IAAIC;IAC5B,MAAMC,iCAAiC;QACrC,2EAA2E;QAC3E,2EAA2E;QAC3E,2CAA2C;QAC3C,MAAMH,IAAAA,wCAA6B;QACnCC,gBAAgBG,KAAK;IACvB;IAEA,yEAAyE;IACzE,8EAA8E;IAC9E,0EAA0E;IAC1E,+CAA+C;IAC/C,MAAMC,eAA4D,EAAE;IACpE,MAAM,EAAEC,SAASC,UAAU,EAAE,GAAG,MAAMC,IAAAA,0BAAS,EAC7C,yEAAyE;IACzE,wEAAwE;IACxE,oEAAoE;IACpE,mBAAmB;kBACnB,qBAACC;QACClB,6BAA6BA;QAC7BC,oBAAoBA;QACpBG,wBAAwBA;QACxBD,eAAeA;QACfD,WAAWA;QACXY,cAAcA;QACdF,gCAAgCA;QAElCT,eACA;QACEhB;QACAgC,QAAQT,gBAAgBS,MAAM;QAC9BC,SAASxB;IACX;IAGF,sDAAsD;IACtD,MAAMyB,aAAa,MAAMC,IAAAA,oCAAc,EAACN;IACxCX,UAAUkB,GAAG,CAAC,UAA+BF;IAE7C,0EAA0E;IAC1E,yEAAyE;IACzE,gCAAgC;IAChC,KAAK,MAAM,CAACG,aAAaC,OAAO,IAAI,CAAA,MAAMC,QAAQC,GAAG,CAACb,aAAY,EAAG;QACnET,UAAUkB,GAAG,CAACC,aAAaC;IAC7B;IAEA,OAAOpB;AACT;AAEA,eAAea,iBAAiB,EAC9BlB,2BAA2B,EAC3BC,kBAAkB,EAClBG,sBAAsB,EACtBD,aAAa,EACbD,SAAS,EACTY,YAAY,EACZF,8BAA8B,EAS/B;IACC,2EAA2E;IAC3E,8EAA8E;IAC9E,4EAA4E;IAC5E,gEAAgE;IAChE,6EAA6E;IAC7E,MAAMgB,oBAAuC,MAAMrB,IAAAA,gCAAwB,EACzEsB,8BAA8BrB,IAAAA,sCAAgB,EAACP,sBAC/C;QACEP;QACAU;IACF;IAGF,MAAM0B,UAAUF,kBAAkBG,CAAC;IAEnC,kEAAkE;IAClE,MAAMC,kBAAkBJ,kBAAkBK,CAAC;IAC3C,IAAID,gBAAgBE,MAAM,KAAK,KAAKF,eAAe,CAAC,EAAE,CAACE,MAAM,KAAK,GAAG;QACnEC,QAAQtC,KAAK,CACX,2EACE;QAEJ,OAAO;IACT;IACA,MAAMuC,oBAAuCJ,eAAe,CAAC,EAAE,CAAC,EAAE;IAClE,MAAMK,WAA8BL,eAAe,CAAC,EAAE,CAAC,EAAE;IACzD,MAAMM,OAAiBN,eAAe,CAAC,EAAE,CAAC,EAAE;IAE5C,6EAA6E;IAC7E,8EAA8E;IAC9E,gBAAgB;IAChB,MAAMO,OAAOC,uBACXxC,6BACAoC,mBACAN,SACAO,UACAlC,eACAsC,8CAAwB,EACxB3B;IAGF,MAAM4B,gBAAgB,MAAMC,iBAAiBL,MAAMnC;IAEnD,yEAAyE;IACzE,sEAAsE;IACtE,mCAAmC;IACnCS;IAEA,uDAAuD;IACvD,MAAMgC,eAAiC;QACrCd;QACAS;QACAD;QACAI;QACAxC;IACF;IACA,OAAO0C;AACT;AAEA,SAASJ,uBACPxC,2BAAoC,EACpC6C,KAAwB,EACxBf,OAAe,EACfO,QAAkC,EAClClC,aAA2B,EAC3B2C,UAA6B,EAC7BhC,YAA8C;IAE9C,yEAAyE;IACzE,yBAAyB;IACzB,IAAIiC,eAAoE;IAExE,MAAMC,WAAWH,KAAK,CAAC,EAAE;IACzB,MAAMI,mBAAmBZ,aAAa,OAAOA,QAAQ,CAAC,EAAE,GAAG;IAC3D,IAAK,MAAMa,oBAAoBF,SAAU;QACvC,MAAMG,aAAaH,QAAQ,CAACE,iBAAiB;QAC7C,MAAME,eAAeD,UAAU,CAAC,EAAE;QAClC,MAAME,gBACJJ,qBAAqB,OAAOA,gBAAgB,CAACC,iBAAiB,GAAG;QAEnE,MAAMI,kBAAkBC,IAAAA,iDAA2B,EACjDT,YACAI,kBACAM,IAAAA,iDAA2B,EAACJ;QAE9B,MAAMK,YAAYjB,uBAChBxC,6BACAmD,YACArB,SACAuB,eACAlD,eACAmD,iBACAxC;QAEF,IAAIiC,iBAAiB,MAAM;YACzBA,eAAe,CAAC;QAClB;QACAA,YAAY,CAACG,iBAAiB,GAAGO;IACnC;IAEA,IAAIpB,aAAa,MAAM;QACrB,iEAAiE;QACjEvB,aAAa4C,IAAI,CACf,sEAAsE;QACtE,wDAAwD;QACxDjD,IAAAA,wCAA6B,IAAGkD,IAAI,CAAC,IACnCC,sBAAsB9B,SAASO,UAAUS,YAAY3C;IAG3D,OAAO;IACL,uEAAuE;IACvE,qEAAqE;IACrE,wEAAwE;IACxE,0EAA0E;IAC1E,mCAAmC;IACrC;IAEA,MAAM0D,UAAUhB,KAAK,CAAC,EAAE;IACxB,IAAIiB;IACJ,IAAIC,YAA2C;IAC/C,IAAIC,WAA0B;IAC9B,IAAI,OAAOH,YAAY,UAAU;QAC/BC,OAAOD;QACPG,WAAWH;QACXE,YAAY;IACd,OAAO;QACLD,OAAOD,OAAO,CAAC,EAAE;QACjBG,WAAWH,OAAO,CAAC,EAAE;QACrBE,YAAYF,OAAO,CAAC,EAAE;IACxB;IAEA,gEAAgE;IAChE,iBAAiB;IACjB,OAAO;QACLC;QACAC;QACA,4EAA4E;QAC5E,uEAAuE;QACvE,4CAA4C;QAC5C,8DAA8D;QAC9DC,UAAUhE,8BAA8B,OAAOgE;QAC/CC,OAAOlB;QACPmB,cAAcrB,KAAK,CAAC,EAAE,KAAK;IAC7B;AACF;AAEA,eAAee,sBACb9B,OAAe,EACfO,QAA2B,EAC3BS,UAA6B,EAC7B3C,aAA2B;IAE3B,uCAAuC;IACvC,4EAA4E;IAC5E,6BAA6B;IAC7B,MAAMgE,MAAM9B,QAAQ,CAAC,EAAE;IACvB,MAAM+B,UAAU/B,QAAQ,CAAC,EAAE;IAC3B,MAAMgC,kBAAmC;QACvCvC;QACAqC;QACAC;QACAE,WAAW,MAAM3B,iBAAiBwB,KAAKhE;IACzC;IACA,2EAA2E;IAC3E,wEAAwE;IACxE,2EAA2E;IAC3E,MAAMO,kBAAkB,IAAIC;IAC5BF,IAAAA,wCAA6B,IAAGkD,IAAI,CAAC,IAAMjD,gBAAgBG,KAAK;IAChE,MAAM,EAAEE,SAASwD,aAAa,EAAE,GAAG,MAAMtD,IAAAA,0BAAS,EAChDoD,iBACAlE,eACA;QACEhB;QACAgC,QAAQT,gBAAgBS,MAAM;QAC9BC,SAASxB;IACX;IAEF,MAAM4E,gBAAgB,MAAMlD,IAAAA,oCAAc,EAACiD;IAC3C,IAAIzB,eAAeL,8CAAwB,EAAE;QAC3C,OAAO;YAAC;YAAgC+B;SAAc;IACxD,OAAO;QACL,OAAO;YAAC1B;YAAY0B;SAAc;IACpC;AACF;AAEA,eAAe7B,iBACbwB,GAAoB,EACpBhE,aAA2B;IAE3B,8EAA8E;IAC9E,wEAAwE;IACxE,4EAA4E;IAC5E,oEAAoE;IACpE,oCAAoC;IACpC,IAAImE,YAAY;IAChB,MAAM5D,kBAAkB,IAAIC;IAC5BF,IAAAA,wCAA6B,IAAGkD,IAAI,CAAC;QACnC,wEAAwE;QACxE,yBAAyB;QACzBW,YAAY;QACZ5D,gBAAgBG,KAAK;IACvB;IACA,MAAMI,IAAAA,0BAAS,EAACkD,KAAKhE,eAAe;QAClChB;QACAgC,QAAQT,gBAAgBS,MAAM;QAC9BC,YAAW;QACXqD;YACE,4EAA4E;YAC5E,sCAAsC;YACtCH,YAAY;QACd;IACF;IACA,OAAOA;AACT;AAEA,SAASzC,8BACP6C,oBAAgD;IAEhD,0EAA0E;IAC1E,4EAA4E;IAC5E,uEAAuE;IACvE,0EAA0E;IAC1E,8DAA8D;IAC9D,2CAA2C;IAC3C,EAAE;IACF,2EAA2E;IAC3E,0EAA0E;IAC1E,8EAA8E;IAC9E,+BAA+B;IAC/B,MAAMC,SAASD,qBAAqBE,SAAS;IAC7C,OAAO,IAAIC,eAAe;QACxB,MAAMC,MAAKC,UAAU;YACnB,MAAO,KAAM;gBACX,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMN,OAAOO,IAAI;gBACzC,IAAI,CAACF,MAAM;oBACT,mEAAmE;oBACnE,mBAAmB;oBACnBD,WAAWI,OAAO,CAACF;oBACnB;gBACF;gBACA,qEAAqE;gBACrE,qBAAqB;gBACrB;YACF;QACF;IACF;AACF", "ignoreList": [0]}