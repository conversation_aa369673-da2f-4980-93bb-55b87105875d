{"version": 3, "sources": ["../../../../src/shared/lib/turbopack/manifest-loader.ts"], "sourcesContent": ["import type {\n  EdgeFunctionDefinition,\n  MiddlewareManifest,\n} from '../../../build/webpack/plugins/middleware-plugin'\nimport type {\n  StatsAsset,\n  StatsChunk,\n  StatsChunkGroup,\n  StatsModule,\n  StatsCompilation as WebpackStats,\n} from 'webpack'\nimport type { BuildManifest } from '../../../server/get-page-files'\nimport type { AppBuildManifest } from '../../../build/webpack/plugins/app-build-manifest-plugin'\nimport type { PagesManifest } from '../../../build/webpack/plugins/pages-manifest-plugin'\nimport type { ActionManifest } from '../../../build/webpack/plugins/flight-client-entry-plugin'\nimport type { NextFontManifest } from '../../../build/webpack/plugins/next-font-manifest-plugin'\nimport type { REACT_LOADABLE_MANIFEST } from '../constants'\nimport {\n  APP_BUILD_MANIFEST,\n  APP_PATHS_MANIFEST,\n  BUILD_MANIFEST,\n  INTERCEPTION_ROUTE_REWRITE_MANIFEST,\n  MIDDLEWARE_BUILD_MANIFEST,\n  MIDDLEWARE_MANIFEST,\n  NEXT_FONT_MANIFEST,\n  PAGES_MANIFEST,\n  SERVER_REFERENCE_MANIFEST,\n  TURBOPACK_CLIENT_BUILD_MANIFEST,\n  TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST,\n  WEBPACK_STATS,\n} from '../constants'\nimport { join, posix } from 'path'\nimport { readFile } from 'fs/promises'\nimport type { SetupOpts } from '../../../server/lib/router-utils/setup-dev-bundler'\nimport { deleteCache } from '../../../server/dev/require-cache'\nimport { writeFileAtomic } from '../../../lib/fs/write-atomic'\nimport { isInterceptionRouteRewrite } from '../../../lib/generate-interception-routes-rewrites'\nimport {\n  type ClientBuildManifest,\n  normalizeRewritesForBuildManifest,\n  srcEmptySsgManifest,\n  processRoute,\n  createEdgeRuntimeManifest,\n} from '../../../build/webpack/plugins/build-manifest-plugin'\nimport getAssetPathFromRoute from '../router/utils/get-asset-path-from-route'\nimport { getEntryKey, type EntryKey } from './entry-key'\nimport type { CustomRoutes } from '../../../lib/load-custom-routes'\nimport { getSortedRoutes } from '../router/utils'\nimport { existsSync } from 'fs'\nimport {\n  addMetadataIdToRoute,\n  addRouteSuffix,\n  removeRouteSuffix,\n} from '../../../server/dev/turbopack-utils'\nimport { tryToParsePath } from '../../../lib/try-to-parse-path'\nimport { safePathToRegexp } from '../router/utils/route-match-utils'\nimport type { Entrypoints } from '../../../build/swc/types'\n\ninterface InstrumentationDefinition {\n  files: string[]\n  name: 'instrumentation'\n}\n\ntype TurbopackMiddlewareManifest = MiddlewareManifest & {\n  instrumentation?: InstrumentationDefinition\n}\n\ntype ManifestName =\n  | typeof MIDDLEWARE_MANIFEST\n  | typeof BUILD_MANIFEST\n  | typeof APP_BUILD_MANIFEST\n  | typeof PAGES_MANIFEST\n  | typeof WEBPACK_STATS\n  | typeof APP_PATHS_MANIFEST\n  | `${typeof SERVER_REFERENCE_MANIFEST}.json`\n  | `${typeof NEXT_FONT_MANIFEST}.json`\n  | typeof REACT_LOADABLE_MANIFEST\n  | typeof TURBOPACK_CLIENT_BUILD_MANIFEST\n\nconst getManifestPath = (\n  page: string,\n  distDir: string,\n  name: ManifestName,\n  type: string,\n  firstCall: boolean\n) => {\n  let manifestPath = posix.join(\n    distDir,\n    `server`,\n    type,\n    type === 'middleware' || type === 'instrumentation'\n      ? ''\n      : type === 'app'\n        ? page\n        : getAssetPathFromRoute(page),\n    name\n  )\n\n  if (firstCall) {\n    const isSitemapRoute = /[\\\\/]sitemap(.xml)?\\/route$/.test(page)\n    // Check the ambiguity of /sitemap and /sitemap.xml\n    if (isSitemapRoute && !existsSync(manifestPath)) {\n      manifestPath = getManifestPath(\n        page.replace(/\\/sitemap\\/route$/, '/sitemap.xml/route'),\n        distDir,\n        name,\n        type,\n        false\n      )\n    }\n    // existsSync is faster than using the async version\n    if (!existsSync(manifestPath) && page.endsWith('/route')) {\n      // TODO: Improve implementation of metadata routes, currently it requires this extra check for the variants of the files that can be written.\n      let metadataPage = addRouteSuffix(\n        addMetadataIdToRoute(removeRouteSuffix(page))\n      )\n      manifestPath = getManifestPath(metadataPage, distDir, name, type, false)\n    }\n  }\n\n  return manifestPath\n}\n\nasync function readPartialManifest<T>(\n  distDir: string,\n  name: ManifestName,\n  pageName: string,\n  type: 'pages' | 'app' | 'middleware' | 'instrumentation' = 'pages'\n): Promise<T> {\n  const page = pageName\n  const manifestPath = getManifestPath(page, distDir, name, type, true)\n  return JSON.parse(await readFile(posix.join(manifestPath), 'utf-8')) as T\n}\n\nexport class TurbopackManifestLoader {\n  private actionManifests: Map<EntryKey, ActionManifest> = new Map()\n  private appBuildManifests: Map<EntryKey, AppBuildManifest> = new Map()\n  private appPathsManifests: Map<EntryKey, PagesManifest> = new Map()\n  private buildManifests: Map<EntryKey, BuildManifest> = new Map()\n  private clientBuildManifests: Map<EntryKey, ClientBuildManifest> = new Map()\n  private fontManifests: Map<EntryKey, NextFontManifest> = new Map()\n  private middlewareManifests: Map<EntryKey, TurbopackMiddlewareManifest> =\n    new Map()\n  private pagesManifests: Map<string, PagesManifest> = new Map()\n  private webpackStats: Map<EntryKey, WebpackStats> = new Map()\n  private encryptionKey: string\n\n  private readonly distDir: string\n  private readonly buildId: string\n\n  constructor({\n    distDir,\n    buildId,\n    encryptionKey,\n  }: {\n    buildId: string\n    distDir: string\n    encryptionKey: string\n  }) {\n    this.distDir = distDir\n    this.buildId = buildId\n    this.encryptionKey = encryptionKey\n  }\n\n  delete(key: EntryKey) {\n    this.actionManifests.delete(key)\n    this.appBuildManifests.delete(key)\n    this.appPathsManifests.delete(key)\n    this.buildManifests.delete(key)\n    this.clientBuildManifests.delete(key)\n    this.fontManifests.delete(key)\n    this.middlewareManifests.delete(key)\n    this.pagesManifests.delete(key)\n    this.webpackStats.delete(key)\n  }\n\n  async loadActionManifest(pageName: string): Promise<void> {\n    this.actionManifests.set(\n      getEntryKey('app', 'server', pageName),\n      await readPartialManifest(\n        this.distDir,\n        `${SERVER_REFERENCE_MANIFEST}.json`,\n        pageName,\n        'app'\n      )\n    )\n  }\n\n  private async mergeActionManifests(manifests: Iterable<ActionManifest>) {\n    type ActionEntries = ActionManifest['edge' | 'node']\n    const manifest: ActionManifest = {\n      node: {},\n      edge: {},\n      encryptionKey: this.encryptionKey,\n    }\n\n    function mergeActionIds(\n      actionEntries: ActionEntries,\n      other: ActionEntries\n    ): void {\n      for (const key in other) {\n        const action = (actionEntries[key] ??= {\n          workers: {},\n          layer: {},\n        })\n        action.filename = other[key].filename\n        action.exportedName = other[key].exportedName\n        Object.assign(action.workers, other[key].workers)\n        Object.assign(action.layer, other[key].layer)\n      }\n    }\n\n    for (const m of manifests) {\n      mergeActionIds(manifest.node, m.node)\n      mergeActionIds(manifest.edge, m.edge)\n    }\n    for (const key in manifest.node) {\n      const entry = manifest.node[key]\n      entry.workers = sortObjectByKey(entry.workers)\n      entry.layer = sortObjectByKey(entry.layer)\n    }\n    for (const key in manifest.edge) {\n      const entry = manifest.edge[key]\n      entry.workers = sortObjectByKey(entry.workers)\n      entry.layer = sortObjectByKey(entry.layer)\n    }\n\n    return manifest\n  }\n\n  private async writeActionManifest(): Promise<void> {\n    const actionManifest = await this.mergeActionManifests(\n      this.actionManifests.values()\n    )\n    const actionManifestJsonPath = join(\n      this.distDir,\n      'server',\n      `${SERVER_REFERENCE_MANIFEST}.json`\n    )\n    const actionManifestJsPath = join(\n      this.distDir,\n      'server',\n      `${SERVER_REFERENCE_MANIFEST}.js`\n    )\n    const json = JSON.stringify(actionManifest, null, 2)\n    deleteCache(actionManifestJsonPath)\n    deleteCache(actionManifestJsPath)\n    await writeFileAtomic(actionManifestJsonPath, json)\n    await writeFileAtomic(\n      actionManifestJsPath,\n      `self.__RSC_SERVER_MANIFEST=${JSON.stringify(json)}`\n    )\n  }\n\n  async loadAppBuildManifest(pageName: string): Promise<void> {\n    this.appBuildManifests.set(\n      getEntryKey('app', 'server', pageName),\n      await readPartialManifest(\n        this.distDir,\n        APP_BUILD_MANIFEST,\n        pageName,\n        'app'\n      )\n    )\n  }\n\n  private mergeAppBuildManifests(manifests: Iterable<AppBuildManifest>) {\n    const manifest: AppBuildManifest = {\n      pages: {},\n    }\n    for (const m of manifests) {\n      Object.assign(manifest.pages, m.pages)\n    }\n    manifest.pages = sortObjectByKey(manifest.pages)\n    return manifest\n  }\n\n  private async writeAppBuildManifest(): Promise<void> {\n    const appBuildManifest = this.mergeAppBuildManifests(\n      this.appBuildManifests.values()\n    )\n    const appBuildManifestPath = join(this.distDir, APP_BUILD_MANIFEST)\n    deleteCache(appBuildManifestPath)\n    await writeFileAtomic(\n      appBuildManifestPath,\n      JSON.stringify(appBuildManifest, null, 2)\n    )\n  }\n\n  async loadAppPathsManifest(pageName: string): Promise<void> {\n    this.appPathsManifests.set(\n      getEntryKey('app', 'server', pageName),\n      await readPartialManifest(\n        this.distDir,\n        APP_PATHS_MANIFEST,\n        pageName,\n        'app'\n      )\n    )\n  }\n\n  private async writeAppPathsManifest(): Promise<void> {\n    const appPathsManifest = this.mergePagesManifests(\n      this.appPathsManifests.values()\n    )\n    const appPathsManifestPath = join(\n      this.distDir,\n      'server',\n      APP_PATHS_MANIFEST\n    )\n    deleteCache(appPathsManifestPath)\n    await writeFileAtomic(\n      appPathsManifestPath,\n      JSON.stringify(appPathsManifest, null, 2)\n    )\n  }\n\n  private async writeWebpackStats(): Promise<void> {\n    const webpackStats = this.mergeWebpackStats(this.webpackStats.values())\n    const path = join(this.distDir, 'server', WEBPACK_STATS)\n    deleteCache(path)\n    await writeFileAtomic(path, JSON.stringify(webpackStats, null, 2))\n  }\n\n  async loadBuildManifest(\n    pageName: string,\n    type: 'app' | 'pages' = 'pages'\n  ): Promise<void> {\n    this.buildManifests.set(\n      getEntryKey(type, 'server', pageName),\n      await readPartialManifest(this.distDir, BUILD_MANIFEST, pageName, type)\n    )\n  }\n\n  async loadClientBuildManifest(\n    pageName: string,\n    type: 'app' | 'pages' = 'pages'\n  ): Promise<void> {\n    this.clientBuildManifests.set(\n      getEntryKey(type, 'server', pageName),\n      await readPartialManifest(\n        this.distDir,\n        TURBOPACK_CLIENT_BUILD_MANIFEST,\n        pageName,\n        type\n      )\n    )\n  }\n\n  async loadWebpackStats(\n    pageName: string,\n    type: 'app' | 'pages' = 'pages'\n  ): Promise<void> {\n    this.webpackStats.set(\n      getEntryKey(type, 'client', pageName),\n      await readPartialManifest(this.distDir, WEBPACK_STATS, pageName, type)\n    )\n  }\n\n  private mergeWebpackStats(statsFiles: Iterable<WebpackStats>): WebpackStats {\n    const entrypoints: Record<string, StatsChunkGroup> = {}\n    const assets: Map<string, StatsAsset> = new Map()\n    const chunks: Map<string | number, StatsChunk> = new Map()\n    const modules: Map<string | number, StatsModule> = new Map()\n\n    for (const statsFile of statsFiles) {\n      if (statsFile.entrypoints) {\n        for (const [k, v] of Object.entries(statsFile.entrypoints)) {\n          if (!entrypoints[k]) {\n            entrypoints[k] = v\n          }\n        }\n      }\n\n      if (statsFile.assets) {\n        for (const asset of statsFile.assets) {\n          if (!assets.has(asset.name)) {\n            assets.set(asset.name, asset)\n          }\n        }\n      }\n\n      if (statsFile.chunks) {\n        for (const chunk of statsFile.chunks) {\n          if (!chunks.has(chunk.id!)) {\n            chunks.set(chunk.id!, chunk)\n          }\n        }\n      }\n\n      if (statsFile.modules) {\n        for (const module of statsFile.modules) {\n          const id = module.id\n          if (id != null) {\n            // Merge the chunk list for the module. This can vary across endpoints.\n            const existing = modules.get(id)\n            if (existing == null) {\n              modules.set(id, module)\n            } else if (module.chunks != null && existing.chunks != null) {\n              for (const chunk of module.chunks) {\n                if (!existing.chunks.includes(chunk)) {\n                  existing.chunks.push(chunk)\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n\n    return {\n      version: 'Turbopack',\n      entrypoints,\n      assets: [...assets.values()],\n      chunks: [...chunks.values()],\n      modules: [...modules.values()],\n    }\n  }\n\n  private mergeBuildManifests(manifests: Iterable<BuildManifest>) {\n    const manifest: Partial<BuildManifest> & Pick<BuildManifest, 'pages'> = {\n      pages: {\n        '/_app': [],\n      },\n      // Something in next.js depends on these to exist even for app dir rendering\n      devFiles: [],\n      ampDevFiles: [],\n      polyfillFiles: [],\n      lowPriorityFiles: [\n        `static/${this.buildId}/_ssgManifest.js`,\n        `static/${this.buildId}/_buildManifest.js`,\n      ],\n      rootMainFiles: [],\n      ampFirstPages: [],\n    }\n    for (const m of manifests) {\n      Object.assign(manifest.pages, m.pages)\n      if (m.rootMainFiles.length) manifest.rootMainFiles = m.rootMainFiles\n      // polyfillFiles should always be the same, so we can overwrite instead of actually merging\n      if (m.polyfillFiles.length) manifest.polyfillFiles = m.polyfillFiles\n    }\n    manifest.pages = sortObjectByKey(manifest.pages) as BuildManifest['pages']\n    return manifest\n  }\n\n  private mergeClientBuildManifests(\n    rewrites: CustomRoutes['rewrites'],\n    manifests: Iterable<ClientBuildManifest>,\n    sortedPageKeys: string[]\n  ): ClientBuildManifest {\n    const manifest = {\n      __rewrites: normalizeRewritesForBuildManifest(rewrites) as any,\n      sortedPages: sortedPageKeys,\n    }\n    for (const m of manifests) {\n      Object.assign(manifest, m)\n    }\n    return sortObjectByKey(manifest)\n  }\n\n  private async writeBuildManifest(\n    entrypoints: Entrypoints,\n    devRewrites: SetupOpts['fsChecker']['rewrites'] | undefined,\n    productionRewrites: CustomRoutes['rewrites'] | undefined\n  ): Promise<void> {\n    const rewrites = productionRewrites ?? {\n      ...devRewrites,\n      beforeFiles: (devRewrites?.beforeFiles ?? []).map(processRoute),\n      afterFiles: (devRewrites?.afterFiles ?? []).map(processRoute),\n      fallback: (devRewrites?.fallback ?? []).map(processRoute),\n    }\n    const buildManifest = this.mergeBuildManifests(this.buildManifests.values())\n    const buildManifestPath = join(this.distDir, BUILD_MANIFEST)\n    const middlewareBuildManifestPath = join(\n      this.distDir,\n      'server',\n      `${MIDDLEWARE_BUILD_MANIFEST}.js`\n    )\n    const interceptionRewriteManifestPath = join(\n      this.distDir,\n      'server',\n      `${INTERCEPTION_ROUTE_REWRITE_MANIFEST}.js`\n    )\n    deleteCache(buildManifestPath)\n    deleteCache(middlewareBuildManifestPath)\n    deleteCache(interceptionRewriteManifestPath)\n    await writeFileAtomic(\n      buildManifestPath,\n      JSON.stringify(buildManifest, null, 2)\n    )\n    await writeFileAtomic(\n      middlewareBuildManifestPath,\n      // we use globalThis here because middleware can be node\n      // which doesn't have \"self\"\n      createEdgeRuntimeManifest(buildManifest)\n    )\n\n    const interceptionRewrites = JSON.stringify(\n      rewrites.beforeFiles.filter(isInterceptionRouteRewrite)\n    )\n\n    await writeFileAtomic(\n      interceptionRewriteManifestPath,\n      `self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST=${JSON.stringify(\n        interceptionRewrites\n      )};`\n    )\n\n    const pagesKeys = [...entrypoints.page.keys()]\n    if (entrypoints.global.app) {\n      pagesKeys.push('/_app')\n    }\n    if (entrypoints.global.error) {\n      pagesKeys.push('/_error')\n    }\n\n    const sortedPageKeys = getSortedRoutes(pagesKeys)\n    const clientBuildManifest = this.mergeClientBuildManifests(\n      rewrites,\n      this.clientBuildManifests.values(),\n      sortedPageKeys\n    )\n    const clientBuildManifestJs = `self.__BUILD_MANIFEST = ${JSON.stringify(\n      clientBuildManifest,\n      null,\n      2\n    )};self.__BUILD_MANIFEST_CB && self.__BUILD_MANIFEST_CB()`\n    await writeFileAtomic(\n      join(this.distDir, 'static', this.buildId, '_buildManifest.js'),\n      clientBuildManifestJs\n    )\n    await writeFileAtomic(\n      join(this.distDir, 'static', this.buildId, '_ssgManifest.js'),\n      srcEmptySsgManifest\n    )\n  }\n\n  private async writeClientMiddlewareManifest(): Promise<void> {\n    const middlewareManifest = this.mergeMiddlewareManifests(\n      this.middlewareManifests.values()\n    )\n\n    const matchers = middlewareManifest?.middleware['/']?.matchers || []\n\n    const clientMiddlewareManifestPath = join(\n      this.distDir,\n      'static',\n      this.buildId,\n      `${TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST}`\n    )\n    deleteCache(clientMiddlewareManifestPath)\n    await writeFileAtomic(\n      clientMiddlewareManifestPath,\n      JSON.stringify(matchers, null, 2)\n    )\n  }\n\n  private async writeFallbackBuildManifest(): Promise<void> {\n    const fallbackBuildManifest = this.mergeBuildManifests(\n      [\n        this.buildManifests.get(getEntryKey('pages', 'server', '_app')),\n        this.buildManifests.get(getEntryKey('pages', 'server', '_error')),\n      ].filter(Boolean) as BuildManifest[]\n    )\n    const fallbackBuildManifestPath = join(\n      this.distDir,\n      `fallback-${BUILD_MANIFEST}`\n    )\n    deleteCache(fallbackBuildManifestPath)\n    await writeFileAtomic(\n      fallbackBuildManifestPath,\n      JSON.stringify(fallbackBuildManifest, null, 2)\n    )\n  }\n\n  async loadFontManifest(\n    pageName: string,\n    type: 'app' | 'pages' = 'pages'\n  ): Promise<void> {\n    this.fontManifests.set(\n      getEntryKey(type, 'server', pageName),\n      await readPartialManifest(\n        this.distDir,\n        `${NEXT_FONT_MANIFEST}.json`,\n        pageName,\n        type\n      )\n    )\n  }\n\n  private mergeFontManifests(manifests: Iterable<NextFontManifest>) {\n    const manifest: NextFontManifest = {\n      app: {},\n      appUsingSizeAdjust: false,\n      pages: {},\n      pagesUsingSizeAdjust: false,\n    }\n    for (const m of manifests) {\n      Object.assign(manifest.app, m.app)\n      Object.assign(manifest.pages, m.pages)\n\n      manifest.appUsingSizeAdjust =\n        manifest.appUsingSizeAdjust || m.appUsingSizeAdjust\n      manifest.pagesUsingSizeAdjust =\n        manifest.pagesUsingSizeAdjust || m.pagesUsingSizeAdjust\n    }\n    manifest.app = sortObjectByKey(manifest.app)\n    manifest.pages = sortObjectByKey(manifest.pages)\n    return manifest\n  }\n\n  private async writeNextFontManifest(): Promise<void> {\n    const fontManifest = this.mergeFontManifests(this.fontManifests.values())\n    const json = JSON.stringify(fontManifest, null, 2)\n\n    const fontManifestJsonPath = join(\n      this.distDir,\n      'server',\n      `${NEXT_FONT_MANIFEST}.json`\n    )\n    const fontManifestJsPath = join(\n      this.distDir,\n      'server',\n      `${NEXT_FONT_MANIFEST}.js`\n    )\n    deleteCache(fontManifestJsonPath)\n    deleteCache(fontManifestJsPath)\n    await writeFileAtomic(fontManifestJsonPath, json)\n    await writeFileAtomic(\n      fontManifestJsPath,\n      `self.__NEXT_FONT_MANIFEST=${JSON.stringify(json)}`\n    )\n  }\n\n  /**\n   * @returns If the manifest was written or not\n   */\n  async loadMiddlewareManifest(\n    pageName: string,\n    type: 'pages' | 'app' | 'middleware' | 'instrumentation'\n  ): Promise<boolean> {\n    const middlewareManifestPath = getManifestPath(\n      pageName,\n      this.distDir,\n      MIDDLEWARE_MANIFEST,\n      type,\n      true\n    )\n\n    // middlewareManifest is actually \"edge manifest\" and not all routes are edge runtime. If it is not written we skip it.\n    if (!existsSync(middlewareManifestPath)) {\n      return false\n    }\n\n    this.middlewareManifests.set(\n      getEntryKey(\n        type === 'middleware' || type === 'instrumentation' ? 'root' : type,\n        'server',\n        pageName\n      ),\n      await readPartialManifest(\n        this.distDir,\n        MIDDLEWARE_MANIFEST,\n        pageName,\n        type\n      )\n    )\n\n    return true\n  }\n\n  getMiddlewareManifest(key: EntryKey) {\n    return this.middlewareManifests.get(key)\n  }\n\n  deleteMiddlewareManifest(key: EntryKey) {\n    return this.middlewareManifests.delete(key)\n  }\n\n  private mergeMiddlewareManifests(\n    manifests: Iterable<TurbopackMiddlewareManifest>\n  ): MiddlewareManifest {\n    const manifest: MiddlewareManifest = {\n      version: 3,\n      middleware: {},\n      sortedMiddleware: [],\n      functions: {},\n    }\n    let instrumentation: InstrumentationDefinition | undefined = undefined\n    for (const m of manifests) {\n      Object.assign(manifest.functions, m.functions)\n      Object.assign(manifest.middleware, m.middleware)\n      if (m.instrumentation) {\n        instrumentation = m.instrumentation\n      }\n    }\n    manifest.functions = sortObjectByKey(manifest.functions)\n    manifest.middleware = sortObjectByKey(manifest.middleware)\n    const updateFunctionDefinition = (\n      fun: EdgeFunctionDefinition\n    ): EdgeFunctionDefinition => {\n      return {\n        ...fun,\n        files: [...(instrumentation?.files ?? []), ...fun.files],\n      }\n    }\n    for (const key of Object.keys(manifest.middleware)) {\n      const value = manifest.middleware[key]\n      manifest.middleware[key] = updateFunctionDefinition(value)\n    }\n    for (const key of Object.keys(manifest.functions)) {\n      const value = manifest.functions[key]\n      manifest.functions[key] = updateFunctionDefinition(value)\n    }\n    for (const fun of Object.values(manifest.functions).concat(\n      Object.values(manifest.middleware)\n    )) {\n      for (const matcher of fun.matchers) {\n        if (!matcher.regexp) {\n          matcher.regexp = safePathToRegexp(matcher.originalSource, [], {\n            delimiter: '/',\n            sensitive: false,\n            strict: true,\n          }).source.replaceAll('\\\\/', '/')\n        }\n      }\n    }\n    manifest.sortedMiddleware = Object.keys(manifest.middleware)\n\n    return manifest\n  }\n\n  private async writeMiddlewareManifest(): Promise<void> {\n    const middlewareManifest = this.mergeMiddlewareManifests(\n      this.middlewareManifests.values()\n    )\n\n    // Normalize regexes as it uses path-to-regexp\n    for (const key in middlewareManifest.middleware) {\n      middlewareManifest.middleware[key].matchers.forEach((matcher) => {\n        if (!matcher.regexp.startsWith('^')) {\n          const parsedPage = tryToParsePath(matcher.regexp)\n          if (parsedPage.error || !parsedPage.regexStr) {\n            throw new Error(`Invalid source: ${matcher.regexp}`)\n          }\n          matcher.regexp = parsedPage.regexStr\n        }\n      })\n    }\n\n    const middlewareManifestPath = join(\n      this.distDir,\n      'server',\n      MIDDLEWARE_MANIFEST\n    )\n    deleteCache(middlewareManifestPath)\n    await writeFileAtomic(\n      middlewareManifestPath,\n      JSON.stringify(middlewareManifest, null, 2)\n    )\n  }\n\n  async loadPagesManifest(pageName: string): Promise<void> {\n    this.pagesManifests.set(\n      getEntryKey('pages', 'server', pageName),\n      await readPartialManifest(this.distDir, PAGES_MANIFEST, pageName)\n    )\n  }\n\n  private mergePagesManifests(manifests: Iterable<PagesManifest>) {\n    const manifest: PagesManifest = {}\n    for (const m of manifests) {\n      Object.assign(manifest, m)\n    }\n    return sortObjectByKey(manifest)\n  }\n\n  private async writePagesManifest(): Promise<void> {\n    const pagesManifest = this.mergePagesManifests(this.pagesManifests.values())\n    const pagesManifestPath = join(this.distDir, 'server', PAGES_MANIFEST)\n    deleteCache(pagesManifestPath)\n    await writeFileAtomic(\n      pagesManifestPath,\n      JSON.stringify(pagesManifest, null, 2)\n    )\n  }\n\n  async writeManifests({\n    devRewrites,\n    productionRewrites,\n    entrypoints,\n  }: {\n    devRewrites: SetupOpts['fsChecker']['rewrites'] | undefined\n    productionRewrites: CustomRoutes['rewrites'] | undefined\n    entrypoints: Entrypoints\n  }) {\n    await this.writeActionManifest()\n    await this.writeAppBuildManifest()\n    await this.writeAppPathsManifest()\n    await this.writeBuildManifest(entrypoints, devRewrites, productionRewrites)\n    await this.writeFallbackBuildManifest()\n    await this.writeMiddlewareManifest()\n    await this.writeClientMiddlewareManifest()\n    await this.writeNextFontManifest()\n    await this.writePagesManifest()\n\n    if (process.env.TURBOPACK_STATS != null) {\n      await this.writeWebpackStats()\n    }\n  }\n}\n\nfunction sortObjectByKey(obj: Record<string, any>) {\n  return Object.keys(obj)\n    .sort()\n    .reduce(\n      (acc, key) => {\n        acc[key] = obj[key]\n        return acc\n      },\n      {} as Record<string, any>\n    )\n}\n"], "names": ["APP_BUILD_MANIFEST", "APP_PATHS_MANIFEST", "BUILD_MANIFEST", "INTERCEPTION_ROUTE_REWRITE_MANIFEST", "MIDDLEWARE_BUILD_MANIFEST", "MIDDLEWARE_MANIFEST", "NEXT_FONT_MANIFEST", "PAGES_MANIFEST", "SERVER_REFERENCE_MANIFEST", "TURBOPACK_CLIENT_BUILD_MANIFEST", "TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST", "WEBPACK_STATS", "join", "posix", "readFile", "deleteCache", "writeFileAtomic", "isInterceptionRouteRewrite", "normalizeRewritesForBuildManifest", "srcEmptySsgManifest", "processRoute", "createEdgeRuntimeManifest", "getAssetPathFromRoute", "getEntry<PERSON>ey", "getSortedRoutes", "existsSync", "addMetadataIdToRoute", "addRouteSuffix", "removeRouteSuffix", "tryToParsePath", "safePathToRegexp", "getManifestPath", "page", "distDir", "name", "type", "firstCall", "manifestPath", "isSitemapRoute", "test", "replace", "endsWith", "metadataPage", "readPartialManifest", "pageName", "JSON", "parse", "TurbopackManifestLoader", "delete", "key", "actionManifests", "appBuildManifests", "appPathsManifests", "buildManifests", "clientBuildManifests", "fontManifests", "middlewareManifests", "pagesManifests", "webpackStats", "loadActionManifest", "set", "mergeActionManifests", "manifests", "manifest", "node", "edge", "<PERSON><PERSON><PERSON>", "mergeActionIds", "actionEntries", "other", "action", "workers", "layer", "filename", "exportedName", "Object", "assign", "m", "entry", "sortObjectByKey", "writeActionManifest", "actionManifest", "values", "actionManifestJsonPath", "actionManifestJsPath", "json", "stringify", "loadAppBuildManifest", "mergeAppBuildManifests", "pages", "writeAppBuildManifest", "appBuildManifest", "appBuildManifestPath", "loadAppPathsManifest", "writeAppPathsManifest", "appPathsManifest", "mergePagesManifests", "appPathsManifestPath", "writeWebpackStats", "mergeWebpackStats", "path", "loadBuildManifest", "loadClientBuildManifest", "loadWebpackStats", "statsFiles", "entrypoints", "assets", "Map", "chunks", "modules", "statsFile", "k", "v", "entries", "asset", "has", "chunk", "id", "module", "existing", "get", "includes", "push", "version", "mergeBuildManifests", "devFiles", "ampDevFiles", "polyfillFiles", "lowPriorityFiles", "buildId", "rootMainFiles", "ampFirstPages", "length", "mergeClientBuildManifests", "rewrites", "sortedPageKeys", "__rewrites", "sortedPages", "writeBuildManifest", "devRewrites", "productionRewrites", "beforeFiles", "map", "afterFiles", "fallback", "buildManifest", "buildManifestPath", "middlewareBuildManifestPath", "interceptionRewriteManifestPath", "interceptionRewrites", "filter", "pagesKeys", "keys", "global", "app", "error", "clientBuildManifest", "clientBuildManifestJs", "writeClientMiddlewareManifest", "middlewareManifest", "mergeMiddlewareManifests", "matchers", "middleware", "clientMiddlewareManifestPath", "writeFallbackBuildManifest", "fallbackBuildManifest", "Boolean", "fallbackBuildManifestPath", "loadFontManifest", "mergeFontManifests", "appUsingSizeAdjust", "pagesUsingSizeAdjust", "writeNextFontManifest", "fontManifest", "fontManifestJsonPath", "fontManifestJsPath", "loadMiddlewareManifest", "middlewareManifestPath", "getMiddlewareManifest", "deleteMiddlewareManifest", "sortedMiddleware", "functions", "instrumentation", "undefined", "updateFunctionDefinition", "fun", "files", "value", "concat", "matcher", "regexp", "originalSource", "delimiter", "sensitive", "strict", "source", "replaceAll", "writeMiddlewareManifest", "for<PERSON>ach", "startsWith", "parsedPage", "regexStr", "Error", "loadPagesManifest", "writePagesManifest", "pagesManifest", "pagesManifestPath", "writeManifests", "process", "env", "TURBOPACK_STATS", "constructor", "obj", "sort", "reduce", "acc"], "mappings": "AAiBA,SACEA,kBAAkB,EAClBC,kBAAkB,EAClBC,cAAc,EACdC,mCAAmC,EACnCC,yBAAyB,EACzBC,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,EACdC,yBAAyB,EACzBC,+BAA+B,EAC/BC,oCAAoC,EACpCC,aAAa,QACR,eAAc;AACrB,SAASC,IAAI,EAAEC,KAAK,QAAQ,OAAM;AAClC,SAASC,QAAQ,QAAQ,cAAa;AAEtC,SAASC,WAAW,QAAQ,oCAAmC;AAC/D,SAASC,eAAe,QAAQ,+BAA8B;AAC9D,SAASC,0BAA0B,QAAQ,qDAAoD;AAC/F,SAEEC,iCAAiC,EACjCC,mBAAmB,EACnBC,YAAY,EACZC,yBAAyB,QACpB,uDAAsD;AAC7D,OAAOC,2BAA2B,4CAA2C;AAC7E,SAASC,WAAW,QAAuB,cAAa;AAExD,SAASC,eAAe,QAAQ,kBAAiB;AACjD,SAASC,UAAU,QAAQ,KAAI;AAC/B,SACEC,oBAAoB,EACpBC,cAAc,EACdC,iBAAiB,QACZ,sCAAqC;AAC5C,SAASC,cAAc,QAAQ,iCAAgC;AAC/D,SAASC,gBAAgB,QAAQ,oCAAmC;AAwBpE,MAAMC,kBAAkB,CACtBC,MACAC,SACAC,MACAC,MACAC;IAEA,IAAIC,eAAexB,MAAMD,IAAI,CAC3BqB,SACC,UACDE,MACAA,SAAS,gBAAgBA,SAAS,oBAC9B,KACAA,SAAS,QACPH,OACAV,sBAAsBU,OAC5BE;IAGF,IAAIE,WAAW;QACb,MAAME,iBAAiB,8BAA8BC,IAAI,CAACP;QAC1D,mDAAmD;QACnD,IAAIM,kBAAkB,CAACb,WAAWY,eAAe;YAC/CA,eAAeN,gBACbC,KAAKQ,OAAO,CAAC,qBAAqB,uBAClCP,SACAC,MACAC,MACA;QAEJ;QACA,oDAAoD;QACpD,IAAI,CAACV,WAAWY,iBAAiBL,KAAKS,QAAQ,CAAC,WAAW;YACxD,6IAA6I;YAC7I,IAAIC,eAAef,eACjBD,qBAAqBE,kBAAkBI;YAEzCK,eAAeN,gBAAgBW,cAAcT,SAASC,MAAMC,MAAM;QACpE;IACF;IAEA,OAAOE;AACT;AAEA,eAAeM,oBACbV,OAAe,EACfC,IAAkB,EAClBU,QAAgB,EAChBT,IAAkE;IAAlEA,IAAAA,iBAAAA,OAA2D;IAE3D,MAAMH,OAAOY;IACb,MAAMP,eAAeN,gBAAgBC,MAAMC,SAASC,MAAMC,MAAM;IAChE,OAAOU,KAAKC,KAAK,CAAC,MAAMhC,SAASD,MAAMD,IAAI,CAACyB,eAAe;AAC7D;AAEA,OAAO,MAAMU;IA8BXC,OAAOC,GAAa,EAAE;QACpB,IAAI,CAACC,eAAe,CAACF,MAAM,CAACC;QAC5B,IAAI,CAACE,iBAAiB,CAACH,MAAM,CAACC;QAC9B,IAAI,CAACG,iBAAiB,CAACJ,MAAM,CAACC;QAC9B,IAAI,CAACI,cAAc,CAACL,MAAM,CAACC;QAC3B,IAAI,CAACK,oBAAoB,CAACN,MAAM,CAACC;QACjC,IAAI,CAACM,aAAa,CAACP,MAAM,CAACC;QAC1B,IAAI,CAACO,mBAAmB,CAACR,MAAM,CAACC;QAChC,IAAI,CAACQ,cAAc,CAACT,MAAM,CAACC;QAC3B,IAAI,CAACS,YAAY,CAACV,MAAM,CAACC;IAC3B;IAEA,MAAMU,mBAAmBf,QAAgB,EAAiB;QACxD,IAAI,CAACM,eAAe,CAACU,GAAG,CACtBrC,YAAY,OAAO,UAAUqB,WAC7B,MAAMD,oBACJ,IAAI,CAACV,OAAO,EACZ,AAAC,KAAEzB,4BAA0B,SAC7BoC,UACA;IAGN;IAEA,MAAciB,qBAAqBC,SAAmC,EAAE;QAEtE,MAAMC,WAA2B;YAC/BC,MAAM,CAAC;YACPC,MAAM,CAAC;YACPC,eAAe,IAAI,CAACA,aAAa;QACnC;QAEA,SAASC,eACPC,aAA4B,EAC5BC,KAAoB;YAEpB,IAAK,MAAMpB,OAAOoB,MAAO;oBACPD,gBAAcnB;;gBAA9B,MAAMqB,SAAUF,MAAAA,iBAAAA,cAAa,CAACnB,OAAAA,IAAI,gBAAlBmB,cAAa,CAACnB,KAAI,GAAK;oBACrCsB,SAAS,CAAC;oBACVC,OAAO,CAAC;gBACV;gBACAF,OAAOG,QAAQ,GAAGJ,KAAK,CAACpB,IAAI,CAACwB,QAAQ;gBACrCH,OAAOI,YAAY,GAAGL,KAAK,CAACpB,IAAI,CAACyB,YAAY;gBAC7CC,OAAOC,MAAM,CAACN,OAAOC,OAAO,EAAEF,KAAK,CAACpB,IAAI,CAACsB,OAAO;gBAChDI,OAAOC,MAAM,CAACN,OAAOE,KAAK,EAAEH,KAAK,CAACpB,IAAI,CAACuB,KAAK;YAC9C;QACF;QAEA,KAAK,MAAMK,KAAKf,UAAW;YACzBK,eAAeJ,SAASC,IAAI,EAAEa,EAAEb,IAAI;YACpCG,eAAeJ,SAASE,IAAI,EAAEY,EAAEZ,IAAI;QACtC;QACA,IAAK,MAAMhB,OAAOc,SAASC,IAAI,CAAE;YAC/B,MAAMc,QAAQf,SAASC,IAAI,CAACf,IAAI;YAChC6B,MAAMP,OAAO,GAAGQ,gBAAgBD,MAAMP,OAAO;YAC7CO,MAAMN,KAAK,GAAGO,gBAAgBD,MAAMN,KAAK;QAC3C;QACA,IAAK,MAAMvB,OAAOc,SAASE,IAAI,CAAE;YAC/B,MAAMa,QAAQf,SAASE,IAAI,CAAChB,IAAI;YAChC6B,MAAMP,OAAO,GAAGQ,gBAAgBD,MAAMP,OAAO;YAC7CO,MAAMN,KAAK,GAAGO,gBAAgBD,MAAMN,KAAK;QAC3C;QAEA,OAAOT;IACT;IAEA,MAAciB,sBAAqC;QACjD,MAAMC,iBAAiB,MAAM,IAAI,CAACpB,oBAAoB,CACpD,IAAI,CAACX,eAAe,CAACgC,MAAM;QAE7B,MAAMC,yBAAyBvE,KAC7B,IAAI,CAACqB,OAAO,EACZ,UACA,AAAC,KAAEzB,4BAA0B;QAE/B,MAAM4E,uBAAuBxE,KAC3B,IAAI,CAACqB,OAAO,EACZ,UACA,AAAC,KAAEzB,4BAA0B;QAE/B,MAAM6E,OAAOxC,KAAKyC,SAAS,CAACL,gBAAgB,MAAM;QAClDlE,YAAYoE;QACZpE,YAAYqE;QACZ,MAAMpE,gBAAgBmE,wBAAwBE;QAC9C,MAAMrE,gBACJoE,sBACA,AAAC,gCAA6BvC,KAAKyC,SAAS,CAACD;IAEjD;IAEA,MAAME,qBAAqB3C,QAAgB,EAAiB;QAC1D,IAAI,CAACO,iBAAiB,CAACS,GAAG,CACxBrC,YAAY,OAAO,UAAUqB,WAC7B,MAAMD,oBACJ,IAAI,CAACV,OAAO,EACZjC,oBACA4C,UACA;IAGN;IAEQ4C,uBAAuB1B,SAAqC,EAAE;QACpE,MAAMC,WAA6B;YACjC0B,OAAO,CAAC;QACV;QACA,KAAK,MAAMZ,KAAKf,UAAW;YACzBa,OAAOC,MAAM,CAACb,SAAS0B,KAAK,EAAEZ,EAAEY,KAAK;QACvC;QACA1B,SAAS0B,KAAK,GAAGV,gBAAgBhB,SAAS0B,KAAK;QAC/C,OAAO1B;IACT;IAEA,MAAc2B,wBAAuC;QACnD,MAAMC,mBAAmB,IAAI,CAACH,sBAAsB,CAClD,IAAI,CAACrC,iBAAiB,CAAC+B,MAAM;QAE/B,MAAMU,uBAAuBhF,KAAK,IAAI,CAACqB,OAAO,EAAEjC;QAChDe,YAAY6E;QACZ,MAAM5E,gBACJ4E,sBACA/C,KAAKyC,SAAS,CAACK,kBAAkB,MAAM;IAE3C;IAEA,MAAME,qBAAqBjD,QAAgB,EAAiB;QAC1D,IAAI,CAACQ,iBAAiB,CAACQ,GAAG,CACxBrC,YAAY,OAAO,UAAUqB,WAC7B,MAAMD,oBACJ,IAAI,CAACV,OAAO,EACZhC,oBACA2C,UACA;IAGN;IAEA,MAAckD,wBAAuC;QACnD,MAAMC,mBAAmB,IAAI,CAACC,mBAAmB,CAC/C,IAAI,CAAC5C,iBAAiB,CAAC8B,MAAM;QAE/B,MAAMe,uBAAuBrF,KAC3B,IAAI,CAACqB,OAAO,EACZ,UACAhC;QAEFc,YAAYkF;QACZ,MAAMjF,gBACJiF,sBACApD,KAAKyC,SAAS,CAACS,kBAAkB,MAAM;IAE3C;IAEA,MAAcG,oBAAmC;QAC/C,MAAMxC,eAAe,IAAI,CAACyC,iBAAiB,CAAC,IAAI,CAACzC,YAAY,CAACwB,MAAM;QACpE,MAAMkB,OAAOxF,KAAK,IAAI,CAACqB,OAAO,EAAE,UAAUtB;QAC1CI,YAAYqF;QACZ,MAAMpF,gBAAgBoF,MAAMvD,KAAKyC,SAAS,CAAC5B,cAAc,MAAM;IACjE;IAEA,MAAM2C,kBACJzD,QAAgB,EAChBT,IAA+B,EAChB;QADfA,IAAAA,iBAAAA,OAAwB;QAExB,IAAI,CAACkB,cAAc,CAACO,GAAG,CACrBrC,YAAYY,MAAM,UAAUS,WAC5B,MAAMD,oBAAoB,IAAI,CAACV,OAAO,EAAE/B,gBAAgB0C,UAAUT;IAEtE;IAEA,MAAMmE,wBACJ1D,QAAgB,EAChBT,IAA+B,EAChB;QADfA,IAAAA,iBAAAA,OAAwB;QAExB,IAAI,CAACmB,oBAAoB,CAACM,GAAG,CAC3BrC,YAAYY,MAAM,UAAUS,WAC5B,MAAMD,oBACJ,IAAI,CAACV,OAAO,EACZxB,iCACAmC,UACAT;IAGN;IAEA,MAAMoE,iBACJ3D,QAAgB,EAChBT,IAA+B,EAChB;QADfA,IAAAA,iBAAAA,OAAwB;QAExB,IAAI,CAACuB,YAAY,CAACE,GAAG,CACnBrC,YAAYY,MAAM,UAAUS,WAC5B,MAAMD,oBAAoB,IAAI,CAACV,OAAO,EAAEtB,eAAeiC,UAAUT;IAErE;IAEQgE,kBAAkBK,UAAkC,EAAgB;QAC1E,MAAMC,cAA+C,CAAC;QACtD,MAAMC,SAAkC,IAAIC;QAC5C,MAAMC,SAA2C,IAAID;QACrD,MAAME,UAA6C,IAAIF;QAEvD,KAAK,MAAMG,aAAaN,WAAY;YAClC,IAAIM,UAAUL,WAAW,EAAE;gBACzB,KAAK,MAAM,CAACM,GAAGC,EAAE,IAAIrC,OAAOsC,OAAO,CAACH,UAAUL,WAAW,EAAG;oBAC1D,IAAI,CAACA,WAAW,CAACM,EAAE,EAAE;wBACnBN,WAAW,CAACM,EAAE,GAAGC;oBACnB;gBACF;YACF;YAEA,IAAIF,UAAUJ,MAAM,EAAE;gBACpB,KAAK,MAAMQ,SAASJ,UAAUJ,MAAM,CAAE;oBACpC,IAAI,CAACA,OAAOS,GAAG,CAACD,MAAMhF,IAAI,GAAG;wBAC3BwE,OAAO9C,GAAG,CAACsD,MAAMhF,IAAI,EAAEgF;oBACzB;gBACF;YACF;YAEA,IAAIJ,UAAUF,MAAM,EAAE;gBACpB,KAAK,MAAMQ,SAASN,UAAUF,MAAM,CAAE;oBACpC,IAAI,CAACA,OAAOO,GAAG,CAACC,MAAMC,EAAE,GAAI;wBAC1BT,OAAOhD,GAAG,CAACwD,MAAMC,EAAE,EAAGD;oBACxB;gBACF;YACF;YAEA,IAAIN,UAAUD,OAAO,EAAE;gBACrB,KAAK,MAAMS,UAAUR,UAAUD,OAAO,CAAE;oBACtC,MAAMQ,KAAKC,OAAOD,EAAE;oBACpB,IAAIA,MAAM,MAAM;wBACd,uEAAuE;wBACvE,MAAME,WAAWV,QAAQW,GAAG,CAACH;wBAC7B,IAAIE,YAAY,MAAM;4BACpBV,QAAQjD,GAAG,CAACyD,IAAIC;wBAClB,OAAO,IAAIA,OAAOV,MAAM,IAAI,QAAQW,SAASX,MAAM,IAAI,MAAM;4BAC3D,KAAK,MAAMQ,SAASE,OAAOV,MAAM,CAAE;gCACjC,IAAI,CAACW,SAASX,MAAM,CAACa,QAAQ,CAACL,QAAQ;oCACpCG,SAASX,MAAM,CAACc,IAAI,CAACN;gCACvB;4BACF;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAO;YACLO,SAAS;YACTlB;YACAC,QAAQ;mBAAIA,OAAOxB,MAAM;aAAG;YAC5B0B,QAAQ;mBAAIA,OAAO1B,MAAM;aAAG;YAC5B2B,SAAS;mBAAIA,QAAQ3B,MAAM;aAAG;QAChC;IACF;IAEQ0C,oBAAoB9D,SAAkC,EAAE;QAC9D,MAAMC,WAAkE;YACtE0B,OAAO;gBACL,SAAS,EAAE;YACb;YACA,4EAA4E;YAC5EoC,UAAU,EAAE;YACZC,aAAa,EAAE;YACfC,eAAe,EAAE;YACjBC,kBAAkB;gBACf,YAAS,IAAI,CAACC,OAAO,GAAC;gBACtB,YAAS,IAAI,CAACA,OAAO,GAAC;aACxB;YACDC,eAAe,EAAE;YACjBC,eAAe,EAAE;QACnB;QACA,KAAK,MAAMtD,KAAKf,UAAW;YACzBa,OAAOC,MAAM,CAACb,SAAS0B,KAAK,EAAEZ,EAAEY,KAAK;YACrC,IAAIZ,EAAEqD,aAAa,CAACE,MAAM,EAAErE,SAASmE,aAAa,GAAGrD,EAAEqD,aAAa;YACpE,2FAA2F;YAC3F,IAAIrD,EAAEkD,aAAa,CAACK,MAAM,EAAErE,SAASgE,aAAa,GAAGlD,EAAEkD,aAAa;QACtE;QACAhE,SAAS0B,KAAK,GAAGV,gBAAgBhB,SAAS0B,KAAK;QAC/C,OAAO1B;IACT;IAEQsE,0BACNC,QAAkC,EAClCxE,SAAwC,EACxCyE,cAAwB,EACH;QACrB,MAAMxE,WAAW;YACfyE,YAAYtH,kCAAkCoH;YAC9CG,aAAaF;QACf;QACA,KAAK,MAAM1D,KAAKf,UAAW;YACzBa,OAAOC,MAAM,CAACb,UAAUc;QAC1B;QACA,OAAOE,gBAAgBhB;IACzB;IAEA,MAAc2E,mBACZjC,WAAwB,EACxBkC,WAA2D,EAC3DC,kBAAwD,EACzC;YAGCD,0BACDA,yBACFA;QAJb,MAAML,WAAWM,6BAAAA,qBAAsB;YACrC,GAAGD,WAAW;YACdE,aAAa,AAACF,CAAAA,CAAAA,2BAAAA,+BAAAA,YAAaE,WAAW,YAAxBF,2BAA4B,EAAE,AAAD,EAAGG,GAAG,CAAC1H;YAClD2H,YAAY,AAACJ,CAAAA,CAAAA,0BAAAA,+BAAAA,YAAaI,UAAU,YAAvBJ,0BAA2B,EAAE,AAAD,EAAGG,GAAG,CAAC1H;YAChD4H,UAAU,AAACL,CAAAA,CAAAA,wBAAAA,+BAAAA,YAAaK,QAAQ,YAArBL,wBAAyB,EAAE,AAAD,EAAGG,GAAG,CAAC1H;QAC9C;QACA,MAAM6H,gBAAgB,IAAI,CAACrB,mBAAmB,CAAC,IAAI,CAACvE,cAAc,CAAC6B,MAAM;QACzE,MAAMgE,oBAAoBtI,KAAK,IAAI,CAACqB,OAAO,EAAE/B;QAC7C,MAAMiJ,8BAA8BvI,KAClC,IAAI,CAACqB,OAAO,EACZ,UACA,AAAC,KAAE7B,4BAA0B;QAE/B,MAAMgJ,kCAAkCxI,KACtC,IAAI,CAACqB,OAAO,EACZ,UACA,AAAC,KAAE9B,sCAAoC;QAEzCY,YAAYmI;QACZnI,YAAYoI;QACZpI,YAAYqI;QACZ,MAAMpI,gBACJkI,mBACArG,KAAKyC,SAAS,CAAC2D,eAAe,MAAM;QAEtC,MAAMjI,gBACJmI,6BACA,wDAAwD;QACxD,4BAA4B;QAC5B9H,0BAA0B4H;QAG5B,MAAMI,uBAAuBxG,KAAKyC,SAAS,CACzCgD,SAASO,WAAW,CAACS,MAAM,CAACrI;QAG9B,MAAMD,gBACJoI,iCACA,AAAC,gDAA6CvG,KAAKyC,SAAS,CAC1D+D,wBACA;QAGJ,MAAME,YAAY;eAAI9C,YAAYzE,IAAI,CAACwH,IAAI;SAAG;QAC9C,IAAI/C,YAAYgD,MAAM,CAACC,GAAG,EAAE;YAC1BH,UAAU7B,IAAI,CAAC;QACjB;QACA,IAAIjB,YAAYgD,MAAM,CAACE,KAAK,EAAE;YAC5BJ,UAAU7B,IAAI,CAAC;QACjB;QAEA,MAAMa,iBAAiB/G,gBAAgB+H;QACvC,MAAMK,sBAAsB,IAAI,CAACvB,yBAAyB,CACxDC,UACA,IAAI,CAAChF,oBAAoB,CAAC4B,MAAM,IAChCqD;QAEF,MAAMsB,wBAAwB,AAAC,6BAA0BhH,KAAKyC,SAAS,CACrEsE,qBACA,MACA,KACA;QACF,MAAM5I,gBACJJ,KAAK,IAAI,CAACqB,OAAO,EAAE,UAAU,IAAI,CAACgG,OAAO,EAAE,sBAC3C4B;QAEF,MAAM7I,gBACJJ,KAAK,IAAI,CAACqB,OAAO,EAAE,UAAU,IAAI,CAACgG,OAAO,EAAE,oBAC3C9G;IAEJ;IAEA,MAAc2I,gCAA+C;YAK1CC;QAJjB,MAAMA,qBAAqB,IAAI,CAACC,wBAAwB,CACtD,IAAI,CAACxG,mBAAmB,CAAC0B,MAAM;QAGjC,MAAM+E,WAAWF,CAAAA,uCAAAA,kCAAAA,mBAAoBG,UAAU,CAAC,IAAI,qBAAnCH,gCAAqCE,QAAQ,KAAI,EAAE;QAEpE,MAAME,+BAA+BvJ,KACnC,IAAI,CAACqB,OAAO,EACZ,UACA,IAAI,CAACgG,OAAO,EACZ,AAAC,KAAEvH;QAELK,YAAYoJ;QACZ,MAAMnJ,gBACJmJ,8BACAtH,KAAKyC,SAAS,CAAC2E,UAAU,MAAM;IAEnC;IAEA,MAAcG,6BAA4C;QACxD,MAAMC,wBAAwB,IAAI,CAACzC,mBAAmB,CACpD;YACE,IAAI,CAACvE,cAAc,CAACmE,GAAG,CAACjG,YAAY,SAAS,UAAU;YACvD,IAAI,CAAC8B,cAAc,CAACmE,GAAG,CAACjG,YAAY,SAAS,UAAU;SACxD,CAAC+H,MAAM,CAACgB;QAEX,MAAMC,4BAA4B3J,KAChC,IAAI,CAACqB,OAAO,EACZ,AAAC,cAAW/B;QAEda,YAAYwJ;QACZ,MAAMvJ,gBACJuJ,2BACA1H,KAAKyC,SAAS,CAAC+E,uBAAuB,MAAM;IAEhD;IAEA,MAAMG,iBACJ5H,QAAgB,EAChBT,IAA+B,EAChB;QADfA,IAAAA,iBAAAA,OAAwB;QAExB,IAAI,CAACoB,aAAa,CAACK,GAAG,CACpBrC,YAAYY,MAAM,UAAUS,WAC5B,MAAMD,oBACJ,IAAI,CAACV,OAAO,EACZ,AAAC,KAAE3B,qBAAmB,SACtBsC,UACAT;IAGN;IAEQsI,mBAAmB3G,SAAqC,EAAE;QAChE,MAAMC,WAA6B;YACjC2F,KAAK,CAAC;YACNgB,oBAAoB;YACpBjF,OAAO,CAAC;YACRkF,sBAAsB;QACxB;QACA,KAAK,MAAM9F,KAAKf,UAAW;YACzBa,OAAOC,MAAM,CAACb,SAAS2F,GAAG,EAAE7E,EAAE6E,GAAG;YACjC/E,OAAOC,MAAM,CAACb,SAAS0B,KAAK,EAAEZ,EAAEY,KAAK;YAErC1B,SAAS2G,kBAAkB,GACzB3G,SAAS2G,kBAAkB,IAAI7F,EAAE6F,kBAAkB;YACrD3G,SAAS4G,oBAAoB,GAC3B5G,SAAS4G,oBAAoB,IAAI9F,EAAE8F,oBAAoB;QAC3D;QACA5G,SAAS2F,GAAG,GAAG3E,gBAAgBhB,SAAS2F,GAAG;QAC3C3F,SAAS0B,KAAK,GAAGV,gBAAgBhB,SAAS0B,KAAK;QAC/C,OAAO1B;IACT;IAEA,MAAc6G,wBAAuC;QACnD,MAAMC,eAAe,IAAI,CAACJ,kBAAkB,CAAC,IAAI,CAAClH,aAAa,CAAC2B,MAAM;QACtE,MAAMG,OAAOxC,KAAKyC,SAAS,CAACuF,cAAc,MAAM;QAEhD,MAAMC,uBAAuBlK,KAC3B,IAAI,CAACqB,OAAO,EACZ,UACA,AAAC,KAAE3B,qBAAmB;QAExB,MAAMyK,qBAAqBnK,KACzB,IAAI,CAACqB,OAAO,EACZ,UACA,AAAC,KAAE3B,qBAAmB;QAExBS,YAAY+J;QACZ/J,YAAYgK;QACZ,MAAM/J,gBAAgB8J,sBAAsBzF;QAC5C,MAAMrE,gBACJ+J,oBACA,AAAC,+BAA4BlI,KAAKyC,SAAS,CAACD;IAEhD;IAEA;;GAEC,GACD,MAAM2F,uBACJpI,QAAgB,EAChBT,IAAwD,EACtC;QAClB,MAAM8I,yBAAyBlJ,gBAC7Ba,UACA,IAAI,CAACX,OAAO,EACZ5B,qBACA8B,MACA;QAGF,uHAAuH;QACvH,IAAI,CAACV,WAAWwJ,yBAAyB;YACvC,OAAO;QACT;QAEA,IAAI,CAACzH,mBAAmB,CAACI,GAAG,CAC1BrC,YACEY,SAAS,gBAAgBA,SAAS,oBAAoB,SAASA,MAC/D,UACAS,WAEF,MAAMD,oBACJ,IAAI,CAACV,OAAO,EACZ5B,qBACAuC,UACAT;QAIJ,OAAO;IACT;IAEA+I,sBAAsBjI,GAAa,EAAE;QACnC,OAAO,IAAI,CAACO,mBAAmB,CAACgE,GAAG,CAACvE;IACtC;IAEAkI,yBAAyBlI,GAAa,EAAE;QACtC,OAAO,IAAI,CAACO,mBAAmB,CAACR,MAAM,CAACC;IACzC;IAEQ+G,yBACNlG,SAAgD,EAC5B;QACpB,MAAMC,WAA+B;YACnC4D,SAAS;YACTuC,YAAY,CAAC;YACbkB,kBAAkB,EAAE;YACpBC,WAAW,CAAC;QACd;QACA,IAAIC,kBAAyDC;QAC7D,KAAK,MAAM1G,KAAKf,UAAW;YACzBa,OAAOC,MAAM,CAACb,SAASsH,SAAS,EAAExG,EAAEwG,SAAS;YAC7C1G,OAAOC,MAAM,CAACb,SAASmG,UAAU,EAAErF,EAAEqF,UAAU;YAC/C,IAAIrF,EAAEyG,eAAe,EAAE;gBACrBA,kBAAkBzG,EAAEyG,eAAe;YACrC;QACF;QACAvH,SAASsH,SAAS,GAAGtG,gBAAgBhB,SAASsH,SAAS;QACvDtH,SAASmG,UAAU,GAAGnF,gBAAgBhB,SAASmG,UAAU;QACzD,MAAMsB,2BAA2B,CAC/BC;gBAIcH;YAFd,OAAO;gBACL,GAAGG,GAAG;gBACNC,OAAO;uBAAKJ,CAAAA,yBAAAA,mCAAAA,gBAAiBI,KAAK,YAAtBJ,yBAA0B,EAAE;uBAAMG,IAAIC,KAAK;iBAAC;YAC1D;QACF;QACA,KAAK,MAAMzI,OAAO0B,OAAO6E,IAAI,CAACzF,SAASmG,UAAU,EAAG;YAClD,MAAMyB,QAAQ5H,SAASmG,UAAU,CAACjH,IAAI;YACtCc,SAASmG,UAAU,CAACjH,IAAI,GAAGuI,yBAAyBG;QACtD;QACA,KAAK,MAAM1I,OAAO0B,OAAO6E,IAAI,CAACzF,SAASsH,SAAS,EAAG;YACjD,MAAMM,QAAQ5H,SAASsH,SAAS,CAACpI,IAAI;YACrCc,SAASsH,SAAS,CAACpI,IAAI,GAAGuI,yBAAyBG;QACrD;QACA,KAAK,MAAMF,OAAO9G,OAAOO,MAAM,CAACnB,SAASsH,SAAS,EAAEO,MAAM,CACxDjH,OAAOO,MAAM,CAACnB,SAASmG,UAAU,GAChC;YACD,KAAK,MAAM2B,WAAWJ,IAAIxB,QAAQ,CAAE;gBAClC,IAAI,CAAC4B,QAAQC,MAAM,EAAE;oBACnBD,QAAQC,MAAM,GAAGhK,iBAAiB+J,QAAQE,cAAc,EAAE,EAAE,EAAE;wBAC5DC,WAAW;wBACXC,WAAW;wBACXC,QAAQ;oBACV,GAAGC,MAAM,CAACC,UAAU,CAAC,OAAO;gBAC9B;YACF;QACF;QACArI,SAASqH,gBAAgB,GAAGzG,OAAO6E,IAAI,CAACzF,SAASmG,UAAU;QAE3D,OAAOnG;IACT;IAEA,MAAcsI,0BAAyC;QACrD,MAAMtC,qBAAqB,IAAI,CAACC,wBAAwB,CACtD,IAAI,CAACxG,mBAAmB,CAAC0B,MAAM;QAGjC,8CAA8C;QAC9C,IAAK,MAAMjC,OAAO8G,mBAAmBG,UAAU,CAAE;YAC/CH,mBAAmBG,UAAU,CAACjH,IAAI,CAACgH,QAAQ,CAACqC,OAAO,CAAC,CAACT;gBACnD,IAAI,CAACA,QAAQC,MAAM,CAACS,UAAU,CAAC,MAAM;oBACnC,MAAMC,aAAa3K,eAAegK,QAAQC,MAAM;oBAChD,IAAIU,WAAW7C,KAAK,IAAI,CAAC6C,WAAWC,QAAQ,EAAE;wBAC5C,MAAM,qBAA8C,CAA9C,IAAIC,MAAM,AAAC,qBAAkBb,QAAQC,MAAM,GAA3C,qBAAA;mCAAA;wCAAA;0CAAA;wBAA6C;oBACrD;oBACAD,QAAQC,MAAM,GAAGU,WAAWC,QAAQ;gBACtC;YACF;QACF;QAEA,MAAMxB,yBAAyBrK,KAC7B,IAAI,CAACqB,OAAO,EACZ,UACA5B;QAEFU,YAAYkK;QACZ,MAAMjK,gBACJiK,wBACApI,KAAKyC,SAAS,CAACyE,oBAAoB,MAAM;IAE7C;IAEA,MAAM4C,kBAAkB/J,QAAgB,EAAiB;QACvD,IAAI,CAACa,cAAc,CAACG,GAAG,CACrBrC,YAAY,SAAS,UAAUqB,WAC/B,MAAMD,oBAAoB,IAAI,CAACV,OAAO,EAAE1B,gBAAgBqC;IAE5D;IAEQoD,oBAAoBlC,SAAkC,EAAE;QAC9D,MAAMC,WAA0B,CAAC;QACjC,KAAK,MAAMc,KAAKf,UAAW;YACzBa,OAAOC,MAAM,CAACb,UAAUc;QAC1B;QACA,OAAOE,gBAAgBhB;IACzB;IAEA,MAAc6I,qBAAoC;QAChD,MAAMC,gBAAgB,IAAI,CAAC7G,mBAAmB,CAAC,IAAI,CAACvC,cAAc,CAACyB,MAAM;QACzE,MAAM4H,oBAAoBlM,KAAK,IAAI,CAACqB,OAAO,EAAE,UAAU1B;QACvDQ,YAAY+L;QACZ,MAAM9L,gBACJ8L,mBACAjK,KAAKyC,SAAS,CAACuH,eAAe,MAAM;IAExC;IAEA,MAAME,eAAe,KAQpB,EAAE;QARkB,IAAA,EACnBpE,WAAW,EACXC,kBAAkB,EAClBnC,WAAW,EAKZ,GARoB;QASnB,MAAM,IAAI,CAACzB,mBAAmB;QAC9B,MAAM,IAAI,CAACU,qBAAqB;QAChC,MAAM,IAAI,CAACI,qBAAqB;QAChC,MAAM,IAAI,CAAC4C,kBAAkB,CAACjC,aAAakC,aAAaC;QACxD,MAAM,IAAI,CAACwB,0BAA0B;QACrC,MAAM,IAAI,CAACiC,uBAAuB;QAClC,MAAM,IAAI,CAACvC,6BAA6B;QACxC,MAAM,IAAI,CAACc,qBAAqB;QAChC,MAAM,IAAI,CAACgC,kBAAkB;QAE7B,IAAII,QAAQC,GAAG,CAACC,eAAe,IAAI,MAAM;YACvC,MAAM,IAAI,CAAChH,iBAAiB;QAC9B;IACF;IAnpBAiH,YAAY,EACVlL,OAAO,EACPgG,OAAO,EACP/D,aAAa,EAKd,CAAE;aAvBKhB,kBAAiD,IAAIyD;aACrDxD,oBAAqD,IAAIwD;aACzDvD,oBAAkD,IAAIuD;aACtDtD,iBAA+C,IAAIsD;aACnDrD,uBAA2D,IAAIqD;aAC/DpD,gBAAiD,IAAIoD;aACrDnD,sBACN,IAAImD;aACElD,iBAA6C,IAAIkD;aACjDjD,eAA4C,IAAIiD;QAetD,IAAI,CAAC1E,OAAO,GAAGA;QACf,IAAI,CAACgG,OAAO,GAAGA;QACf,IAAI,CAAC/D,aAAa,GAAGA;IACvB;AAwoBF;AAEA,SAASa,gBAAgBqI,GAAwB;IAC/C,OAAOzI,OAAO6E,IAAI,CAAC4D,KAChBC,IAAI,GACJC,MAAM,CACL,CAACC,KAAKtK;QACJsK,GAAG,CAACtK,IAAI,GAAGmK,GAAG,CAACnK,IAAI;QACnB,OAAOsK;IACT,GACA,CAAC;AAEP", "ignoreList": [0]}