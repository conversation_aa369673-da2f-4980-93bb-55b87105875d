{"version": 3, "sources": ["../../src/export/index.ts"], "sourcesContent": ["import type {\n  ExportAppResult,\n  ExportAppOptions,\n  WorkerRenderOptsPartial,\n  ExportPagesResult,\n  ExportPathEntry,\n} from './types'\nimport {\n  createStaticWorker,\n  type PrerenderManifest,\n  type StaticWorker,\n} from '../build'\nimport type { PagesManifest } from '../build/webpack/plugins/pages-manifest-plugin'\n\nimport { bold, yellow } from '../lib/picocolors'\nimport findUp from 'next/dist/compiled/find-up'\nimport { existsSync, promises as fs } from 'fs'\n\nimport '../server/require-hook'\n\nimport { dirname, join, resolve, sep, relative } from 'path'\nimport { formatAmpMessages } from '../build/output/index'\nimport type { AmpPageStatus } from '../build/output/index'\nimport * as Log from '../build/output/log'\nimport {\n  RSC_SEGMENT_SUFFIX,\n  RSC_SEGMENTS_DIR_SUFFIX,\n  RSC_SUFFIX,\n  SSG_FALLBACK_EXPORT_ERROR,\n} from '../lib/constants'\nimport { recursiveCopy } from '../lib/recursive-copy'\nimport {\n  BUILD_ID_FILE,\n  CLIENT_PUBLIC_FILES_PATH,\n  CLIENT_STATIC_FILES_PATH,\n  EXPORT_DETAIL,\n  EXPORT_MARKER,\n  NEXT_FONT_MANIFEST,\n  MIDDLEWARE_MANIFEST,\n  PAGES_MANIFEST,\n  PHASE_EXPORT,\n  PRERENDER_MANIFEST,\n  SERVER_DIRECTORY,\n  SERVER_REFERENCE_MANIFEST,\n  APP_PATH_ROUTES_MANIFEST,\n  ROUTES_MANIFEST,\n  FUNCTIONS_CONFIG_MANIFEST,\n} from '../shared/lib/constants'\nimport loadConfig from '../server/config'\nimport type { ExportPathMap } from '../server/config-shared'\nimport { eventCliSession } from '../telemetry/events'\nimport { hasNextSupport } from '../server/ci-info'\nimport { Telemetry } from '../telemetry/storage'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport { denormalizePagePath } from '../shared/lib/page-path/denormalize-page-path'\nimport { loadEnvConfig } from '@next/env'\nimport { isAPIRoute } from '../lib/is-api-route'\nimport { getPagePath } from '../server/require'\nimport type { Span } from '../trace'\nimport type { MiddlewareManifest } from '../build/webpack/plugins/middleware-plugin'\nimport { isAppRouteRoute } from '../lib/is-app-route-route'\nimport { isAppPageRoute } from '../lib/is-app-page-route'\nimport isError from '../lib/is-error'\nimport { formatManifest } from '../build/manifests/formatter/format-manifest'\nimport { TurborepoAccessTraceResult } from '../build/turborepo-access-trace'\nimport { createProgress } from '../build/progress'\nimport type { DeepReadonly } from '../shared/lib/deep-readonly'\nimport { isInterceptionRouteRewrite } from '../lib/generate-interception-routes-rewrites'\nimport type { ActionManifest } from '../build/webpack/plugins/flight-client-entry-plugin'\nimport { extractInfoFromServerReferenceId } from '../shared/lib/server-reference-info'\nimport { convertSegmentPathToStaticExportFilename } from '../shared/lib/segment-cache/segment-value-encoding'\nimport { getNextBuildDebuggerPortOffset } from '../lib/worker'\n\nexport class ExportError extends Error {\n  code = 'NEXT_EXPORT_ERROR'\n}\n\nasync function exportAppImpl(\n  dir: string,\n  options: Readonly<ExportAppOptions>,\n  span: Span\n): Promise<ExportAppResult | null> {\n  dir = resolve(dir)\n\n  // attempt to load global env values so they are available in next.config.js\n  span.traceChild('load-dotenv').traceFn(() => loadEnvConfig(dir, false, Log))\n\n  const { enabledDirectories } = options\n\n  const nextConfig =\n    options.nextConfig ||\n    (await span.traceChild('load-next-config').traceAsyncFn(() =>\n      loadConfig(PHASE_EXPORT, dir, {\n        debugPrerender: options.debugPrerender,\n      })\n    ))\n\n  const distDir = join(dir, nextConfig.distDir)\n  const telemetry = options.buildExport ? null : new Telemetry({ distDir })\n\n  if (telemetry) {\n    telemetry.record(\n      eventCliSession(distDir, nextConfig, {\n        webpackVersion: null,\n        cliCommand: 'export',\n        isSrcDir: null,\n        hasNowJson: !!(await findUp('now.json', { cwd: dir })),\n        isCustomServer: null,\n        turboFlag: false,\n        pagesDir: null,\n        appDir: null,\n      })\n    )\n  }\n\n  const subFolders = nextConfig.trailingSlash && !options.buildExport\n\n  if (!options.silent && !options.buildExport) {\n    Log.info(`using build directory: ${distDir}`)\n  }\n\n  const buildIdFile = join(distDir, BUILD_ID_FILE)\n\n  if (!existsSync(buildIdFile)) {\n    throw new ExportError(\n      `Could not find a production build in the '${distDir}' directory. Try building your app with 'next build' before starting the static export. https://nextjs.org/docs/messages/next-export-no-build-id`\n    )\n  }\n\n  const customRoutes = ['rewrites', 'redirects', 'headers'].filter(\n    (config) => typeof nextConfig[config] === 'function'\n  )\n\n  if (!hasNextSupport && !options.buildExport && customRoutes.length > 0) {\n    Log.warn(\n      `rewrites, redirects, and headers are not applied when exporting your application, detected (${customRoutes.join(\n        ', '\n      )}). See more info here: https://nextjs.org/docs/messages/export-no-custom-routes`\n    )\n  }\n\n  const buildId = await fs.readFile(buildIdFile, 'utf8')\n\n  const pagesManifest =\n    !options.pages &&\n    (require(join(distDir, SERVER_DIRECTORY, PAGES_MANIFEST)) as PagesManifest)\n\n  let prerenderManifest: DeepReadonly<PrerenderManifest> | undefined\n  try {\n    prerenderManifest = require(join(distDir, PRERENDER_MANIFEST))\n  } catch {}\n\n  let appRoutePathManifest: Record<string, string> | undefined\n  try {\n    appRoutePathManifest = require(join(distDir, APP_PATH_ROUTES_MANIFEST))\n  } catch (err) {\n    if (\n      isError(err) &&\n      (err.code === 'ENOENT' || err.code === 'MODULE_NOT_FOUND')\n    ) {\n      // the manifest doesn't exist which will happen when using\n      // \"pages\" dir instead of \"app\" dir.\n      appRoutePathManifest = undefined\n    } else {\n      // the manifest is malformed (invalid json)\n      throw err\n    }\n  }\n\n  const excludedPrerenderRoutes = new Set<string>()\n  const pages = options.pages || Object.keys(pagesManifest)\n  const defaultPathMap: ExportPathMap = {}\n\n  let hasApiRoutes = false\n  for (const page of pages) {\n    // _document and _app are not real pages\n    // _error is exported as 404.html later on\n    // API Routes are Node.js functions\n\n    if (isAPIRoute(page)) {\n      hasApiRoutes = true\n      continue\n    }\n\n    if (page === '/_document' || page === '/_app' || page === '/_error') {\n      continue\n    }\n\n    // iSSG pages that are dynamic should not export templated version by\n    // default. In most cases, this would never work. There is no server that\n    // could run `getStaticProps`. If users make their page work lazily, they\n    // can manually add it to the `exportPathMap`.\n    if (prerenderManifest?.dynamicRoutes[page]) {\n      excludedPrerenderRoutes.add(page)\n      continue\n    }\n\n    defaultPathMap[page] = { page }\n  }\n\n  const mapAppRouteToPage = new Map<string, string>()\n  if (!options.buildExport && appRoutePathManifest) {\n    for (const [pageName, routePath] of Object.entries(appRoutePathManifest)) {\n      mapAppRouteToPage.set(routePath, pageName)\n      if (\n        isAppPageRoute(pageName) &&\n        !prerenderManifest?.routes[routePath] &&\n        !prerenderManifest?.dynamicRoutes[routePath]\n      ) {\n        defaultPathMap[routePath] = {\n          page: pageName,\n          _isAppDir: true,\n        }\n      }\n    }\n  }\n\n  // Initialize the output directory\n  const outDir = options.outdir\n\n  if (outDir === join(dir, 'public')) {\n    throw new ExportError(\n      `The 'public' directory is reserved in Next.js and can not be used as the export out directory. https://nextjs.org/docs/messages/can-not-output-to-public`\n    )\n  }\n\n  if (outDir === join(dir, 'static')) {\n    throw new ExportError(\n      `The 'static' directory is reserved in Next.js and can not be used as the export out directory. https://nextjs.org/docs/messages/can-not-output-to-static`\n    )\n  }\n\n  await fs.rm(outDir, { recursive: true, force: true })\n  await fs.mkdir(join(outDir, '_next', buildId), { recursive: true })\n\n  await fs.writeFile(\n    join(distDir, EXPORT_DETAIL),\n    formatManifest({\n      version: 1,\n      outDirectory: outDir,\n      success: false,\n    }),\n    'utf8'\n  )\n\n  // Copy static directory\n  if (!options.buildExport && existsSync(join(dir, 'static'))) {\n    if (!options.silent) {\n      Log.info('Copying \"static\" directory')\n    }\n    await span\n      .traceChild('copy-static-directory')\n      .traceAsyncFn(() =>\n        recursiveCopy(join(dir, 'static'), join(outDir, 'static'))\n      )\n  }\n\n  // Copy .next/static directory\n  if (\n    !options.buildExport &&\n    existsSync(join(distDir, CLIENT_STATIC_FILES_PATH))\n  ) {\n    if (!options.silent) {\n      Log.info('Copying \"static build\" directory')\n    }\n    await span\n      .traceChild('copy-next-static-directory')\n      .traceAsyncFn(() =>\n        recursiveCopy(\n          join(distDir, CLIENT_STATIC_FILES_PATH),\n          join(outDir, '_next', CLIENT_STATIC_FILES_PATH)\n        )\n      )\n  }\n\n  // Get the exportPathMap from the config file\n  if (typeof nextConfig.exportPathMap !== 'function') {\n    nextConfig.exportPathMap = async (defaultMap) => {\n      return defaultMap\n    }\n  }\n\n  const {\n    i18n,\n    images: { loader = 'default', unoptimized },\n  } = nextConfig\n\n  if (i18n && !options.buildExport) {\n    throw new ExportError(\n      `i18n support is not compatible with next export. See here for more info on deploying: https://nextjs.org/docs/messages/export-no-custom-routes`\n    )\n  }\n\n  if (!options.buildExport) {\n    const { isNextImageImported } = await span\n      .traceChild('is-next-image-imported')\n      .traceAsyncFn(() =>\n        fs\n          .readFile(join(distDir, EXPORT_MARKER), 'utf8')\n          .then((text) => JSON.parse(text))\n          .catch(() => ({}))\n      )\n\n    if (\n      isNextImageImported &&\n      loader === 'default' &&\n      !unoptimized &&\n      !hasNextSupport\n    ) {\n      throw new ExportError(\n        `Image Optimization using the default loader is not compatible with export.\n  Possible solutions:\n    - Use \\`next start\\` to run a server, which includes the Image Optimization API.\n    - Configure \\`images.unoptimized = true\\` in \\`next.config.js\\` to disable the Image Optimization API.\n  Read more: https://nextjs.org/docs/messages/export-image-api`\n      )\n    }\n  }\n\n  let serverActionsManifest: ActionManifest | undefined\n  if (enabledDirectories.app) {\n    serverActionsManifest = require(\n      join(distDir, SERVER_DIRECTORY, SERVER_REFERENCE_MANIFEST + '.json')\n    ) as ActionManifest\n\n    if (nextConfig.output === 'export') {\n      const routesManifest = require(join(distDir, ROUTES_MANIFEST))\n\n      // We already prevent rewrites earlier in the process, however Next.js will insert rewrites\n      // for interception routes so we need to check for that here.\n      if (routesManifest?.rewrites?.beforeFiles?.length > 0) {\n        const hasInterceptionRouteRewrite =\n          routesManifest.rewrites.beforeFiles.some(isInterceptionRouteRewrite)\n\n        if (hasInterceptionRouteRewrite) {\n          throw new ExportError(\n            `Intercepting routes are not supported with static export.\\nRead more: https://nextjs.org/docs/app/building-your-application/deploying/static-exports#unsupported-features`\n          )\n        }\n      }\n\n      const actionIds = [\n        ...Object.keys(serverActionsManifest.node),\n        ...Object.keys(serverActionsManifest.edge),\n      ]\n\n      if (\n        actionIds.some(\n          (actionId) =>\n            extractInfoFromServerReferenceId(actionId).type === 'server-action'\n        )\n      ) {\n        throw new ExportError(\n          `Server Actions are not supported with static export.\\nRead more: https://nextjs.org/docs/app/building-your-application/deploying/static-exports#unsupported-features`\n        )\n      }\n    }\n  }\n\n  // Start the rendering process\n  const renderOpts: WorkerRenderOptsPartial = {\n    previewProps: prerenderManifest?.preview,\n    nextExport: true,\n    assetPrefix: nextConfig.assetPrefix.replace(/\\/$/, ''),\n    distDir,\n    dev: false,\n    basePath: nextConfig.basePath,\n    trailingSlash: nextConfig.trailingSlash,\n    canonicalBase: nextConfig.amp?.canonicalBase || '',\n    ampSkipValidation: nextConfig.experimental.amp?.skipValidation || false,\n    ampOptimizerConfig: nextConfig.experimental.amp?.optimizer || undefined,\n    locales: i18n?.locales,\n    locale: i18n?.defaultLocale,\n    defaultLocale: i18n?.defaultLocale,\n    domainLocales: i18n?.domains,\n    disableOptimizedLoading: nextConfig.experimental.disableOptimizedLoading,\n    // Exported pages do not currently support dynamic HTML.\n    supportsDynamicResponse: false,\n    crossOrigin: nextConfig.crossOrigin,\n    optimizeCss: nextConfig.experimental.optimizeCss,\n    nextConfigOutput: nextConfig.output,\n    nextScriptWorkers: nextConfig.experimental.nextScriptWorkers,\n    largePageDataBytes: nextConfig.experimental.largePageDataBytes,\n    serverActions: nextConfig.experimental.serverActions,\n    serverComponents: enabledDirectories.app,\n    cacheLifeProfiles: nextConfig.experimental.cacheLife,\n    nextFontManifest: require(\n      join(distDir, 'server', `${NEXT_FONT_MANIFEST}.json`)\n    ),\n    images: nextConfig.images,\n    ...(enabledDirectories.app\n      ? {\n          serverActionsManifest,\n        }\n      : {}),\n    deploymentId: nextConfig.deploymentId,\n    htmlLimitedBots: nextConfig.htmlLimitedBots.source,\n    experimental: {\n      clientTraceMetadata: nextConfig.experimental.clientTraceMetadata,\n      expireTime: nextConfig.expireTime,\n      staleTimes: nextConfig.experimental.staleTimes,\n      cacheComponents: nextConfig.experimental.cacheComponents ?? false,\n      clientSegmentCache:\n        nextConfig.experimental.clientSegmentCache === 'client-only'\n          ? 'client-only'\n          : Boolean(nextConfig.experimental.clientSegmentCache),\n      clientParamParsing: nextConfig.experimental.clientParamParsing ?? false,\n      dynamicOnHover: nextConfig.experimental.dynamicOnHover ?? false,\n      inlineCss: nextConfig.experimental.inlineCss ?? false,\n      authInterrupts: !!nextConfig.experimental.authInterrupts,\n    },\n    reactMaxHeadersLength: nextConfig.reactMaxHeadersLength,\n    hasReadableErrorStacks:\n      nextConfig.experimental.serverSourceMaps === true &&\n      // TODO(NDX-531): Checking (and setting) the minify flags should be\n      // unnecessary once name mapping is fixed.\n      (process.env.TURBOPACK\n        ? nextConfig.experimental.turbopackMinify === false\n        : nextConfig.experimental.serverMinification === false) &&\n      nextConfig.experimental.enablePrerenderSourceMaps === true,\n  }\n\n  const { publicRuntimeConfig } = nextConfig\n\n  if (Object.keys(publicRuntimeConfig).length > 0) {\n    renderOpts.runtimeConfig = publicRuntimeConfig\n  }\n\n  // We need this for server rendering the Link component.\n  ;(globalThis as any).__NEXT_DATA__ = {\n    nextExport: true,\n  }\n\n  const exportPathMap = await span\n    .traceChild('run-export-path-map')\n    .traceAsyncFn(async () => {\n      const exportMap = await nextConfig.exportPathMap(defaultPathMap, {\n        dev: false,\n        dir,\n        outDir,\n        distDir,\n        buildId,\n      })\n      return exportMap\n    })\n\n  // only add missing 404 page when `buildExport` is false\n  if (!options.buildExport) {\n    // only add missing /404 if not specified in `exportPathMap`\n    if (!exportPathMap['/404']) {\n      exportPathMap['/404'] = { page: '/_error' }\n    }\n\n    /**\n     * exports 404.html for backwards compat\n     * E.g. GitHub Pages, GitLab Pages, Cloudflare Pages, Netlify\n     */\n    if (!exportPathMap['/404.html']) {\n      // alias /404.html to /404 to be compatible with custom 404 / _error page\n      exportPathMap['/404.html'] = exportPathMap['/404']\n    }\n  }\n\n  const allExportPaths: ExportPathEntry[] = []\n  const seenExportPaths = new Set<string>()\n  const fallbackEnabledPages = new Set<string>()\n\n  for (const [path, entry] of Object.entries(exportPathMap)) {\n    // make sure to prevent duplicates\n    const normalizedPath = denormalizePagePath(normalizePagePath(path))\n\n    if (seenExportPaths.has(normalizedPath)) {\n      continue\n    }\n\n    seenExportPaths.add(normalizedPath)\n\n    if (!entry._isAppDir && isAPIRoute(entry.page)) {\n      hasApiRoutes = true\n      continue\n    }\n\n    allExportPaths.push({ ...entry, path: normalizedPath })\n\n    if (prerenderManifest && !options.buildExport) {\n      const prerenderInfo = prerenderManifest.dynamicRoutes[entry.page]\n\n      if (prerenderInfo && prerenderInfo.fallback !== false) {\n        fallbackEnabledPages.add(entry.page)\n      }\n    }\n  }\n\n  if (allExportPaths.length === 0) {\n    return null\n  }\n\n  if (fallbackEnabledPages.size > 0) {\n    throw new ExportError(\n      `Found pages with \\`fallback\\` enabled:\\n${[...fallbackEnabledPages].join(\n        '\\n'\n      )}\\n${SSG_FALLBACK_EXPORT_ERROR}\\n`\n    )\n  }\n\n  let hasMiddleware = false\n\n  if (!options.buildExport) {\n    try {\n      const middlewareManifest = require(\n        join(distDir, SERVER_DIRECTORY, MIDDLEWARE_MANIFEST)\n      ) as MiddlewareManifest\n\n      const functionsConfigManifest = require(\n        join(distDir, SERVER_DIRECTORY, FUNCTIONS_CONFIG_MANIFEST)\n      )\n\n      hasMiddleware =\n        Object.keys(middlewareManifest.middleware).length > 0 ||\n        Boolean(functionsConfigManifest.functions?.['/_middleware'])\n    } catch {}\n\n    // Warn if the user defines a path for an API page\n    if (hasApiRoutes || hasMiddleware) {\n      if (nextConfig.output === 'export') {\n        Log.warn(\n          yellow(\n            `Statically exporting a Next.js application via \\`next export\\` disables API routes and middleware.`\n          ) +\n            `\\n` +\n            yellow(\n              `This command is meant for static-only hosts, and is` +\n                ' ' +\n                bold(`not necessary to make your application static.`)\n            ) +\n            `\\n` +\n            yellow(\n              `Pages in your application without server-side data dependencies will be automatically statically exported by \\`next build\\`, including pages powered by \\`getStaticProps\\`.`\n            ) +\n            `\\n` +\n            yellow(\n              `Learn more: https://nextjs.org/docs/messages/api-routes-static-export`\n            )\n        )\n      }\n    }\n  }\n\n  const pagesDataDir = options.buildExport\n    ? outDir\n    : join(outDir, '_next/data', buildId)\n\n  const ampValidations: AmpPageStatus = {}\n\n  const publicDir = join(dir, CLIENT_PUBLIC_FILES_PATH)\n  // Copy public directory\n  if (!options.buildExport && existsSync(publicDir)) {\n    if (!options.silent) {\n      Log.info('Copying \"public\" directory')\n    }\n    await span.traceChild('copy-public-directory').traceAsyncFn(() =>\n      recursiveCopy(publicDir, outDir, {\n        filter(path) {\n          // Exclude paths used by pages\n          return !exportPathMap[path]\n        },\n      })\n    )\n  }\n\n  const exportPagesInBatches = async (\n    worker: StaticWorker,\n    exportPaths: ExportPathEntry[],\n    renderResumeDataCachesByPage?: Record<string, string>\n  ): Promise<ExportPagesResult> => {\n    // Batch filtered pages into smaller batches, and call the export worker on\n    // each batch. We've set a default minimum of 25 pages per batch to ensure\n    // that even setups with only a few static pages can leverage a shared\n    // incremental cache, however this value can be configured.\n    const minPageCountPerBatch =\n      nextConfig.experimental.staticGenerationMinPagesPerWorker ?? 25\n\n    // Calculate the number of workers needed to ensure each batch has at least\n    // minPageCountPerBatch pages.\n    const numWorkers = Math.min(\n      options.numWorkers,\n      Math.ceil(exportPaths.length / minPageCountPerBatch)\n    )\n\n    // Calculate the page count per batch based on the number of workers.\n    const pageCountPerBatch = Math.ceil(exportPaths.length / numWorkers)\n\n    const batches = Array.from({ length: numWorkers }, (_, i) =>\n      exportPaths.slice(i * pageCountPerBatch, (i + 1) * pageCountPerBatch)\n    )\n\n    // Distribute remaining pages.\n    const remainingPages = exportPaths.slice(numWorkers * pageCountPerBatch)\n    remainingPages.forEach((page, index) => {\n      batches[index % batches.length].push(page)\n    })\n\n    return (\n      await Promise.all(\n        batches.map(async (batch) =>\n          worker.exportPages({\n            buildId,\n            exportPaths: batch,\n            parentSpanId: span.getId(),\n            pagesDataDir,\n            renderOpts,\n            options,\n            dir,\n            distDir,\n            outDir,\n            nextConfig,\n            cacheHandler: nextConfig.cacheHandler,\n            cacheMaxMemorySize: nextConfig.cacheMaxMemorySize,\n            fetchCache: true,\n            fetchCacheKeyPrefix: nextConfig.experimental.fetchCacheKeyPrefix,\n            renderResumeDataCachesByPage,\n          })\n        )\n      )\n    ).flat()\n  }\n\n  let initialPhaseExportPaths: ExportPathEntry[] = []\n  const finalPhaseExportPaths: ExportPathEntry[] = []\n\n  if (renderOpts.experimental.cacheComponents) {\n    for (const exportPath of allExportPaths) {\n      if (exportPath._allowEmptyStaticShell) {\n        finalPhaseExportPaths.push(exportPath)\n      } else {\n        initialPhaseExportPaths.push(exportPath)\n      }\n    }\n  } else {\n    initialPhaseExportPaths = allExportPaths\n  }\n\n  const progress = createProgress(\n    initialPhaseExportPaths.length + finalPhaseExportPaths.length,\n    options.statusMessage || 'Exporting'\n  )\n\n  const worker = createStaticWorker(nextConfig, {\n    debuggerPortOffset: getNextBuildDebuggerPortOffset({ kind: 'export-page' }),\n    progress,\n  })\n\n  const results = await exportPagesInBatches(worker, initialPhaseExportPaths)\n\n  if (finalPhaseExportPaths.length > 0) {\n    const renderResumeDataCachesByPage: Record<string, string> = {}\n\n    for (const { page, result } of results) {\n      if (!result) {\n        continue\n      }\n\n      if ('renderResumeDataCache' in result && result.renderResumeDataCache) {\n        // The last RDC for each page is used. We only need one. It should have\n        // all the entries that the fallback shell also needs. We don't need to\n        // merge them per page.\n        renderResumeDataCachesByPage[page] = result.renderResumeDataCache\n        // Remove the RDC string from the result so that it can be garbage\n        // collected, when there are more results for the same page.\n        result.renderResumeDataCache = undefined\n      }\n    }\n\n    const finalPhaseResults = await exportPagesInBatches(\n      worker,\n      finalPhaseExportPaths,\n      renderResumeDataCachesByPage\n    )\n\n    results.push(...finalPhaseResults)\n  }\n\n  let hadValidationError = false\n\n  const collector: ExportAppResult = {\n    byPath: new Map(),\n    byPage: new Map(),\n    ssgNotFoundPaths: new Set(),\n    turborepoAccessTraceResults: new Map(),\n  }\n\n  const failedExportAttemptsByPage: Map<string, boolean> = new Map()\n\n  for (const { result, path, page, pageKey } of results) {\n    if (!result) continue\n    if ('error' in result) {\n      failedExportAttemptsByPage.set(pageKey, true)\n      continue\n    }\n\n    if (result.turborepoAccessTraceResult) {\n      collector.turborepoAccessTraceResults?.set(\n        path,\n        TurborepoAccessTraceResult.fromSerialized(\n          result.turborepoAccessTraceResult\n        )\n      )\n    }\n\n    // Capture any amp validations.\n    if (result.ampValidations) {\n      for (const validation of result.ampValidations) {\n        ampValidations[validation.page] = validation.result\n        hadValidationError ||= validation.result.errors.length > 0\n      }\n    }\n\n    if (options.buildExport) {\n      // Update path info by path.\n      const info = collector.byPath.get(path) ?? {}\n      if (result.cacheControl) {\n        info.cacheControl = result.cacheControl\n      }\n      if (typeof result.metadata !== 'undefined') {\n        info.metadata = result.metadata\n      }\n\n      if (typeof result.hasEmptyStaticShell !== 'undefined') {\n        info.hasEmptyStaticShell = result.hasEmptyStaticShell\n      }\n\n      if (typeof result.hasPostponed !== 'undefined') {\n        info.hasPostponed = result.hasPostponed\n      }\n\n      if (typeof result.fetchMetrics !== 'undefined') {\n        info.fetchMetrics = result.fetchMetrics\n      }\n\n      collector.byPath.set(path, info)\n\n      // Update not found.\n      if (result.ssgNotFound === true) {\n        collector.ssgNotFoundPaths.add(path)\n      }\n\n      // Update durations.\n      const durations = collector.byPage.get(page) ?? {\n        durationsByPath: new Map<string, number>(),\n      }\n      durations.durationsByPath.set(path, result.duration)\n      collector.byPage.set(page, durations)\n    }\n  }\n\n  // Export mode provide static outputs that are not compatible with PPR mode.\n  if (!options.buildExport && nextConfig.experimental.ppr) {\n    // TODO: add message\n    throw new Error('Invariant: PPR cannot be enabled in export mode')\n  }\n\n  // copy prerendered routes to outDir\n  if (!options.buildExport && prerenderManifest) {\n    await Promise.all(\n      Object.keys(prerenderManifest.routes).map(async (unnormalizedRoute) => {\n        // Skip handling /_not-found route, it will copy the 404.html file later\n        if (unnormalizedRoute === '/_not-found') {\n          return\n        }\n        const { srcRoute } = prerenderManifest!.routes[unnormalizedRoute]\n        const appPageName = mapAppRouteToPage.get(srcRoute || '')\n        const pageName = appPageName || srcRoute || unnormalizedRoute\n        const isAppPath = Boolean(appPageName)\n        const isAppRouteHandler = appPageName && isAppRouteRoute(appPageName)\n\n        // returning notFound: true from getStaticProps will not\n        // output html/json files during the build\n        if (prerenderManifest!.notFoundRoutes.includes(unnormalizedRoute)) {\n          return\n        }\n        // TODO: This rewrites /index/foo to /index/index/foo. Investigate and\n        // fix. I presume this was because normalizePagePath was designed for\n        // some other use case and then reused here for static exports without\n        // realizing the implications.\n        const route = normalizePagePath(unnormalizedRoute)\n\n        const pagePath = getPagePath(pageName, distDir, undefined, isAppPath)\n        const distPagesDir = join(\n          pagePath,\n          // strip leading / and then recurse number of nested dirs\n          // to place from base folder\n          pageName\n            .slice(1)\n            .split('/')\n            .map(() => '..')\n            .join('/')\n        )\n\n        const orig = join(distPagesDir, route)\n        const handlerSrc = `${orig}.body`\n        const handlerDest = join(outDir, route)\n\n        if (isAppRouteHandler && existsSync(handlerSrc)) {\n          await fs.mkdir(dirname(handlerDest), { recursive: true })\n          await fs.copyFile(handlerSrc, handlerDest)\n          return\n        }\n\n        const htmlDest = join(\n          outDir,\n          `${route}${\n            subFolders && route !== '/index' ? `${sep}index` : ''\n          }.html`\n        )\n        const ampHtmlDest = join(\n          outDir,\n          `${route}.amp${subFolders ? `${sep}index` : ''}.html`\n        )\n        const jsonDest = isAppPath\n          ? join(\n              outDir,\n              `${route}${\n                subFolders && route !== '/index' ? `${sep}index` : ''\n              }.txt`\n            )\n          : join(pagesDataDir, `${route}.json`)\n\n        await fs.mkdir(dirname(htmlDest), { recursive: true })\n        await fs.mkdir(dirname(jsonDest), { recursive: true })\n\n        const htmlSrc = `${orig}.html`\n        const jsonSrc = `${orig}${isAppPath ? RSC_SUFFIX : '.json'}`\n\n        await fs.copyFile(htmlSrc, htmlDest)\n        await fs.copyFile(jsonSrc, jsonDest)\n\n        if (existsSync(`${orig}.amp.html`)) {\n          await fs.mkdir(dirname(ampHtmlDest), { recursive: true })\n          await fs.copyFile(`${orig}.amp.html`, ampHtmlDest)\n        }\n\n        const segmentsDir = `${orig}${RSC_SEGMENTS_DIR_SUFFIX}`\n        if (isAppPath && existsSync(segmentsDir)) {\n          // Output a data file for each of this page's segments\n          //\n          // These files are requested by the client router's internal\n          // prefetcher, not the user directly. So we don't need to account for\n          // things like trailing slash handling.\n          //\n          // To keep the protocol simple, we can use the non-normalized route\n          // path instead of the normalized one (which, among other things,\n          // rewrites `/` to `/index`).\n          const segmentsDirDest = join(outDir, unnormalizedRoute)\n          const segmentPaths = await collectSegmentPaths(segmentsDir)\n          await Promise.all(\n            segmentPaths.map(async (segmentFileSrc) => {\n              const segmentPath =\n                '/' + segmentFileSrc.slice(0, -RSC_SEGMENT_SUFFIX.length)\n              const segmentFilename =\n                convertSegmentPathToStaticExportFilename(segmentPath)\n              const segmentFileDest = join(segmentsDirDest, segmentFilename)\n              await fs.mkdir(dirname(segmentFileDest), { recursive: true })\n              await fs.copyFile(\n                join(segmentsDir, segmentFileSrc),\n                segmentFileDest\n              )\n            })\n          )\n        }\n      })\n    )\n  }\n\n  if (Object.keys(ampValidations).length) {\n    console.log(formatAmpMessages(ampValidations))\n  }\n  if (hadValidationError) {\n    throw new ExportError(\n      `AMP Validation caused the export to fail. https://nextjs.org/docs/messages/amp-export-validation`\n    )\n  }\n\n  if (failedExportAttemptsByPage.size > 0) {\n    const failedPages = Array.from(failedExportAttemptsByPage.keys())\n    throw new ExportError(\n      `Export encountered errors on following paths:\\n\\t${failedPages\n        .sort()\n        .join('\\n\\t')}`\n    )\n  }\n\n  await fs.writeFile(\n    join(distDir, EXPORT_DETAIL),\n    formatManifest({\n      version: 1,\n      outDirectory: outDir,\n      success: true,\n    }),\n    'utf8'\n  )\n\n  if (telemetry) {\n    await telemetry.flush()\n  }\n\n  await worker.end()\n\n  return collector\n}\n\nasync function collectSegmentPaths(segmentsDirectory: string) {\n  const results: Array<string> = []\n  await collectSegmentPathsImpl(segmentsDirectory, segmentsDirectory, results)\n  return results\n}\n\nasync function collectSegmentPathsImpl(\n  segmentsDirectory: string,\n  directory: string,\n  results: Array<string>\n) {\n  const segmentFiles = await fs.readdir(directory, {\n    withFileTypes: true,\n  })\n  await Promise.all(\n    segmentFiles.map(async (segmentFile) => {\n      if (segmentFile.isDirectory()) {\n        await collectSegmentPathsImpl(\n          segmentsDirectory,\n          join(directory, segmentFile.name),\n          results\n        )\n        return\n      }\n      if (!segmentFile.name.endsWith(RSC_SEGMENT_SUFFIX)) {\n        return\n      }\n      results.push(\n        relative(segmentsDirectory, join(directory, segmentFile.name))\n      )\n    })\n  )\n}\n\nexport default async function exportApp(\n  dir: string,\n  options: ExportAppOptions,\n  span: Span\n): Promise<ExportAppResult | null> {\n  const nextExportSpan = span.traceChild('next-export')\n\n  return nextExportSpan.traceAsyncFn(async () => {\n    return await exportAppImpl(dir, options, nextExportSpan)\n  })\n}\n"], "names": ["createStaticWorker", "bold", "yellow", "findUp", "existsSync", "promises", "fs", "dirname", "join", "resolve", "sep", "relative", "formatAmpMessages", "Log", "RSC_SEGMENT_SUFFIX", "RSC_SEGMENTS_DIR_SUFFIX", "RSC_SUFFIX", "SSG_FALLBACK_EXPORT_ERROR", "recursiveCopy", "BUILD_ID_FILE", "CLIENT_PUBLIC_FILES_PATH", "CLIENT_STATIC_FILES_PATH", "EXPORT_DETAIL", "EXPORT_MARKER", "NEXT_FONT_MANIFEST", "MIDDLEWARE_MANIFEST", "PAGES_MANIFEST", "PHASE_EXPORT", "PRERENDER_MANIFEST", "SERVER_DIRECTORY", "SERVER_REFERENCE_MANIFEST", "APP_PATH_ROUTES_MANIFEST", "ROUTES_MANIFEST", "FUNCTIONS_CONFIG_MANIFEST", "loadConfig", "eventCliSession", "hasNextSupport", "Telemetry", "normalizePagePath", "denormalizePagePath", "loadEnvConfig", "isAPIRoute", "getPagePath", "isAppRouteRoute", "isAppPageRoute", "isError", "formatManifest", "TurborepoAccessTraceResult", "createProgress", "isInterceptionRouteRewrite", "extractInfoFromServerReferenceId", "convertSegmentPathToStaticExportFilename", "getNextBuildDebuggerPortOffset", "ExportError", "Error", "code", "exportAppImpl", "dir", "options", "span", "nextConfig", "<PERSON><PERSON><PERSON><PERSON>", "traceFn", "enabledDirectories", "traceAsyncFn", "debugPrerender", "distDir", "telemetry", "buildExport", "record", "webpackVersion", "cliCommand", "isSrcDir", "has<PERSON>ow<PERSON><PERSON>", "cwd", "isCustomServer", "turboFlag", "pagesDir", "appDir", "subFolders", "trailingSlash", "silent", "info", "buildIdFile", "customRoutes", "filter", "config", "length", "warn", "buildId", "readFile", "pagesManifest", "pages", "require", "prerenderManifest", "appRoutePathManifest", "err", "undefined", "excludedPrerenderRoutes", "Set", "Object", "keys", "defaultPathMap", "hasApiRoutes", "page", "dynamicRoutes", "add", "mapAppRouteToPage", "Map", "pageName", "routePath", "entries", "set", "routes", "_isAppDir", "outDir", "outdir", "rm", "recursive", "force", "mkdir", "writeFile", "version", "outDirectory", "success", "exportPathMap", "defaultMap", "i18n", "images", "loader", "unoptimized", "isNextImageImported", "then", "text", "JSON", "parse", "catch", "serverActionsManifest", "app", "output", "routesManifest", "rewrites", "beforeFiles", "hasInterceptionRouteRewrite", "some", "actionIds", "node", "edge", "actionId", "type", "renderOpts", "previewProps", "preview", "nextExport", "assetPrefix", "replace", "dev", "basePath", "canonicalBase", "amp", "ampSkipValidation", "experimental", "skipValidation", "ampOptimizerConfig", "optimizer", "locales", "locale", "defaultLocale", "domainLocales", "domains", "disableOptimizedLoading", "supportsDynamicResponse", "crossOrigin", "optimizeCss", "nextConfigOutput", "nextScriptWorkers", "largePageDataBytes", "serverActions", "serverComponents", "cacheLifeProfiles", "cacheLife", "nextFontManifest", "deploymentId", "htmlLimitedBots", "source", "clientTraceMetadata", "expireTime", "staleTimes", "cacheComponents", "clientSegmentCache", "Boolean", "clientParamParsing", "dynamicOnHover", "inlineCss", "authInterrupts", "reactMaxHeadersLength", "hasReadableErrorStacks", "serverSourceMaps", "process", "env", "TURBOPACK", "turbopackMinify", "serverMinification", "enablePrerenderSourceMaps", "publicRuntimeConfig", "runtimeConfig", "globalThis", "__NEXT_DATA__", "exportMap", "allExportPaths", "seenExportPaths", "fallbackEnabledPages", "path", "entry", "normalizedPath", "has", "push", "prerenderInfo", "fallback", "size", "hasMiddleware", "functionsConfigManifest", "middlewareManifest", "middleware", "functions", "pagesDataDir", "ampValidations", "publicDir", "exportPagesInBatches", "worker", "exportPaths", "renderResumeDataCachesByPage", "minPageCountPerBatch", "staticGenerationMinPagesPerWorker", "numWorkers", "Math", "min", "ceil", "pageCountPerBatch", "batches", "Array", "from", "_", "i", "slice", "remainingPages", "for<PERSON>ach", "index", "Promise", "all", "map", "batch", "exportPages", "parentSpanId", "getId", "cache<PERSON><PERSON><PERSON>", "cacheMaxMemorySize", "fetchCache", "fetchCacheKeyPrefix", "flat", "initialPhaseExportPaths", "finalPhaseExportPaths", "exportPath", "_allowEmptyStaticShell", "progress", "statusMessage", "debuggerPortOffset", "kind", "results", "result", "renderResumeDataCache", "finalPhaseResults", "hadValidationError", "collector", "by<PERSON><PERSON>", "byPage", "ssgNotFoundPaths", "turborepoAccessTraceResults", "failedExportAttemptsByPage", "page<PERSON><PERSON>", "turborepoAccessTraceResult", "fromSerialized", "validation", "errors", "get", "cacheControl", "metadata", "hasEmptyStaticShell", "hasPostponed", "fetchMetrics", "ssgNotFound", "durations", "durationsByPath", "duration", "ppr", "unnormalizedRoute", "srcRoute", "appPageName", "isAppPath", "isAppRouteHandler", "notFoundRoutes", "includes", "route", "pagePath", "distPagesDir", "split", "orig", "handlerSrc", "handlerDest", "copyFile", "htmlDest", "ampHtmlDest", "jsonDest", "htmlSrc", "jsonSrc", "segmentsDir", "segmentsDirDest", "segmentPaths", "collectSegmentPaths", "segmentFileSrc", "segmentPath", "segmentFilename", "segmentFileDest", "console", "log", "failedPages", "sort", "flush", "end", "segmentsDirectory", "collectSegmentPathsImpl", "directory", "segmentFiles", "readdir", "withFileTypes", "segmentFile", "isDirectory", "name", "endsWith", "exportApp", "nextExportSpan"], "mappings": "AAOA,SACEA,kBAAkB,QAGb,WAAU;AAGjB,SAASC,IAAI,EAAEC,MAAM,QAAQ,oBAAmB;AAChD,OAAOC,YAAY,6BAA4B;AAC/C,SAASC,UAAU,EAAEC,YAAYC,EAAE,QAAQ,KAAI;AAE/C,OAAO,yBAAwB;AAE/B,SAASC,OAAO,EAAEC,IAAI,EAAEC,OAAO,EAAEC,GAAG,EAAEC,QAAQ,QAAQ,OAAM;AAC5D,SAASC,iBAAiB,QAAQ,wBAAuB;AAEzD,YAAYC,SAAS,sBAAqB;AAC1C,SACEC,kBAAkB,EAClBC,uBAAuB,EACvBC,UAAU,EACVC,yBAAyB,QACpB,mBAAkB;AACzB,SAASC,aAAa,QAAQ,wBAAuB;AACrD,SACEC,aAAa,EACbC,wBAAwB,EACxBC,wBAAwB,EACxBC,aAAa,EACbC,aAAa,EACbC,kBAAkB,EAClBC,mBAAmB,EACnBC,cAAc,EACdC,YAAY,EACZC,kBAAkB,EAClBC,gBAAgB,EAChBC,yBAAyB,EACzBC,wBAAwB,EACxBC,eAAe,EACfC,yBAAyB,QACpB,0BAAyB;AAChC,OAAOC,gBAAgB,mBAAkB;AAEzC,SAASC,eAAe,QAAQ,sBAAqB;AACrD,SAASC,cAAc,QAAQ,oBAAmB;AAClD,SAASC,SAAS,QAAQ,uBAAsB;AAChD,SAASC,iBAAiB,QAAQ,8CAA6C;AAC/E,SAASC,mBAAmB,QAAQ,gDAA+C;AACnF,SAASC,aAAa,QAAQ,YAAW;AACzC,SAASC,UAAU,QAAQ,sBAAqB;AAChD,SAASC,WAAW,QAAQ,oBAAmB;AAG/C,SAASC,eAAe,QAAQ,4BAA2B;AAC3D,SAASC,cAAc,QAAQ,2BAA0B;AACzD,OAAOC,aAAa,kBAAiB;AACrC,SAASC,cAAc,QAAQ,+CAA8C;AAC7E,SAASC,0BAA0B,QAAQ,kCAAiC;AAC5E,SAASC,cAAc,QAAQ,oBAAmB;AAElD,SAASC,0BAA0B,QAAQ,+CAA8C;AAEzF,SAASC,gCAAgC,QAAQ,sCAAqC;AACtF,SAASC,wCAAwC,QAAQ,qDAAoD;AAC7G,SAASC,8BAA8B,QAAQ,gBAAe;AAE9D,OAAO,MAAMC,oBAAoBC;;QAA1B,qBACLC,OAAO;;AACT;AAEA,eAAeC,cACbC,GAAW,EACXC,OAAmC,EACnCC,IAAU;QAgSOC,iBACIA,8BACCA;IAhStBH,MAAMhD,QAAQgD;IAEd,4EAA4E;IAC5EE,KAAKE,UAAU,CAAC,eAAeC,OAAO,CAAC,IAAMtB,cAAciB,KAAK,OAAO5C;IAEvE,MAAM,EAAEkD,kBAAkB,EAAE,GAAGL;IAE/B,MAAME,aACJF,QAAQE,UAAU,IACjB,MAAMD,KAAKE,UAAU,CAAC,oBAAoBG,YAAY,CAAC,IACtD9B,WAAWP,cAAc8B,KAAK;YAC5BQ,gBAAgBP,QAAQO,cAAc;QACxC;IAGJ,MAAMC,UAAU1D,KAAKiD,KAAKG,WAAWM,OAAO;IAC5C,MAAMC,YAAYT,QAAQU,WAAW,GAAG,OAAO,IAAI/B,UAAU;QAAE6B;IAAQ;IAEvE,IAAIC,WAAW;QACbA,UAAUE,MAAM,CACdlC,gBAAgB+B,SAASN,YAAY;YACnCU,gBAAgB;YAChBC,YAAY;YACZC,UAAU;YACVC,YAAY,CAAC,CAAE,MAAMtE,OAAO,YAAY;gBAAEuE,KAAKjB;YAAI;YACnDkB,gBAAgB;YAChBC,WAAW;YACXC,UAAU;YACVC,QAAQ;QACV;IAEJ;IAEA,MAAMC,aAAanB,WAAWoB,aAAa,IAAI,CAACtB,QAAQU,WAAW;IAEnE,IAAI,CAACV,QAAQuB,MAAM,IAAI,CAACvB,QAAQU,WAAW,EAAE;QAC3CvD,IAAIqE,IAAI,CAAC,CAAC,uBAAuB,EAAEhB,SAAS;IAC9C;IAEA,MAAMiB,cAAc3E,KAAK0D,SAAS/C;IAElC,IAAI,CAACf,WAAW+E,cAAc;QAC5B,MAAM,qBAEL,CAFK,IAAI9B,YACR,CAAC,0CAA0C,EAAEa,QAAQ,gJAAgJ,CAAC,GADlM,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMkB,eAAe;QAAC;QAAY;QAAa;KAAU,CAACC,MAAM,CAC9D,CAACC,SAAW,OAAO1B,UAAU,CAAC0B,OAAO,KAAK;IAG5C,IAAI,CAAClD,kBAAkB,CAACsB,QAAQU,WAAW,IAAIgB,aAAaG,MAAM,GAAG,GAAG;QACtE1E,IAAI2E,IAAI,CACN,CAAC,4FAA4F,EAAEJ,aAAa5E,IAAI,CAC9G,MACA,+EAA+E,CAAC;IAEtF;IAEA,MAAMiF,UAAU,MAAMnF,GAAGoF,QAAQ,CAACP,aAAa;IAE/C,MAAMQ,gBACJ,CAACjC,QAAQkC,KAAK,IACbC,QAAQrF,KAAK0D,SAASrC,kBAAkBH;IAE3C,IAAIoE;IACJ,IAAI;QACFA,oBAAoBD,QAAQrF,KAAK0D,SAAStC;IAC5C,EAAE,OAAM,CAAC;IAET,IAAImE;IACJ,IAAI;QACFA,uBAAuBF,QAAQrF,KAAK0D,SAASnC;IAC/C,EAAE,OAAOiE,KAAK;QACZ,IACEnD,QAAQmD,QACPA,CAAAA,IAAIzC,IAAI,KAAK,YAAYyC,IAAIzC,IAAI,KAAK,kBAAiB,GACxD;YACA,0DAA0D;YAC1D,oCAAoC;YACpCwC,uBAAuBE;QACzB,OAAO;YACL,2CAA2C;YAC3C,MAAMD;QACR;IACF;IAEA,MAAME,0BAA0B,IAAIC;IACpC,MAAMP,QAAQlC,QAAQkC,KAAK,IAAIQ,OAAOC,IAAI,CAACV;IAC3C,MAAMW,iBAAgC,CAAC;IAEvC,IAAIC,eAAe;IACnB,KAAK,MAAMC,QAAQZ,MAAO;QACxB,wCAAwC;QACxC,0CAA0C;QAC1C,mCAAmC;QAEnC,IAAInD,WAAW+D,OAAO;YACpBD,eAAe;YACf;QACF;QAEA,IAAIC,SAAS,gBAAgBA,SAAS,WAAWA,SAAS,WAAW;YACnE;QACF;QAEA,qEAAqE;QACrE,yEAAyE;QACzE,yEAAyE;QACzE,8CAA8C;QAC9C,IAAIV,qCAAAA,kBAAmBW,aAAa,CAACD,KAAK,EAAE;YAC1CN,wBAAwBQ,GAAG,CAACF;YAC5B;QACF;QAEAF,cAAc,CAACE,KAAK,GAAG;YAAEA;QAAK;IAChC;IAEA,MAAMG,oBAAoB,IAAIC;IAC9B,IAAI,CAAClD,QAAQU,WAAW,IAAI2B,sBAAsB;QAChD,KAAK,MAAM,CAACc,UAAUC,UAAU,IAAIV,OAAOW,OAAO,CAAChB,sBAAuB;YACxEY,kBAAkBK,GAAG,CAACF,WAAWD;YACjC,IACEjE,eAAeiE,aACf,EAACf,qCAAAA,kBAAmBmB,MAAM,CAACH,UAAU,KACrC,EAAChB,qCAAAA,kBAAmBW,aAAa,CAACK,UAAU,GAC5C;gBACAR,cAAc,CAACQ,UAAU,GAAG;oBAC1BN,MAAMK;oBACNK,WAAW;gBACb;YACF;QACF;IACF;IAEA,kCAAkC;IAClC,MAAMC,SAASzD,QAAQ0D,MAAM;IAE7B,IAAID,WAAW3G,KAAKiD,KAAK,WAAW;QAClC,MAAM,qBAEL,CAFK,IAAIJ,YACR,CAAC,wJAAwJ,CAAC,GADtJ,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAI8D,WAAW3G,KAAKiD,KAAK,WAAW;QAClC,MAAM,qBAEL,CAFK,IAAIJ,YACR,CAAC,wJAAwJ,CAAC,GADtJ,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAM/C,GAAG+G,EAAE,CAACF,QAAQ;QAAEG,WAAW;QAAMC,OAAO;IAAK;IACnD,MAAMjH,GAAGkH,KAAK,CAAChH,KAAK2G,QAAQ,SAAS1B,UAAU;QAAE6B,WAAW;IAAK;IAEjE,MAAMhH,GAAGmH,SAAS,CAChBjH,KAAK0D,SAAS5C,gBACdwB,eAAe;QACb4E,SAAS;QACTC,cAAcR;QACdS,SAAS;IACX,IACA;IAGF,wBAAwB;IACxB,IAAI,CAAClE,QAAQU,WAAW,IAAIhE,WAAWI,KAAKiD,KAAK,YAAY;QAC3D,IAAI,CAACC,QAAQuB,MAAM,EAAE;YACnBpE,IAAIqE,IAAI,CAAC;QACX;QACA,MAAMvB,KACHE,UAAU,CAAC,yBACXG,YAAY,CAAC,IACZ9C,cAAcV,KAAKiD,KAAK,WAAWjD,KAAK2G,QAAQ;IAEtD;IAEA,8BAA8B;IAC9B,IACE,CAACzD,QAAQU,WAAW,IACpBhE,WAAWI,KAAK0D,SAAS7C,4BACzB;QACA,IAAI,CAACqC,QAAQuB,MAAM,EAAE;YACnBpE,IAAIqE,IAAI,CAAC;QACX;QACA,MAAMvB,KACHE,UAAU,CAAC,8BACXG,YAAY,CAAC,IACZ9C,cACEV,KAAK0D,SAAS7C,2BACdb,KAAK2G,QAAQ,SAAS9F;IAG9B;IAEA,6CAA6C;IAC7C,IAAI,OAAOuC,WAAWiE,aAAa,KAAK,YAAY;QAClDjE,WAAWiE,aAAa,GAAG,OAAOC;YAChC,OAAOA;QACT;IACF;IAEA,MAAM,EACJC,IAAI,EACJC,QAAQ,EAAEC,SAAS,SAAS,EAAEC,WAAW,EAAE,EAC5C,GAAGtE;IAEJ,IAAImE,QAAQ,CAACrE,QAAQU,WAAW,EAAE;QAChC,MAAM,qBAEL,CAFK,IAAIf,YACR,CAAC,8IAA8I,CAAC,GAD5I,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAI,CAACK,QAAQU,WAAW,EAAE;QACxB,MAAM,EAAE+D,mBAAmB,EAAE,GAAG,MAAMxE,KACnCE,UAAU,CAAC,0BACXG,YAAY,CAAC,IACZ1D,GACGoF,QAAQ,CAAClF,KAAK0D,SAAS3C,gBAAgB,QACvC6G,IAAI,CAAC,CAACC,OAASC,KAAKC,KAAK,CAACF,OAC1BG,KAAK,CAAC,IAAO,CAAA,CAAC,CAAA;QAGrB,IACEL,uBACAF,WAAW,aACX,CAACC,eACD,CAAC9F,gBACD;YACA,MAAM,qBAML,CANK,IAAIiB,YACR,CAAC;;;;8DAIqD,CAAC,GALnD,qBAAA;uBAAA;4BAAA;8BAAA;YAMN;QACF;IACF;IAEA,IAAIoF;IACJ,IAAI1E,mBAAmB2E,GAAG,EAAE;QAC1BD,wBAAwB5C,QACtBrF,KAAK0D,SAASrC,kBAAkBC,4BAA4B;QAG9D,IAAI8B,WAAW+E,MAAM,KAAK,UAAU;gBAK9BC,sCAAAA;YAJJ,MAAMA,iBAAiB/C,QAAQrF,KAAK0D,SAASlC;YAE7C,2FAA2F;YAC3F,6DAA6D;YAC7D,IAAI4G,CAAAA,mCAAAA,2BAAAA,eAAgBC,QAAQ,sBAAxBD,uCAAAA,yBAA0BE,WAAW,qBAArCF,qCAAuCrD,MAAM,IAAG,GAAG;gBACrD,MAAMwD,8BACJH,eAAeC,QAAQ,CAACC,WAAW,CAACE,IAAI,CAAC/F;gBAE3C,IAAI8F,6BAA6B;oBAC/B,MAAM,qBAEL,CAFK,IAAI1F,YACR,CAAC,yKAAyK,CAAC,GADvK,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;YAEA,MAAM4F,YAAY;mBACb7C,OAAOC,IAAI,CAACoC,sBAAsBS,IAAI;mBACtC9C,OAAOC,IAAI,CAACoC,sBAAsBU,IAAI;aAC1C;YAED,IACEF,UAAUD,IAAI,CACZ,CAACI,WACClG,iCAAiCkG,UAAUC,IAAI,KAAK,kBAExD;gBACA,MAAM,qBAEL,CAFK,IAAIhG,YACR,CAAC,oKAAoK,CAAC,GADlK,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;IACF;IAEA,8BAA8B;IAC9B,MAAMiG,aAAsC;QAC1CC,YAAY,EAAEzD,qCAAAA,kBAAmB0D,OAAO;QACxCC,YAAY;QACZC,aAAa9F,WAAW8F,WAAW,CAACC,OAAO,CAAC,OAAO;QACnDzF;QACA0F,KAAK;QACLC,UAAUjG,WAAWiG,QAAQ;QAC7B7E,eAAepB,WAAWoB,aAAa;QACvC8E,eAAelG,EAAAA,kBAAAA,WAAWmG,GAAG,qBAAdnG,gBAAgBkG,aAAa,KAAI;QAChDE,mBAAmBpG,EAAAA,+BAAAA,WAAWqG,YAAY,CAACF,GAAG,qBAA3BnG,6BAA6BsG,cAAc,KAAI;QAClEC,oBAAoBvG,EAAAA,gCAAAA,WAAWqG,YAAY,CAACF,GAAG,qBAA3BnG,8BAA6BwG,SAAS,KAAInE;QAC9DoE,OAAO,EAAEtC,wBAAAA,KAAMsC,OAAO;QACtBC,MAAM,EAAEvC,wBAAAA,KAAMwC,aAAa;QAC3BA,aAAa,EAAExC,wBAAAA,KAAMwC,aAAa;QAClCC,aAAa,EAAEzC,wBAAAA,KAAM0C,OAAO;QAC5BC,yBAAyB9G,WAAWqG,YAAY,CAACS,uBAAuB;QACxE,wDAAwD;QACxDC,yBAAyB;QACzBC,aAAahH,WAAWgH,WAAW;QACnCC,aAAajH,WAAWqG,YAAY,CAACY,WAAW;QAChDC,kBAAkBlH,WAAW+E,MAAM;QACnCoC,mBAAmBnH,WAAWqG,YAAY,CAACc,iBAAiB;QAC5DC,oBAAoBpH,WAAWqG,YAAY,CAACe,kBAAkB;QAC9DC,eAAerH,WAAWqG,YAAY,CAACgB,aAAa;QACpDC,kBAAkBnH,mBAAmB2E,GAAG;QACxCyC,mBAAmBvH,WAAWqG,YAAY,CAACmB,SAAS;QACpDC,kBAAkBxF,QAChBrF,KAAK0D,SAAS,UAAU,GAAG1C,mBAAmB,KAAK,CAAC;QAEtDwG,QAAQpE,WAAWoE,MAAM;QACzB,GAAIjE,mBAAmB2E,GAAG,GACtB;YACED;QACF,IACA,CAAC,CAAC;QACN6C,cAAc1H,WAAW0H,YAAY;QACrCC,iBAAiB3H,WAAW2H,eAAe,CAACC,MAAM;QAClDvB,cAAc;YACZwB,qBAAqB7H,WAAWqG,YAAY,CAACwB,mBAAmB;YAChEC,YAAY9H,WAAW8H,UAAU;YACjCC,YAAY/H,WAAWqG,YAAY,CAAC0B,UAAU;YAC9CC,iBAAiBhI,WAAWqG,YAAY,CAAC2B,eAAe,IAAI;YAC5DC,oBACEjI,WAAWqG,YAAY,CAAC4B,kBAAkB,KAAK,gBAC3C,gBACAC,QAAQlI,WAAWqG,YAAY,CAAC4B,kBAAkB;YACxDE,oBAAoBnI,WAAWqG,YAAY,CAAC8B,kBAAkB,IAAI;YAClEC,gBAAgBpI,WAAWqG,YAAY,CAAC+B,cAAc,IAAI;YAC1DC,WAAWrI,WAAWqG,YAAY,CAACgC,SAAS,IAAI;YAChDC,gBAAgB,CAAC,CAACtI,WAAWqG,YAAY,CAACiC,cAAc;QAC1D;QACAC,uBAAuBvI,WAAWuI,qBAAqB;QACvDC,wBACExI,WAAWqG,YAAY,CAACoC,gBAAgB,KAAK,QAC7C,mEAAmE;QACnE,0CAA0C;QACzCC,CAAAA,QAAQC,GAAG,CAACC,SAAS,GAClB5I,WAAWqG,YAAY,CAACwC,eAAe,KAAK,QAC5C7I,WAAWqG,YAAY,CAACyC,kBAAkB,KAAK,KAAI,KACvD9I,WAAWqG,YAAY,CAAC0C,yBAAyB,KAAK;IAC1D;IAEA,MAAM,EAAEC,mBAAmB,EAAE,GAAGhJ;IAEhC,IAAIwC,OAAOC,IAAI,CAACuG,qBAAqBrH,MAAM,GAAG,GAAG;QAC/C+D,WAAWuD,aAAa,GAAGD;IAC7B;IAEA,wDAAwD;;IACtDE,WAAmBC,aAAa,GAAG;QACnCtD,YAAY;IACd;IAEA,MAAM5B,gBAAgB,MAAMlE,KACzBE,UAAU,CAAC,uBACXG,YAAY,CAAC;QACZ,MAAMgJ,YAAY,MAAMpJ,WAAWiE,aAAa,CAACvB,gBAAgB;YAC/DsD,KAAK;YACLnG;YACA0D;YACAjD;YACAuB;QACF;QACA,OAAOuH;IACT;IAEF,wDAAwD;IACxD,IAAI,CAACtJ,QAAQU,WAAW,EAAE;QACxB,4DAA4D;QAC5D,IAAI,CAACyD,aAAa,CAAC,OAAO,EAAE;YAC1BA,aAAa,CAAC,OAAO,GAAG;gBAAErB,MAAM;YAAU;QAC5C;QAEA;;;KAGC,GACD,IAAI,CAACqB,aAAa,CAAC,YAAY,EAAE;YAC/B,yEAAyE;YACzEA,aAAa,CAAC,YAAY,GAAGA,aAAa,CAAC,OAAO;QACpD;IACF;IAEA,MAAMoF,iBAAoC,EAAE;IAC5C,MAAMC,kBAAkB,IAAI/G;IAC5B,MAAMgH,uBAAuB,IAAIhH;IAEjC,KAAK,MAAM,CAACiH,MAAMC,MAAM,IAAIjH,OAAOW,OAAO,CAACc,eAAgB;QACzD,kCAAkC;QAClC,MAAMyF,iBAAiB/K,oBAAoBD,kBAAkB8K;QAE7D,IAAIF,gBAAgBK,GAAG,CAACD,iBAAiB;YACvC;QACF;QAEAJ,gBAAgBxG,GAAG,CAAC4G;QAEpB,IAAI,CAACD,MAAMnG,SAAS,IAAIzE,WAAW4K,MAAM7G,IAAI,GAAG;YAC9CD,eAAe;YACf;QACF;QAEA0G,eAAeO,IAAI,CAAC;YAAE,GAAGH,KAAK;YAAED,MAAME;QAAe;QAErD,IAAIxH,qBAAqB,CAACpC,QAAQU,WAAW,EAAE;YAC7C,MAAMqJ,gBAAgB3H,kBAAkBW,aAAa,CAAC4G,MAAM7G,IAAI,CAAC;YAEjE,IAAIiH,iBAAiBA,cAAcC,QAAQ,KAAK,OAAO;gBACrDP,qBAAqBzG,GAAG,CAAC2G,MAAM7G,IAAI;YACrC;QACF;IACF;IAEA,IAAIyG,eAAe1H,MAAM,KAAK,GAAG;QAC/B,OAAO;IACT;IAEA,IAAI4H,qBAAqBQ,IAAI,GAAG,GAAG;QACjC,MAAM,qBAIL,CAJK,IAAItK,YACR,CAAC,wCAAwC,EAAE;eAAI8J;SAAqB,CAAC3M,IAAI,CACvE,MACA,EAAE,EAAES,0BAA0B,EAAE,CAAC,GAH/B,qBAAA;mBAAA;wBAAA;0BAAA;QAIN;IACF;IAEA,IAAI2M,gBAAgB;IAEpB,IAAI,CAAClK,QAAQU,WAAW,EAAE;QACxB,IAAI;gBAWQyJ;YAVV,MAAMC,qBAAqBjI,QACzBrF,KAAK0D,SAASrC,kBAAkBJ;YAGlC,MAAMoM,0BAA0BhI,QAC9BrF,KAAK0D,SAASrC,kBAAkBI;YAGlC2L,gBACExH,OAAOC,IAAI,CAACyH,mBAAmBC,UAAU,EAAExI,MAAM,GAAG,KACpDuG,SAAQ+B,qCAAAA,wBAAwBG,SAAS,qBAAjCH,kCAAmC,CAAC,eAAe;QAC/D,EAAE,OAAM,CAAC;QAET,kDAAkD;QAClD,IAAItH,gBAAgBqH,eAAe;YACjC,IAAIhK,WAAW+E,MAAM,KAAK,UAAU;gBAClC9H,IAAI2E,IAAI,CACNtF,OACE,CAAC,kGAAkG,CAAC,IAEpG,CAAC,EAAE,CAAC,GACJA,OACE,CAAC,mDAAmD,CAAC,GACnD,MACAD,KAAK,CAAC,8CAA8C,CAAC,KAEzD,CAAC,EAAE,CAAC,GACJC,OACE,CAAC,2KAA2K,CAAC,IAE/K,CAAC,EAAE,CAAC,GACJA,OACE,CAAC,qEAAqE,CAAC;YAG/E;QACF;IACF;IAEA,MAAM+N,eAAevK,QAAQU,WAAW,GACpC+C,SACA3G,KAAK2G,QAAQ,cAAc1B;IAE/B,MAAMyI,iBAAgC,CAAC;IAEvC,MAAMC,YAAY3N,KAAKiD,KAAKrC;IAC5B,wBAAwB;IACxB,IAAI,CAACsC,QAAQU,WAAW,IAAIhE,WAAW+N,YAAY;QACjD,IAAI,CAACzK,QAAQuB,MAAM,EAAE;YACnBpE,IAAIqE,IAAI,CAAC;QACX;QACA,MAAMvB,KAAKE,UAAU,CAAC,yBAAyBG,YAAY,CAAC,IAC1D9C,cAAciN,WAAWhH,QAAQ;gBAC/B9B,QAAO+H,IAAI;oBACT,8BAA8B;oBAC9B,OAAO,CAACvF,aAAa,CAACuF,KAAK;gBAC7B;YACF;IAEJ;IAEA,MAAMgB,uBAAuB,OAC3BC,QACAC,aACAC;QAEA,2EAA2E;QAC3E,0EAA0E;QAC1E,sEAAsE;QACtE,2DAA2D;QAC3D,MAAMC,uBACJ5K,WAAWqG,YAAY,CAACwE,iCAAiC,IAAI;QAE/D,2EAA2E;QAC3E,8BAA8B;QAC9B,MAAMC,aAAaC,KAAKC,GAAG,CACzBlL,QAAQgL,UAAU,EAClBC,KAAKE,IAAI,CAACP,YAAY/I,MAAM,GAAGiJ;QAGjC,qEAAqE;QACrE,MAAMM,oBAAoBH,KAAKE,IAAI,CAACP,YAAY/I,MAAM,GAAGmJ;QAEzD,MAAMK,UAAUC,MAAMC,IAAI,CAAC;YAAE1J,QAAQmJ;QAAW,GAAG,CAACQ,GAAGC,IACrDb,YAAYc,KAAK,CAACD,IAAIL,mBAAmB,AAACK,CAAAA,IAAI,CAAA,IAAKL;QAGrD,8BAA8B;QAC9B,MAAMO,iBAAiBf,YAAYc,KAAK,CAACV,aAAaI;QACtDO,eAAeC,OAAO,CAAC,CAAC9I,MAAM+I;YAC5BR,OAAO,CAACQ,QAAQR,QAAQxJ,MAAM,CAAC,CAACiI,IAAI,CAAChH;QACvC;QAEA,OAAO,AACL,CAAA,MAAMgJ,QAAQC,GAAG,CACfV,QAAQW,GAAG,CAAC,OAAOC,QACjBtB,OAAOuB,WAAW,CAAC;gBACjBnK;gBACA6I,aAAaqB;gBACbE,cAAclM,KAAKmM,KAAK;gBACxB7B;gBACA3E;gBACA5F;gBACAD;gBACAS;gBACAiD;gBACAvD;gBACAmM,cAAcnM,WAAWmM,YAAY;gBACrCC,oBAAoBpM,WAAWoM,kBAAkB;gBACjDC,YAAY;gBACZC,qBAAqBtM,WAAWqG,YAAY,CAACiG,mBAAmB;gBAChE3B;YACF,IAEJ,EACA4B,IAAI;IACR;IAEA,IAAIC,0BAA6C,EAAE;IACnD,MAAMC,wBAA2C,EAAE;IAEnD,IAAI/G,WAAWW,YAAY,CAAC2B,eAAe,EAAE;QAC3C,KAAK,MAAM0E,cAAcrD,eAAgB;YACvC,IAAIqD,WAAWC,sBAAsB,EAAE;gBACrCF,sBAAsB7C,IAAI,CAAC8C;YAC7B,OAAO;gBACLF,wBAAwB5C,IAAI,CAAC8C;YAC/B;QACF;IACF,OAAO;QACLF,0BAA0BnD;IAC5B;IAEA,MAAMuD,WAAWxN,eACfoN,wBAAwB7K,MAAM,GAAG8K,sBAAsB9K,MAAM,EAC7D7B,QAAQ+M,aAAa,IAAI;IAG3B,MAAMpC,SAASrO,mBAAmB4D,YAAY;QAC5C8M,oBAAoBtN,+BAA+B;YAAEuN,MAAM;QAAc;QACzEH;IACF;IAEA,MAAMI,UAAU,MAAMxC,qBAAqBC,QAAQ+B;IAEnD,IAAIC,sBAAsB9K,MAAM,GAAG,GAAG;QACpC,MAAMgJ,+BAAuD,CAAC;QAE9D,KAAK,MAAM,EAAE/H,IAAI,EAAEqK,MAAM,EAAE,IAAID,QAAS;YACtC,IAAI,CAACC,QAAQ;gBACX;YACF;YAEA,IAAI,2BAA2BA,UAAUA,OAAOC,qBAAqB,EAAE;gBACrE,uEAAuE;gBACvE,uEAAuE;gBACvE,uBAAuB;gBACvBvC,4BAA4B,CAAC/H,KAAK,GAAGqK,OAAOC,qBAAqB;gBACjE,kEAAkE;gBAClE,4DAA4D;gBAC5DD,OAAOC,qBAAqB,GAAG7K;YACjC;QACF;QAEA,MAAM8K,oBAAoB,MAAM3C,qBAC9BC,QACAgC,uBACA9B;QAGFqC,QAAQpD,IAAI,IAAIuD;IAClB;IAEA,IAAIC,qBAAqB;IAEzB,MAAMC,YAA6B;QACjCC,QAAQ,IAAItK;QACZuK,QAAQ,IAAIvK;QACZwK,kBAAkB,IAAIjL;QACtBkL,6BAA6B,IAAIzK;IACnC;IAEA,MAAM0K,6BAAmD,IAAI1K;IAE7D,KAAK,MAAM,EAAEiK,MAAM,EAAEzD,IAAI,EAAE5G,IAAI,EAAE+K,OAAO,EAAE,IAAIX,QAAS;QACrD,IAAI,CAACC,QAAQ;QACb,IAAI,WAAWA,QAAQ;YACrBS,2BAA2BtK,GAAG,CAACuK,SAAS;YACxC;QACF;QAEA,IAAIV,OAAOW,0BAA0B,EAAE;gBACrCP;aAAAA,yCAAAA,UAAUI,2BAA2B,qBAArCJ,uCAAuCjK,GAAG,CACxCoG,MACArK,2BAA2B0O,cAAc,CACvCZ,OAAOW,0BAA0B;QAGvC;QAEA,+BAA+B;QAC/B,IAAIX,OAAO3C,cAAc,EAAE;YACzB,KAAK,MAAMwD,cAAcb,OAAO3C,cAAc,CAAE;gBAC9CA,cAAc,CAACwD,WAAWlL,IAAI,CAAC,GAAGkL,WAAWb,MAAM;gBACnDG,uBAAuBU,WAAWb,MAAM,CAACc,MAAM,CAACpM,MAAM,GAAG;YAC3D;QACF;QAEA,IAAI7B,QAAQU,WAAW,EAAE;YACvB,4BAA4B;YAC5B,MAAMc,OAAO+L,UAAUC,MAAM,CAACU,GAAG,CAACxE,SAAS,CAAC;YAC5C,IAAIyD,OAAOgB,YAAY,EAAE;gBACvB3M,KAAK2M,YAAY,GAAGhB,OAAOgB,YAAY;YACzC;YACA,IAAI,OAAOhB,OAAOiB,QAAQ,KAAK,aAAa;gBAC1C5M,KAAK4M,QAAQ,GAAGjB,OAAOiB,QAAQ;YACjC;YAEA,IAAI,OAAOjB,OAAOkB,mBAAmB,KAAK,aAAa;gBACrD7M,KAAK6M,mBAAmB,GAAGlB,OAAOkB,mBAAmB;YACvD;YAEA,IAAI,OAAOlB,OAAOmB,YAAY,KAAK,aAAa;gBAC9C9M,KAAK8M,YAAY,GAAGnB,OAAOmB,YAAY;YACzC;YAEA,IAAI,OAAOnB,OAAOoB,YAAY,KAAK,aAAa;gBAC9C/M,KAAK+M,YAAY,GAAGpB,OAAOoB,YAAY;YACzC;YAEAhB,UAAUC,MAAM,CAAClK,GAAG,CAACoG,MAAMlI;YAE3B,oBAAoB;YACpB,IAAI2L,OAAOqB,WAAW,KAAK,MAAM;gBAC/BjB,UAAUG,gBAAgB,CAAC1K,GAAG,CAAC0G;YACjC;YAEA,oBAAoB;YACpB,MAAM+E,YAAYlB,UAAUE,MAAM,CAACS,GAAG,CAACpL,SAAS;gBAC9C4L,iBAAiB,IAAIxL;YACvB;YACAuL,UAAUC,eAAe,CAACpL,GAAG,CAACoG,MAAMyD,OAAOwB,QAAQ;YACnDpB,UAAUE,MAAM,CAACnK,GAAG,CAACR,MAAM2L;QAC7B;IACF;IAEA,4EAA4E;IAC5E,IAAI,CAACzO,QAAQU,WAAW,IAAIR,WAAWqG,YAAY,CAACqI,GAAG,EAAE;QACvD,oBAAoB;QACpB,MAAM,qBAA4D,CAA5D,IAAIhP,MAAM,oDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA2D;IACnE;IAEA,oCAAoC;IACpC,IAAI,CAACI,QAAQU,WAAW,IAAI0B,mBAAmB;QAC7C,MAAM0J,QAAQC,GAAG,CACfrJ,OAAOC,IAAI,CAACP,kBAAkBmB,MAAM,EAAEyI,GAAG,CAAC,OAAO6C;YAC/C,wEAAwE;YACxE,IAAIA,sBAAsB,eAAe;gBACvC;YACF;YACA,MAAM,EAAEC,QAAQ,EAAE,GAAG1M,kBAAmBmB,MAAM,CAACsL,kBAAkB;YACjE,MAAME,cAAc9L,kBAAkBiL,GAAG,CAACY,YAAY;YACtD,MAAM3L,WAAW4L,eAAeD,YAAYD;YAC5C,MAAMG,YAAY5G,QAAQ2G;YAC1B,MAAME,oBAAoBF,eAAe9P,gBAAgB8P;YAEzD,wDAAwD;YACxD,0CAA0C;YAC1C,IAAI3M,kBAAmB8M,cAAc,CAACC,QAAQ,CAACN,oBAAoB;gBACjE;YACF;YACA,sEAAsE;YACtE,qEAAqE;YACrE,sEAAsE;YACtE,8BAA8B;YAC9B,MAAMO,QAAQxQ,kBAAkBiQ;YAEhC,MAAMQ,WAAWrQ,YAAYmE,UAAU3C,SAAS+B,WAAWyM;YAC3D,MAAMM,eAAexS,KACnBuS,UACA,yDAAyD;YACzD,4BAA4B;YAC5BlM,SACGuI,KAAK,CAAC,GACN6D,KAAK,CAAC,KACNvD,GAAG,CAAC,IAAM,MACVlP,IAAI,CAAC;YAGV,MAAM0S,OAAO1S,KAAKwS,cAAcF;YAChC,MAAMK,aAAa,GAAGD,KAAK,KAAK,CAAC;YACjC,MAAME,cAAc5S,KAAK2G,QAAQ2L;YAEjC,IAAIH,qBAAqBvS,WAAW+S,aAAa;gBAC/C,MAAM7S,GAAGkH,KAAK,CAACjH,QAAQ6S,cAAc;oBAAE9L,WAAW;gBAAK;gBACvD,MAAMhH,GAAG+S,QAAQ,CAACF,YAAYC;gBAC9B;YACF;YAEA,MAAME,WAAW9S,KACf2G,QACA,GAAG2L,QACD/N,cAAc+N,UAAU,WAAW,GAAGpS,IAAI,KAAK,CAAC,GAAG,GACpD,KAAK,CAAC;YAET,MAAM6S,cAAc/S,KAClB2G,QACA,GAAG2L,MAAM,IAAI,EAAE/N,aAAa,GAAGrE,IAAI,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC;YAEvD,MAAM8S,WAAWd,YACblS,KACE2G,QACA,GAAG2L,QACD/N,cAAc+N,UAAU,WAAW,GAAGpS,IAAI,KAAK,CAAC,GAAG,GACpD,IAAI,CAAC,IAERF,KAAKyN,cAAc,GAAG6E,MAAM,KAAK,CAAC;YAEtC,MAAMxS,GAAGkH,KAAK,CAACjH,QAAQ+S,WAAW;gBAAEhM,WAAW;YAAK;YACpD,MAAMhH,GAAGkH,KAAK,CAACjH,QAAQiT,WAAW;gBAAElM,WAAW;YAAK;YAEpD,MAAMmM,UAAU,GAAGP,KAAK,KAAK,CAAC;YAC9B,MAAMQ,UAAU,GAAGR,OAAOR,YAAY1R,aAAa,SAAS;YAE5D,MAAMV,GAAG+S,QAAQ,CAACI,SAASH;YAC3B,MAAMhT,GAAG+S,QAAQ,CAACK,SAASF;YAE3B,IAAIpT,WAAW,GAAG8S,KAAK,SAAS,CAAC,GAAG;gBAClC,MAAM5S,GAAGkH,KAAK,CAACjH,QAAQgT,cAAc;oBAAEjM,WAAW;gBAAK;gBACvD,MAAMhH,GAAG+S,QAAQ,CAAC,GAAGH,KAAK,SAAS,CAAC,EAAEK;YACxC;YAEA,MAAMI,cAAc,GAAGT,OAAOnS,yBAAyB;YACvD,IAAI2R,aAAatS,WAAWuT,cAAc;gBACxC,sDAAsD;gBACtD,EAAE;gBACF,4DAA4D;gBAC5D,qEAAqE;gBACrE,uCAAuC;gBACvC,EAAE;gBACF,mEAAmE;gBACnE,iEAAiE;gBACjE,6BAA6B;gBAC7B,MAAMC,kBAAkBpT,KAAK2G,QAAQoL;gBACrC,MAAMsB,eAAe,MAAMC,oBAAoBH;gBAC/C,MAAMnE,QAAQC,GAAG,CACfoE,aAAanE,GAAG,CAAC,OAAOqE;oBACtB,MAAMC,cACJ,MAAMD,eAAe3E,KAAK,CAAC,GAAG,CAACtO,mBAAmByE,MAAM;oBAC1D,MAAM0O,kBACJ9Q,yCAAyC6Q;oBAC3C,MAAME,kBAAkB1T,KAAKoT,iBAAiBK;oBAC9C,MAAM3T,GAAGkH,KAAK,CAACjH,QAAQ2T,kBAAkB;wBAAE5M,WAAW;oBAAK;oBAC3D,MAAMhH,GAAG+S,QAAQ,CACf7S,KAAKmT,aAAaI,iBAClBG;gBAEJ;YAEJ;QACF;IAEJ;IAEA,IAAI9N,OAAOC,IAAI,CAAC6H,gBAAgB3I,MAAM,EAAE;QACtC4O,QAAQC,GAAG,CAACxT,kBAAkBsN;IAChC;IACA,IAAI8C,oBAAoB;QACtB,MAAM,qBAEL,CAFK,IAAI3N,YACR,CAAC,gGAAgG,CAAC,GAD9F,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAIiO,2BAA2B3D,IAAI,GAAG,GAAG;QACvC,MAAM0G,cAAcrF,MAAMC,IAAI,CAACqC,2BAA2BjL,IAAI;QAC9D,MAAM,qBAIL,CAJK,IAAIhD,YACR,CAAC,iDAAiD,EAAEgR,YACjDC,IAAI,GACJ9T,IAAI,CAAC,SAAS,GAHb,qBAAA;mBAAA;wBAAA;0BAAA;QAIN;IACF;IAEA,MAAMF,GAAGmH,SAAS,CAChBjH,KAAK0D,SAAS5C,gBACdwB,eAAe;QACb4E,SAAS;QACTC,cAAcR;QACdS,SAAS;IACX,IACA;IAGF,IAAIzD,WAAW;QACb,MAAMA,UAAUoQ,KAAK;IACvB;IAEA,MAAMlG,OAAOmG,GAAG;IAEhB,OAAOvD;AACT;AAEA,eAAe6C,oBAAoBW,iBAAyB;IAC1D,MAAM7D,UAAyB,EAAE;IACjC,MAAM8D,wBAAwBD,mBAAmBA,mBAAmB7D;IACpE,OAAOA;AACT;AAEA,eAAe8D,wBACbD,iBAAyB,EACzBE,SAAiB,EACjB/D,OAAsB;IAEtB,MAAMgE,eAAe,MAAMtU,GAAGuU,OAAO,CAACF,WAAW;QAC/CG,eAAe;IACjB;IACA,MAAMtF,QAAQC,GAAG,CACfmF,aAAalF,GAAG,CAAC,OAAOqF;QACtB,IAAIA,YAAYC,WAAW,IAAI;YAC7B,MAAMN,wBACJD,mBACAjU,KAAKmU,WAAWI,YAAYE,IAAI,GAChCrE;YAEF;QACF;QACA,IAAI,CAACmE,YAAYE,IAAI,CAACC,QAAQ,CAACpU,qBAAqB;YAClD;QACF;QACA8P,QAAQpD,IAAI,CACV7M,SAAS8T,mBAAmBjU,KAAKmU,WAAWI,YAAYE,IAAI;IAEhE;AAEJ;AAEA,eAAe,eAAeE,UAC5B1R,GAAW,EACXC,OAAyB,EACzBC,IAAU;IAEV,MAAMyR,iBAAiBzR,KAAKE,UAAU,CAAC;IAEvC,OAAOuR,eAAepR,YAAY,CAAC;QACjC,OAAO,MAAMR,cAAcC,KAAKC,SAAS0R;IAC3C;AACF", "ignoreList": [0]}