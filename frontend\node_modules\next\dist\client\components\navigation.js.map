{"version": 3, "sources": ["../../../src/client/components/navigation.ts"], "sourcesContent": ["import type { Flight<PERSON>outerState } from '../../server/app-render/types'\nimport type { Params } from '../../server/request/params'\n\nimport { useContext, useMemo } from 'react'\nimport {\n  AppRouterContext,\n  LayoutRouterContext,\n  type AppRouterInstance,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport {\n  SearchParamsContext,\n  PathnameContext,\n  PathParamsContext,\n} from '../../shared/lib/hooks-client-context.shared-runtime'\nimport { getSegmentValue } from './router-reducer/reducers/get-segment-value'\nimport { PAGE_SEGMENT_KEY, DEFAULT_SEGMENT_KEY } from '../../shared/lib/segment'\nimport { ReadonlyURLSearchParams } from './navigation.react-server'\n\nconst useDynamicRouteParams =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/dynamic-rendering') as typeof import('../../server/app-render/dynamic-rendering')\n      ).useDynamicRouteParams\n    : undefined\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you *read* the current URL's search parameters.\n *\n * Learn more about [`URLSearchParams` on MDN](https://developer.mozilla.org/docs/Web/API/URLSearchParams)\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useSearchParams } from 'next/navigation'\n *\n * export default function Page() {\n *   const searchParams = useSearchParams()\n *   searchParams.get('foo') // returns 'bar' when ?foo=bar\n *   // ...\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSearchParams`](https://nextjs.org/docs/app/api-reference/functions/use-search-params)\n */\n// Client components API\nexport function useSearchParams(): ReadonlyURLSearchParams {\n  const searchParams = useContext(SearchParamsContext)\n\n  // In the case where this is `null`, the compat types added in\n  // `next-env.d.ts` will add a new overload that changes the return type to\n  // include `null`.\n  const readonlySearchParams = useMemo(() => {\n    if (!searchParams) {\n      // When the router is not ready in pages, we won't have the search params\n      // available.\n      return null\n    }\n\n    return new ReadonlyURLSearchParams(searchParams)\n  }, [searchParams]) as ReadonlyURLSearchParams\n\n  if (typeof window === 'undefined') {\n    // AsyncLocalStorage should not be included in the client bundle.\n    const { bailoutToClientRendering } =\n      require('./bailout-to-client-rendering') as typeof import('./bailout-to-client-rendering')\n    // TODO-APP: handle dynamic = 'force-static' here and on the client\n    bailoutToClientRendering('useSearchParams()')\n  }\n\n  return readonlySearchParams\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the current URL's pathname.\n *\n * @example\n * ```ts\n * \"use client\"\n * import { usePathname } from 'next/navigation'\n *\n * export default function Page() {\n *  const pathname = usePathname() // returns \"/dashboard\" on /dashboard?foo=bar\n *  // ...\n * }\n * ```\n *\n * Read more: [Next.js Docs: `usePathname`](https://nextjs.org/docs/app/api-reference/functions/use-pathname)\n */\n// Client components API\nexport function usePathname(): string {\n  useDynamicRouteParams?.('usePathname()')\n\n  // In the case where this is `null`, the compat types added in `next-env.d.ts`\n  // will add a new overload that changes the return type to include `null`.\n  return useContext(PathnameContext) as string\n}\n\n// Client components API\nexport {\n  ServerInsertedHTMLContext,\n  useServerInsertedHTML,\n} from '../../shared/lib/server-inserted-html.shared-runtime'\n\n/**\n *\n * This hook allows you to programmatically change routes inside [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components).\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useRouter } from 'next/navigation'\n *\n * export default function Page() {\n *  const router = useRouter()\n *  // ...\n *  router.push('/dashboard') // Navigate to /dashboard\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useRouter`](https://nextjs.org/docs/app/api-reference/functions/use-router)\n */\n// Client components API\nexport function useRouter(): AppRouterInstance {\n  const router = useContext(AppRouterContext)\n  if (router === null) {\n    throw new Error('invariant expected app router to be mounted')\n  }\n\n  return router\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read a route's dynamic params filled in by the current URL.\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useParams } from 'next/navigation'\n *\n * export default function Page() {\n *   // on /dashboard/[team] where pathname is /dashboard/nextjs\n *   const { team } = useParams() // team === \"nextjs\"\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useParams`](https://nextjs.org/docs/app/api-reference/functions/use-params)\n */\n// Client components API\nexport function useParams<T extends Params = Params>(): T {\n  useDynamicRouteParams?.('useParams()')\n\n  return useContext(PathParamsContext) as T\n}\n\n/** Get the canonical parameters from the current level to the leaf node. */\n// Client components API\nfunction getSelectedLayoutSegmentPath(\n  tree: FlightRouterState,\n  parallelRouteKey: string,\n  first = true,\n  segmentPath: string[] = []\n): string[] {\n  let node: FlightRouterState\n  if (first) {\n    // Use the provided parallel route key on the first parallel route\n    node = tree[1][parallelRouteKey]\n  } else {\n    // After first parallel route prefer children, if there's no children pick the first parallel route.\n    const parallelRoutes = tree[1]\n    node = parallelRoutes.children ?? Object.values(parallelRoutes)[0]\n  }\n\n  if (!node) return segmentPath\n  const segment = node[0]\n\n  let segmentValue = getSegmentValue(segment)\n\n  if (!segmentValue || segmentValue.startsWith(PAGE_SEGMENT_KEY)) {\n    return segmentPath\n  }\n\n  segmentPath.push(segmentValue)\n\n  return getSelectedLayoutSegmentPath(\n    node,\n    parallelRouteKey,\n    false,\n    segmentPath\n  )\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the active route segments **below** the Layout it is called from.\n *\n * @example\n * ```ts\n * 'use client'\n *\n * import { useSelectedLayoutSegments } from 'next/navigation'\n *\n * export default function ExampleClientComponent() {\n *   const segments = useSelectedLayoutSegments()\n *\n *   return (\n *     <ul>\n *       {segments.map((segment, index) => (\n *         <li key={index}>{segment}</li>\n *       ))}\n *     </ul>\n *   )\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSelectedLayoutSegments`](https://nextjs.org/docs/app/api-reference/functions/use-selected-layout-segments)\n */\n// Client components API\nexport function useSelectedLayoutSegments(\n  parallelRouteKey: string = 'children'\n): string[] {\n  useDynamicRouteParams?.('useSelectedLayoutSegments()')\n\n  const context = useContext(LayoutRouterContext)\n  // @ts-expect-error This only happens in `pages`. Type is overwritten in navigation.d.ts\n  if (!context) return null\n\n  return getSelectedLayoutSegmentPath(context.parentTree, parallelRouteKey)\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the active route segment **one level below** the Layout it is called from.\n *\n * @example\n * ```ts\n * 'use client'\n * import { useSelectedLayoutSegment } from 'next/navigation'\n *\n * export default function ExampleClientComponent() {\n *   const segment = useSelectedLayoutSegment()\n *\n *   return <p>Active segment: {segment}</p>\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSelectedLayoutSegment`](https://nextjs.org/docs/app/api-reference/functions/use-selected-layout-segment)\n */\n// Client components API\nexport function useSelectedLayoutSegment(\n  parallelRouteKey: string = 'children'\n): string | null {\n  useDynamicRouteParams?.('useSelectedLayoutSegment()')\n\n  const selectedLayoutSegments = useSelectedLayoutSegments(parallelRouteKey)\n\n  if (!selectedLayoutSegments || selectedLayoutSegments.length === 0) {\n    return null\n  }\n\n  const selectedLayoutSegment =\n    parallelRouteKey === 'children'\n      ? selectedLayoutSegments[0]\n      : selectedLayoutSegments[selectedLayoutSegments.length - 1]\n\n  // if the default slot is showing, we return null since it's not technically \"selected\" (it's a fallback)\n  // and returning an internal value like `__DEFAULT__` would be confusing.\n  return selectedLayoutSegment === DEFAULT_SEGMENT_KEY\n    ? null\n    : selectedLayoutSegment\n}\n\nexport { unstable_isUnrecognizedActionError } from './unrecognized-action-error'\n\n// Shared components APIs\nexport {\n  notFound,\n  forbidden,\n  unauthorized,\n  redirect,\n  permanentRedirect,\n  RedirectType,\n  ReadonlyURLSearchParams,\n  unstable_rethrow,\n} from './navigation.react-server'\n"], "names": ["ReadonlyURLSearchParams", "RedirectType", "ServerInsertedHTMLContext", "forbidden", "notFound", "permanentRedirect", "redirect", "unauthorized", "unstable_isUnrecognizedActionError", "unstable_rethrow", "useParams", "usePathname", "useRouter", "useSearchParams", "useSelectedLayoutSegment", "useSelectedLayoutSegments", "useServerInsertedHTML", "useDynamicRouteParams", "window", "require", "undefined", "searchParams", "useContext", "SearchParamsContext", "readonlySearchParams", "useMemo", "bailoutToClientRendering", "PathnameContext", "router", "AppRouterContext", "Error", "PathParamsContext", "getSelectedLayoutSegmentPath", "tree", "parallelRouteKey", "first", "segmentPath", "node", "parallelRoutes", "children", "Object", "values", "segment", "segmentValue", "getSegmentValue", "startsWith", "PAGE_SEGMENT_KEY", "push", "context", "LayoutRouterContext", "parentTree", "selectedLayoutSegments", "length", "selectedLayoutSegment", "DEFAULT_SEGMENT_KEY"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA4REA,uBAAuB;eAAvBA,8CAAuB;;IADvBC,YAAY;eAAZA,mCAAY;;IAtLZC,yBAAyB;eAAzBA,0DAAyB;;IAkLzBC,SAAS;eAATA,gCAAS;;IADTC,QAAQ;eAARA,+BAAQ;;IAIRC,iBAAiB;eAAjBA,wCAAiB;;IADjBC,QAAQ;eAARA,+BAAQ;;IADRC,YAAY;eAAZA,mCAAY;;IANLC,kCAAkC;eAAlCA,2DAAkC;;IAWzCC,gBAAgB;eAAhBA,uCAAgB;;IAtIFC,SAAS;eAATA;;IA5DAC,WAAW;eAAXA;;IAiCAC,SAAS;eAATA;;IA9EAC,eAAe;eAAfA;;IA6MAC,wBAAwB;eAAxBA;;IA/BAC,yBAAyB;eAAzBA;;IAtHdC,qBAAqB;eAArBA,sDAAqB;;;uBAnGa;+CAK7B;iDAKA;iCACyB;yBACsB;uCACd;iDAuFjC;yCA2K4C;AAhQnD,MAAMC,wBACJ,OAAOC,WAAW,cACd,AACEC,QAAQ,6CACRF,qBAAqB,GACvBG;AAuBC,SAASP;IACd,MAAMQ,eAAeC,IAAAA,iBAAU,EAACC,oDAAmB;IAEnD,8DAA8D;IAC9D,0EAA0E;IAC1E,kBAAkB;IAClB,MAAMC,uBAAuBC,IAAAA,cAAO,EAAC;QACnC,IAAI,CAACJ,cAAc;YACjB,yEAAyE;YACzE,aAAa;YACb,OAAO;QACT;QAEA,OAAO,IAAIrB,8CAAuB,CAACqB;IACrC,GAAG;QAACA;KAAa;IAEjB,IAAI,OAAOH,WAAW,aAAa;QACjC,iEAAiE;QACjE,MAAM,EAAEQ,wBAAwB,EAAE,GAChCP,QAAQ;QACV,mEAAmE;QACnEO,yBAAyB;IAC3B;IAEA,OAAOF;AACT;AAoBO,SAASb;IACdM,yCAAAA,sBAAwB;IAExB,8EAA8E;IAC9E,0EAA0E;IAC1E,OAAOK,IAAAA,iBAAU,EAACK,gDAAe;AACnC;AA2BO,SAASf;IACd,MAAMgB,SAASN,IAAAA,iBAAU,EAACO,+CAAgB;IAC1C,IAAID,WAAW,MAAM;QACnB,MAAM,qBAAwD,CAAxD,IAAIE,MAAM,gDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAuD;IAC/D;IAEA,OAAOF;AACT;AAoBO,SAASlB;IACdO,yCAAAA,sBAAwB;IAExB,OAAOK,IAAAA,iBAAU,EAACS,kDAAiB;AACrC;AAEA,0EAA0E,GAC1E,wBAAwB;AACxB,SAASC,6BACPC,IAAuB,EACvBC,gBAAwB,EACxBC,KAAY,EACZC,WAA0B;IAD1BD,IAAAA,kBAAAA,QAAQ;IACRC,IAAAA,wBAAAA,cAAwB,EAAE;IAE1B,IAAIC;IACJ,IAAIF,OAAO;QACT,kEAAkE;QAClEE,OAAOJ,IAAI,CAAC,EAAE,CAACC,iBAAiB;IAClC,OAAO;QACL,oGAAoG;QACpG,MAAMI,iBAAiBL,IAAI,CAAC,EAAE;YACvBK;QAAPD,OAAOC,CAAAA,2BAAAA,eAAeC,QAAQ,YAAvBD,2BAA2BE,OAAOC,MAAM,CAACH,eAAe,CAAC,EAAE;IACpE;IAEA,IAAI,CAACD,MAAM,OAAOD;IAClB,MAAMM,UAAUL,IAAI,CAAC,EAAE;IAEvB,IAAIM,eAAeC,IAAAA,gCAAe,EAACF;IAEnC,IAAI,CAACC,gBAAgBA,aAAaE,UAAU,CAACC,yBAAgB,GAAG;QAC9D,OAAOV;IACT;IAEAA,YAAYW,IAAI,CAACJ;IAEjB,OAAOX,6BACLK,MACAH,kBACA,OACAE;AAEJ;AA4BO,SAASrB,0BACdmB,gBAAqC;IAArCA,IAAAA,6BAAAA,mBAA2B;IAE3BjB,yCAAAA,sBAAwB;IAExB,MAAM+B,UAAU1B,IAAAA,iBAAU,EAAC2B,kDAAmB;IAC9C,wFAAwF;IACxF,IAAI,CAACD,SAAS,OAAO;IAErB,OAAOhB,6BAA6BgB,QAAQE,UAAU,EAAEhB;AAC1D;AAqBO,SAASpB,yBACdoB,gBAAqC;IAArCA,IAAAA,6BAAAA,mBAA2B;IAE3BjB,yCAAAA,sBAAwB;IAExB,MAAMkC,yBAAyBpC,0BAA0BmB;IAEzD,IAAI,CAACiB,0BAA0BA,uBAAuBC,MAAM,KAAK,GAAG;QAClE,OAAO;IACT;IAEA,MAAMC,wBACJnB,qBAAqB,aACjBiB,sBAAsB,CAAC,EAAE,GACzBA,sBAAsB,CAACA,uBAAuBC,MAAM,GAAG,EAAE;IAE/D,yGAAyG;IACzG,yEAAyE;IACzE,OAAOC,0BAA0BC,4BAAmB,GAChD,OACAD;AACN", "ignoreList": [0]}