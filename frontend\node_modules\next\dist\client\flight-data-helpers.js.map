{"version": 3, "sources": ["../../src/client/flight-data-helpers.ts"], "sourcesContent": ["import type {\n  CacheNodeSeedData,\n  FlightData,\n  FlightDataPath,\n  FlightRouterState,\n  FlightSegmentPath,\n  Segment,\n} from '../server/app-render/types'\nimport type { HeadData } from '../shared/lib/app-router-context.shared-runtime'\nimport { PAGE_SEGMENT_KEY } from '../shared/lib/segment'\n\nexport type NormalizedFlightData = {\n  /**\n   * The full `FlightSegmentPath` inclusive of the final `Segment`\n   */\n  segmentPath: FlightSegmentPath\n  /**\n   * The `FlightSegmentPath` exclusive of the final `Segment`\n   */\n  pathToSegment: FlightSegmentPath\n  segment: Segment\n  tree: FlightRouterState\n  seedData: CacheNodeSeedData | null\n  head: HeadData\n  isHeadPartial: boolean\n  isRootRender: boolean\n}\n\n// TODO: We should only have to export `normalizeFlightData`, however because the initial flight data\n// that gets passed to `createInitialRouterState` doesn't conform to the `FlightDataPath` type (it's missing the root segment)\n// we're currently exporting it so we can use it directly. This should be fixed as part of the unification of\n// the different ways we express `FlightSegmentPath`.\nexport function getFlightDataPartsFromPath(\n  flightDataPath: FlightDataPath\n): NormalizedFlightData {\n  // Pick the last 4 items from the `FlightDataPath` to get the [tree, seedData, viewport, isHeadPartial].\n  const flightDataPathLength = 4\n  // tree, seedData, and head are *always* the last three items in the `FlightDataPath`.\n  const [tree, seedData, head, isHeadPartial] =\n    flightDataPath.slice(-flightDataPathLength)\n  // The `FlightSegmentPath` is everything except the last three items. For a root render, it won't be present.\n  const segmentPath = flightDataPath.slice(0, -flightDataPathLength)\n\n  return {\n    // TODO: Unify these two segment path helpers. We are inconsistently pushing an empty segment (\"\")\n    // to the start of the segment path in some places which makes it hard to use solely the segment path.\n    // Look for \"// TODO-APP: remove ''\" in the codebase.\n    pathToSegment: segmentPath.slice(0, -1),\n    segmentPath,\n    // if the `FlightDataPath` corresponds with the root, there'll be no segment path,\n    // in which case we default to ''.\n    segment: segmentPath[segmentPath.length - 1] ?? '',\n    tree,\n    seedData,\n    head,\n    isHeadPartial,\n    isRootRender: flightDataPath.length === flightDataPathLength,\n  }\n}\n\nexport function getNextFlightSegmentPath(\n  flightSegmentPath: FlightSegmentPath\n): FlightSegmentPath {\n  // Since `FlightSegmentPath` is a repeated tuple of `Segment` and `ParallelRouteKey`, we slice off two items\n  // to get the next segment path.\n  return flightSegmentPath.slice(2)\n}\n\nexport function normalizeFlightData(\n  flightData: FlightData\n): NormalizedFlightData[] | string {\n  // FlightData can be a string when the server didn't respond with a proper flight response,\n  // or when a redirect happens, to signal to the client that it needs to perform an MPA navigation.\n  if (typeof flightData === 'string') {\n    return flightData\n  }\n\n  return flightData.map((flightDataPath) =>\n    getFlightDataPartsFromPath(flightDataPath)\n  )\n}\n\n/**\n * This function is used to prepare the flight router state for the request.\n * It removes markers that are not needed by the server, and are purely used\n * for stashing state on the client.\n * @param flightRouterState - The flight router state to prepare.\n * @param isHmrRefresh - Whether this is an HMR refresh request.\n * @returns The prepared flight router state.\n */\nexport function prepareFlightRouterStateForRequest(\n  flightRouterState: FlightRouterState,\n  isHmrRefresh?: boolean\n): string {\n  // HMR requests need the complete, unmodified state for proper functionality\n  if (isHmrRefresh) {\n    return encodeURIComponent(JSON.stringify(flightRouterState))\n  }\n\n  return encodeURIComponent(\n    JSON.stringify(stripClientOnlyDataFromFlightRouterState(flightRouterState))\n  )\n}\n\n/**\n * Recursively strips client-only data from FlightRouterState while preserving\n * server-needed information for proper rendering decisions.\n */\nfunction stripClientOnlyDataFromFlightRouterState(\n  flightRouterState: FlightRouterState\n): FlightRouterState {\n  const [\n    segment,\n    parallelRoutes,\n    _url, // Intentionally unused - URLs are client-only\n    refreshMarker,\n    isRootLayout,\n    hasLoadingBoundary,\n  ] = flightRouterState\n\n  // __PAGE__ segments are always fetched from the server, so there's\n  // no need to send them up\n  const cleanedSegment = stripSearchParamsFromPageSegment(segment)\n\n  // Recursively process parallel routes\n  const cleanedParallelRoutes: { [key: string]: FlightRouterState } = {}\n  for (const [key, childState] of Object.entries(parallelRoutes)) {\n    cleanedParallelRoutes[key] =\n      stripClientOnlyDataFromFlightRouterState(childState)\n  }\n\n  const result: FlightRouterState = [\n    cleanedSegment,\n    cleanedParallelRoutes,\n    null, // URLs omitted - server reconstructs paths from segments\n    shouldPreserveRefreshMarker(refreshMarker) ? refreshMarker : null,\n  ]\n\n  // Append optional fields if present\n  if (isRootLayout !== undefined) {\n    result[4] = isRootLayout\n  }\n  if (hasLoadingBoundary !== undefined) {\n    result[5] = hasLoadingBoundary\n  }\n\n  return result\n}\n\n/**\n * Strips search parameters from __PAGE__ segments to prevent sensitive\n * client-side data from being sent to the server.\n */\nfunction stripSearchParamsFromPageSegment(segment: Segment): Segment {\n  if (\n    typeof segment === 'string' &&\n    segment.startsWith(PAGE_SEGMENT_KEY + '?')\n  ) {\n    return PAGE_SEGMENT_KEY\n  }\n  return segment\n}\n\n/**\n * Determines whether the refresh marker should be sent to the server\n * Client-only markers like 'refresh' are stripped, while server-needed markers\n * like 'refetch' and 'inside-shared-layout' are preserved.\n */\nfunction shouldPreserveRefreshMarker(\n  refreshMarker: FlightRouterState[3]\n): boolean {\n  return Boolean(refreshMarker && refreshMarker !== 'refresh')\n}\n"], "names": ["getFlightDataPartsFromPath", "getNextFlightSegmentPath", "normalizeFlightData", "prepareFlightRouterStateForRequest", "flightDataPath", "flightDataPathLength", "tree", "seedData", "head", "isHeadPartial", "slice", "segmentPath", "pathToSegment", "segment", "length", "isRootRender", "flightSegmentPath", "flightData", "map", "flightRouterState", "isHmrRefresh", "encodeURIComponent", "JSON", "stringify", "stripClientOnlyDataFromFlightRouterState", "parallelRoutes", "_url", "refresh<PERSON><PERSON><PERSON>", "isRootLayout", "hasLoadingBoundary", "cleanedSegment", "stripSearchParamsFromPageSegment", "cleanedParallelRoutes", "key", "childState", "Object", "entries", "result", "shouldPreserveRefreshMarker", "undefined", "startsWith", "PAGE_SEGMENT_KEY", "Boolean"], "mappings": ";;;;;;;;;;;;;;;;;IAgCgBA,0BAA0B;eAA1BA;;IA4BAC,wBAAwB;eAAxBA;;IAQAC,mBAAmB;eAAnBA;;IAsBAC,kCAAkC;eAAlCA;;;yBAjFiB;AAuB1B,SAASH,2BACdI,cAA8B;IAE9B,wGAAwG;IACxG,MAAMC,uBAAuB;IAC7B,sFAAsF;IACtF,MAAM,CAACC,MAAMC,UAAUC,MAAMC,cAAc,GACzCL,eAAeM,KAAK,CAAC,CAACL;IACxB,6GAA6G;IAC7G,MAAMM,cAAcP,eAAeM,KAAK,CAAC,GAAG,CAACL;QAUlCM;IARX,OAAO;QACL,kGAAkG;QAClG,sGAAsG;QACtG,qDAAqD;QACrDC,eAAeD,YAAYD,KAAK,CAAC,GAAG,CAAC;QACrCC;QACA,kFAAkF;QAClF,kCAAkC;QAClCE,SAASF,CAAAA,gBAAAA,WAAW,CAACA,YAAYG,MAAM,GAAG,EAAE,YAAnCH,gBAAuC;QAChDL;QACAC;QACAC;QACAC;QACAM,cAAcX,eAAeU,MAAM,KAAKT;IAC1C;AACF;AAEO,SAASJ,yBACde,iBAAoC;IAEpC,4GAA4G;IAC5G,gCAAgC;IAChC,OAAOA,kBAAkBN,KAAK,CAAC;AACjC;AAEO,SAASR,oBACde,UAAsB;IAEtB,2FAA2F;IAC3F,kGAAkG;IAClG,IAAI,OAAOA,eAAe,UAAU;QAClC,OAAOA;IACT;IAEA,OAAOA,WAAWC,GAAG,CAAC,CAACd,iBACrBJ,2BAA2BI;AAE/B;AAUO,SAASD,mCACdgB,iBAAoC,EACpCC,YAAsB;IAEtB,4EAA4E;IAC5E,IAAIA,cAAc;QAChB,OAAOC,mBAAmBC,KAAKC,SAAS,CAACJ;IAC3C;IAEA,OAAOE,mBACLC,KAAKC,SAAS,CAACC,yCAAyCL;AAE5D;AAEA;;;CAGC,GACD,SAASK,yCACPL,iBAAoC;IAEpC,MAAM,CACJN,SACAY,gBACAC,MACAC,eACAC,cACAC,mBACD,GAAGV;IAEJ,mEAAmE;IACnE,0BAA0B;IAC1B,MAAMW,iBAAiBC,iCAAiClB;IAExD,sCAAsC;IACtC,MAAMmB,wBAA8D,CAAC;IACrE,KAAK,MAAM,CAACC,KAAKC,WAAW,IAAIC,OAAOC,OAAO,CAACX,gBAAiB;QAC9DO,qBAAqB,CAACC,IAAI,GACxBT,yCAAyCU;IAC7C;IAEA,MAAMG,SAA4B;QAChCP;QACAE;QACA;QACAM,4BAA4BX,iBAAiBA,gBAAgB;KAC9D;IAED,oCAAoC;IACpC,IAAIC,iBAAiBW,WAAW;QAC9BF,MAAM,CAAC,EAAE,GAAGT;IACd;IACA,IAAIC,uBAAuBU,WAAW;QACpCF,MAAM,CAAC,EAAE,GAAGR;IACd;IAEA,OAAOQ;AACT;AAEA;;;CAGC,GACD,SAASN,iCAAiClB,OAAgB;IACxD,IACE,OAAOA,YAAY,YACnBA,QAAQ2B,UAAU,CAACC,yBAAgB,GAAG,MACtC;QACA,OAAOA,yBAAgB;IACzB;IACA,OAAO5B;AACT;AAEA;;;;CAIC,GACD,SAASyB,4BACPX,aAAmC;IAEnC,OAAOe,QAAQf,iBAAiBA,kBAAkB;AACpD", "ignoreList": [0]}