{"version": 3, "sources": ["../../../src/server/dev/hot-reloader-webpack.ts"], "sourcesContent": ["import type { NextConfigComplete } from '../config-shared'\nimport type { CustomRoutes } from '../../lib/load-custom-routes'\nimport type { Duplex } from 'stream'\nimport type { Telemetry } from '../../telemetry/storage'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { UrlObject } from 'url'\nimport type { RouteDefinition } from '../route-definitions/route-definition'\n\nimport { type webpack, StringXor } from 'next/dist/compiled/webpack/webpack'\nimport {\n  getOverlayMiddleware,\n  getSourceMapMiddleware,\n} from './middleware-webpack'\nimport { WebpackHotMiddleware } from './hot-middleware'\nimport { join, relative, isAbsolute, posix, dirname } from 'path'\nimport {\n  createEntrypoints,\n  createPagesMapping,\n  finalizeEntrypoint,\n  getClientEntry,\n  getEdgeServerEntry,\n  getAppEntry,\n  runDependingOnPageType,\n  getStaticInfoIncludingLayouts,\n  getInstrumentationEntry,\n} from '../../build/entries'\nimport { watchCompilers } from '../../build/output'\nimport * as Log from '../../build/output/log'\nimport getBaseWebpackConfig, {\n  getCacheDirectories,\n  loadProjectInfo,\n} from '../../build/webpack-config'\nimport { APP_DIR_ALIAS, WEBPACK_LAYERS } from '../../lib/constants'\nimport { recursiveDelete } from '../../lib/recursive-delete'\nimport {\n  BLOCKED_PAGES,\n  CLIENT_STATIC_FILES_RUNTIME_AMP,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN_APP,\n  CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH,\n  COMPILER_NAMES,\n  RSC_MODULE_TYPES,\n} from '../../shared/lib/constants'\nimport type { __ApiPreviewProps } from '../api-utils'\nimport { getPathMatch } from '../../shared/lib/router/utils/path-match'\nimport { findPageFile } from '../lib/find-page-file'\nimport {\n  BUILDING,\n  getEntries,\n  EntryTypes,\n  getInvalidator,\n  onDemandEntryHandler,\n} from './on-demand-entry-handler'\nimport { denormalizePagePath } from '../../shared/lib/page-path/denormalize-page-path'\nimport { normalizePathSep } from '../../shared/lib/page-path/normalize-path-sep'\nimport getRouteFromEntrypoint from '../get-route-from-entrypoint'\nimport {\n  difference,\n  isInstrumentationHookFile,\n  isMiddlewareFile,\n  isMiddlewareFilename,\n} from '../../build/utils'\nimport { DecodeError } from '../../shared/lib/utils'\nimport { type Span, trace } from '../../trace'\nimport { getProperError } from '../../lib/is-error'\nimport ws from 'next/dist/compiled/ws'\nimport { existsSync, promises as fs } from 'fs'\nimport type { UnwrapPromise } from '../../lib/coalesced-function'\nimport { parseVersionInfo } from './parse-version-info'\nimport type { VersionInfo } from './parse-version-info'\nimport { isAPIRoute } from '../../lib/is-api-route'\nimport { getRouteLoaderEntry } from '../../build/webpack/loaders/next-route-loader'\nimport {\n  isInternalComponent,\n  isNonRoutePagesPage,\n} from '../../lib/is-internal-component'\nimport { RouteKind } from '../route-kind'\nimport {\n  HMR_ACTIONS_SENT_TO_BROWSER,\n  type NextJsHotReloaderInterface,\n} from './hot-reloader-types'\nimport type { HMR_ACTION_TYPES } from './hot-reloader-types'\nimport type { WebpackError } from 'webpack'\nimport { PAGE_TYPES } from '../../lib/page-types'\nimport { FAST_REFRESH_RUNTIME_RELOAD } from './messages'\nimport { getNodeDebugType } from '../lib/utils'\nimport { getNextErrorFeedbackMiddleware } from '../../next-devtools/server/get-next-error-feedback-middleware'\nimport { getDevOverlayFontMiddleware } from '../../next-devtools/server/font/get-dev-overlay-font-middleware'\nimport { getDisableDevIndicatorMiddleware } from '../../next-devtools/server/dev-indicator-middleware'\nimport getWebpackBundler from '../../shared/lib/get-webpack-bundler'\nimport { getRestartDevServerMiddleware } from '../../next-devtools/server/restart-dev-server-middleware'\nimport { checkPersistentCacheInvalidationAndCleanup } from '../../build/webpack/cache-invalidation'\nimport { receiveBrowserLogsWebpack } from './browser-logs/receive-logs'\nimport {\n  devToolsConfigMiddleware,\n  getDevToolsConfig,\n} from '../../next-devtools/server/devtools-config-middleware'\n\nconst MILLISECONDS_IN_NANOSECOND = BigInt(1_000_000)\n\nfunction diff(a: Set<any>, b: Set<any>) {\n  return new Set([...a].filter((v) => !b.has(v)))\n}\n\nconst wsServer = new ws.Server({ noServer: true })\n\nexport async function renderScriptError(\n  res: ServerResponse,\n  error: Error,\n  { verbose = true } = {}\n): Promise<{ finished: true | undefined }> {\n  // Asks CDNs and others to not to cache the errored page\n  res.setHeader(\n    'Cache-Control',\n    'no-cache, no-store, max-age=0, must-revalidate'\n  )\n\n  if ((error as any).code === 'ENOENT') {\n    return { finished: undefined }\n  }\n\n  if (verbose) {\n    console.error(error.stack)\n  }\n  res.statusCode = 500\n  res.end('500 - Internal Error')\n  return { finished: true }\n}\n\nfunction addCorsSupport(req: IncomingMessage, res: ServerResponse) {\n  // Only rewrite CORS handling when URL matches a hot-reloader middleware\n  if (!req.url!.startsWith('/__next')) {\n    return { preflight: false }\n  }\n\n  if (!req.headers.origin) {\n    return { preflight: false }\n  }\n\n  res.setHeader('Access-Control-Allow-Origin', req.headers.origin)\n  res.setHeader('Access-Control-Allow-Methods', 'OPTIONS, GET')\n  // Based on https://github.com/primus/access-control/blob/4cf1bc0e54b086c91e6aa44fb14966fa5ef7549c/index.js#L158\n  if (req.headers['access-control-request-headers']) {\n    res.setHeader(\n      'Access-Control-Allow-Headers',\n      req.headers['access-control-request-headers'] as string\n    )\n  }\n\n  if (req.method === 'OPTIONS') {\n    res.writeHead(200)\n    res.end()\n    return { preflight: true }\n  }\n\n  return { preflight: false }\n}\n\nexport const matchNextPageBundleRequest = getPathMatch(\n  '/_next/static/chunks/pages/:path*.js(\\\\.map|)'\n)\n\n// Iteratively look up the issuer till it ends up at the root\nfunction findEntryModule(\n  module: webpack.Module,\n  compilation: webpack.Compilation\n): any {\n  for (;;) {\n    const issuer = compilation.moduleGraph.getIssuer(module)\n    if (!issuer) return module\n    module = issuer\n  }\n}\n\nfunction erroredPages(compilation: webpack.Compilation) {\n  const failedPages: { [page: string]: WebpackError[] } = {}\n  for (const error of compilation.errors) {\n    if (!error.module) {\n      continue\n    }\n\n    const entryModule = findEntryModule(error.module, compilation)\n    const { name } = entryModule\n    if (!name) {\n      continue\n    }\n\n    // Only pages have to be reloaded\n    const enhancedName = getRouteFromEntrypoint(name)\n\n    if (!enhancedName) {\n      continue\n    }\n\n    if (!failedPages[enhancedName]) {\n      failedPages[enhancedName] = []\n    }\n\n    failedPages[enhancedName].push(error)\n  }\n\n  return failedPages\n}\n\nexport async function getVersionInfo(): Promise<VersionInfo> {\n  let installed = '0.0.0'\n\n  try {\n    installed = require('next/package.json').version\n\n    let res\n\n    try {\n      // use NPM registry regardless user using Yarn\n      res = await fetch('https://registry.npmjs.org/-/package/next/dist-tags')\n    } catch {\n      // ignore fetch errors\n    }\n\n    if (!res || !res.ok) return { installed, staleness: 'unknown' }\n\n    const { latest, canary } = await res.json()\n\n    return parseVersionInfo({ installed, latest, canary })\n  } catch (e: any) {\n    console.error(e)\n    return { installed, staleness: 'unknown' }\n  }\n}\n\nexport default class HotReloaderWebpack implements NextJsHotReloaderInterface {\n  private hasAmpEntrypoints: boolean\n  private hasAppRouterEntrypoints: boolean\n  private hasPagesRouterEntrypoints: boolean\n  private dir: string\n  private buildId: string\n  private encryptionKey: string\n  private middlewares: ((\n    req: IncomingMessage,\n    res: ServerResponse,\n    next: () => void\n  ) => Promise<void>)[]\n  private pagesDir?: string\n  private distDir: string\n  private webpackHotMiddleware?: WebpackHotMiddleware\n  private config: NextConfigComplete\n  private clientStats: webpack.Stats | null\n  private clientError: Error | null = null\n  private serverError: Error | null = null\n  private hmrServerError: Error | null = null\n  private serverPrevDocumentHash: string | null\n  private serverChunkNames?: Set<string>\n  private prevChunkNames?: Set<any>\n  private onDemandEntries?: ReturnType<typeof onDemandEntryHandler>\n  private previewProps: __ApiPreviewProps\n  private watcher: any\n  private rewrites: CustomRoutes['rewrites']\n  private fallbackWatcher: any\n  private hotReloaderSpan: Span\n  private pagesMapping: { [key: string]: string } = {}\n  private appDir?: string\n  private telemetry: Telemetry\n  private resetFetch: () => void\n  private versionInfo: VersionInfo = {\n    staleness: 'unknown',\n    installed: '0.0.0',\n  }\n  private devtoolsFrontendUrl: string | undefined\n  private reloadAfterInvalidation: boolean = false\n  private isSrcDir: boolean\n\n  public serverStats: webpack.Stats | null\n  public edgeServerStats: webpack.Stats | null\n  public multiCompiler?: webpack.MultiCompiler\n  public activeWebpackConfigs?: Array<\n    UnwrapPromise<ReturnType<typeof getBaseWebpackConfig>>\n  >\n\n  constructor(\n    dir: string,\n    {\n      config,\n      isSrcDir,\n      pagesDir,\n      distDir,\n      buildId,\n      encryptionKey,\n      previewProps,\n      rewrites,\n      appDir,\n      telemetry,\n      resetFetch,\n    }: {\n      config: NextConfigComplete\n      isSrcDir: boolean\n      pagesDir?: string\n      distDir: string\n      buildId: string\n      encryptionKey: string\n      previewProps: __ApiPreviewProps\n      rewrites: CustomRoutes['rewrites']\n      appDir?: string\n      telemetry: Telemetry\n      resetFetch: () => void\n    }\n  ) {\n    this.hasAmpEntrypoints = false\n    this.hasAppRouterEntrypoints = false\n    this.hasPagesRouterEntrypoints = false\n    this.buildId = buildId\n    this.encryptionKey = encryptionKey\n    this.dir = dir\n    this.isSrcDir = isSrcDir\n    this.middlewares = []\n    this.pagesDir = pagesDir\n    this.appDir = appDir\n    this.distDir = distDir\n    this.clientStats = null\n    this.serverStats = null\n    this.edgeServerStats = null\n    this.serverPrevDocumentHash = null\n    this.telemetry = telemetry\n    this.resetFetch = resetFetch\n\n    this.config = config\n    this.previewProps = previewProps\n    this.rewrites = rewrites\n    this.hotReloaderSpan = trace('hot-reloader', undefined, {\n      version: process.env.__NEXT_VERSION as string,\n    })\n    // Ensure the hotReloaderSpan is flushed immediately as it's the parentSpan for all processing\n    // of the current `next dev` invocation.\n    this.hotReloaderSpan.stop()\n  }\n\n  public async run(\n    req: IncomingMessage,\n    res: ServerResponse,\n    parsedUrl: UrlObject\n  ): Promise<{ finished?: true }> {\n    // Usually CORS support is not needed for the hot-reloader (this is dev only feature)\n    // With when the app runs for multi-zones support behind a proxy,\n    // the current page is trying to access this URL via assetPrefix.\n    // That's when the CORS support is needed.\n    const { preflight } = addCorsSupport(req, res)\n    if (preflight) {\n      return {}\n    }\n\n    // When a request comes in that is a page bundle, e.g. /_next/static/<buildid>/pages/index.js\n    // we have to compile the page using on-demand-entries, this middleware will handle doing that\n    // by adding the page to on-demand-entries, waiting till it's done\n    // and then the bundle will be served like usual by the actual route in server/index.js\n    const handlePageBundleRequest = async (\n      pageBundleRes: ServerResponse,\n      parsedPageBundleUrl: UrlObject\n    ): Promise<{ finished?: true }> => {\n      const { pathname } = parsedPageBundleUrl\n      if (!pathname) return {}\n\n      const params = matchNextPageBundleRequest(pathname)\n      if (!params) return {}\n\n      let decodedPagePath: string\n\n      try {\n        decodedPagePath = `/${params.path\n          .map((param: string) => decodeURIComponent(param))\n          .join('/')}`\n      } catch (_) {\n        throw new DecodeError('failed to decode param')\n      }\n\n      const page = denormalizePagePath(decodedPagePath)\n\n      if (page === '/_error' || BLOCKED_PAGES.indexOf(page) === -1) {\n        try {\n          await this.ensurePage({ page, clientOnly: true, url: req.url })\n        } catch (error) {\n          return await renderScriptError(pageBundleRes, getProperError(error))\n        }\n\n        const errors = await this.getCompilationErrors(page)\n        if (errors.length > 0) {\n          return await renderScriptError(pageBundleRes, errors[0], {\n            verbose: false,\n          })\n        }\n      }\n\n      return {}\n    }\n\n    const { finished } = await handlePageBundleRequest(res, parsedUrl)\n\n    for (const middleware of this.middlewares) {\n      let calledNext = false\n\n      await middleware(req, res, () => {\n        calledNext = true\n      })\n\n      if (!calledNext) {\n        return { finished: true }\n      }\n    }\n\n    return { finished }\n  }\n\n  public setHmrServerError(error: Error | null): void {\n    this.hmrServerError = error\n  }\n\n  public clearHmrServerError(): void {\n    if (this.hmrServerError) {\n      this.setHmrServerError(null)\n      this.send({\n        action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n        data: 'clear hmr server error',\n      })\n    }\n  }\n\n  protected async refreshServerComponents(hash: string): Promise<void> {\n    this.send({\n      action: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES,\n      hash,\n      // TODO: granular reloading of changes\n      // entrypoints: serverComponentChanges,\n    })\n  }\n\n  public onHMR(\n    req: IncomingMessage,\n    _socket: Duplex,\n    head: Buffer,\n    callback: (client: ws.WebSocket) => void\n  ) {\n    wsServer.handleUpgrade(req, req.socket, head, (client) => {\n      this.webpackHotMiddleware?.onHMR(client)\n      this.onDemandEntries?.onHMR(client, () => this.hmrServerError)\n      callback(client)\n\n      client.addEventListener('message', async ({ data }) => {\n        data = typeof data !== 'string' ? data.toString() : data\n\n        try {\n          const payload = JSON.parse(data)\n\n          let traceChild:\n            | {\n                name: string\n                startTime?: bigint\n                endTime?: bigint\n                attrs?: Record<string, number | string | undefined | string[]>\n              }\n            | undefined\n\n          switch (payload.event) {\n            case 'span-end': {\n              traceChild = {\n                name: payload.spanName,\n                startTime:\n                  BigInt(Math.floor(payload.startTime)) *\n                  MILLISECONDS_IN_NANOSECOND,\n                attrs: payload.attributes,\n                endTime:\n                  BigInt(Math.floor(payload.endTime)) *\n                  MILLISECONDS_IN_NANOSECOND,\n              }\n              break\n            }\n            case 'client-hmr-latency': {\n              traceChild = {\n                name: payload.event,\n                startTime:\n                  BigInt(payload.startTime) * MILLISECONDS_IN_NANOSECOND,\n                endTime: BigInt(payload.endTime) * MILLISECONDS_IN_NANOSECOND,\n                attrs: {\n                  updatedModules: payload.updatedModules.map((m: string) =>\n                    m\n                      .replace(`(${WEBPACK_LAYERS.appPagesBrowser})/`, '')\n                      .replace(/^\\.\\//, '[project]/')\n                  ),\n                  page: payload.page,\n                  isPageHidden: payload.isPageHidden,\n                },\n              }\n              break\n            }\n            case 'client-reload-page':\n            case 'client-success': {\n              traceChild = {\n                name: payload.event,\n              }\n              break\n            }\n            case 'client-error': {\n              traceChild = {\n                name: payload.event,\n                attrs: { errorCount: payload.errorCount },\n              }\n              break\n            }\n            case 'client-warning': {\n              traceChild = {\n                name: payload.event,\n                attrs: { warningCount: payload.warningCount },\n              }\n              break\n            }\n            case 'client-removed-page':\n            case 'client-added-page': {\n              traceChild = {\n                name: payload.event,\n                attrs: { page: payload.page || '' },\n              }\n              break\n            }\n            case 'client-full-reload': {\n              const { event, stackTrace, hadRuntimeError } = payload\n\n              traceChild = {\n                name: event,\n                attrs: { stackTrace: stackTrace ?? '' },\n              }\n\n              if (hadRuntimeError) {\n                Log.warn(FAST_REFRESH_RUNTIME_RELOAD)\n                break\n              }\n\n              let fileMessage = ''\n              if (stackTrace) {\n                const file = /Aborted because (.+) is not accepted/.exec(\n                  stackTrace\n                )?.[1]\n                if (file) {\n                  // `file` is filepath in `pages/` but it can be a webpack url.\n                  // If it's a webpack loader URL, it will include the app-pages layer\n                  if (file.startsWith(`(${WEBPACK_LAYERS.appPagesBrowser})/`)) {\n                    const fileUrl = new URL(file, 'file://')\n                    const cwd = process.cwd()\n                    const modules = fileUrl.searchParams\n                      .getAll('modules')\n                      .map((filepath) => filepath.slice(cwd.length + 1))\n                      .filter(\n                        (filepath) => !filepath.startsWith('node_modules')\n                      )\n\n                    if (modules.length > 0) {\n                      fileMessage = ` when ${modules.join(', ')} changed`\n                    }\n                  } else if (\n                    // Handle known webpack layers\n                    file.startsWith(`(${WEBPACK_LAYERS.pagesDirBrowser})/`)\n                  ) {\n                    const cleanedFilePath = file.slice(\n                      `(${WEBPACK_LAYERS.pagesDirBrowser})/`.length\n                    )\n\n                    fileMessage = ` when ${cleanedFilePath} changed`\n                  } else {\n                    fileMessage = ` when ${file} changed`\n                  }\n                }\n              }\n\n              Log.warn(\n                `Fast Refresh had to perform a full reload${fileMessage}. Read more: https://nextjs.org/docs/messages/fast-refresh-reload`\n              )\n              break\n            }\n            case 'browser-logs': {\n              if (this.config.experimental.browserDebugInfoInTerminal) {\n                await receiveBrowserLogsWebpack({\n                  entries: payload.entries,\n                  router: payload.router,\n                  sourceType: payload.sourceType,\n                  clientStats: () => this.clientStats,\n                  serverStats: () => this.serverStats,\n                  edgeServerStats: () => this.edgeServerStats,\n                  rootDirectory: this.dir,\n                  distDir: this.distDir,\n                  config: this.config.experimental.browserDebugInfoInTerminal,\n                })\n              }\n              break\n            }\n            default: {\n              break\n            }\n          }\n\n          if (traceChild) {\n            this.hotReloaderSpan.manualTraceChild(\n              traceChild.name,\n              traceChild.startTime,\n              traceChild.endTime,\n              { ...traceChild.attrs, clientId: payload.id }\n            )\n          }\n        } catch (_) {\n          // invalid WebSocket message\n        }\n      })\n    })\n  }\n\n  private async clean(span: Span): Promise<void> {\n    return span\n      .traceChild('clean')\n      .traceAsyncFn(() =>\n        recursiveDelete(join(this.dir, this.config.distDir), /^cache/)\n      )\n  }\n\n  private async getWebpackConfig(span: Span) {\n    const webpackConfigSpan = span.traceChild('get-webpack-config')\n\n    const pageExtensions = this.config.pageExtensions\n\n    return webpackConfigSpan.traceAsyncFn(async () => {\n      const pagePaths = !this.pagesDir\n        ? ([] as (string | null)[])\n        : await webpackConfigSpan\n            .traceChild('get-page-paths')\n            .traceAsyncFn(() =>\n              Promise.all([\n                findPageFile(this.pagesDir!, '/_app', pageExtensions, false),\n                findPageFile(\n                  this.pagesDir!,\n                  '/_document',\n                  pageExtensions,\n                  false\n                ),\n              ])\n            )\n\n      this.pagesMapping = await webpackConfigSpan\n        .traceChild('create-pages-mapping')\n        .traceAsyncFn(() =>\n          createPagesMapping({\n            isDev: true,\n            pageExtensions: this.config.pageExtensions,\n            pagesType: PAGE_TYPES.PAGES,\n            pagePaths: pagePaths.filter(\n              (i: string | null): i is string => typeof i === 'string'\n            ),\n            pagesDir: this.pagesDir,\n            appDir: this.appDir,\n          })\n        )\n\n      const entrypoints = await webpackConfigSpan\n        .traceChild('create-entrypoints')\n        .traceAsyncFn(() =>\n          createEntrypoints({\n            appDir: this.appDir,\n            buildId: this.buildId,\n            config: this.config,\n            envFiles: [],\n            isDev: true,\n            pages: this.pagesMapping,\n            pagesDir: this.pagesDir,\n            previewMode: this.previewProps,\n            rootDir: this.dir,\n            pageExtensions: this.config.pageExtensions,\n          })\n        )\n\n      const commonWebpackOptions = {\n        dev: true,\n        buildId: this.buildId,\n        encryptionKey: this.encryptionKey,\n        config: this.config,\n        pagesDir: this.pagesDir,\n        rewrites: this.rewrites,\n        originalRewrites: this.config._originalRewrites,\n        originalRedirects: this.config._originalRedirects,\n        runWebpackSpan: this.hotReloaderSpan,\n        appDir: this.appDir,\n        previewProps: this.previewProps,\n      }\n\n      return webpackConfigSpan\n        .traceChild('generate-webpack-config')\n        .traceAsyncFn(async () => {\n          const info = await loadProjectInfo({\n            dir: this.dir,\n            config: commonWebpackOptions.config,\n            dev: true,\n          })\n          return Promise.all([\n            // order is important here\n            getBaseWebpackConfig(this.dir, {\n              ...commonWebpackOptions,\n              compilerType: COMPILER_NAMES.client,\n              entrypoints: entrypoints.client,\n              ...info,\n            }),\n            getBaseWebpackConfig(this.dir, {\n              ...commonWebpackOptions,\n              compilerType: COMPILER_NAMES.server,\n              entrypoints: entrypoints.server,\n              ...info,\n            }),\n            getBaseWebpackConfig(this.dir, {\n              ...commonWebpackOptions,\n              compilerType: COMPILER_NAMES.edgeServer,\n              entrypoints: entrypoints.edgeServer,\n              ...info,\n            }),\n          ])\n        })\n    })\n  }\n\n  public async buildFallbackError(): Promise<void> {\n    if (this.fallbackWatcher) return\n\n    const info = await loadProjectInfo({\n      dir: this.dir,\n      config: this.config,\n      dev: true,\n    })\n    const fallbackConfig = await getBaseWebpackConfig(this.dir, {\n      previewProps: this.previewProps,\n      runWebpackSpan: this.hotReloaderSpan,\n      dev: true,\n      compilerType: COMPILER_NAMES.client,\n      config: this.config,\n      buildId: this.buildId,\n      encryptionKey: this.encryptionKey,\n      appDir: this.appDir,\n      pagesDir: this.pagesDir,\n      rewrites: {\n        beforeFiles: [],\n        afterFiles: [],\n        fallback: [],\n      },\n      originalRewrites: {\n        beforeFiles: [],\n        afterFiles: [],\n        fallback: [],\n      },\n      originalRedirects: [],\n      isDevFallback: true,\n      entrypoints: (\n        await createEntrypoints({\n          appDir: this.appDir,\n          buildId: this.buildId,\n          config: this.config,\n          envFiles: [],\n          isDev: true,\n          pages: {\n            '/_app': 'next/dist/pages/_app',\n            '/_error': 'next/dist/pages/_error',\n          },\n          pagesDir: this.pagesDir,\n          previewMode: this.previewProps,\n          rootDir: this.dir,\n          pageExtensions: this.config.pageExtensions,\n        })\n      ).client,\n      ...info,\n    })\n\n    const fallbackCompiler = getWebpackBundler()(fallbackConfig)\n\n    this.fallbackWatcher = await new Promise((resolve) => {\n      let bootedFallbackCompiler = false\n      fallbackCompiler.watch(\n        // @ts-ignore webpack supports an array of watchOptions when using a multiCompiler\n        fallbackConfig.watchOptions,\n        // Errors are handled separately\n        (_err: any) => {\n          if (!bootedFallbackCompiler) {\n            bootedFallbackCompiler = true\n            resolve(true)\n          }\n        }\n      )\n    })\n  }\n\n  private async tracedGetVersionInfo(span: Span) {\n    const versionInfoSpan = span.traceChild('get-version-info')\n    return versionInfoSpan.traceAsyncFn<VersionInfo>(async () =>\n      getVersionInfo()\n    )\n  }\n\n  public async start(): Promise<void> {\n    const startSpan = this.hotReloaderSpan.traceChild('start')\n    startSpan.stop() // Stop immediately to create an artificial parent span\n\n    this.versionInfo = await this.tracedGetVersionInfo(startSpan)\n\n    const nodeDebugType = getNodeDebugType()\n    if (nodeDebugType && !this.devtoolsFrontendUrl) {\n      const debugPort = process.debugPort\n      let debugInfo\n      try {\n        // It requires to use 127.0.0.1 instead of localhost for server-side fetching.\n        const debugInfoList = await fetch(\n          `http://127.0.0.1:${debugPort}/json/list`\n        ).then((res) => res.json())\n        // There will be only one item for current process, so always get the first item.\n        debugInfo = debugInfoList[0]\n      } catch {}\n      if (debugInfo) {\n        this.devtoolsFrontendUrl = debugInfo.devtoolsFrontendUrl\n      }\n    }\n\n    await this.clean(startSpan)\n    // Ensure distDir exists before writing package.json\n    await fs.mkdir(this.distDir, { recursive: true })\n\n    const initialDevToolsConfig = await getDevToolsConfig(this.distDir)\n\n    const distPackageJsonPath = join(this.distDir, 'package.json')\n    // Ensure commonjs handling is used for files in the distDir (generally .next)\n    // Files outside of the distDir can be \"type\": \"module\"\n    await fs.writeFile(distPackageJsonPath, '{\"type\": \"commonjs\"}')\n\n    this.activeWebpackConfigs = await this.getWebpackConfig(startSpan)\n\n    for (const config of this.activeWebpackConfigs) {\n      const defaultEntry = config.entry\n      config.entry = async (...args) => {\n        const outputPath = this.multiCompiler?.outputPath || ''\n        const entries = getEntries(outputPath)\n        // @ts-ignore entry is always a function\n        const entrypoints = await defaultEntry(...args)\n        const isClientCompilation = config.name === COMPILER_NAMES.client\n        const isNodeServerCompilation = config.name === COMPILER_NAMES.server\n        const isEdgeServerCompilation =\n          config.name === COMPILER_NAMES.edgeServer\n\n        await Promise.all(\n          Object.keys(entries).map(async (entryKey) => {\n            const entryData = entries[entryKey]\n            const { bundlePath, dispose } = entryData\n\n            const result =\n              /^(client|server|edge-server)@(app|pages|root)@(.*)/g.exec(\n                entryKey\n              )\n            const [, key /* pageType */, , page] = result! // this match should always happen\n\n            if (key === COMPILER_NAMES.client && !isClientCompilation) return\n            if (key === COMPILER_NAMES.server && !isNodeServerCompilation)\n              return\n            if (key === COMPILER_NAMES.edgeServer && !isEdgeServerCompilation)\n              return\n\n            const isEntry = entryData.type === EntryTypes.ENTRY\n            const isChildEntry = entryData.type === EntryTypes.CHILD_ENTRY\n\n            // Check if the page was removed or disposed and remove it\n            if (isEntry) {\n              const pageExists =\n                !dispose && existsSync(entryData.absolutePagePath)\n              if (!pageExists) {\n                delete entries[entryKey]\n                return\n              }\n            }\n\n            // For child entries, if it has an entry file and it's gone, remove it\n            if (isChildEntry) {\n              if (entryData.absoluteEntryFilePath) {\n                const pageExists =\n                  !dispose && existsSync(entryData.absoluteEntryFilePath)\n                if (!pageExists) {\n                  delete entries[entryKey]\n                  return\n                }\n              }\n            }\n\n            // Ensure _error is considered a `pages` page.\n            if (page === '/_error') {\n              this.hasPagesRouterEntrypoints = true\n            }\n\n            const hasAppDir = !!this.appDir\n            const isAppPath = hasAppDir && bundlePath.startsWith('app/')\n            const staticInfo = isEntry\n              ? await getStaticInfoIncludingLayouts({\n                  isInsideAppDir: isAppPath,\n                  pageExtensions: this.config.pageExtensions,\n                  pageFilePath: entryData.absolutePagePath,\n                  appDir: this.appDir,\n                  config: this.config,\n                  isDev: true,\n                  page,\n                })\n              : undefined\n\n            if (staticInfo?.type === PAGE_TYPES.PAGES) {\n              if (\n                staticInfo.config?.config?.amp === true ||\n                staticInfo.config?.config?.amp === 'hybrid'\n              ) {\n                this.hasAmpEntrypoints = true\n              }\n            }\n\n            const isServerComponent =\n              isAppPath && staticInfo?.rsc !== RSC_MODULE_TYPES.client\n\n            const pageType: PAGE_TYPES = entryData.bundlePath.startsWith(\n              'pages/'\n            )\n              ? PAGE_TYPES.PAGES\n              : entryData.bundlePath.startsWith('app/')\n                ? PAGE_TYPES.APP\n                : PAGE_TYPES.ROOT\n\n            if (pageType === 'pages') {\n              this.hasPagesRouterEntrypoints = true\n            }\n            if (pageType === 'app') {\n              this.hasAppRouterEntrypoints = true\n            }\n\n            const isInstrumentation =\n              isInstrumentationHookFile(page) && pageType === PAGE_TYPES.ROOT\n\n            let pageRuntime = staticInfo?.runtime\n\n            runDependingOnPageType({\n              page,\n              pageRuntime,\n              pageType,\n              onEdgeServer: () => {\n                // TODO-APP: verify if child entry should support.\n                if (!isEdgeServerCompilation || !isEntry) return\n                entries[entryKey].status = BUILDING\n\n                if (isInstrumentation) {\n                  const normalizedBundlePath = bundlePath.replace('src/', '')\n                  entrypoints[normalizedBundlePath] = finalizeEntrypoint({\n                    compilerType: COMPILER_NAMES.edgeServer,\n                    name: normalizedBundlePath,\n                    value: getInstrumentationEntry({\n                      absolutePagePath: entryData.absolutePagePath,\n                      isEdgeServer: true,\n                      isDev: true,\n                    }),\n                    isServerComponent: true,\n                    hasAppDir,\n                  })\n                  return\n                }\n                const appDirLoader = isAppPath\n                  ? getAppEntry({\n                      name: bundlePath,\n                      page,\n                      appPaths: entryData.appPaths,\n                      pagePath: posix.join(\n                        APP_DIR_ALIAS,\n                        relative(\n                          this.appDir!,\n                          entryData.absolutePagePath\n                        ).replace(/\\\\/g, '/')\n                      ),\n                      appDir: this.appDir!,\n                      pageExtensions: this.config.pageExtensions,\n                      rootDir: this.dir,\n                      isDev: true,\n                      tsconfigPath: this.config.typescript.tsconfigPath,\n                      basePath: this.config.basePath,\n                      assetPrefix: this.config.assetPrefix,\n                      nextConfigOutput: this.config.output,\n                      preferredRegion: staticInfo?.preferredRegion,\n                      middlewareConfig: Buffer.from(\n                        JSON.stringify(staticInfo?.middleware || {})\n                      ).toString('base64'),\n                      isGlobalNotFoundEnabled: this.config.experimental\n                        .globalNotFound\n                        ? true\n                        : undefined,\n                    }).import\n                  : undefined\n\n                entrypoints[bundlePath] = finalizeEntrypoint({\n                  compilerType: COMPILER_NAMES.edgeServer,\n                  name: bundlePath,\n                  value: getEdgeServerEntry({\n                    absolutePagePath: entryData.absolutePagePath,\n                    rootDir: this.dir,\n                    buildId: this.buildId,\n                    bundlePath,\n                    config: this.config,\n                    isDev: true,\n                    page,\n                    pages: this.pagesMapping,\n                    isServerComponent,\n                    appDirLoader,\n                    pagesType: isAppPath ? PAGE_TYPES.APP : PAGE_TYPES.PAGES,\n                    preferredRegion: staticInfo?.preferredRegion,\n                  }),\n                  hasAppDir,\n                })\n              },\n              onClient: () => {\n                if (!isClientCompilation) return\n                if (isChildEntry) {\n                  entries[entryKey].status = BUILDING\n                  entrypoints[bundlePath] = finalizeEntrypoint({\n                    name: bundlePath,\n                    compilerType: COMPILER_NAMES.client,\n                    value: entryData.request,\n                    hasAppDir,\n                  })\n                } else {\n                  entries[entryKey].status = BUILDING\n                  entrypoints[bundlePath] = finalizeEntrypoint({\n                    name: bundlePath,\n                    compilerType: COMPILER_NAMES.client,\n                    value: getClientEntry({\n                      absolutePagePath: entryData.absolutePagePath,\n                      page,\n                    }),\n                    hasAppDir,\n                  })\n                }\n              },\n              onServer: () => {\n                // TODO-APP: verify if child entry should support.\n                if (!isNodeServerCompilation || !isEntry) return\n                entries[entryKey].status = BUILDING\n                let relativeRequest = relative(\n                  config.context!,\n                  entryData.absolutePagePath\n                )\n                if (\n                  !isAbsolute(relativeRequest) &&\n                  !relativeRequest.startsWith('../')\n                ) {\n                  relativeRequest = `./${relativeRequest}`\n                }\n\n                let value: { import: string; layer?: string } | string\n                if (isInstrumentation) {\n                  value = getInstrumentationEntry({\n                    absolutePagePath: entryData.absolutePagePath,\n                    isEdgeServer: false,\n                    isDev: true,\n                  })\n                  entrypoints[bundlePath] = finalizeEntrypoint({\n                    compilerType: COMPILER_NAMES.server,\n                    name: bundlePath,\n                    isServerComponent: true,\n                    value,\n                    hasAppDir,\n                  })\n                } else if (isMiddlewareFile(page)) {\n                  value = getEdgeServerEntry({\n                    absolutePagePath: entryData.absolutePagePath,\n                    rootDir: this.dir,\n                    buildId: this.buildId,\n                    bundlePath,\n                    config: this.config,\n                    isDev: true,\n                    page,\n                    pages: this.pagesMapping,\n                    isServerComponent,\n                    pagesType: PAGE_TYPES.PAGES,\n                    preferredRegion: staticInfo?.preferredRegion,\n                  })\n                } else if (isAppPath) {\n                  // This path normalization is critical for webpack to resolve the next internals as entry.\n                  const pagePath = entryData.absolutePagePath.startsWith(\n                    dirname(require.resolve('next/package.json'))\n                  )\n                    ? entryData.absolutePagePath\n                    : posix.join(\n                        APP_DIR_ALIAS,\n                        relative(\n                          this.appDir!,\n                          entryData.absolutePagePath\n                        ).replace(/\\\\/g, '/')\n                      )\n                  value = getAppEntry({\n                    name: bundlePath,\n                    page,\n                    appPaths: entryData.appPaths,\n                    pagePath,\n                    appDir: this.appDir!,\n                    pageExtensions: this.config.pageExtensions,\n                    rootDir: this.dir,\n                    isDev: true,\n                    tsconfigPath: this.config.typescript.tsconfigPath,\n                    basePath: this.config.basePath,\n                    assetPrefix: this.config.assetPrefix,\n                    nextConfigOutput: this.config.output,\n                    preferredRegion: staticInfo?.preferredRegion,\n                    middlewareConfig: Buffer.from(\n                      JSON.stringify(staticInfo?.middleware || {})\n                    ).toString('base64'),\n                    isGlobalNotFoundEnabled: this.config.experimental\n                      .globalNotFound\n                      ? true\n                      : undefined,\n                  })\n                } else if (isAPIRoute(page)) {\n                  value = getRouteLoaderEntry({\n                    kind: RouteKind.PAGES_API,\n                    page,\n                    absolutePagePath: relativeRequest,\n                    preferredRegion: staticInfo?.preferredRegion,\n                    middlewareConfig: staticInfo?.middleware || {},\n                  })\n                } else if (\n                  !isMiddlewareFile(page) &&\n                  !isInternalComponent(relativeRequest) &&\n                  !isNonRoutePagesPage(page) &&\n                  !isInstrumentation\n                ) {\n                  value = getRouteLoaderEntry({\n                    kind: RouteKind.PAGES,\n                    page,\n                    pages: this.pagesMapping,\n                    absolutePagePath: relativeRequest,\n                    preferredRegion: staticInfo?.preferredRegion,\n                    middlewareConfig: staticInfo?.middleware ?? {},\n                  })\n                } else {\n                  value = relativeRequest\n                }\n\n                entrypoints[bundlePath] = finalizeEntrypoint({\n                  compilerType: COMPILER_NAMES.server,\n                  name: bundlePath,\n                  isServerComponent,\n                  value,\n                  hasAppDir,\n                })\n              },\n            })\n          })\n        )\n\n        if (!this.hasAmpEntrypoints) {\n          delete entrypoints[CLIENT_STATIC_FILES_RUNTIME_AMP]\n        }\n        if (!this.hasPagesRouterEntrypoints) {\n          delete entrypoints[CLIENT_STATIC_FILES_RUNTIME_MAIN]\n          delete entrypoints['pages/_app']\n          delete entrypoints['pages/_error']\n          delete entrypoints['/_error']\n          delete entrypoints['pages/_document']\n        }\n        // Remove React Refresh entrypoint chunk as `app` doesn't require it.\n        if (!this.hasAmpEntrypoints && !this.hasPagesRouterEntrypoints) {\n          delete entrypoints[CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH]\n        }\n        if (!this.hasAppRouterEntrypoints) {\n          delete entrypoints[CLIENT_STATIC_FILES_RUNTIME_MAIN_APP]\n        }\n\n        return entrypoints\n      }\n    }\n\n    // Enable building of client compilation before server compilation in development\n    // @ts-ignore webpack 5\n    this.activeWebpackConfigs.parallelism = 1\n\n    await Promise.all(\n      Array.from(getCacheDirectories(this.activeWebpackConfigs)).map(\n        checkPersistentCacheInvalidationAndCleanup\n      )\n    )\n    this.multiCompiler = getWebpackBundler()(\n      this.activeWebpackConfigs\n    ) as unknown as webpack.MultiCompiler\n\n    // Copy over the filesystem so that it is shared between all compilers.\n    const inputFileSystem = this.multiCompiler.compilers[0].inputFileSystem\n    for (const compiler of this.multiCompiler.compilers) {\n      compiler.inputFileSystem = inputFileSystem\n      // This is set for the initial compile. After that Watching class in webpack adds it.\n      compiler.fsStartTime = Date.now()\n      // Ensure NodeEnvironmentPlugin doesn't purge the inputFileSystem. Purging is handled in `done` below.\n      compiler.hooks.beforeRun.intercept({\n        register(tapInfo: any) {\n          if (tapInfo.name === 'NodeEnvironmentPlugin') {\n            return null\n          }\n          return tapInfo\n        },\n      })\n    }\n\n    this.multiCompiler.hooks.done.tap('NextjsHotReloader', () => {\n      inputFileSystem?.purge?.()\n    })\n    watchCompilers(\n      this.multiCompiler.compilers[0],\n      this.multiCompiler.compilers[1],\n      this.multiCompiler.compilers[2]\n    )\n\n    // Watch for changes to client/server page files so we can tell when just\n    // the server file changes and trigger a reload for GS(S)P pages\n    const changedClientPages = new Set<string>()\n    const changedServerPages = new Set<string>()\n    const changedEdgeServerPages = new Set<string>()\n\n    const changedServerComponentPages = new Set<string>()\n    const changedCSSImportPages = new Set<string>()\n\n    const prevClientPageHashes = new Map<string, string>()\n    const prevServerPageHashes = new Map<string, string>()\n    const prevEdgeServerPageHashes = new Map<string, string>()\n    const prevCSSImportModuleHashes = new Map<string, string>()\n\n    const pageExtensionRegex = new RegExp(\n      `\\\\.(?:${this.config.pageExtensions.join('|')})$`\n    )\n\n    const trackPageChanges =\n      (\n        pageHashMap: Map<string, string>,\n        changedItems: Set<string>,\n        serverComponentChangedItems?: Set<string>\n      ) =>\n      (stats: webpack.Compilation) => {\n        try {\n          stats.entrypoints.forEach((entry, key) => {\n            if (\n              key.startsWith('pages/') ||\n              key.startsWith('app/') ||\n              isMiddlewareFilename(key)\n            ) {\n              // TODO this doesn't handle on demand loaded chunks\n              entry.chunks.forEach((chunk) => {\n                if (chunk.id === key) {\n                  const modsIterable: any =\n                    stats.chunkGraph.getChunkModulesIterable(chunk)\n\n                  let hasCSSModuleChanges = false\n                  let chunksHash = new StringXor()\n                  let chunksHashServerLayer = new StringXor()\n\n                  modsIterable.forEach((mod: any) => {\n                    if (\n                      mod.resource &&\n                      mod.resource.replace(/\\\\/g, '/').includes(key) &&\n                      // Shouldn't match CSS modules, etc.\n                      pageExtensionRegex.test(mod.resource)\n                    ) {\n                      // use original source to calculate hash since mod.hash\n                      // includes the source map in development which changes\n                      // every time for both server and client so we calculate\n                      // the hash without the source map for the page module\n                      const hash = (\n                        require('crypto') as typeof import('crypto')\n                      )\n                        .createHash('sha1')\n                        .update(mod.originalSource().buffer())\n                        .digest()\n                        .toString('hex')\n\n                      if (\n                        mod.layer === WEBPACK_LAYERS.reactServerComponents &&\n                        mod?.buildInfo?.rsc?.type !== 'client'\n                      ) {\n                        chunksHashServerLayer.add(hash)\n                      }\n\n                      chunksHash.add(hash)\n                    } else {\n                      // for non-pages we can use the module hash directly\n                      const hash = stats.chunkGraph.getModuleHash(\n                        mod,\n                        chunk.runtime\n                      )\n\n                      if (\n                        mod.layer === WEBPACK_LAYERS.reactServerComponents &&\n                        mod?.buildInfo?.rsc?.type !== 'client'\n                      ) {\n                        chunksHashServerLayer.add(hash)\n                      }\n\n                      chunksHash.add(hash)\n\n                      // Both CSS import changes from server and client\n                      // components are tracked.\n                      if (\n                        key.startsWith('app/') &&\n                        /\\.(css|scss|sass)$/.test(mod.resource || '')\n                      ) {\n                        const resourceKey = mod.layer + ':' + mod.resource\n                        const prevHash =\n                          prevCSSImportModuleHashes.get(resourceKey)\n                        if (prevHash && prevHash !== hash) {\n                          hasCSSModuleChanges = true\n                        }\n                        prevCSSImportModuleHashes.set(resourceKey, hash)\n                      }\n                    }\n                  })\n\n                  const prevHash = pageHashMap.get(key)\n                  const curHash = chunksHash.toString()\n                  if (prevHash && prevHash !== curHash) {\n                    changedItems.add(key)\n                  }\n                  pageHashMap.set(key, curHash)\n\n                  if (serverComponentChangedItems) {\n                    const serverKey =\n                      WEBPACK_LAYERS.reactServerComponents + ':' + key\n                    const prevServerHash = pageHashMap.get(serverKey)\n                    const curServerHash = chunksHashServerLayer.toString()\n                    if (prevServerHash && prevServerHash !== curServerHash) {\n                      serverComponentChangedItems.add(key)\n                    }\n                    pageHashMap.set(serverKey, curServerHash)\n                  }\n\n                  if (hasCSSModuleChanges) {\n                    changedCSSImportPages.add(key)\n                  }\n                }\n              })\n            }\n          })\n        } catch (err) {\n          console.error(err)\n        }\n      }\n\n    this.multiCompiler.compilers[0].hooks.emit.tap(\n      'NextjsHotReloaderForClient',\n      trackPageChanges(prevClientPageHashes, changedClientPages)\n    )\n    this.multiCompiler.compilers[1].hooks.emit.tap(\n      'NextjsHotReloaderForServer',\n      trackPageChanges(\n        prevServerPageHashes,\n        changedServerPages,\n        changedServerComponentPages\n      )\n    )\n    this.multiCompiler.compilers[2].hooks.emit.tap(\n      'NextjsHotReloaderForServer',\n      trackPageChanges(\n        prevEdgeServerPageHashes,\n        changedEdgeServerPages,\n        changedServerComponentPages\n      )\n    )\n\n    // This plugin watches for changes to _document.js and notifies the client side that it should reload the page\n    this.multiCompiler.compilers[1].hooks.failed.tap(\n      'NextjsHotReloaderForServer',\n      (err: Error) => {\n        this.serverError = err\n        this.serverStats = null\n        this.serverChunkNames = undefined\n      }\n    )\n\n    this.multiCompiler.compilers[2].hooks.done.tap(\n      'NextjsHotReloaderForServer',\n      (stats) => {\n        this.serverError = null\n        this.edgeServerStats = stats\n      }\n    )\n\n    this.multiCompiler.compilers[1].hooks.done.tap(\n      'NextjsHotReloaderForServer',\n      (stats) => {\n        this.serverError = null\n        this.serverStats = stats\n\n        if (!this.pagesDir) {\n          return\n        }\n\n        const { compilation } = stats\n\n        // We only watch `_document` for changes on the server compilation\n        // the rest of the files will be triggered by the client compilation\n        const documentChunk = compilation.namedChunks.get('pages/_document')\n        // If the document chunk can't be found we do nothing\n        if (!documentChunk) {\n          return\n        }\n\n        // Initial value\n        if (this.serverPrevDocumentHash === null) {\n          this.serverPrevDocumentHash = documentChunk.hash || null\n          return\n        }\n\n        // If _document.js didn't change we don't trigger a reload.\n        if (documentChunk.hash === this.serverPrevDocumentHash) {\n          return\n        }\n\n        // As document chunk will change if new app pages are joined,\n        // since react bundle is different it will effect the chunk hash.\n        // So we diff the chunk changes, if there's only new app page chunk joins,\n        // then we don't trigger a reload by checking pages/_document chunk change.\n        if (this.appDir) {\n          const chunkNames = new Set(compilation.namedChunks.keys())\n          const diffChunkNames = difference<string>(\n            this.serverChunkNames || new Set(),\n            chunkNames\n          )\n\n          if (\n            diffChunkNames.length === 0 ||\n            diffChunkNames.every((chunkName) => chunkName.startsWith('app/'))\n          ) {\n            return\n          }\n          this.serverChunkNames = chunkNames\n        }\n\n        this.serverPrevDocumentHash = documentChunk.hash || null\n\n        // Notify reload to reload the page, as _document.js was changed (different hash)\n        this.send({\n          action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n          data: '_document has changed',\n        })\n      }\n    )\n\n    this.multiCompiler.hooks.done.tap('NextjsHotReloaderForServer', (stats) => {\n      const reloadAfterInvalidation = this.reloadAfterInvalidation\n      this.reloadAfterInvalidation = false\n\n      const serverOnlyChanges = difference<string>(\n        changedServerPages,\n        changedClientPages\n      )\n\n      const edgeServerOnlyChanges = difference<string>(\n        changedEdgeServerPages,\n        changedClientPages\n      )\n\n      const pageChanges = serverOnlyChanges\n        .concat(edgeServerOnlyChanges)\n        .filter((key) => key.startsWith('pages/'))\n\n      const middlewareChanges = [\n        ...Array.from(changedEdgeServerPages),\n        ...Array.from(changedServerPages),\n      ].filter((name) => isMiddlewareFilename(name))\n\n      if (middlewareChanges.length > 0) {\n        this.send({\n          event: HMR_ACTIONS_SENT_TO_BROWSER.MIDDLEWARE_CHANGES,\n        })\n      }\n\n      if (pageChanges.length > 0) {\n        this.send({\n          event: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ONLY_CHANGES,\n          pages: serverOnlyChanges.map((pg) =>\n            denormalizePagePath(pg.slice('pages'.length))\n          ),\n        })\n      }\n\n      if (\n        changedServerComponentPages.size ||\n        changedCSSImportPages.size ||\n        reloadAfterInvalidation\n      ) {\n        this.resetFetch()\n        this.refreshServerComponents(stats.hash)\n      }\n\n      changedClientPages.clear()\n      changedServerPages.clear()\n      changedEdgeServerPages.clear()\n      changedServerComponentPages.clear()\n      changedCSSImportPages.clear()\n    })\n\n    this.multiCompiler.compilers[0].hooks.failed.tap(\n      'NextjsHotReloaderForClient',\n      (err: Error) => {\n        this.clientError = err\n        this.clientStats = null\n      }\n    )\n    this.multiCompiler.compilers[0].hooks.done.tap(\n      'NextjsHotReloaderForClient',\n      (stats) => {\n        this.clientError = null\n        this.clientStats = stats\n\n        const { compilation } = stats\n        const chunkNames = new Set(\n          [...compilation.namedChunks.keys()].filter(\n            (name) => !!getRouteFromEntrypoint(name)\n          )\n        )\n\n        if (this.prevChunkNames) {\n          // detect chunks which have to be replaced with a new template\n          // e.g, pages/index.js <-> pages/_error.js\n          const addedPages = diff(chunkNames, this.prevChunkNames!)\n          const removedPages = diff(this.prevChunkNames!, chunkNames)\n\n          if (addedPages.size > 0) {\n            for (const addedPage of addedPages) {\n              const page = getRouteFromEntrypoint(addedPage)\n              this.send({\n                action: HMR_ACTIONS_SENT_TO_BROWSER.ADDED_PAGE,\n                data: [page],\n              })\n            }\n          }\n\n          if (removedPages.size > 0) {\n            for (const removedPage of removedPages) {\n              const page = getRouteFromEntrypoint(removedPage)\n              this.send({\n                action: HMR_ACTIONS_SENT_TO_BROWSER.REMOVED_PAGE,\n                data: [page],\n              })\n            }\n          }\n        }\n\n        this.prevChunkNames = chunkNames\n      }\n    )\n\n    this.webpackHotMiddleware = new WebpackHotMiddleware(\n      this.multiCompiler.compilers,\n      this.versionInfo,\n      this.devtoolsFrontendUrl,\n      initialDevToolsConfig\n    )\n\n    let booted = false\n\n    this.watcher = await new Promise((resolve) => {\n      const watcher = this.multiCompiler?.watch(\n        // @ts-ignore webpack supports an array of watchOptions when using a multiCompiler\n        this.activeWebpackConfigs.map((config) => config.watchOptions!),\n        // Errors are handled separately\n        (_err: any) => {\n          if (!booted) {\n            booted = true\n            resolve(watcher)\n          }\n        }\n      )\n    })\n\n    this.onDemandEntries = onDemandEntryHandler({\n      hotReloader: this,\n      multiCompiler: this.multiCompiler,\n      pagesDir: this.pagesDir,\n      appDir: this.appDir,\n      rootDir: this.dir,\n      nextConfig: this.config,\n      ...(this.config.onDemandEntries as {\n        maxInactiveAge: number\n        pagesBufferLength: number\n      }),\n    })\n\n    this.middlewares = [\n      getOverlayMiddleware({\n        rootDirectory: this.dir,\n        isSrcDir: this.isSrcDir,\n        clientStats: () => this.clientStats,\n        serverStats: () => this.serverStats,\n        edgeServerStats: () => this.edgeServerStats,\n      }),\n      getSourceMapMiddleware({\n        clientStats: () => this.clientStats,\n        serverStats: () => this.serverStats,\n        edgeServerStats: () => this.edgeServerStats,\n      }),\n      getNextErrorFeedbackMiddleware(this.telemetry),\n      getDevOverlayFontMiddleware(),\n      getDisableDevIndicatorMiddleware(),\n      getRestartDevServerMiddleware({\n        telemetry: this.telemetry,\n        webpackCacheDirectories:\n          this.activeWebpackConfigs != null\n            ? getCacheDirectories(this.activeWebpackConfigs)\n            : undefined,\n      }),\n      devToolsConfigMiddleware({\n        distDir: this.distDir,\n        sendUpdateSignal: (data) => {\n          // Update the in-memory devToolsConfig value\n          // which will be used for the next onHMR call.\n          this.webpackHotMiddleware?.updateDevToolsConfig(data)\n\n          this.send({\n            action: HMR_ACTIONS_SENT_TO_BROWSER.DEVTOOLS_CONFIG,\n            data,\n          })\n        },\n      }),\n    ]\n  }\n\n  public invalidate(\n    { reloadAfterInvalidation }: { reloadAfterInvalidation: boolean } = {\n      reloadAfterInvalidation: false,\n    }\n  ) {\n    // Cache the `reloadAfterInvalidation` flag, and use it to reload the page when compilation is done\n    this.reloadAfterInvalidation = reloadAfterInvalidation\n    const outputPath = this.multiCompiler?.outputPath\n    if (outputPath) {\n      getInvalidator(outputPath)?.invalidate()\n    }\n  }\n\n  public async getCompilationErrors(page: string) {\n    const getErrors = ({ compilation }: webpack.Stats) => {\n      const failedPages = erroredPages(compilation)\n      const normalizedPage = normalizePathSep(page)\n      // If there is an error related to the requesting page we display it instead of the first error\n      return failedPages[normalizedPage]?.length > 0\n        ? failedPages[normalizedPage]\n        : compilation.errors\n    }\n\n    if (this.clientError) {\n      return [this.clientError]\n    } else if (this.serverError) {\n      return [this.serverError]\n    } else if (this.clientStats?.hasErrors()) {\n      return getErrors(this.clientStats)\n    } else if (this.serverStats?.hasErrors()) {\n      return getErrors(this.serverStats)\n    } else if (this.edgeServerStats?.hasErrors()) {\n      return getErrors(this.edgeServerStats)\n    } else {\n      return []\n    }\n  }\n\n  public send(action: HMR_ACTION_TYPES): void {\n    this.webpackHotMiddleware!.publish(action)\n  }\n\n  public async ensurePage({\n    page,\n    clientOnly,\n    appPaths,\n    definition,\n    isApp,\n    url,\n  }: {\n    page: string\n    clientOnly: boolean\n    appPaths?: ReadonlyArray<string> | null\n    isApp?: boolean\n    definition?: RouteDefinition\n    url?: string\n  }): Promise<void> {\n    return this.hotReloaderSpan\n      .traceChild('ensure-page', {\n        inputPage: page,\n      })\n      .traceAsyncFn(async () => {\n        // Make sure we don't re-build or dispose prebuilt pages\n        if (page !== '/_error' && BLOCKED_PAGES.indexOf(page) !== -1) {\n          return\n        }\n        const error = clientOnly\n          ? this.clientError\n          : this.serverError || this.clientError\n        if (error) {\n          throw error\n        }\n\n        return this.onDemandEntries?.ensurePage({\n          page,\n          appPaths,\n          definition,\n          isApp,\n          url,\n        })\n      })\n  }\n\n  public close() {\n    this.webpackHotMiddleware?.close()\n  }\n}\n"], "names": ["HotReloaderWebpack", "getVersionInfo", "matchNextPageBundleRequest", "renderScriptError", "MILLISECONDS_IN_NANOSECOND", "BigInt", "diff", "a", "b", "Set", "filter", "v", "has", "wsServer", "ws", "Server", "noServer", "res", "error", "verbose", "<PERSON><PERSON><PERSON><PERSON>", "code", "finished", "undefined", "console", "stack", "statusCode", "end", "addCorsSupport", "req", "url", "startsWith", "preflight", "headers", "origin", "method", "writeHead", "getPathMatch", "findEntryModule", "module", "compilation", "issuer", "moduleGraph", "get<PERSON><PERSON><PERSON>", "erroredPages", "failedPages", "errors", "entryModule", "name", "enhancedName", "getRouteFromEntrypoint", "push", "installed", "require", "version", "fetch", "ok", "staleness", "latest", "canary", "json", "parseVersionInfo", "e", "constructor", "dir", "config", "isSrcDir", "pagesDir", "distDir", "buildId", "<PERSON><PERSON><PERSON>", "previewProps", "rewrites", "appDir", "telemetry", "resetFetch", "clientError", "serverError", "hmrServerError", "pagesMapping", "versionInfo", "reloadAfterInvalidation", "hasAmpEntrypoints", "hasAppRouterEntrypoints", "hasPagesRouterEntrypoints", "middlewares", "clientStats", "serverStats", "edgeServerStats", "serverPrevDocumentHash", "hotReloaderSpan", "trace", "process", "env", "__NEXT_VERSION", "stop", "run", "parsedUrl", "handlePageBundleRequest", "pageBundleRes", "parsedPageBundleUrl", "pathname", "params", "decodedPagePath", "path", "map", "param", "decodeURIComponent", "join", "_", "DecodeError", "page", "denormalizePagePath", "BLOCKED_PAGES", "indexOf", "ensurePage", "clientOnly", "getProperError", "getCompilationErrors", "length", "middleware", "calledNext", "setHmrServerError", "clearHmrServerError", "send", "action", "HMR_ACTIONS_SENT_TO_BROWSER", "RELOAD_PAGE", "data", "refreshServerComponents", "hash", "SERVER_COMPONENT_CHANGES", "onHMR", "_socket", "head", "callback", "handleUpgrade", "socket", "client", "webpackHotMiddleware", "onDemandEntries", "addEventListener", "toString", "payload", "JSON", "parse", "<PERSON><PERSON><PERSON><PERSON>", "event", "spanName", "startTime", "Math", "floor", "attrs", "attributes", "endTime", "updatedModules", "m", "replace", "WEBPACK_LAYERS", "appPagesBrowser", "isPageHidden", "errorCount", "warningCount", "stackTrace", "hadRuntimeError", "Log", "warn", "FAST_REFRESH_RUNTIME_RELOAD", "fileMessage", "file", "exec", "fileUrl", "URL", "cwd", "modules", "searchParams", "getAll", "filepath", "slice", "pagesDirBrowser", "cleanedFile<PERSON>ath", "experimental", "browserDebugInfoInTerminal", "receiveBrowserLogsWebpack", "entries", "router", "sourceType", "rootDirectory", "manualTraceChild", "clientId", "id", "clean", "span", "traceAsyncFn", "recursiveDelete", "getWebpackConfig", "webpackConfigSpan", "pageExtensions", "pagePaths", "Promise", "all", "findPageFile", "createPagesMapping", "isDev", "pagesType", "PAGE_TYPES", "PAGES", "i", "entrypoints", "createEntrypoints", "envFiles", "pages", "previewMode", "rootDir", "commonWebpackOptions", "dev", "originalRewrites", "_originalRewrites", "originalRedirects", "_originalRedirects", "runWebpackSpan", "info", "loadProjectInfo", "getBaseWebpackConfig", "compilerType", "COMPILER_NAMES", "server", "edgeServer", "buildFallbackError", "fallback<PERSON><PERSON><PERSON>", "fallbackConfig", "beforeFiles", "afterFiles", "fallback", "isDev<PERSON><PERSON><PERSON>", "fallbackCompiler", "getWebpackBundler", "resolve", "bootedFallbackCompiler", "watch", "watchOptions", "_err", "tracedGetVersionInfo", "versionInfoSpan", "start", "startSpan", "nodeDebugType", "getNodeDebugType", "devtoolsFrontendUrl", "debugPort", "debugInfo", "debugInfoList", "then", "fs", "mkdir", "recursive", "initialDevToolsConfig", "getDevToolsConfig", "distPackageJsonPath", "writeFile", "activeWebpackConfigs", "defaultEntry", "entry", "args", "outputPath", "multiCompiler", "getEntries", "isClientCompilation", "isNodeServerCompilation", "isEdgeServerCompilation", "Object", "keys", "<PERSON><PERSON><PERSON>", "entryData", "bundlePath", "dispose", "result", "key", "isEntry", "type", "EntryTypes", "ENTRY", "isChildEntry", "CHILD_ENTRY", "pageExists", "existsSync", "absolutePagePath", "absoluteEntryFilePath", "hasAppDir", "isAppPath", "staticInfo", "getStaticInfoIncludingLayouts", "isInsideAppDir", "pageFilePath", "amp", "isServerComponent", "rsc", "RSC_MODULE_TYPES", "pageType", "APP", "ROOT", "isInstrumentation", "isInstrumentationHookFile", "pageRuntime", "runtime", "runDependingOnPageType", "onEdgeServer", "status", "BUILDING", "normalizedBundlePath", "finalizeEntrypoint", "value", "getInstrumentationEntry", "isEdgeServer", "appDirLoader", "getAppEntry", "appPaths", "pagePath", "posix", "APP_DIR_ALIAS", "relative", "tsconfigPath", "typescript", "basePath", "assetPrefix", "nextConfigOutput", "output", "preferredRegion", "middlewareConfig", "<PERSON><PERSON><PERSON>", "from", "stringify", "isGlobalNotFoundEnabled", "globalNotFound", "import", "getEdgeServerEntry", "onClient", "request", "getClientEntry", "onServer", "relativeRequest", "context", "isAbsolute", "isMiddlewareFile", "dirname", "isAPIRoute", "getRouteLoaderEntry", "kind", "RouteKind", "PAGES_API", "isInternalComponent", "isNonRoutePagesPage", "CLIENT_STATIC_FILES_RUNTIME_AMP", "CLIENT_STATIC_FILES_RUNTIME_MAIN", "CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "parallelism", "Array", "getCacheDirectories", "checkPersistentCacheInvalidationAndCleanup", "inputFileSystem", "compilers", "compiler", "fsStartTime", "Date", "now", "hooks", "beforeRun", "intercept", "register", "tapInfo", "done", "tap", "purge", "watchCompilers", "changedClientPages", "changedServerPages", "changedEdgeServerPages", "changedServerComponentPages", "changedCSSImportPages", "prevClientPageHashes", "Map", "prevServerPageHashes", "prevEdgeServerPageHashes", "prevCSSImportModuleHashes", "pageExtensionRegex", "RegExp", "trackPageChanges", "pageHashMap", "changedItems", "serverComponentChangedItems", "stats", "for<PERSON>ach", "isMiddlewareFilename", "chunks", "chunk", "modsIterable", "chunkGraph", "getChunkModulesIterable", "hasCSSModuleChanges", "chunksHash", "StringXor", "chunksHashServerLayer", "mod", "resource", "includes", "test", "createHash", "update", "originalSource", "buffer", "digest", "layer", "reactServerComponents", "buildInfo", "add", "getModuleHash", "resourceKey", "prevHash", "get", "set", "curHash", "server<PERSON>ey", "prevServerHash", "curServerHash", "err", "emit", "failed", "serverChunkNames", "documentChunk", "namedChunks", "chunkNames", "diffChunkNames", "difference", "every", "chunkName", "serverOnlyChanges", "edgeServerOnlyChanges", "pageChanges", "concat", "middlewareChanges", "MIDDLEWARE_CHANGES", "SERVER_ONLY_CHANGES", "pg", "size", "clear", "prevChunkNames", "addedPages", "removedPages", "addedPage", "ADDED_PAGE", "removedPage", "REMOVED_PAGE", "WebpackHotMiddleware", "booted", "watcher", "onDemandEntryHandler", "hotReloader", "nextConfig", "getOverlayMiddleware", "getSourceMapMiddleware", "getNextErrorFeedbackMiddleware", "getDevOverlayFontMiddleware", "getDisableDevIndicatorMiddleware", "getRestartDevServerMiddleware", "webpackCacheDirectories", "devToolsConfigMiddleware", "sendUpdateSignal", "updateDevToolsConfig", "DEVTOOLS_CONFIG", "invalidate", "getInvalidator", "getErrors", "normalizedPage", "normalizePathSep", "hasErrors", "publish", "definition", "isApp", "inputPage", "close"], "mappings": ";;;;;;;;;;;;;;;;;IAsOA,OAs8CC;eAt8CoBA;;IA1BCC,cAAc;eAAdA;;IA9CTC,0BAA0B;eAA1BA;;IApDSC,iBAAiB;eAAjBA;;;yBAlGkB;mCAIjC;+BAC8B;sBACsB;yBAWpD;wBACwB;6DACV;uEAId;2BACuC;iCACd;4BASzB;2BAEsB;8BACA;sCAOtB;qCAC6B;kCACH;+EACE;uBAM5B;wBACqB;uBACK;yBACF;2DAChB;oBAC4B;kCAEV;4BAEN;iCACS;qCAI7B;2BACmB;kCAInB;2BAGoB;0BACiB;wBACX;gDACc;6CACH;wCACK;0EACnB;4CACgB;mCACa;6BACjB;0CAInC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEP,MAAMC,6BAA6BC,OAAO;AAE1C,SAASC,KAAKC,CAAW,EAAEC,CAAW;IACpC,OAAO,IAAIC,IAAI;WAAIF;KAAE,CAACG,MAAM,CAAC,CAACC,IAAM,CAACH,EAAEI,GAAG,CAACD;AAC7C;AAEA,MAAME,WAAW,IAAIC,WAAE,CAACC,MAAM,CAAC;IAAEC,UAAU;AAAK;AAEzC,eAAeb,kBACpBc,GAAmB,EACnBC,KAAY,EACZ,EAAEC,UAAU,IAAI,EAAE,GAAG,CAAC,CAAC;IAEvB,wDAAwD;IACxDF,IAAIG,SAAS,CACX,iBACA;IAGF,IAAI,AAACF,MAAcG,IAAI,KAAK,UAAU;QACpC,OAAO;YAAEC,UAAUC;QAAU;IAC/B;IAEA,IAAIJ,SAAS;QACXK,QAAQN,KAAK,CAACA,MAAMO,KAAK;IAC3B;IACAR,IAAIS,UAAU,GAAG;IACjBT,IAAIU,GAAG,CAAC;IACR,OAAO;QAAEL,UAAU;IAAK;AAC1B;AAEA,SAASM,eAAeC,GAAoB,EAAEZ,GAAmB;IAC/D,wEAAwE;IACxE,IAAI,CAACY,IAAIC,GAAG,CAAEC,UAAU,CAAC,YAAY;QACnC,OAAO;YAAEC,WAAW;QAAM;IAC5B;IAEA,IAAI,CAACH,IAAII,OAAO,CAACC,MAAM,EAAE;QACvB,OAAO;YAAEF,WAAW;QAAM;IAC5B;IAEAf,IAAIG,SAAS,CAAC,+BAA+BS,IAAII,OAAO,CAACC,MAAM;IAC/DjB,IAAIG,SAAS,CAAC,gCAAgC;IAC9C,gHAAgH;IAChH,IAAIS,IAAII,OAAO,CAAC,iCAAiC,EAAE;QACjDhB,IAAIG,SAAS,CACX,gCACAS,IAAII,OAAO,CAAC,iCAAiC;IAEjD;IAEA,IAAIJ,IAAIM,MAAM,KAAK,WAAW;QAC5BlB,IAAImB,SAAS,CAAC;QACdnB,IAAIU,GAAG;QACP,OAAO;YAAEK,WAAW;QAAK;IAC3B;IAEA,OAAO;QAAEA,WAAW;IAAM;AAC5B;AAEO,MAAM9B,6BAA6BmC,IAAAA,uBAAY,EACpD;AAGF,6DAA6D;AAC7D,SAASC,gBACPC,OAAsB,EACtBC,WAAgC;IAEhC,OAAS;QACP,MAAMC,SAASD,YAAYE,WAAW,CAACC,SAAS,CAACJ;QACjD,IAAI,CAACE,QAAQ,OAAOF;QACpBA,UAASE;IACX;AACF;AAEA,SAASG,aAAaJ,WAAgC;IACpD,MAAMK,cAAkD,CAAC;IACzD,KAAK,MAAM3B,SAASsB,YAAYM,MAAM,CAAE;QACtC,IAAI,CAAC5B,MAAMqB,MAAM,EAAE;YACjB;QACF;QAEA,MAAMQ,cAAcT,gBAAgBpB,MAAMqB,MAAM,EAAEC;QAClD,MAAM,EAAEQ,IAAI,EAAE,GAAGD;QACjB,IAAI,CAACC,MAAM;YACT;QACF;QAEA,iCAAiC;QACjC,MAAMC,eAAeC,IAAAA,+BAAsB,EAACF;QAE5C,IAAI,CAACC,cAAc;YACjB;QACF;QAEA,IAAI,CAACJ,WAAW,CAACI,aAAa,EAAE;YAC9BJ,WAAW,CAACI,aAAa,GAAG,EAAE;QAChC;QAEAJ,WAAW,CAACI,aAAa,CAACE,IAAI,CAACjC;IACjC;IAEA,OAAO2B;AACT;AAEO,eAAe5C;IACpB,IAAImD,YAAY;IAEhB,IAAI;QACFA,YAAYC,QAAQ,qBAAqBC,OAAO;QAEhD,IAAIrC;QAEJ,IAAI;YACF,8CAA8C;YAC9CA,MAAM,MAAMsC,MAAM;QACpB,EAAE,OAAM;QACN,sBAAsB;QACxB;QAEA,IAAI,CAACtC,OAAO,CAACA,IAAIuC,EAAE,EAAE,OAAO;YAAEJ;YAAWK,WAAW;QAAU;QAE9D,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAE,GAAG,MAAM1C,IAAI2C,IAAI;QAEzC,OAAOC,IAAAA,kCAAgB,EAAC;YAAET;YAAWM;YAAQC;QAAO;IACtD,EAAE,OAAOG,GAAQ;QACftC,QAAQN,KAAK,CAAC4C;QACd,OAAO;YAAEV;YAAWK,WAAW;QAAU;IAC3C;AACF;AAEe,MAAMzD;IAgDnB+D,YACEC,GAAW,EACX,EACEC,MAAM,EACNC,QAAQ,EACRC,QAAQ,EACRC,OAAO,EACPC,OAAO,EACPC,aAAa,EACbC,YAAY,EACZC,QAAQ,EACRC,MAAM,EACNC,SAAS,EACTC,UAAU,EAaX,CACD;aA1DMC,cAA4B;aAC5BC,cAA4B;aAC5BC,iBAA+B;aAU/BC,eAA0C,CAAC;aAI3CC,cAA2B;YACjCvB,WAAW;YACXL,WAAW;QACb;aAEQ6B,0BAAmC;QAsCzC,IAAI,CAACC,iBAAiB,GAAG;QACzB,IAAI,CAACC,uBAAuB,GAAG;QAC/B,IAAI,CAACC,yBAAyB,GAAG;QACjC,IAAI,CAACf,OAAO,GAAGA;QACf,IAAI,CAACC,aAAa,GAAGA;QACrB,IAAI,CAACN,GAAG,GAAGA;QACX,IAAI,CAACE,QAAQ,GAAGA;QAChB,IAAI,CAACmB,WAAW,GAAG,EAAE;QACrB,IAAI,CAAClB,QAAQ,GAAGA;QAChB,IAAI,CAACM,MAAM,GAAGA;QACd,IAAI,CAACL,OAAO,GAAGA;QACf,IAAI,CAACkB,WAAW,GAAG;QACnB,IAAI,CAACC,WAAW,GAAG;QACnB,IAAI,CAACC,eAAe,GAAG;QACvB,IAAI,CAACC,sBAAsB,GAAG;QAC9B,IAAI,CAACf,SAAS,GAAGA;QACjB,IAAI,CAACC,UAAU,GAAGA;QAElB,IAAI,CAACV,MAAM,GAAGA;QACd,IAAI,CAACM,YAAY,GAAGA;QACpB,IAAI,CAACC,QAAQ,GAAGA;QAChB,IAAI,CAACkB,eAAe,GAAGC,IAAAA,YAAK,EAAC,gBAAgBpE,WAAW;YACtD+B,SAASsC,QAAQC,GAAG,CAACC,cAAc;QACrC;QACA,8FAA8F;QAC9F,wCAAwC;QACxC,IAAI,CAACJ,eAAe,CAACK,IAAI;IAC3B;IAEA,MAAaC,IACXnE,GAAoB,EACpBZ,GAAmB,EACnBgF,SAAoB,EACU;QAC9B,qFAAqF;QACrF,iEAAiE;QACjE,iEAAiE;QACjE,0CAA0C;QAC1C,MAAM,EAAEjE,SAAS,EAAE,GAAGJ,eAAeC,KAAKZ;QAC1C,IAAIe,WAAW;YACb,OAAO,CAAC;QACV;QAEA,6FAA6F;QAC7F,8FAA8F;QAC9F,kEAAkE;QAClE,uFAAuF;QACvF,MAAMkE,0BAA0B,OAC9BC,eACAC;YAEA,MAAM,EAAEC,QAAQ,EAAE,GAAGD;YACrB,IAAI,CAACC,UAAU,OAAO,CAAC;YAEvB,MAAMC,SAASpG,2BAA2BmG;YAC1C,IAAI,CAACC,QAAQ,OAAO,CAAC;YAErB,IAAIC;YAEJ,IAAI;gBACFA,kBAAkB,CAAC,CAAC,EAAED,OAAOE,IAAI,CAC9BC,GAAG,CAAC,CAACC,QAAkBC,mBAAmBD,QAC1CE,IAAI,CAAC,MAAM;YAChB,EAAE,OAAOC,GAAG;gBACV,MAAM,qBAAyC,CAAzC,IAAIC,mBAAW,CAAC,2BAAhB,qBAAA;2BAAA;gCAAA;kCAAA;gBAAwC;YAChD;YAEA,MAAMC,OAAOC,IAAAA,wCAAmB,EAACT;YAEjC,IAAIQ,SAAS,aAAaE,yBAAa,CAACC,OAAO,CAACH,UAAU,CAAC,GAAG;gBAC5D,IAAI;oBACF,MAAM,IAAI,CAACI,UAAU,CAAC;wBAAEJ;wBAAMK,YAAY;wBAAMtF,KAAKD,IAAIC,GAAG;oBAAC;gBAC/D,EAAE,OAAOZ,OAAO;oBACd,OAAO,MAAMf,kBAAkBgG,eAAekB,IAAAA,uBAAc,EAACnG;gBAC/D;gBAEA,MAAM4B,SAAS,MAAM,IAAI,CAACwE,oBAAoB,CAACP;gBAC/C,IAAIjE,OAAOyE,MAAM,GAAG,GAAG;oBACrB,OAAO,MAAMpH,kBAAkBgG,eAAerD,MAAM,CAAC,EAAE,EAAE;wBACvD3B,SAAS;oBACX;gBACF;YACF;YAEA,OAAO,CAAC;QACV;QAEA,MAAM,EAAEG,QAAQ,EAAE,GAAG,MAAM4E,wBAAwBjF,KAAKgF;QAExD,KAAK,MAAMuB,cAAc,IAAI,CAACnC,WAAW,CAAE;YACzC,IAAIoC,aAAa;YAEjB,MAAMD,WAAW3F,KAAKZ,KAAK;gBACzBwG,aAAa;YACf;YAEA,IAAI,CAACA,YAAY;gBACf,OAAO;oBAAEnG,UAAU;gBAAK;YAC1B;QACF;QAEA,OAAO;YAAEA;QAAS;IACpB;IAEOoG,kBAAkBxG,KAAmB,EAAQ;QAClD,IAAI,CAAC4D,cAAc,GAAG5D;IACxB;IAEOyG,sBAA4B;QACjC,IAAI,IAAI,CAAC7C,cAAc,EAAE;YACvB,IAAI,CAAC4C,iBAAiB,CAAC;YACvB,IAAI,CAACE,IAAI,CAAC;gBACRC,QAAQC,6CAA2B,CAACC,WAAW;gBAC/CC,MAAM;YACR;QACF;IACF;IAEA,MAAgBC,wBAAwBC,IAAY,EAAiB;QACnE,IAAI,CAACN,IAAI,CAAC;YACRC,QAAQC,6CAA2B,CAACK,wBAAwB;YAC5DD;QAGF;IACF;IAEOE,MACLvG,GAAoB,EACpBwG,OAAe,EACfC,IAAY,EACZC,QAAwC,EACxC;QACA1H,SAAS2H,aAAa,CAAC3G,KAAKA,IAAI4G,MAAM,EAAEH,MAAM,CAACI;gBAC7C,4BACA;aADA,6BAAA,IAAI,CAACC,oBAAoB,qBAAzB,2BAA2BP,KAAK,CAACM;aACjC,wBAAA,IAAI,CAACE,eAAe,qBAApB,sBAAsBR,KAAK,CAACM,QAAQ,IAAM,IAAI,CAAC5D,cAAc;YAC7DyD,SAASG;YAETA,OAAOG,gBAAgB,CAAC,WAAW,OAAO,EAAEb,IAAI,EAAE;gBAChDA,OAAO,OAAOA,SAAS,WAAWA,KAAKc,QAAQ,KAAKd;gBAEpD,IAAI;oBACF,MAAMe,UAAUC,KAAKC,KAAK,CAACjB;oBAE3B,IAAIkB;oBASJ,OAAQH,QAAQI,KAAK;wBACnB,KAAK;4BAAY;gCACfD,aAAa;oCACXlG,MAAM+F,QAAQK,QAAQ;oCACtBC,WACEhJ,OAAOiJ,KAAKC,KAAK,CAACR,QAAQM,SAAS,KACnCjJ;oCACFoJ,OAAOT,QAAQU,UAAU;oCACzBC,SACErJ,OAAOiJ,KAAKC,KAAK,CAACR,QAAQW,OAAO,KACjCtJ;gCACJ;gCACA;4BACF;wBACA,KAAK;4BAAsB;gCACzB8I,aAAa;oCACXlG,MAAM+F,QAAQI,KAAK;oCACnBE,WACEhJ,OAAO0I,QAAQM,SAAS,IAAIjJ;oCAC9BsJ,SAASrJ,OAAO0I,QAAQW,OAAO,IAAItJ;oCACnCoJ,OAAO;wCACLG,gBAAgBZ,QAAQY,cAAc,CAAClD,GAAG,CAAC,CAACmD,IAC1CA,EACGC,OAAO,CAAC,CAAC,CAAC,EAAEC,yBAAc,CAACC,eAAe,CAAC,EAAE,CAAC,EAAE,IAChDF,OAAO,CAAC,SAAS;wCAEtB9C,MAAMgC,QAAQhC,IAAI;wCAClBiD,cAAcjB,QAAQiB,YAAY;oCACpC;gCACF;gCACA;4BACF;wBACA,KAAK;wBACL,KAAK;4BAAkB;gCACrBd,aAAa;oCACXlG,MAAM+F,QAAQI,KAAK;gCACrB;gCACA;4BACF;wBACA,KAAK;4BAAgB;gCACnBD,aAAa;oCACXlG,MAAM+F,QAAQI,KAAK;oCACnBK,OAAO;wCAAES,YAAYlB,QAAQkB,UAAU;oCAAC;gCAC1C;gCACA;4BACF;wBACA,KAAK;4BAAkB;gCACrBf,aAAa;oCACXlG,MAAM+F,QAAQI,KAAK;oCACnBK,OAAO;wCAAEU,cAAcnB,QAAQmB,YAAY;oCAAC;gCAC9C;gCACA;4BACF;wBACA,KAAK;wBACL,KAAK;4BAAqB;gCACxBhB,aAAa;oCACXlG,MAAM+F,QAAQI,KAAK;oCACnBK,OAAO;wCAAEzC,MAAMgC,QAAQhC,IAAI,IAAI;oCAAG;gCACpC;gCACA;4BACF;wBACA,KAAK;4BAAsB;gCACzB,MAAM,EAAEoC,KAAK,EAAEgB,UAAU,EAAEC,eAAe,EAAE,GAAGrB;gCAE/CG,aAAa;oCACXlG,MAAMmG;oCACNK,OAAO;wCAAEW,YAAYA,cAAc;oCAAG;gCACxC;gCAEA,IAAIC,iBAAiB;oCACnBC,KAAIC,IAAI,CAACC,qCAA2B;oCACpC;gCACF;gCAEA,IAAIC,cAAc;gCAClB,IAAIL,YAAY;wCACD;oCAAb,MAAMM,QAAO,QAAA,uCAAuCC,IAAI,CACtDP,gCADW,KAEV,CAAC,EAAE;oCACN,IAAIM,MAAM;wCACR,8DAA8D;wCAC9D,oEAAoE;wCACpE,IAAIA,KAAK1I,UAAU,CAAC,CAAC,CAAC,EAAE+H,yBAAc,CAACC,eAAe,CAAC,EAAE,CAAC,GAAG;4CAC3D,MAAMY,UAAU,IAAIC,IAAIH,MAAM;4CAC9B,MAAMI,MAAMjF,QAAQiF,GAAG;4CACvB,MAAMC,UAAUH,QAAQI,YAAY,CACjCC,MAAM,CAAC,WACPvE,GAAG,CAAC,CAACwE,WAAaA,SAASC,KAAK,CAACL,IAAItD,MAAM,GAAG,IAC9C7G,MAAM,CACL,CAACuK,WAAa,CAACA,SAASlJ,UAAU,CAAC;4CAGvC,IAAI+I,QAAQvD,MAAM,GAAG,GAAG;gDACtBiD,cAAc,CAAC,MAAM,EAAEM,QAAQlE,IAAI,CAAC,MAAM,QAAQ,CAAC;4CACrD;wCACF,OAAO,IACL,8BAA8B;wCAC9B6D,KAAK1I,UAAU,CAAC,CAAC,CAAC,EAAE+H,yBAAc,CAACqB,eAAe,CAAC,EAAE,CAAC,GACtD;4CACA,MAAMC,kBAAkBX,KAAKS,KAAK,CAChC,CAAC,CAAC,EAAEpB,yBAAc,CAACqB,eAAe,CAAC,EAAE,CAAC,CAAC5D,MAAM;4CAG/CiD,cAAc,CAAC,MAAM,EAAEY,gBAAgB,QAAQ,CAAC;wCAClD,OAAO;4CACLZ,cAAc,CAAC,MAAM,EAAEC,KAAK,QAAQ,CAAC;wCACvC;oCACF;gCACF;gCAEAJ,KAAIC,IAAI,CACN,CAAC,yCAAyC,EAAEE,YAAY,iEAAiE,CAAC;gCAE5H;4BACF;wBACA,KAAK;4BAAgB;gCACnB,IAAI,IAAI,CAACvG,MAAM,CAACoH,YAAY,CAACC,0BAA0B,EAAE;oCACvD,MAAMC,IAAAA,sCAAyB,EAAC;wCAC9BC,SAASzC,QAAQyC,OAAO;wCACxBC,QAAQ1C,QAAQ0C,MAAM;wCACtBC,YAAY3C,QAAQ2C,UAAU;wCAC9BpG,aAAa,IAAM,IAAI,CAACA,WAAW;wCACnCC,aAAa,IAAM,IAAI,CAACA,WAAW;wCACnCC,iBAAiB,IAAM,IAAI,CAACA,eAAe;wCAC3CmG,eAAe,IAAI,CAAC3H,GAAG;wCACvBI,SAAS,IAAI,CAACA,OAAO;wCACrBH,QAAQ,IAAI,CAACA,MAAM,CAACoH,YAAY,CAACC,0BAA0B;oCAC7D;gCACF;gCACA;4BACF;wBACA;4BAAS;gCACP;4BACF;oBACF;oBAEA,IAAIpC,YAAY;wBACd,IAAI,CAACxD,eAAe,CAACkG,gBAAgB,CACnC1C,WAAWlG,IAAI,EACfkG,WAAWG,SAAS,EACpBH,WAAWQ,OAAO,EAClB;4BAAE,GAAGR,WAAWM,KAAK;4BAAEqC,UAAU9C,QAAQ+C,EAAE;wBAAC;oBAEhD;gBACF,EAAE,OAAOjF,GAAG;gBACV,4BAA4B;gBAC9B;YACF;QACF;IACF;IAEA,MAAckF,MAAMC,IAAU,EAAiB;QAC7C,OAAOA,KACJ9C,UAAU,CAAC,SACX+C,YAAY,CAAC,IACZC,IAAAA,gCAAe,EAACtF,IAAAA,UAAI,EAAC,IAAI,CAAC5C,GAAG,EAAE,IAAI,CAACC,MAAM,CAACG,OAAO,GAAG;IAE3D;IAEA,MAAc+H,iBAAiBH,IAAU,EAAE;QACzC,MAAMI,oBAAoBJ,KAAK9C,UAAU,CAAC;QAE1C,MAAMmD,iBAAiB,IAAI,CAACpI,MAAM,CAACoI,cAAc;QAEjD,OAAOD,kBAAkBH,YAAY,CAAC;YACpC,MAAMK,YAAY,CAAC,IAAI,CAACnI,QAAQ,GAC3B,EAAE,GACH,MAAMiI,kBACHlD,UAAU,CAAC,kBACX+C,YAAY,CAAC,IACZM,QAAQC,GAAG,CAAC;oBACVC,IAAAA,0BAAY,EAAC,IAAI,CAACtI,QAAQ,EAAG,SAASkI,gBAAgB;oBACtDI,IAAAA,0BAAY,EACV,IAAI,CAACtI,QAAQ,EACb,cACAkI,gBACA;iBAEH;YAGT,IAAI,CAACtH,YAAY,GAAG,MAAMqH,kBACvBlD,UAAU,CAAC,wBACX+C,YAAY,CAAC,IACZS,IAAAA,2BAAkB,EAAC;oBACjBC,OAAO;oBACPN,gBAAgB,IAAI,CAACpI,MAAM,CAACoI,cAAc;oBAC1CO,WAAWC,qBAAU,CAACC,KAAK;oBAC3BR,WAAWA,UAAU5L,MAAM,CACzB,CAACqM,IAAkC,OAAOA,MAAM;oBAElD5I,UAAU,IAAI,CAACA,QAAQ;oBACvBM,QAAQ,IAAI,CAACA,MAAM;gBACrB;YAGJ,MAAMuI,cAAc,MAAMZ,kBACvBlD,UAAU,CAAC,sBACX+C,YAAY,CAAC,IACZgB,IAAAA,0BAAiB,EAAC;oBAChBxI,QAAQ,IAAI,CAACA,MAAM;oBACnBJ,SAAS,IAAI,CAACA,OAAO;oBACrBJ,QAAQ,IAAI,CAACA,MAAM;oBACnBiJ,UAAU,EAAE;oBACZP,OAAO;oBACPQ,OAAO,IAAI,CAACpI,YAAY;oBACxBZ,UAAU,IAAI,CAACA,QAAQ;oBACvBiJ,aAAa,IAAI,CAAC7I,YAAY;oBAC9B8I,SAAS,IAAI,CAACrJ,GAAG;oBACjBqI,gBAAgB,IAAI,CAACpI,MAAM,CAACoI,cAAc;gBAC5C;YAGJ,MAAMiB,uBAAuB;gBAC3BC,KAAK;gBACLlJ,SAAS,IAAI,CAACA,OAAO;gBACrBC,eAAe,IAAI,CAACA,aAAa;gBACjCL,QAAQ,IAAI,CAACA,MAAM;gBACnBE,UAAU,IAAI,CAACA,QAAQ;gBACvBK,UAAU,IAAI,CAACA,QAAQ;gBACvBgJ,kBAAkB,IAAI,CAACvJ,MAAM,CAACwJ,iBAAiB;gBAC/CC,mBAAmB,IAAI,CAACzJ,MAAM,CAAC0J,kBAAkB;gBACjDC,gBAAgB,IAAI,CAAClI,eAAe;gBACpCjB,QAAQ,IAAI,CAACA,MAAM;gBACnBF,cAAc,IAAI,CAACA,YAAY;YACjC;YAEA,OAAO6H,kBACJlD,UAAU,CAAC,2BACX+C,YAAY,CAAC;gBACZ,MAAM4B,OAAO,MAAMC,IAAAA,8BAAe,EAAC;oBACjC9J,KAAK,IAAI,CAACA,GAAG;oBACbC,QAAQqJ,qBAAqBrJ,MAAM;oBACnCsJ,KAAK;gBACP;gBACA,OAAOhB,QAAQC,GAAG,CAAC;oBACjB,0BAA0B;oBAC1BuB,IAAAA,sBAAoB,EAAC,IAAI,CAAC/J,GAAG,EAAE;wBAC7B,GAAGsJ,oBAAoB;wBACvBU,cAAcC,0BAAc,CAACvF,MAAM;wBACnCsE,aAAaA,YAAYtE,MAAM;wBAC/B,GAAGmF,IAAI;oBACT;oBACAE,IAAAA,sBAAoB,EAAC,IAAI,CAAC/J,GAAG,EAAE;wBAC7B,GAAGsJ,oBAAoB;wBACvBU,cAAcC,0BAAc,CAACC,MAAM;wBACnClB,aAAaA,YAAYkB,MAAM;wBAC/B,GAAGL,IAAI;oBACT;oBACAE,IAAAA,sBAAoB,EAAC,IAAI,CAAC/J,GAAG,EAAE;wBAC7B,GAAGsJ,oBAAoB;wBACvBU,cAAcC,0BAAc,CAACE,UAAU;wBACvCnB,aAAaA,YAAYmB,UAAU;wBACnC,GAAGN,IAAI;oBACT;iBACD;YACH;QACJ;IACF;IAEA,MAAaO,qBAAoC;QAC/C,IAAI,IAAI,CAACC,eAAe,EAAE;QAE1B,MAAMR,OAAO,MAAMC,IAAAA,8BAAe,EAAC;YACjC9J,KAAK,IAAI,CAACA,GAAG;YACbC,QAAQ,IAAI,CAACA,MAAM;YACnBsJ,KAAK;QACP;QACA,MAAMe,iBAAiB,MAAMP,IAAAA,sBAAoB,EAAC,IAAI,CAAC/J,GAAG,EAAE;YAC1DO,cAAc,IAAI,CAACA,YAAY;YAC/BqJ,gBAAgB,IAAI,CAAClI,eAAe;YACpC6H,KAAK;YACLS,cAAcC,0BAAc,CAACvF,MAAM;YACnCzE,QAAQ,IAAI,CAACA,MAAM;YACnBI,SAAS,IAAI,CAACA,OAAO;YACrBC,eAAe,IAAI,CAACA,aAAa;YACjCG,QAAQ,IAAI,CAACA,MAAM;YACnBN,UAAU,IAAI,CAACA,QAAQ;YACvBK,UAAU;gBACR+J,aAAa,EAAE;gBACfC,YAAY,EAAE;gBACdC,UAAU,EAAE;YACd;YACAjB,kBAAkB;gBAChBe,aAAa,EAAE;gBACfC,YAAY,EAAE;gBACdC,UAAU,EAAE;YACd;YACAf,mBAAmB,EAAE;YACrBgB,eAAe;YACf1B,aAAa,AACX,CAAA,MAAMC,IAAAA,0BAAiB,EAAC;gBACtBxI,QAAQ,IAAI,CAACA,MAAM;gBACnBJ,SAAS,IAAI,CAACA,OAAO;gBACrBJ,QAAQ,IAAI,CAACA,MAAM;gBACnBiJ,UAAU,EAAE;gBACZP,OAAO;gBACPQ,OAAO;oBACL,SAAS;oBACT,WAAW;gBACb;gBACAhJ,UAAU,IAAI,CAACA,QAAQ;gBACvBiJ,aAAa,IAAI,CAAC7I,YAAY;gBAC9B8I,SAAS,IAAI,CAACrJ,GAAG;gBACjBqI,gBAAgB,IAAI,CAACpI,MAAM,CAACoI,cAAc;YAC5C,EAAC,EACD3D,MAAM;YACR,GAAGmF,IAAI;QACT;QAEA,MAAMc,mBAAmBC,IAAAA,0BAAiB,IAAGN;QAE7C,IAAI,CAACD,eAAe,GAAG,MAAM,IAAI9B,QAAQ,CAACsC;YACxC,IAAIC,yBAAyB;YAC7BH,iBAAiBI,KAAK,CACpB,kFAAkF;YAClFT,eAAeU,YAAY,EAC3B,gCAAgC;YAChC,CAACC;gBACC,IAAI,CAACH,wBAAwB;oBAC3BA,yBAAyB;oBACzBD,QAAQ;gBACV;YACF;QAEJ;IACF;IAEA,MAAcK,qBAAqBlD,IAAU,EAAE;QAC7C,MAAMmD,kBAAkBnD,KAAK9C,UAAU,CAAC;QACxC,OAAOiG,gBAAgBlD,YAAY,CAAc,UAC/ChM;IAEJ;IAEA,MAAamP,QAAuB;QAClC,MAAMC,YAAY,IAAI,CAAC3J,eAAe,CAACwD,UAAU,CAAC;QAClDmG,UAAUtJ,IAAI,GAAG,uDAAuD;;QAExE,IAAI,CAACf,WAAW,GAAG,MAAM,IAAI,CAACkK,oBAAoB,CAACG;QAEnD,MAAMC,gBAAgBC,IAAAA,wBAAgB;QACtC,IAAID,iBAAiB,CAAC,IAAI,CAACE,mBAAmB,EAAE;YAC9C,MAAMC,YAAY7J,QAAQ6J,SAAS;YACnC,IAAIC;YACJ,IAAI;gBACF,8EAA8E;gBAC9E,MAAMC,gBAAgB,MAAMpM,MAC1B,CAAC,iBAAiB,EAAEkM,UAAU,UAAU,CAAC,EACzCG,IAAI,CAAC,CAAC3O,MAAQA,IAAI2C,IAAI;gBACxB,iFAAiF;gBACjF8L,YAAYC,aAAa,CAAC,EAAE;YAC9B,EAAE,OAAM,CAAC;YACT,IAAID,WAAW;gBACb,IAAI,CAACF,mBAAmB,GAAGE,UAAUF,mBAAmB;YAC1D;QACF;QAEA,MAAM,IAAI,CAACzD,KAAK,CAACsD;QACjB,oDAAoD;QACpD,MAAMQ,YAAE,CAACC,KAAK,CAAC,IAAI,CAAC1L,OAAO,EAAE;YAAE2L,WAAW;QAAK;QAE/C,MAAMC,wBAAwB,MAAMC,IAAAA,2CAAiB,EAAC,IAAI,CAAC7L,OAAO;QAElE,MAAM8L,sBAAsBtJ,IAAAA,UAAI,EAAC,IAAI,CAACxC,OAAO,EAAE;QAC/C,8EAA8E;QAC9E,uDAAuD;QACvD,MAAMyL,YAAE,CAACM,SAAS,CAACD,qBAAqB;QAExC,IAAI,CAACE,oBAAoB,GAAG,MAAM,IAAI,CAACjE,gBAAgB,CAACkD;QAExD,KAAK,MAAMpL,UAAU,IAAI,CAACmM,oBAAoB,CAAE;YAC9C,MAAMC,eAAepM,OAAOqM,KAAK;YACjCrM,OAAOqM,KAAK,GAAG,OAAO,GAAGC;oBACJ;gBAAnB,MAAMC,aAAa,EAAA,sBAAA,IAAI,CAACC,aAAa,qBAAlB,oBAAoBD,UAAU,KAAI;gBACrD,MAAMhF,UAAUkF,IAAAA,gCAAU,EAACF;gBAC3B,wCAAwC;gBACxC,MAAMxD,cAAc,MAAMqD,gBAAgBE;gBAC1C,MAAMI,sBAAsB1M,OAAOjB,IAAI,KAAKiL,0BAAc,CAACvF,MAAM;gBACjE,MAAMkI,0BAA0B3M,OAAOjB,IAAI,KAAKiL,0BAAc,CAACC,MAAM;gBACrE,MAAM2C,0BACJ5M,OAAOjB,IAAI,KAAKiL,0BAAc,CAACE,UAAU;gBAE3C,MAAM5B,QAAQC,GAAG,CACfsE,OAAOC,IAAI,CAACvF,SAAS/E,GAAG,CAAC,OAAOuK;oBAC9B,MAAMC,YAAYzF,OAAO,CAACwF,SAAS;oBACnC,MAAM,EAAEE,UAAU,EAAEC,OAAO,EAAE,GAAGF;oBAEhC,MAAMG,SACJ,sDAAsD1G,IAAI,CACxDsG;oBAEJ,MAAM,GAAGK,IAAI,YAAY,OAAMtK,KAAK,GAAGqK,MAAQ,kCAAkC;;oBAEjF,IAAIC,QAAQpD,0BAAc,CAACvF,MAAM,IAAI,CAACiI,qBAAqB;oBAC3D,IAAIU,QAAQpD,0BAAc,CAACC,MAAM,IAAI,CAAC0C,yBACpC;oBACF,IAAIS,QAAQpD,0BAAc,CAACE,UAAU,IAAI,CAAC0C,yBACxC;oBAEF,MAAMS,UAAUL,UAAUM,IAAI,KAAKC,gCAAU,CAACC,KAAK;oBACnD,MAAMC,eAAeT,UAAUM,IAAI,KAAKC,gCAAU,CAACG,WAAW;oBAE9D,0DAA0D;oBAC1D,IAAIL,SAAS;wBACX,MAAMM,aACJ,CAACT,WAAWU,IAAAA,cAAU,EAACZ,UAAUa,gBAAgB;wBACnD,IAAI,CAACF,YAAY;4BACf,OAAOpG,OAAO,CAACwF,SAAS;4BACxB;wBACF;oBACF;oBAEA,sEAAsE;oBACtE,IAAIU,cAAc;wBAChB,IAAIT,UAAUc,qBAAqB,EAAE;4BACnC,MAAMH,aACJ,CAACT,WAAWU,IAAAA,cAAU,EAACZ,UAAUc,qBAAqB;4BACxD,IAAI,CAACH,YAAY;gCACf,OAAOpG,OAAO,CAACwF,SAAS;gCACxB;4BACF;wBACF;oBACF;oBAEA,8CAA8C;oBAC9C,IAAIjK,SAAS,WAAW;wBACtB,IAAI,CAAC3B,yBAAyB,GAAG;oBACnC;oBAEA,MAAM4M,YAAY,CAAC,CAAC,IAAI,CAACvN,MAAM;oBAC/B,MAAMwN,YAAYD,aAAad,WAAWnP,UAAU,CAAC;oBACrD,MAAMmQ,aAAaZ,UACf,MAAMa,IAAAA,sCAA6B,EAAC;wBAClCC,gBAAgBH;wBAChB5F,gBAAgB,IAAI,CAACpI,MAAM,CAACoI,cAAc;wBAC1CgG,cAAcpB,UAAUa,gBAAgB;wBACxCrN,QAAQ,IAAI,CAACA,MAAM;wBACnBR,QAAQ,IAAI,CAACA,MAAM;wBACnB0I,OAAO;wBACP5F;oBACF,KACAxF;oBAEJ,IAAI2Q,CAAAA,8BAAAA,WAAYX,IAAI,MAAK1E,qBAAU,CAACC,KAAK,EAAE;4BAEvCoF,2BAAAA,oBACAA,4BAAAA;wBAFF,IACEA,EAAAA,qBAAAA,WAAWjO,MAAM,sBAAjBiO,4BAAAA,mBAAmBjO,MAAM,qBAAzBiO,0BAA2BI,GAAG,MAAK,QACnCJ,EAAAA,sBAAAA,WAAWjO,MAAM,sBAAjBiO,6BAAAA,oBAAmBjO,MAAM,qBAAzBiO,2BAA2BI,GAAG,MAAK,UACnC;4BACA,IAAI,CAACpN,iBAAiB,GAAG;wBAC3B;oBACF;oBAEA,MAAMqN,oBACJN,aAAaC,CAAAA,8BAAAA,WAAYM,GAAG,MAAKC,4BAAgB,CAAC/J,MAAM;oBAE1D,MAAMgK,WAAuBzB,UAAUC,UAAU,CAACnP,UAAU,CAC1D,YAEE8K,qBAAU,CAACC,KAAK,GAChBmE,UAAUC,UAAU,CAACnP,UAAU,CAAC,UAC9B8K,qBAAU,CAAC8F,GAAG,GACd9F,qBAAU,CAAC+F,IAAI;oBAErB,IAAIF,aAAa,SAAS;wBACxB,IAAI,CAACtN,yBAAyB,GAAG;oBACnC;oBACA,IAAIsN,aAAa,OAAO;wBACtB,IAAI,CAACvN,uBAAuB,GAAG;oBACjC;oBAEA,MAAM0N,oBACJC,IAAAA,gCAAyB,EAAC/L,SAAS2L,aAAa7F,qBAAU,CAAC+F,IAAI;oBAEjE,IAAIG,cAAcb,8BAAAA,WAAYc,OAAO;oBAErCC,IAAAA,+BAAsB,EAAC;wBACrBlM;wBACAgM;wBACAL;wBACAQ,cAAc;4BACZ,kDAAkD;4BAClD,IAAI,CAACrC,2BAA2B,CAACS,SAAS;4BAC1C9F,OAAO,CAACwF,SAAS,CAACmC,MAAM,GAAGC,8BAAQ;4BAEnC,IAAIP,mBAAmB;gCACrB,MAAMQ,uBAAuBnC,WAAWrH,OAAO,CAAC,QAAQ;gCACxDmD,WAAW,CAACqG,qBAAqB,GAAGC,IAAAA,2BAAkB,EAAC;oCACrDtF,cAAcC,0BAAc,CAACE,UAAU;oCACvCnL,MAAMqQ;oCACNE,OAAOC,IAAAA,gCAAuB,EAAC;wCAC7B1B,kBAAkBb,UAAUa,gBAAgB;wCAC5C2B,cAAc;wCACd9G,OAAO;oCACT;oCACA4F,mBAAmB;oCACnBP;gCACF;gCACA;4BACF;4BACA,MAAM0B,eAAezB,YACjB0B,IAAAA,oBAAW,EAAC;gCACV3Q,MAAMkO;gCACNnK;gCACA6M,UAAU3C,UAAU2C,QAAQ;gCAC5BC,UAAUC,WAAK,CAAClN,IAAI,CAClBmN,wBAAa,EACbC,IAAAA,cAAQ,EACN,IAAI,CAACvP,MAAM,EACXwM,UAAUa,gBAAgB,EAC1BjI,OAAO,CAAC,OAAO;gCAEnBpF,QAAQ,IAAI,CAACA,MAAM;gCACnB4H,gBAAgB,IAAI,CAACpI,MAAM,CAACoI,cAAc;gCAC1CgB,SAAS,IAAI,CAACrJ,GAAG;gCACjB2I,OAAO;gCACPsH,cAAc,IAAI,CAAChQ,MAAM,CAACiQ,UAAU,CAACD,YAAY;gCACjDE,UAAU,IAAI,CAAClQ,MAAM,CAACkQ,QAAQ;gCAC9BC,aAAa,IAAI,CAACnQ,MAAM,CAACmQ,WAAW;gCACpCC,kBAAkB,IAAI,CAACpQ,MAAM,CAACqQ,MAAM;gCACpCC,eAAe,EAAErC,8BAAAA,WAAYqC,eAAe;gCAC5CC,kBAAkBC,OAAOC,IAAI,CAC3B1L,KAAK2L,SAAS,CAACzC,CAAAA,8BAAAA,WAAY1K,UAAU,KAAI,CAAC,IAC1CsB,QAAQ,CAAC;gCACX8L,yBAAyB,IAAI,CAAC3Q,MAAM,CAACoH,YAAY,CAC9CwJ,cAAc,GACb,OACAtT;4BACN,GAAGuT,MAAM,GACTvT;4BAEJyL,WAAW,CAACkE,WAAW,GAAGoC,IAAAA,2BAAkB,EAAC;gCAC3CtF,cAAcC,0BAAc,CAACE,UAAU;gCACvCnL,MAAMkO;gCACNqC,OAAOwB,IAAAA,2BAAkB,EAAC;oCACxBjD,kBAAkBb,UAAUa,gBAAgB;oCAC5CzE,SAAS,IAAI,CAACrJ,GAAG;oCACjBK,SAAS,IAAI,CAACA,OAAO;oCACrB6M;oCACAjN,QAAQ,IAAI,CAACA,MAAM;oCACnB0I,OAAO;oCACP5F;oCACAoG,OAAO,IAAI,CAACpI,YAAY;oCACxBwN;oCACAmB;oCACA9G,WAAWqF,YAAYpF,qBAAU,CAAC8F,GAAG,GAAG9F,qBAAU,CAACC,KAAK;oCACxDyH,eAAe,EAAErC,8BAAAA,WAAYqC,eAAe;gCAC9C;gCACAvC;4BACF;wBACF;wBACAgD,UAAU;4BACR,IAAI,CAACrE,qBAAqB;4BAC1B,IAAIe,cAAc;gCAChBlG,OAAO,CAACwF,SAAS,CAACmC,MAAM,GAAGC,8BAAQ;gCACnCpG,WAAW,CAACkE,WAAW,GAAGoC,IAAAA,2BAAkB,EAAC;oCAC3CtQ,MAAMkO;oCACNlD,cAAcC,0BAAc,CAACvF,MAAM;oCACnC6K,OAAOtC,UAAUgE,OAAO;oCACxBjD;gCACF;4BACF,OAAO;gCACLxG,OAAO,CAACwF,SAAS,CAACmC,MAAM,GAAGC,8BAAQ;gCACnCpG,WAAW,CAACkE,WAAW,GAAGoC,IAAAA,2BAAkB,EAAC;oCAC3CtQ,MAAMkO;oCACNlD,cAAcC,0BAAc,CAACvF,MAAM;oCACnC6K,OAAO2B,IAAAA,uBAAc,EAAC;wCACpBpD,kBAAkBb,UAAUa,gBAAgB;wCAC5C/K;oCACF;oCACAiL;gCACF;4BACF;wBACF;wBACAmD,UAAU;4BACR,kDAAkD;4BAClD,IAAI,CAACvE,2BAA2B,CAACU,SAAS;4BAC1C9F,OAAO,CAACwF,SAAS,CAACmC,MAAM,GAAGC,8BAAQ;4BACnC,IAAIgC,kBAAkBpB,IAAAA,cAAQ,EAC5B/P,OAAOoR,OAAO,EACdpE,UAAUa,gBAAgB;4BAE5B,IACE,CAACwD,IAAAA,gBAAU,EAACF,oBACZ,CAACA,gBAAgBrT,UAAU,CAAC,QAC5B;gCACAqT,kBAAkB,CAAC,EAAE,EAAEA,iBAAiB;4BAC1C;4BAEA,IAAI7B;4BACJ,IAAIV,mBAAmB;gCACrBU,QAAQC,IAAAA,gCAAuB,EAAC;oCAC9B1B,kBAAkBb,UAAUa,gBAAgB;oCAC5C2B,cAAc;oCACd9G,OAAO;gCACT;gCACAK,WAAW,CAACkE,WAAW,GAAGoC,IAAAA,2BAAkB,EAAC;oCAC3CtF,cAAcC,0BAAc,CAACC,MAAM;oCACnClL,MAAMkO;oCACNqB,mBAAmB;oCACnBgB;oCACAvB;gCACF;4BACF,OAAO,IAAIuD,IAAAA,uBAAgB,EAACxO,OAAO;gCACjCwM,QAAQwB,IAAAA,2BAAkB,EAAC;oCACzBjD,kBAAkBb,UAAUa,gBAAgB;oCAC5CzE,SAAS,IAAI,CAACrJ,GAAG;oCACjBK,SAAS,IAAI,CAACA,OAAO;oCACrB6M;oCACAjN,QAAQ,IAAI,CAACA,MAAM;oCACnB0I,OAAO;oCACP5F;oCACAoG,OAAO,IAAI,CAACpI,YAAY;oCACxBwN;oCACA3F,WAAWC,qBAAU,CAACC,KAAK;oCAC3ByH,eAAe,EAAErC,8BAAAA,WAAYqC,eAAe;gCAC9C;4BACF,OAAO,IAAItC,WAAW;gCACpB,0FAA0F;gCAC1F,MAAM4B,WAAW5C,UAAUa,gBAAgB,CAAC/P,UAAU,CACpDyT,IAAAA,aAAO,EAACnS,QAAQwL,OAAO,CAAC,yBAEtBoC,UAAUa,gBAAgB,GAC1BgC,WAAK,CAAClN,IAAI,CACRmN,wBAAa,EACbC,IAAAA,cAAQ,EACN,IAAI,CAACvP,MAAM,EACXwM,UAAUa,gBAAgB,EAC1BjI,OAAO,CAAC,OAAO;gCAEvB0J,QAAQI,IAAAA,oBAAW,EAAC;oCAClB3Q,MAAMkO;oCACNnK;oCACA6M,UAAU3C,UAAU2C,QAAQ;oCAC5BC;oCACApP,QAAQ,IAAI,CAACA,MAAM;oCACnB4H,gBAAgB,IAAI,CAACpI,MAAM,CAACoI,cAAc;oCAC1CgB,SAAS,IAAI,CAACrJ,GAAG;oCACjB2I,OAAO;oCACPsH,cAAc,IAAI,CAAChQ,MAAM,CAACiQ,UAAU,CAACD,YAAY;oCACjDE,UAAU,IAAI,CAAClQ,MAAM,CAACkQ,QAAQ;oCAC9BC,aAAa,IAAI,CAACnQ,MAAM,CAACmQ,WAAW;oCACpCC,kBAAkB,IAAI,CAACpQ,MAAM,CAACqQ,MAAM;oCACpCC,eAAe,EAAErC,8BAAAA,WAAYqC,eAAe;oCAC5CC,kBAAkBC,OAAOC,IAAI,CAC3B1L,KAAK2L,SAAS,CAACzC,CAAAA,8BAAAA,WAAY1K,UAAU,KAAI,CAAC,IAC1CsB,QAAQ,CAAC;oCACX8L,yBAAyB,IAAI,CAAC3Q,MAAM,CAACoH,YAAY,CAC9CwJ,cAAc,GACb,OACAtT;gCACN;4BACF,OAAO,IAAIkU,IAAAA,sBAAU,EAAC1O,OAAO;gCAC3BwM,QAAQmC,IAAAA,oCAAmB,EAAC;oCAC1BC,MAAMC,oBAAS,CAACC,SAAS;oCACzB9O;oCACA+K,kBAAkBsD;oCAClBb,eAAe,EAAErC,8BAAAA,WAAYqC,eAAe;oCAC5CC,kBAAkBtC,CAAAA,8BAAAA,WAAY1K,UAAU,KAAI,CAAC;gCAC/C;4BACF,OAAO,IACL,CAAC+N,IAAAA,uBAAgB,EAACxO,SAClB,CAAC+O,IAAAA,wCAAmB,EAACV,oBACrB,CAACW,IAAAA,wCAAmB,EAAChP,SACrB,CAAC8L,mBACD;gCACAU,QAAQmC,IAAAA,oCAAmB,EAAC;oCAC1BC,MAAMC,oBAAS,CAAC9I,KAAK;oCACrB/F;oCACAoG,OAAO,IAAI,CAACpI,YAAY;oCACxB+M,kBAAkBsD;oCAClBb,eAAe,EAAErC,8BAAAA,WAAYqC,eAAe;oCAC5CC,kBAAkBtC,CAAAA,8BAAAA,WAAY1K,UAAU,KAAI,CAAC;gCAC/C;4BACF,OAAO;gCACL+L,QAAQ6B;4BACV;4BAEApI,WAAW,CAACkE,WAAW,GAAGoC,IAAAA,2BAAkB,EAAC;gCAC3CtF,cAAcC,0BAAc,CAACC,MAAM;gCACnClL,MAAMkO;gCACNqB;gCACAgB;gCACAvB;4BACF;wBACF;oBACF;gBACF;gBAGF,IAAI,CAAC,IAAI,CAAC9M,iBAAiB,EAAE;oBAC3B,OAAO8H,WAAW,CAACgJ,2CAA+B,CAAC;gBACrD;gBACA,IAAI,CAAC,IAAI,CAAC5Q,yBAAyB,EAAE;oBACnC,OAAO4H,WAAW,CAACiJ,4CAAgC,CAAC;oBACpD,OAAOjJ,WAAW,CAAC,aAAa;oBAChC,OAAOA,WAAW,CAAC,eAAe;oBAClC,OAAOA,WAAW,CAAC,UAAU;oBAC7B,OAAOA,WAAW,CAAC,kBAAkB;gBACvC;gBACA,qEAAqE;gBACrE,IAAI,CAAC,IAAI,CAAC9H,iBAAiB,IAAI,CAAC,IAAI,CAACE,yBAAyB,EAAE;oBAC9D,OAAO4H,WAAW,CAACkJ,qDAAyC,CAAC;gBAC/D;gBACA,IAAI,CAAC,IAAI,CAAC/Q,uBAAuB,EAAE;oBACjC,OAAO6H,WAAW,CAACmJ,gDAAoC,CAAC;gBAC1D;gBAEA,OAAOnJ;YACT;QACF;QAEA,iFAAiF;QACjF,uBAAuB;QACvB,IAAI,CAACoD,oBAAoB,CAACgG,WAAW,GAAG;QAExC,MAAM7J,QAAQC,GAAG,CACf6J,MAAM3B,IAAI,CAAC4B,IAAAA,kCAAmB,EAAC,IAAI,CAAClG,oBAAoB,GAAG3J,GAAG,CAC5D8P,6DAA0C;QAG9C,IAAI,CAAC9F,aAAa,GAAG7B,IAAAA,0BAAiB,IACpC,IAAI,CAACwB,oBAAoB;QAG3B,uEAAuE;QACvE,MAAMoG,kBAAkB,IAAI,CAAC/F,aAAa,CAACgG,SAAS,CAAC,EAAE,CAACD,eAAe;QACvE,KAAK,MAAME,YAAY,IAAI,CAACjG,aAAa,CAACgG,SAAS,CAAE;YACnDC,SAASF,eAAe,GAAGA;YAC3B,qFAAqF;YACrFE,SAASC,WAAW,GAAGC,KAAKC,GAAG;YAC/B,sGAAsG;YACtGH,SAASI,KAAK,CAACC,SAAS,CAACC,SAAS,CAAC;gBACjCC,UAASC,OAAY;oBACnB,IAAIA,QAAQlU,IAAI,KAAK,yBAAyB;wBAC5C,OAAO;oBACT;oBACA,OAAOkU;gBACT;YACF;QACF;QAEA,IAAI,CAACzG,aAAa,CAACqG,KAAK,CAACK,IAAI,CAACC,GAAG,CAAC,qBAAqB;gBACrDZ;YAAAA,oCAAAA,yBAAAA,gBAAiBa,KAAK,qBAAtBb,4BAAAA;QACF;QACAc,IAAAA,sBAAc,EACZ,IAAI,CAAC7G,aAAa,CAACgG,SAAS,CAAC,EAAE,EAC/B,IAAI,CAAChG,aAAa,CAACgG,SAAS,CAAC,EAAE,EAC/B,IAAI,CAAChG,aAAa,CAACgG,SAAS,CAAC,EAAE;QAGjC,yEAAyE;QACzE,gEAAgE;QAChE,MAAMc,qBAAqB,IAAI9W;QAC/B,MAAM+W,qBAAqB,IAAI/W;QAC/B,MAAMgX,yBAAyB,IAAIhX;QAEnC,MAAMiX,8BAA8B,IAAIjX;QACxC,MAAMkX,wBAAwB,IAAIlX;QAElC,MAAMmX,uBAAuB,IAAIC;QACjC,MAAMC,uBAAuB,IAAID;QACjC,MAAME,2BAA2B,IAAIF;QACrC,MAAMG,4BAA4B,IAAIH;QAEtC,MAAMI,qBAAqB,IAAIC,OAC7B,CAAC,MAAM,EAAE,IAAI,CAACjU,MAAM,CAACoI,cAAc,CAACzF,IAAI,CAAC,KAAK,EAAE,CAAC;QAGnD,MAAMuR,mBACJ,CACEC,aACAC,cACAC,8BAEF,CAACC;gBACC,IAAI;oBACFA,MAAMvL,WAAW,CAACwL,OAAO,CAAC,CAAClI,OAAOe;wBAChC,IACEA,IAAItP,UAAU,CAAC,aACfsP,IAAItP,UAAU,CAAC,WACf0W,IAAAA,2BAAoB,EAACpH,MACrB;4BACA,mDAAmD;4BACnDf,MAAMoI,MAAM,CAACF,OAAO,CAAC,CAACG;gCACpB,IAAIA,MAAM7M,EAAE,KAAKuF,KAAK;oCACpB,MAAMuH,eACJL,MAAMM,UAAU,CAACC,uBAAuB,CAACH;oCAE3C,IAAII,sBAAsB;oCAC1B,IAAIC,aAAa,IAAIC,kBAAS;oCAC9B,IAAIC,wBAAwB,IAAID,kBAAS;oCAEzCL,aAAaJ,OAAO,CAAC,CAACW;wCACpB,IACEA,IAAIC,QAAQ,IACZD,IAAIC,QAAQ,CAACvP,OAAO,CAAC,OAAO,KAAKwP,QAAQ,CAAChI,QAC1C,oCAAoC;wCACpC4G,mBAAmBqB,IAAI,CAACH,IAAIC,QAAQ,GACpC;gDAeED,oBAAAA;4CAdF,uDAAuD;4CACvD,uDAAuD;4CACvD,wDAAwD;4CACxD,sDAAsD;4CACtD,MAAMjR,OAAO,AACX7E,QAAQ,UAEPkW,UAAU,CAAC,QACXC,MAAM,CAACL,IAAIM,cAAc,GAAGC,MAAM,IAClCC,MAAM,GACN7Q,QAAQ,CAAC;4CAEZ,IACEqQ,IAAIS,KAAK,KAAK9P,yBAAc,CAAC+P,qBAAqB,IAClDV,CAAAA,wBAAAA,iBAAAA,IAAKW,SAAS,sBAAdX,qBAAAA,eAAgB3G,GAAG,qBAAnB2G,mBAAqB5H,IAAI,MAAK,UAC9B;gDACA2H,sBAAsBa,GAAG,CAAC7R;4CAC5B;4CAEA8Q,WAAWe,GAAG,CAAC7R;wCACjB,OAAO;gDASHiR,qBAAAA;4CARF,oDAAoD;4CACpD,MAAMjR,OAAOqQ,MAAMM,UAAU,CAACmB,aAAa,CACzCb,KACAR,MAAM3F,OAAO;4CAGf,IACEmG,IAAIS,KAAK,KAAK9P,yBAAc,CAAC+P,qBAAqB,IAClDV,CAAAA,wBAAAA,kBAAAA,IAAKW,SAAS,sBAAdX,sBAAAA,gBAAgB3G,GAAG,qBAAnB2G,oBAAqB5H,IAAI,MAAK,UAC9B;gDACA2H,sBAAsBa,GAAG,CAAC7R;4CAC5B;4CAEA8Q,WAAWe,GAAG,CAAC7R;4CAEf,iDAAiD;4CACjD,0BAA0B;4CAC1B,IACEmJ,IAAItP,UAAU,CAAC,WACf,qBAAqBuX,IAAI,CAACH,IAAIC,QAAQ,IAAI,KAC1C;gDACA,MAAMa,cAAcd,IAAIS,KAAK,GAAG,MAAMT,IAAIC,QAAQ;gDAClD,MAAMc,WACJlC,0BAA0BmC,GAAG,CAACF;gDAChC,IAAIC,YAAYA,aAAahS,MAAM;oDACjC6Q,sBAAsB;gDACxB;gDACAf,0BAA0BoC,GAAG,CAACH,aAAa/R;4CAC7C;wCACF;oCACF;oCAEA,MAAMgS,WAAW9B,YAAY+B,GAAG,CAAC9I;oCACjC,MAAMgJ,UAAUrB,WAAWlQ,QAAQ;oCACnC,IAAIoR,YAAYA,aAAaG,SAAS;wCACpChC,aAAa0B,GAAG,CAAC1I;oCACnB;oCACA+G,YAAYgC,GAAG,CAAC/I,KAAKgJ;oCAErB,IAAI/B,6BAA6B;wCAC/B,MAAMgC,YACJxQ,yBAAc,CAAC+P,qBAAqB,GAAG,MAAMxI;wCAC/C,MAAMkJ,iBAAiBnC,YAAY+B,GAAG,CAACG;wCACvC,MAAME,gBAAgBtB,sBAAsBpQ,QAAQ;wCACpD,IAAIyR,kBAAkBA,mBAAmBC,eAAe;4CACtDlC,4BAA4ByB,GAAG,CAAC1I;wCAClC;wCACA+G,YAAYgC,GAAG,CAACE,WAAWE;oCAC7B;oCAEA,IAAIzB,qBAAqB;wCACvBpB,sBAAsBoC,GAAG,CAAC1I;oCAC5B;gCACF;4BACF;wBACF;oBACF;gBACF,EAAE,OAAOoJ,KAAK;oBACZjZ,QAAQN,KAAK,CAACuZ;gBAChB;YACF;QAEF,IAAI,CAAChK,aAAa,CAACgG,SAAS,CAAC,EAAE,CAACK,KAAK,CAAC4D,IAAI,CAACtD,GAAG,CAC5C,8BACAe,iBAAiBP,sBAAsBL;QAEzC,IAAI,CAAC9G,aAAa,CAACgG,SAAS,CAAC,EAAE,CAACK,KAAK,CAAC4D,IAAI,CAACtD,GAAG,CAC5C,8BACAe,iBACEL,sBACAN,oBACAE;QAGJ,IAAI,CAACjH,aAAa,CAACgG,SAAS,CAAC,EAAE,CAACK,KAAK,CAAC4D,IAAI,CAACtD,GAAG,CAC5C,8BACAe,iBACEJ,0BACAN,wBACAC;QAIJ,8GAA8G;QAC9G,IAAI,CAACjH,aAAa,CAACgG,SAAS,CAAC,EAAE,CAACK,KAAK,CAAC6D,MAAM,CAACvD,GAAG,CAC9C,8BACA,CAACqD;YACC,IAAI,CAAC5V,WAAW,GAAG4V;YACnB,IAAI,CAAClV,WAAW,GAAG;YACnB,IAAI,CAACqV,gBAAgB,GAAGrZ;QAC1B;QAGF,IAAI,CAACkP,aAAa,CAACgG,SAAS,CAAC,EAAE,CAACK,KAAK,CAACK,IAAI,CAACC,GAAG,CAC5C,8BACA,CAACmB;YACC,IAAI,CAAC1T,WAAW,GAAG;YACnB,IAAI,CAACW,eAAe,GAAG+S;QACzB;QAGF,IAAI,CAAC9H,aAAa,CAACgG,SAAS,CAAC,EAAE,CAACK,KAAK,CAACK,IAAI,CAACC,GAAG,CAC5C,8BACA,CAACmB;YACC,IAAI,CAAC1T,WAAW,GAAG;YACnB,IAAI,CAACU,WAAW,GAAGgT;YAEnB,IAAI,CAAC,IAAI,CAACpU,QAAQ,EAAE;gBAClB;YACF;YAEA,MAAM,EAAE3B,WAAW,EAAE,GAAG+V;YAExB,kEAAkE;YAClE,oEAAoE;YACpE,MAAMsC,gBAAgBrY,YAAYsY,WAAW,CAACX,GAAG,CAAC;YAClD,qDAAqD;YACrD,IAAI,CAACU,eAAe;gBAClB;YACF;YAEA,gBAAgB;YAChB,IAAI,IAAI,CAACpV,sBAAsB,KAAK,MAAM;gBACxC,IAAI,CAACA,sBAAsB,GAAGoV,cAAc3S,IAAI,IAAI;gBACpD;YACF;YAEA,2DAA2D;YAC3D,IAAI2S,cAAc3S,IAAI,KAAK,IAAI,CAACzC,sBAAsB,EAAE;gBACtD;YACF;YAEA,6DAA6D;YAC7D,iEAAiE;YACjE,0EAA0E;YAC1E,2EAA2E;YAC3E,IAAI,IAAI,CAAChB,MAAM,EAAE;gBACf,MAAMsW,aAAa,IAAIta,IAAI+B,YAAYsY,WAAW,CAAC/J,IAAI;gBACvD,MAAMiK,iBAAiBC,IAAAA,iBAAU,EAC/B,IAAI,CAACL,gBAAgB,IAAI,IAAIna,OAC7Bsa;gBAGF,IACEC,eAAezT,MAAM,KAAK,KAC1ByT,eAAeE,KAAK,CAAC,CAACC,YAAcA,UAAUpZ,UAAU,CAAC,UACzD;oBACA;gBACF;gBACA,IAAI,CAAC6Y,gBAAgB,GAAGG;YAC1B;YAEA,IAAI,CAACtV,sBAAsB,GAAGoV,cAAc3S,IAAI,IAAI;YAEpD,iFAAiF;YACjF,IAAI,CAACN,IAAI,CAAC;gBACRC,QAAQC,6CAA2B,CAACC,WAAW;gBAC/CC,MAAM;YACR;QACF;QAGF,IAAI,CAACyI,aAAa,CAACqG,KAAK,CAACK,IAAI,CAACC,GAAG,CAAC,8BAA8B,CAACmB;YAC/D,MAAMtT,0BAA0B,IAAI,CAACA,uBAAuB;YAC5D,IAAI,CAACA,uBAAuB,GAAG;YAE/B,MAAMmW,oBAAoBH,IAAAA,iBAAU,EAClCzD,oBACAD;YAGF,MAAM8D,wBAAwBJ,IAAAA,iBAAU,EACtCxD,wBACAF;YAGF,MAAM+D,cAAcF,kBACjBG,MAAM,CAACF,uBACP3a,MAAM,CAAC,CAAC2Q,MAAQA,IAAItP,UAAU,CAAC;YAElC,MAAMyZ,oBAAoB;mBACrBnF,MAAM3B,IAAI,CAAC+C;mBACXpB,MAAM3B,IAAI,CAAC8C;aACf,CAAC9W,MAAM,CAAC,CAACsC,OAASyV,IAAAA,2BAAoB,EAACzV;YAExC,IAAIwY,kBAAkBjU,MAAM,GAAG,GAAG;gBAChC,IAAI,CAACK,IAAI,CAAC;oBACRuB,OAAOrB,6CAA2B,CAAC2T,kBAAkB;gBACvD;YACF;YAEA,IAAIH,YAAY/T,MAAM,GAAG,GAAG;gBAC1B,IAAI,CAACK,IAAI,CAAC;oBACRuB,OAAOrB,6CAA2B,CAAC4T,mBAAmB;oBACtDvO,OAAOiO,kBAAkB3U,GAAG,CAAC,CAACkV,KAC5B3U,IAAAA,wCAAmB,EAAC2U,GAAGzQ,KAAK,CAAC,QAAQ3D,MAAM;gBAE/C;YACF;YAEA,IACEmQ,4BAA4BkE,IAAI,IAChCjE,sBAAsBiE,IAAI,IAC1B3W,yBACA;gBACA,IAAI,CAACN,UAAU;gBACf,IAAI,CAACsD,uBAAuB,CAACsQ,MAAMrQ,IAAI;YACzC;YAEAqP,mBAAmBsE,KAAK;YACxBrE,mBAAmBqE,KAAK;YACxBpE,uBAAuBoE,KAAK;YAC5BnE,4BAA4BmE,KAAK;YACjClE,sBAAsBkE,KAAK;QAC7B;QAEA,IAAI,CAACpL,aAAa,CAACgG,SAAS,CAAC,EAAE,CAACK,KAAK,CAAC6D,MAAM,CAACvD,GAAG,CAC9C,8BACA,CAACqD;YACC,IAAI,CAAC7V,WAAW,GAAG6V;YACnB,IAAI,CAACnV,WAAW,GAAG;QACrB;QAEF,IAAI,CAACmL,aAAa,CAACgG,SAAS,CAAC,EAAE,CAACK,KAAK,CAACK,IAAI,CAACC,GAAG,CAC5C,8BACA,CAACmB;YACC,IAAI,CAAC3T,WAAW,GAAG;YACnB,IAAI,CAACU,WAAW,GAAGiT;YAEnB,MAAM,EAAE/V,WAAW,EAAE,GAAG+V;YACxB,MAAMwC,aAAa,IAAIta,IACrB;mBAAI+B,YAAYsY,WAAW,CAAC/J,IAAI;aAAG,CAACrQ,MAAM,CACxC,CAACsC,OAAS,CAAC,CAACE,IAAAA,+BAAsB,EAACF;YAIvC,IAAI,IAAI,CAAC8Y,cAAc,EAAE;gBACvB,8DAA8D;gBAC9D,0CAA0C;gBAC1C,MAAMC,aAAazb,KAAKya,YAAY,IAAI,CAACe,cAAc;gBACvD,MAAME,eAAe1b,KAAK,IAAI,CAACwb,cAAc,EAAGf;gBAEhD,IAAIgB,WAAWH,IAAI,GAAG,GAAG;oBACvB,KAAK,MAAMK,aAAaF,WAAY;wBAClC,MAAMhV,OAAO7D,IAAAA,+BAAsB,EAAC+Y;wBACpC,IAAI,CAACrU,IAAI,CAAC;4BACRC,QAAQC,6CAA2B,CAACoU,UAAU;4BAC9ClU,MAAM;gCAACjB;6BAAK;wBACd;oBACF;gBACF;gBAEA,IAAIiV,aAAaJ,IAAI,GAAG,GAAG;oBACzB,KAAK,MAAMO,eAAeH,aAAc;wBACtC,MAAMjV,OAAO7D,IAAAA,+BAAsB,EAACiZ;wBACpC,IAAI,CAACvU,IAAI,CAAC;4BACRC,QAAQC,6CAA2B,CAACsU,YAAY;4BAChDpU,MAAM;gCAACjB;6BAAK;wBACd;oBACF;gBACF;YACF;YAEA,IAAI,CAAC+U,cAAc,GAAGf;QACxB;QAGF,IAAI,CAACpS,oBAAoB,GAAG,IAAI0T,mCAAoB,CAClD,IAAI,CAAC5L,aAAa,CAACgG,SAAS,EAC5B,IAAI,CAACzR,WAAW,EAChB,IAAI,CAACwK,mBAAmB,EACxBQ;QAGF,IAAIsM,SAAS;QAEb,IAAI,CAACC,OAAO,GAAG,MAAM,IAAIhQ,QAAQ,CAACsC;gBAChB;YAAhB,MAAM0N,WAAU,sBAAA,IAAI,CAAC9L,aAAa,qBAAlB,oBAAoB1B,KAAK,CACvC,kFAAkF;YAClF,IAAI,CAACqB,oBAAoB,CAAC3J,GAAG,CAAC,CAACxC,SAAWA,OAAO+K,YAAY,GAC7D,gCAAgC;YAChC,CAACC;gBACC,IAAI,CAACqN,QAAQ;oBACXA,SAAS;oBACTzN,QAAQ0N;gBACV;YACF;QAEJ;QAEA,IAAI,CAAC3T,eAAe,GAAG4T,IAAAA,0CAAoB,EAAC;YAC1CC,aAAa,IAAI;YACjBhM,eAAe,IAAI,CAACA,aAAa;YACjCtM,UAAU,IAAI,CAACA,QAAQ;YACvBM,QAAQ,IAAI,CAACA,MAAM;YACnB4I,SAAS,IAAI,CAACrJ,GAAG;YACjB0Y,YAAY,IAAI,CAACzY,MAAM;YACvB,GAAI,IAAI,CAACA,MAAM,CAAC2E,eAAe;QAIjC;QAEA,IAAI,CAACvD,WAAW,GAAG;YACjBsX,IAAAA,uCAAoB,EAAC;gBACnBhR,eAAe,IAAI,CAAC3H,GAAG;gBACvBE,UAAU,IAAI,CAACA,QAAQ;gBACvBoB,aAAa,IAAM,IAAI,CAACA,WAAW;gBACnCC,aAAa,IAAM,IAAI,CAACA,WAAW;gBACnCC,iBAAiB,IAAM,IAAI,CAACA,eAAe;YAC7C;YACAoX,IAAAA,yCAAsB,EAAC;gBACrBtX,aAAa,IAAM,IAAI,CAACA,WAAW;gBACnCC,aAAa,IAAM,IAAI,CAACA,WAAW;gBACnCC,iBAAiB,IAAM,IAAI,CAACA,eAAe;YAC7C;YACAqX,IAAAA,8DAA8B,EAAC,IAAI,CAACnY,SAAS;YAC7CoY,IAAAA,wDAA2B;YAC3BC,IAAAA,wDAAgC;YAChCC,IAAAA,yDAA6B,EAAC;gBAC5BtY,WAAW,IAAI,CAACA,SAAS;gBACzBuY,yBACE,IAAI,CAAC7M,oBAAoB,IAAI,OACzBkG,IAAAA,kCAAmB,EAAC,IAAI,CAAClG,oBAAoB,IAC7C7O;YACR;YACA2b,IAAAA,kDAAwB,EAAC;gBACvB9Y,SAAS,IAAI,CAACA,OAAO;gBACrB+Y,kBAAkB,CAACnV;wBACjB,4CAA4C;oBAC5C,8CAA8C;oBAC9C;qBAAA,6BAAA,IAAI,CAACW,oBAAoB,qBAAzB,2BAA2ByU,oBAAoB,CAACpV;oBAEhD,IAAI,CAACJ,IAAI,CAAC;wBACRC,QAAQC,6CAA2B,CAACuV,eAAe;wBACnDrV;oBACF;gBACF;YACF;SACD;IACH;IAEOsV,WACL,EAAErY,uBAAuB,EAAwC,GAAG;QAClEA,yBAAyB;IAC3B,CAAC,EACD;YAGmB;QAFnB,mGAAmG;QACnG,IAAI,CAACA,uBAAuB,GAAGA;QAC/B,MAAMuL,cAAa,sBAAA,IAAI,CAACC,aAAa,qBAAlB,oBAAoBD,UAAU;QACjD,IAAIA,YAAY;gBACd+M;aAAAA,kBAAAA,IAAAA,oCAAc,EAAC/M,gCAAf+M,gBAA4BD,UAAU;QACxC;IACF;IAEA,MAAahW,qBAAqBP,IAAY,EAAE;YAcnC,mBAEA,mBAEA;QAjBX,MAAMyW,YAAY,CAAC,EAAEhb,WAAW,EAAiB;gBAIxCK;YAHP,MAAMA,cAAcD,aAAaJ;YACjC,MAAMib,iBAAiBC,IAAAA,kCAAgB,EAAC3W;YACxC,+FAA+F;YAC/F,OAAOlE,EAAAA,8BAAAA,WAAW,CAAC4a,eAAe,qBAA3B5a,4BAA6B0E,MAAM,IAAG,IACzC1E,WAAW,CAAC4a,eAAe,GAC3Bjb,YAAYM,MAAM;QACxB;QAEA,IAAI,IAAI,CAAC8B,WAAW,EAAE;YACpB,OAAO;gBAAC,IAAI,CAACA,WAAW;aAAC;QAC3B,OAAO,IAAI,IAAI,CAACC,WAAW,EAAE;YAC3B,OAAO;gBAAC,IAAI,CAACA,WAAW;aAAC;QAC3B,OAAO,KAAI,oBAAA,IAAI,CAACS,WAAW,qBAAhB,kBAAkBqY,SAAS,IAAI;YACxC,OAAOH,UAAU,IAAI,CAAClY,WAAW;QACnC,OAAO,KAAI,oBAAA,IAAI,CAACC,WAAW,qBAAhB,kBAAkBoY,SAAS,IAAI;YACxC,OAAOH,UAAU,IAAI,CAACjY,WAAW;QACnC,OAAO,KAAI,wBAAA,IAAI,CAACC,eAAe,qBAApB,sBAAsBmY,SAAS,IAAI;YAC5C,OAAOH,UAAU,IAAI,CAAChY,eAAe;QACvC,OAAO;YACL,OAAO,EAAE;QACX;IACF;IAEOoC,KAAKC,MAAwB,EAAQ;QAC1C,IAAI,CAACc,oBAAoB,CAAEiV,OAAO,CAAC/V;IACrC;IAEA,MAAaV,WAAW,EACtBJ,IAAI,EACJK,UAAU,EACVwM,QAAQ,EACRiK,UAAU,EACVC,KAAK,EACLhc,GAAG,EAQJ,EAAiB;QAChB,OAAO,IAAI,CAAC4D,eAAe,CACxBwD,UAAU,CAAC,eAAe;YACzB6U,WAAWhX;QACb,GACCkF,YAAY,CAAC;gBAYL;YAXP,wDAAwD;YACxD,IAAIlF,SAAS,aAAaE,yBAAa,CAACC,OAAO,CAACH,UAAU,CAAC,GAAG;gBAC5D;YACF;YACA,MAAM7F,QAAQkG,aACV,IAAI,CAACxC,WAAW,GAChB,IAAI,CAACC,WAAW,IAAI,IAAI,CAACD,WAAW;YACxC,IAAI1D,OAAO;gBACT,MAAMA;YACR;YAEA,QAAO,wBAAA,IAAI,CAAC0H,eAAe,qBAApB,sBAAsBzB,UAAU,CAAC;gBACtCJ;gBACA6M;gBACAiK;gBACAC;gBACAhc;YACF;QACF;IACJ;IAEOkc,QAAQ;YACb;SAAA,6BAAA,IAAI,CAACrV,oBAAoB,qBAAzB,2BAA2BqV,KAAK;IAClC;AACF", "ignoreList": [0]}