{"version": 3, "sources": ["../../src/client/page-bootstrap.ts"], "sourcesContent": ["import '../lib/require-instrumentation-client'\nimport { hydrate, router } from './'\nimport initOnDemandEntries from './dev/on-demand-entries-client'\nimport { displayContent } from './dev/fouc'\nimport {\n  connectHMR,\n  addMessageListener,\n} from './dev/hot-reloader/pages/websocket'\nimport {\n  assign,\n  urlQueryToSearchParams,\n} from '../shared/lib/router/utils/querystring'\nimport { HMR_ACTIONS_SENT_TO_BROWSER } from '../server/dev/hot-reloader-types'\nimport { RuntimeErrorHandler } from './dev/runtime-error-handler'\nimport { REACT_REFRESH_FULL_RELOAD_FROM_ERROR } from './dev/hot-reloader/shared'\nimport { performFullReload } from './dev/hot-reloader/pages/hot-reloader-pages'\nimport { dispatcher } from 'next/dist/compiled/next-devtools'\n\nexport function pageBootstrap(assetPrefix: string) {\n  connectHMR({ assetPrefix, path: '/_next/webpack-hmr' })\n\n  return hydrate({ beforeRender: displayContent }).then(() => {\n    initOnDemandEntries()\n\n    let reloading = false\n\n    addMessageListener((payload) => {\n      if (reloading) return\n      if ('action' in payload) {\n        switch (payload.action) {\n          case HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ERROR: {\n            const { stack, message } = JSON.parse(payload.errorJSON)\n            const error = new Error(message)\n            error.stack = stack\n            throw error\n          }\n          case HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE: {\n            reloading = true\n            window.location.reload()\n            break\n          }\n          case HMR_ACTIONS_SENT_TO_BROWSER.DEV_PAGES_MANIFEST_UPDATE: {\n            fetch(\n              `${assetPrefix}/_next/static/development/_devPagesManifest.json`\n            )\n              .then((res) => res.json())\n              .then((manifest) => {\n                window.__DEV_PAGES_MANIFEST = manifest\n              })\n              .catch((err) => {\n                console.log(`Failed to fetch devPagesManifest`, err)\n              })\n            break\n          }\n          case HMR_ACTIONS_SENT_TO_BROWSER.ADDED_PAGE:\n          case HMR_ACTIONS_SENT_TO_BROWSER.REMOVED_PAGE:\n          case HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES:\n          case HMR_ACTIONS_SENT_TO_BROWSER.SYNC:\n          case HMR_ACTIONS_SENT_TO_BROWSER.BUILT:\n          case HMR_ACTIONS_SENT_TO_BROWSER.BUILDING:\n          case HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE:\n          case HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED:\n          case HMR_ACTIONS_SENT_TO_BROWSER.ISR_MANIFEST:\n          case HMR_ACTIONS_SENT_TO_BROWSER.DEVTOOLS_CONFIG:\n            // Most of these action types are handled in\n            // src/client/dev/hot-reloader/pages/hot-reloader-pages.ts\n            break\n          default:\n            payload satisfies never\n        }\n      } else if ('event' in payload) {\n        switch (payload.event) {\n          case HMR_ACTIONS_SENT_TO_BROWSER.MIDDLEWARE_CHANGES: {\n            return window.location.reload()\n          }\n          case HMR_ACTIONS_SENT_TO_BROWSER.CLIENT_CHANGES: {\n            // This is used in `../server/dev/turbopack-utils.ts`.\n            const isOnErrorPage = window.next.router.pathname === '/_error'\n            // On the error page we want to reload the page when a page was changed\n            if (isOnErrorPage) {\n              if (RuntimeErrorHandler.hadRuntimeError) {\n                console.warn(REACT_REFRESH_FULL_RELOAD_FROM_ERROR)\n              }\n              reloading = true\n              performFullReload(null)\n            }\n            break\n          }\n          case HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ONLY_CHANGES: {\n            if (RuntimeErrorHandler.hadRuntimeError) {\n              console.warn(REACT_REFRESH_FULL_RELOAD_FROM_ERROR)\n              performFullReload(null)\n            }\n\n            const { pages } = payload\n\n            // Make sure to reload when the dev-overlay is showing for an\n            // API route\n            // TODO: Fix `__NEXT_PAGE` type\n            if (pages.includes(router.query.__NEXT_PAGE as string)) {\n              return window.location.reload()\n            }\n\n            if (!router.clc && pages.includes(router.pathname)) {\n              console.log('Refreshing page data due to server-side change')\n              dispatcher.buildingIndicatorShow()\n              const clearIndicator = dispatcher.buildingIndicatorHide\n\n              router\n                .replace(\n                  router.pathname +\n                    '?' +\n                    String(\n                      assign(\n                        urlQueryToSearchParams(router.query),\n                        new URLSearchParams(location.search)\n                      )\n                    ),\n                  router.asPath,\n                  { scroll: false }\n                )\n                .catch(() => {\n                  // trigger hard reload when failing to refresh data\n                  // to show error overlay properly\n                  location.reload()\n                })\n                .finally(clearIndicator)\n            }\n            break\n          }\n          default:\n            payload satisfies never\n        }\n      }\n    })\n  })\n}\n"], "names": ["hydrate", "router", "initOnDemandEntries", "displayContent", "connectHMR", "addMessageListener", "assign", "urlQueryToSearchParams", "HMR_ACTIONS_SENT_TO_BROWSER", "RuntimeError<PERSON>andler", "REACT_REFRESH_FULL_RELOAD_FROM_ERROR", "performFullReload", "dispatcher", "pageBootstrap", "assetPrefix", "path", "beforeRender", "then", "reloading", "payload", "action", "SERVER_ERROR", "stack", "message", "JSON", "parse", "errorJSON", "error", "Error", "RELOAD_PAGE", "window", "location", "reload", "DEV_PAGES_MANIFEST_UPDATE", "fetch", "res", "json", "manifest", "__DEV_PAGES_MANIFEST", "catch", "err", "console", "log", "ADDED_PAGE", "REMOVED_PAGE", "SERVER_COMPONENT_CHANGES", "SYNC", "BUILT", "BUILDING", "TURBOPACK_MESSAGE", "TURBOPACK_CONNECTED", "ISR_MANIFEST", "DEVTOOLS_CONFIG", "event", "MIDDLEWARE_CHANGES", "CLIENT_CHANGES", "isOnErrorPage", "next", "pathname", "hadRuntimeError", "warn", "SERVER_ONLY_CHANGES", "pages", "includes", "query", "__NEXT_PAGE", "clc", "buildingIndicatorShow", "clearIndicator", "buildingIndicatorHide", "replace", "String", "URLSearchParams", "search", "<PERSON><PERSON><PERSON>", "scroll", "finally"], "mappings": "AAAA,OAAO,wCAAuC;AAC9C,SAASA,OAAO,EAAEC,MAAM,QAAQ,KAAI;AACpC,OAAOC,yBAAyB,iCAAgC;AAChE,SAASC,cAAc,QAAQ,aAAY;AAC3C,SACEC,UAAU,EACVC,kBAAkB,QACb,qCAAoC;AAC3C,SACEC,MAAM,EACNC,sBAAsB,QACjB,yCAAwC;AAC/C,SAASC,2BAA2B,QAAQ,mCAAkC;AAC9E,SAASC,mBAAmB,QAAQ,8BAA6B;AACjE,SAASC,oCAAoC,QAAQ,4BAA2B;AAChF,SAASC,iBAAiB,QAAQ,8CAA6C;AAC/E,SAASC,UAAU,QAAQ,mCAAkC;AAE7D,OAAO,SAASC,cAAcC,WAAmB;IAC/CV,WAAW;QAAEU;QAAaC,MAAM;IAAqB;IAErD,OAAOf,QAAQ;QAAEgB,cAAcb;IAAe,GAAGc,IAAI,CAAC;QACpDf;QAEA,IAAIgB,YAAY;QAEhBb,mBAAmB,CAACc;YAClB,IAAID,WAAW;YACf,IAAI,YAAYC,SAAS;gBACvB,OAAQA,QAAQC,MAAM;oBACpB,KAAKZ,4BAA4Ba,YAAY;wBAAE;4BAC7C,MAAM,EAAEC,KAAK,EAAEC,OAAO,EAAE,GAAGC,KAAKC,KAAK,CAACN,QAAQO,SAAS;4BACvD,MAAMC,QAAQ,qBAAkB,CAAlB,IAAIC,MAAML,UAAV,qBAAA;uCAAA;4CAAA;8CAAA;4BAAiB;4BAC/BI,MAAML,KAAK,GAAGA;4BACd,MAAMK;wBACR;oBACA,KAAKnB,4BAA4BqB,WAAW;wBAAE;4BAC5CX,YAAY;4BACZY,OAAOC,QAAQ,CAACC,MAAM;4BACtB;wBACF;oBACA,KAAKxB,4BAA4ByB,yBAAyB;wBAAE;4BAC1DC,MACE,AAAC,KAAEpB,cAAY,oDAEdG,IAAI,CAAC,CAACkB,MAAQA,IAAIC,IAAI,IACtBnB,IAAI,CAAC,CAACoB;gCACLP,OAAOQ,oBAAoB,GAAGD;4BAChC,GACCE,KAAK,CAAC,CAACC;gCACNC,QAAQC,GAAG,CAAE,oCAAmCF;4BAClD;4BACF;wBACF;oBACA,KAAKhC,4BAA4BmC,UAAU;oBAC3C,KAAKnC,4BAA4BoC,YAAY;oBAC7C,KAAKpC,4BAA4BqC,wBAAwB;oBACzD,KAAKrC,4BAA4BsC,IAAI;oBACrC,KAAKtC,4BAA4BuC,KAAK;oBACtC,KAAKvC,4BAA4BwC,QAAQ;oBACzC,KAAKxC,4BAA4ByC,iBAAiB;oBAClD,KAAKzC,4BAA4B0C,mBAAmB;oBACpD,KAAK1C,4BAA4B2C,YAAY;oBAC7C,KAAK3C,4BAA4B4C,eAAe;wBAG9C;oBACF;wBACEjC;gBACJ;YACF,OAAO,IAAI,WAAWA,SAAS;gBAC7B,OAAQA,QAAQkC,KAAK;oBACnB,KAAK7C,4BAA4B8C,kBAAkB;wBAAE;4BACnD,OAAOxB,OAAOC,QAAQ,CAACC,MAAM;wBAC/B;oBACA,KAAKxB,4BAA4B+C,cAAc;wBAAE;4BAC/C,sDAAsD;4BACtD,MAAMC,gBAAgB1B,OAAO2B,IAAI,CAACxD,MAAM,CAACyD,QAAQ,KAAK;4BACtD,uEAAuE;4BACvE,IAAIF,eAAe;gCACjB,IAAI/C,oBAAoBkD,eAAe,EAAE;oCACvClB,QAAQmB,IAAI,CAAClD;gCACf;gCACAQ,YAAY;gCACZP,kBAAkB;4BACpB;4BACA;wBACF;oBACA,KAAKH,4BAA4BqD,mBAAmB;wBAAE;4BACpD,IAAIpD,oBAAoBkD,eAAe,EAAE;gCACvClB,QAAQmB,IAAI,CAAClD;gCACbC,kBAAkB;4BACpB;4BAEA,MAAM,EAAEmD,KAAK,EAAE,GAAG3C;4BAElB,6DAA6D;4BAC7D,YAAY;4BACZ,+BAA+B;4BAC/B,IAAI2C,MAAMC,QAAQ,CAAC9D,OAAO+D,KAAK,CAACC,WAAW,GAAa;gCACtD,OAAOnC,OAAOC,QAAQ,CAACC,MAAM;4BAC/B;4BAEA,IAAI,CAAC/B,OAAOiE,GAAG,IAAIJ,MAAMC,QAAQ,CAAC9D,OAAOyD,QAAQ,GAAG;gCAClDjB,QAAQC,GAAG,CAAC;gCACZ9B,WAAWuD,qBAAqB;gCAChC,MAAMC,iBAAiBxD,WAAWyD,qBAAqB;gCAEvDpE,OACGqE,OAAO,CACNrE,OAAOyD,QAAQ,GACb,MACAa,OACEjE,OACEC,uBAAuBN,OAAO+D,KAAK,GACnC,IAAIQ,gBAAgBzC,SAAS0C,MAAM,KAGzCxE,OAAOyE,MAAM,EACb;oCAAEC,QAAQ;gCAAM,GAEjBpC,KAAK,CAAC;oCACL,mDAAmD;oCACnD,iCAAiC;oCACjCR,SAASC,MAAM;gCACjB,GACC4C,OAAO,CAACR;4BACb;4BACA;wBACF;oBACA;wBACEjD;gBACJ;YACF;QACF;IACF;AACF", "ignoreList": [0]}