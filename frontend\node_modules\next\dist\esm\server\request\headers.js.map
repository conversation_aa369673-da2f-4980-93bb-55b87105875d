{"version": 3, "sources": ["../../../src/server/request/headers.ts"], "sourcesContent": ["import {\n  HeadersAdapter,\n  type ReadonlyHeaders,\n} from '../web/spec-extension/adapters/headers'\nimport {\n  workAsyncStorage,\n  type WorkStore,\n} from '../app-render/work-async-storage.external'\nimport {\n  throwForMissingRequestStore,\n  workUnitAsyncStorage,\n  type PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\nimport {\n  postponeWithTracking,\n  throwToInterruptStaticGeneration,\n  trackDynamicDataInDynamicRender,\n  trackSynchronousRequestDataAccessInDev,\n} from '../app-render/dynamic-rendering'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport {\n  makeDevtoolsIOAwarePromise,\n  makeHangingPromise,\n} from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport { isRequestAPICallableInsideAfter } from './utils'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { ReflectAdapter } from '../web/spec-extension/adapters/reflect'\n\n/**\n * In this version of Next.js `headers()` returns a Promise however you can still reference the properties of the underlying Headers instance\n * synchronously to facilitate migration. The `UnsafeUnwrappedHeaders` type is added to your code by a codemod that attempts to automatically\n * updates callsites to reflect the new Promise return type. There are some cases where `headers()` cannot be automatically converted, namely\n * when it is used inside a synchronous function and we can't be sure the function can be made async automatically. In these cases we add an\n * explicit type case to `UnsafeUnwrappedHeaders` to enable typescript to allow for the synchronous usage only where it is actually necessary.\n *\n * You should should update these callsites to either be async functions where the `headers()` value can be awaited or you should call `headers()`\n * from outside and await the return value before passing it into this function.\n *\n * You can find instances that require manual migration by searching for `UnsafeUnwrappedHeaders` in your codebase or by search for a comment that\n * starts with `@next-codemod-error`.\n *\n * In a future version of Next.js `headers()` will only return a Promise and you will not be able to access the underlying Headers instance\n * without awaiting the return value first. When this change happens the type `UnsafeUnwrappedHeaders` will be updated to reflect that is it no longer\n * usable.\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedHeaders = ReadonlyHeaders\n\n/**\n * This function allows you to read the HTTP incoming request headers in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers) and\n * [Middleware](https://nextjs.org/docs/app/building-your-application/routing/middleware).\n *\n * Read more: [Next.js Docs: `headers`](https://nextjs.org/docs/app/api-reference/functions/headers)\n */\nexport function headers(): Promise<ReadonlyHeaders> {\n  const callingExpression = 'headers'\n  const workStore = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (workStore) {\n    if (\n      workUnitStore &&\n      workUnitStore.phase === 'after' &&\n      !isRequestAPICallableInsideAfter()\n    ) {\n      throw new Error(\n        `Route ${workStore.route} used \"headers\" inside \"after(...)\". This is not supported. If you need this data inside an \"after\" callback, use \"headers\" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`\n      )\n    }\n\n    if (workStore.forceStatic) {\n      // When using forceStatic we override all other logic and always just return an empty\n      // headers object without tracking\n      const underlyingHeaders = HeadersAdapter.seal(new Headers({}))\n      return makeUntrackedExoticHeaders(underlyingHeaders)\n    }\n\n    if (workUnitStore) {\n      switch (workUnitStore.type) {\n        case 'cache': {\n          const error = new Error(\n            `Route ${workStore.route} used \"headers\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"headers\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n          )\n          Error.captureStackTrace(error, headers)\n          workStore.invalidDynamicUsageError ??= error\n          throw error\n        }\n        case 'private-cache': {\n          const error = new Error(\n            `Route ${workStore.route} used \"headers\" inside \"use cache: private\". Accessing \"headers\" inside a private cache scope is not supported. If you need this data inside a cached function use \"headers\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n          )\n          Error.captureStackTrace(error, headers)\n          workStore.invalidDynamicUsageError ??= error\n          throw error\n        }\n        case 'unstable-cache':\n          throw new Error(\n            `Route ${workStore.route} used \"headers\" inside a function cached with \"unstable_cache(...)\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"headers\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`\n          )\n        case 'prerender':\n        case 'prerender-client':\n        case 'prerender-runtime':\n        case 'prerender-ppr':\n        case 'prerender-legacy':\n        case 'request':\n          break\n        default:\n          workUnitStore satisfies never\n      }\n    }\n\n    if (workStore.dynamicShouldError) {\n      throw new StaticGenBailoutError(\n        `Route ${workStore.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`headers\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n      )\n    }\n\n    if (workUnitStore) {\n      switch (workUnitStore.type) {\n        case 'prerender':\n        case 'prerender-runtime':\n          return makeHangingHeaders(workStore, workUnitStore)\n        case 'prerender-client':\n          const exportName = '`headers`'\n          throw new InvariantError(\n            `${exportName} must not be used within a client component. Next.js should be preventing ${exportName} from being included in client components statically, but did not in this case.`\n          )\n        case 'prerender-ppr':\n          // PPR Prerender (no cacheComponents)\n          // We are prerendering with PPR. We need track dynamic access here eagerly\n          // to keep continuity with how headers has worked in PPR without cacheComponents.\n          // TODO consider switching the semantic to throw on property access instead\n          return postponeWithTracking(\n            workStore.route,\n            callingExpression,\n            workUnitStore.dynamicTracking\n          )\n        case 'prerender-legacy':\n          // Legacy Prerender\n          // We are in a legacy static generation mode while prerendering\n          // We track dynamic access here so we don't need to wrap the headers in\n          // individual property access tracking.\n          return throwToInterruptStaticGeneration(\n            callingExpression,\n            workStore,\n            workUnitStore\n          )\n        case 'request':\n          trackDynamicDataInDynamicRender(workUnitStore)\n\n          if (process.env.NODE_ENV === 'development') {\n            // Semantically we only need the dev tracking when running in `next dev`\n            // but since you would never use next dev with production NODE_ENV we use this\n            // as a proxy so we can statically exclude this code from production builds.\n            if (process.env.__NEXT_CACHE_COMPONENTS) {\n              return makeUntrackedHeadersWithDevWarnings(\n                workUnitStore.headers,\n                workStore?.route\n              )\n            }\n\n            return makeUntrackedExoticHeadersWithDevWarnings(\n              workUnitStore.headers,\n              workStore?.route\n            )\n          } else {\n            if (process.env.__NEXT_CACHE_COMPONENTS) {\n              return makeUntrackedHeaders(workUnitStore.headers)\n            }\n\n            return makeUntrackedExoticHeaders(workUnitStore.headers)\n          }\n          break\n        default:\n          workUnitStore satisfies never\n      }\n    }\n  }\n\n  // If we end up here, there was no work store or work unit store present.\n  throwForMissingRequestStore(callingExpression)\n}\n\ninterface CacheLifetime {}\nconst CachedHeaders = new WeakMap<CacheLifetime, Promise<ReadonlyHeaders>>()\n\nfunction makeHangingHeaders(\n  workStore: WorkStore,\n  prerenderStore: PrerenderStoreModern\n): Promise<ReadonlyHeaders> {\n  const cachedHeaders = CachedHeaders.get(prerenderStore)\n  if (cachedHeaders) {\n    return cachedHeaders\n  }\n\n  const promise = makeHangingPromise<ReadonlyHeaders>(\n    prerenderStore.renderSignal,\n    workStore.route,\n    '`headers()`'\n  )\n  CachedHeaders.set(prerenderStore, promise)\n\n  return promise\n}\n\nfunction makeUntrackedHeaders(\n  underlyingHeaders: ReadonlyHeaders\n): Promise<ReadonlyHeaders> {\n  const cachedHeaders = CachedHeaders.get(underlyingHeaders)\n  if (cachedHeaders) {\n    return cachedHeaders\n  }\n\n  const promise = Promise.resolve(underlyingHeaders)\n  CachedHeaders.set(underlyingHeaders, promise)\n\n  return promise\n}\n\nfunction makeUntrackedExoticHeaders(\n  underlyingHeaders: ReadonlyHeaders\n): Promise<ReadonlyHeaders> {\n  const cachedHeaders = CachedHeaders.get(underlyingHeaders)\n  if (cachedHeaders) {\n    return cachedHeaders\n  }\n\n  const promise = Promise.resolve(underlyingHeaders)\n  CachedHeaders.set(underlyingHeaders, promise)\n\n  Object.defineProperties(promise, {\n    append: {\n      value: underlyingHeaders.append.bind(underlyingHeaders),\n    },\n    delete: {\n      value: underlyingHeaders.delete.bind(underlyingHeaders),\n    },\n    get: {\n      value: underlyingHeaders.get.bind(underlyingHeaders),\n    },\n    has: {\n      value: underlyingHeaders.has.bind(underlyingHeaders),\n    },\n    set: {\n      value: underlyingHeaders.set.bind(underlyingHeaders),\n    },\n    getSetCookie: {\n      value: underlyingHeaders.getSetCookie.bind(underlyingHeaders),\n    },\n    forEach: {\n      value: underlyingHeaders.forEach.bind(underlyingHeaders),\n    },\n    keys: {\n      value: underlyingHeaders.keys.bind(underlyingHeaders),\n    },\n    values: {\n      value: underlyingHeaders.values.bind(underlyingHeaders),\n    },\n    entries: {\n      value: underlyingHeaders.entries.bind(underlyingHeaders),\n    },\n    [Symbol.iterator]: {\n      value: underlyingHeaders[Symbol.iterator].bind(underlyingHeaders),\n    },\n  } satisfies HeadersExtensions)\n\n  return promise\n}\n\nfunction makeUntrackedExoticHeadersWithDevWarnings(\n  underlyingHeaders: ReadonlyHeaders,\n  route?: string\n): Promise<ReadonlyHeaders> {\n  const cachedHeaders = CachedHeaders.get(underlyingHeaders)\n  if (cachedHeaders) {\n    return cachedHeaders\n  }\n\n  const promise = makeDevtoolsIOAwarePromise(underlyingHeaders)\n\n  CachedHeaders.set(underlyingHeaders, promise)\n\n  Object.defineProperties(promise, {\n    append: {\n      value: function append() {\n        const expression = `\\`headers().append(${describeNameArg(arguments[0])}, ...)\\``\n        syncIODev(route, expression)\n        return underlyingHeaders.append.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    delete: {\n      value: function _delete() {\n        const expression = `\\`headers().delete(${describeNameArg(arguments[0])})\\``\n        syncIODev(route, expression)\n        return underlyingHeaders.delete.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    get: {\n      value: function get() {\n        const expression = `\\`headers().get(${describeNameArg(arguments[0])})\\``\n        syncIODev(route, expression)\n        return underlyingHeaders.get.apply(underlyingHeaders, arguments as any)\n      },\n    },\n    has: {\n      value: function has() {\n        const expression = `\\`headers().has(${describeNameArg(arguments[0])})\\``\n        syncIODev(route, expression)\n        return underlyingHeaders.has.apply(underlyingHeaders, arguments as any)\n      },\n    },\n    set: {\n      value: function set() {\n        const expression = `\\`headers().set(${describeNameArg(arguments[0])}, ...)\\``\n        syncIODev(route, expression)\n        return underlyingHeaders.set.apply(underlyingHeaders, arguments as any)\n      },\n    },\n    getSetCookie: {\n      value: function getSetCookie() {\n        const expression = '`headers().getSetCookie()`'\n        syncIODev(route, expression)\n        return underlyingHeaders.getSetCookie.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    forEach: {\n      value: function forEach() {\n        const expression = '`headers().forEach(...)`'\n        syncIODev(route, expression)\n        return underlyingHeaders.forEach.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    keys: {\n      value: function keys() {\n        const expression = '`headers().keys()`'\n        syncIODev(route, expression)\n        return underlyingHeaders.keys.apply(underlyingHeaders, arguments as any)\n      },\n    },\n    values: {\n      value: function values() {\n        const expression = '`headers().values()`'\n        syncIODev(route, expression)\n        return underlyingHeaders.values.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    entries: {\n      value: function entries() {\n        const expression = '`headers().entries()`'\n        syncIODev(route, expression)\n        return underlyingHeaders.entries.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    [Symbol.iterator]: {\n      value: function () {\n        const expression = '`...headers()` or similar iteration'\n        syncIODev(route, expression)\n        return underlyingHeaders[Symbol.iterator].apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n  } satisfies HeadersExtensions)\n\n  return promise\n}\n\n// Similar to `makeUntrackedExoticHeadersWithDevWarnings`, but just logging the\n// sync access without actually defining the headers properties on the promise.\nfunction makeUntrackedHeadersWithDevWarnings(\n  underlyingHeaders: ReadonlyHeaders,\n  route?: string\n): Promise<ReadonlyHeaders> {\n  const cachedHeaders = CachedHeaders.get(underlyingHeaders)\n  if (cachedHeaders) {\n    return cachedHeaders\n  }\n\n  const promise = makeDevtoolsIOAwarePromise(underlyingHeaders)\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      switch (prop) {\n        case Symbol.iterator: {\n          warnForSyncAccess(route, '`...headers()` or similar iteration')\n          break\n        }\n        case 'append':\n        case 'delete':\n        case 'get':\n        case 'has':\n        case 'set':\n        case 'getSetCookie':\n        case 'forEach':\n        case 'keys':\n        case 'values':\n        case 'entries': {\n          warnForSyncAccess(route, `\\`headers().${prop}\\``)\n          break\n        }\n        default: {\n          // We only warn for well-defined properties of the headers object.\n        }\n      }\n\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n  })\n\n  CachedHeaders.set(underlyingHeaders, proxiedPromise)\n\n  return proxiedPromise\n}\n\nfunction describeNameArg(arg: unknown) {\n  return typeof arg === 'string' ? `'${arg}'` : '...'\n}\n\nfunction syncIODev(route: string | undefined, expression: string) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'request':\n        if (workUnitStore.prerenderPhase === true) {\n          // When we're rendering dynamically in dev, we need to advance out of\n          // the Prerender environment when we read Request data synchronously.\n          trackSynchronousRequestDataAccessInDev(workUnitStore)\n        }\n        break\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-runtime':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n\n  // In all cases we warn normally\n  warnForSyncAccess(route, expression)\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createHeadersAccessError\n)\n\nfunction createHeadersAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`headers()\\` should be awaited before using its value. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\ntype HeadersExtensions = {\n  [K in keyof ReadonlyHeaders]: unknown\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "workAsyncStorage", "throwForMissingRequestStore", "workUnitAsyncStorage", "postponeWithTracking", "throwToInterruptStaticGeneration", "trackDynamicDataInDynamicRender", "trackSynchronousRequestDataAccessInDev", "StaticGenBailoutError", "makeDevtoolsIOAwarePromise", "makeHangingPromise", "createDedupedByCallsiteServerErrorLoggerDev", "isRequestAPICallableInsideAfter", "InvariantError", "ReflectAdapter", "headers", "callingExpression", "workStore", "getStore", "workUnitStore", "phase", "Error", "route", "forceStatic", "underlyingHeaders", "seal", "Headers", "makeUntrackedExoticHeaders", "type", "error", "captureStackTrace", "invalidDynamicUsageError", "dynamicShouldError", "makeHangingHeaders", "exportName", "dynamicTracking", "process", "env", "NODE_ENV", "__NEXT_CACHE_COMPONENTS", "makeUntrackedHeadersWithDevWarnings", "makeUntrackedExoticHeadersWithDevWarnings", "makeUntrackedHeaders", "CachedHeaders", "WeakMap", "prerenderStore", "cachedHeaders", "get", "promise", "renderSignal", "set", "Promise", "resolve", "Object", "defineProperties", "append", "value", "bind", "delete", "has", "getSetCookie", "for<PERSON>ach", "keys", "values", "entries", "Symbol", "iterator", "expression", "describeNameArg", "arguments", "syncIODev", "apply", "_delete", "proxiedPromise", "Proxy", "target", "prop", "receiver", "warnForSyncAccess", "arg", "prerenderPhase", "createHeadersAccessError", "prefix"], "mappings": "AAAA,SACEA,cAAc,QAET,yCAAwC;AAC/C,SACEC,gBAAgB,QAEX,4CAA2C;AAClD,SACEC,2BAA2B,EAC3BC,oBAAoB,QAEf,iDAAgD;AACvD,SACEC,oBAAoB,EACpBC,gCAAgC,EAChCC,+BAA+B,EAC/BC,sCAAsC,QACjC,kCAAiC;AACxC,SAASC,qBAAqB,QAAQ,oDAAmD;AACzF,SACEC,0BAA0B,EAC1BC,kBAAkB,QACb,6BAA4B;AACnC,SAASC,2CAA2C,QAAQ,oDAAmD;AAC/G,SAASC,+BAA+B,QAAQ,UAAS;AACzD,SAASC,cAAc,QAAQ,mCAAkC;AACjE,SAASC,cAAc,QAAQ,yCAAwC;AAyBvE;;;;;;;;CAQC,GACD,OAAO,SAASC;IACd,MAAMC,oBAAoB;IAC1B,MAAMC,YAAYhB,iBAAiBiB,QAAQ;IAC3C,MAAMC,gBAAgBhB,qBAAqBe,QAAQ;IAEnD,IAAID,WAAW;QACb,IACEE,iBACAA,cAAcC,KAAK,KAAK,WACxB,CAACR,mCACD;YACA,MAAM,qBAEL,CAFK,IAAIS,MACR,CAAC,MAAM,EAAEJ,UAAUK,KAAK,CAAC,yOAAyO,CAAC,GAD/P,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIL,UAAUM,WAAW,EAAE;YACzB,qFAAqF;YACrF,kCAAkC;YAClC,MAAMC,oBAAoBxB,eAAeyB,IAAI,CAAC,IAAIC,QAAQ,CAAC;YAC3D,OAAOC,2BAA2BH;QACpC;QAEA,IAAIL,eAAe;YACjB,OAAQA,cAAcS,IAAI;gBACxB,KAAK;oBAAS;wBACZ,MAAMC,QAAQ,qBAEb,CAFa,IAAIR,MAChB,CAAC,MAAM,EAAEJ,UAAUK,KAAK,CAAC,0UAA0U,CAAC,GADxV,qBAAA;mCAAA;wCAAA;0CAAA;wBAEd;wBACAD,MAAMS,iBAAiB,CAACD,OAAOd;wBAC/BE,UAAUc,wBAAwB,KAAKF;wBACvC,MAAMA;oBACR;gBACA,KAAK;oBAAiB;wBACpB,MAAMA,QAAQ,qBAEb,CAFa,IAAIR,MAChB,CAAC,MAAM,EAAEJ,UAAUK,KAAK,CAAC,gVAAgV,CAAC,GAD9V,qBAAA;mCAAA;wCAAA;0CAAA;wBAEd;wBACAD,MAAMS,iBAAiB,CAACD,OAAOd;wBAC/BE,UAAUc,wBAAwB,KAAKF;wBACvC,MAAMA;oBACR;gBACA,KAAK;oBACH,MAAM,qBAEL,CAFK,IAAIR,MACR,CAAC,MAAM,EAAEJ,UAAUK,KAAK,CAAC,mXAAmX,CAAC,GADzY,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBACH;gBACF;oBACEH;YACJ;QACF;QAEA,IAAIF,UAAUe,kBAAkB,EAAE;YAChC,MAAM,qBAEL,CAFK,IAAIxB,sBACR,CAAC,MAAM,EAAES,UAAUK,KAAK,CAAC,iNAAiN,CAAC,GADvO,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIH,eAAe;YACjB,OAAQA,cAAcS,IAAI;gBACxB,KAAK;gBACL,KAAK;oBACH,OAAOK,mBAAmBhB,WAAWE;gBACvC,KAAK;oBACH,MAAMe,aAAa;oBACnB,MAAM,qBAEL,CAFK,IAAIrB,eACR,GAAGqB,WAAW,0EAA0E,EAAEA,WAAW,+EAA+E,CAAC,GADjL,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF,KAAK;oBACH,qCAAqC;oBACrC,0EAA0E;oBAC1E,iFAAiF;oBACjF,2EAA2E;oBAC3E,OAAO9B,qBACLa,UAAUK,KAAK,EACfN,mBACAG,cAAcgB,eAAe;gBAEjC,KAAK;oBACH,mBAAmB;oBACnB,+DAA+D;oBAC/D,uEAAuE;oBACvE,uCAAuC;oBACvC,OAAO9B,iCACLW,mBACAC,WACAE;gBAEJ,KAAK;oBACHb,gCAAgCa;oBAEhC,IAAIiB,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;wBAC1C,wEAAwE;wBACxE,8EAA8E;wBAC9E,4EAA4E;wBAC5E,IAAIF,QAAQC,GAAG,CAACE,uBAAuB,EAAE;4BACvC,OAAOC,oCACLrB,cAAcJ,OAAO,EACrBE,6BAAAA,UAAWK,KAAK;wBAEpB;wBAEA,OAAOmB,0CACLtB,cAAcJ,OAAO,EACrBE,6BAAAA,UAAWK,KAAK;oBAEpB,OAAO;wBACL,IAAIc,QAAQC,GAAG,CAACE,uBAAuB,EAAE;4BACvC,OAAOG,qBAAqBvB,cAAcJ,OAAO;wBACnD;wBAEA,OAAOY,2BAA2BR,cAAcJ,OAAO;oBACzD;oBACA;gBACF;oBACEI;YACJ;QACF;IACF;IAEA,yEAAyE;IACzEjB,4BAA4Bc;AAC9B;AAGA,MAAM2B,gBAAgB,IAAIC;AAE1B,SAASX,mBACPhB,SAAoB,EACpB4B,cAAoC;IAEpC,MAAMC,gBAAgBH,cAAcI,GAAG,CAACF;IACxC,IAAIC,eAAe;QACjB,OAAOA;IACT;IAEA,MAAME,UAAUtC,mBACdmC,eAAeI,YAAY,EAC3BhC,UAAUK,KAAK,EACf;IAEFqB,cAAcO,GAAG,CAACL,gBAAgBG;IAElC,OAAOA;AACT;AAEA,SAASN,qBACPlB,iBAAkC;IAElC,MAAMsB,gBAAgBH,cAAcI,GAAG,CAACvB;IACxC,IAAIsB,eAAe;QACjB,OAAOA;IACT;IAEA,MAAME,UAAUG,QAAQC,OAAO,CAAC5B;IAChCmB,cAAcO,GAAG,CAAC1B,mBAAmBwB;IAErC,OAAOA;AACT;AAEA,SAASrB,2BACPH,iBAAkC;IAElC,MAAMsB,gBAAgBH,cAAcI,GAAG,CAACvB;IACxC,IAAIsB,eAAe;QACjB,OAAOA;IACT;IAEA,MAAME,UAAUG,QAAQC,OAAO,CAAC5B;IAChCmB,cAAcO,GAAG,CAAC1B,mBAAmBwB;IAErCK,OAAOC,gBAAgB,CAACN,SAAS;QAC/BO,QAAQ;YACNC,OAAOhC,kBAAkB+B,MAAM,CAACE,IAAI,CAACjC;QACvC;QACAkC,QAAQ;YACNF,OAAOhC,kBAAkBkC,MAAM,CAACD,IAAI,CAACjC;QACvC;QACAuB,KAAK;YACHS,OAAOhC,kBAAkBuB,GAAG,CAACU,IAAI,CAACjC;QACpC;QACAmC,KAAK;YACHH,OAAOhC,kBAAkBmC,GAAG,CAACF,IAAI,CAACjC;QACpC;QACA0B,KAAK;YACHM,OAAOhC,kBAAkB0B,GAAG,CAACO,IAAI,CAACjC;QACpC;QACAoC,cAAc;YACZJ,OAAOhC,kBAAkBoC,YAAY,CAACH,IAAI,CAACjC;QAC7C;QACAqC,SAAS;YACPL,OAAOhC,kBAAkBqC,OAAO,CAACJ,IAAI,CAACjC;QACxC;QACAsC,MAAM;YACJN,OAAOhC,kBAAkBsC,IAAI,CAACL,IAAI,CAACjC;QACrC;QACAuC,QAAQ;YACNP,OAAOhC,kBAAkBuC,MAAM,CAACN,IAAI,CAACjC;QACvC;QACAwC,SAAS;YACPR,OAAOhC,kBAAkBwC,OAAO,CAACP,IAAI,CAACjC;QACxC;QACA,CAACyC,OAAOC,QAAQ,CAAC,EAAE;YACjBV,OAAOhC,iBAAiB,CAACyC,OAAOC,QAAQ,CAAC,CAACT,IAAI,CAACjC;QACjD;IACF;IAEA,OAAOwB;AACT;AAEA,SAASP,0CACPjB,iBAAkC,EAClCF,KAAc;IAEd,MAAMwB,gBAAgBH,cAAcI,GAAG,CAACvB;IACxC,IAAIsB,eAAe;QACjB,OAAOA;IACT;IAEA,MAAME,UAAUvC,2BAA2Be;IAE3CmB,cAAcO,GAAG,CAAC1B,mBAAmBwB;IAErCK,OAAOC,gBAAgB,CAACN,SAAS;QAC/BO,QAAQ;YACNC,OAAO,SAASD;gBACd,MAAMY,aAAa,CAAC,mBAAmB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,QAAQ,CAAC;gBAChFC,UAAUhD,OAAO6C;gBACjB,OAAO3C,kBAAkB+B,MAAM,CAACgB,KAAK,CACnC/C,mBACA6C;YAEJ;QACF;QACAX,QAAQ;YACNF,OAAO,SAASgB;gBACd,MAAML,aAAa,CAAC,mBAAmB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBAC3EC,UAAUhD,OAAO6C;gBACjB,OAAO3C,kBAAkBkC,MAAM,CAACa,KAAK,CACnC/C,mBACA6C;YAEJ;QACF;QACAtB,KAAK;YACHS,OAAO,SAAST;gBACd,MAAMoB,aAAa,CAAC,gBAAgB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACxEC,UAAUhD,OAAO6C;gBACjB,OAAO3C,kBAAkBuB,GAAG,CAACwB,KAAK,CAAC/C,mBAAmB6C;YACxD;QACF;QACAV,KAAK;YACHH,OAAO,SAASG;gBACd,MAAMQ,aAAa,CAAC,gBAAgB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACxEC,UAAUhD,OAAO6C;gBACjB,OAAO3C,kBAAkBmC,GAAG,CAACY,KAAK,CAAC/C,mBAAmB6C;YACxD;QACF;QACAnB,KAAK;YACHM,OAAO,SAASN;gBACd,MAAMiB,aAAa,CAAC,gBAAgB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,QAAQ,CAAC;gBAC7EC,UAAUhD,OAAO6C;gBACjB,OAAO3C,kBAAkB0B,GAAG,CAACqB,KAAK,CAAC/C,mBAAmB6C;YACxD;QACF;QACAT,cAAc;YACZJ,OAAO,SAASI;gBACd,MAAMO,aAAa;gBACnBG,UAAUhD,OAAO6C;gBACjB,OAAO3C,kBAAkBoC,YAAY,CAACW,KAAK,CACzC/C,mBACA6C;YAEJ;QACF;QACAR,SAAS;YACPL,OAAO,SAASK;gBACd,MAAMM,aAAa;gBACnBG,UAAUhD,OAAO6C;gBACjB,OAAO3C,kBAAkBqC,OAAO,CAACU,KAAK,CACpC/C,mBACA6C;YAEJ;QACF;QACAP,MAAM;YACJN,OAAO,SAASM;gBACd,MAAMK,aAAa;gBACnBG,UAAUhD,OAAO6C;gBACjB,OAAO3C,kBAAkBsC,IAAI,CAACS,KAAK,CAAC/C,mBAAmB6C;YACzD;QACF;QACAN,QAAQ;YACNP,OAAO,SAASO;gBACd,MAAMI,aAAa;gBACnBG,UAAUhD,OAAO6C;gBACjB,OAAO3C,kBAAkBuC,MAAM,CAACQ,KAAK,CACnC/C,mBACA6C;YAEJ;QACF;QACAL,SAAS;YACPR,OAAO,SAASQ;gBACd,MAAMG,aAAa;gBACnBG,UAAUhD,OAAO6C;gBACjB,OAAO3C,kBAAkBwC,OAAO,CAACO,KAAK,CACpC/C,mBACA6C;YAEJ;QACF;QACA,CAACJ,OAAOC,QAAQ,CAAC,EAAE;YACjBV,OAAO;gBACL,MAAMW,aAAa;gBACnBG,UAAUhD,OAAO6C;gBACjB,OAAO3C,iBAAiB,CAACyC,OAAOC,QAAQ,CAAC,CAACK,KAAK,CAC7C/C,mBACA6C;YAEJ;QACF;IACF;IAEA,OAAOrB;AACT;AAEA,+EAA+E;AAC/E,+EAA+E;AAC/E,SAASR,oCACPhB,iBAAkC,EAClCF,KAAc;IAEd,MAAMwB,gBAAgBH,cAAcI,GAAG,CAACvB;IACxC,IAAIsB,eAAe;QACjB,OAAOA;IACT;IAEA,MAAME,UAAUvC,2BAA2Be;IAE3C,MAAMiD,iBAAiB,IAAIC,MAAM1B,SAAS;QACxCD,KAAI4B,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,OAAQD;gBACN,KAAKX,OAAOC,QAAQ;oBAAE;wBACpBY,kBAAkBxD,OAAO;wBACzB;oBACF;gBACA,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBAAW;wBACdwD,kBAAkBxD,OAAO,CAAC,YAAY,EAAEsD,KAAK,EAAE,CAAC;wBAChD;oBACF;gBACA;oBAAS;oBACP,kEAAkE;oBACpE;YACF;YAEA,OAAO9D,eAAeiC,GAAG,CAAC4B,QAAQC,MAAMC;QAC1C;IACF;IAEAlC,cAAcO,GAAG,CAAC1B,mBAAmBiD;IAErC,OAAOA;AACT;AAEA,SAASL,gBAAgBW,GAAY;IACnC,OAAO,OAAOA,QAAQ,WAAW,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,GAAG;AAChD;AAEA,SAAST,UAAUhD,KAAyB,EAAE6C,UAAkB;IAC9D,MAAMhD,gBAAgBhB,qBAAqBe,QAAQ;IAEnD,IAAIC,eAAe;QACjB,OAAQA,cAAcS,IAAI;YACxB,KAAK;gBACH,IAAIT,cAAc6D,cAAc,KAAK,MAAM;oBACzC,qEAAqE;oBACrE,qEAAqE;oBACrEzE,uCAAuCY;gBACzC;gBACA;YACF,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH;YACF;gBACEA;QACJ;IACF;IAEA,gCAAgC;IAChC2D,kBAAkBxD,OAAO6C;AAC3B;AAEA,MAAMW,oBAAoBnE,4CACxBsE;AAGF,SAASA,yBACP3D,KAAyB,EACzB6C,UAAkB;IAElB,MAAMe,SAAS5D,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,qBAIN,CAJM,IAAID,MACT,GAAG6D,OAAO,KAAK,EAAEf,WAAW,EAAE,CAAC,GAC7B,CAAC,wDAAwD,CAAC,GAC1D,CAAC,8DAA8D,CAAC,GAH7D,qBAAA;eAAA;oBAAA;sBAAA;IAIP;AACF", "ignoreList": [0]}