{"version": 3, "sources": ["../../../src/server/app-render/get-layer-assets.tsx"], "sourcesContent": ["import React from 'react'\nimport { getLinkAndScriptTags } from './get-css-inlined-link-tags'\nimport { getPreloadableFonts } from './get-preloadable-fonts'\nimport type { AppRenderContext } from './app-render'\nimport { getAssetQueryString } from './get-asset-query-string'\nimport { encodeURIPath } from '../../shared/lib/encode-uri-path'\nimport type { PreloadCallbacks } from './types'\nimport { renderCssResource } from './render-css-resource'\n\nexport function getLayerAssets({\n  ctx,\n  layoutOrPagePath,\n  injectedCSS: injectedCSSWithCurrentLayout,\n  injectedJS: injectedJSWithCurrentLayout,\n  injectedFontPreloadTags: injectedFontPreloadTagsWithCurrentLayout,\n  preloadCallbacks,\n}: {\n  layoutOrPagePath: string | undefined\n  injectedCSS: Set<string>\n  injectedJS: Set<string>\n  injectedFontPreloadTags: Set<string>\n  ctx: AppRenderContext\n  preloadCallbacks: PreloadCallbacks\n}): React.ReactNode {\n  const { styles: styleTags, scripts: scriptTags } = layoutOrPagePath\n    ? getLinkAndScriptTags(\n        ctx.clientReferenceManifest,\n        layoutOrPagePath,\n        injectedCSSWithCurrentLayout,\n        injectedJSWithCurrentLayout,\n        true\n      )\n    : { styles: [], scripts: [] }\n\n  const preloadedFontFiles = layoutOrPagePath\n    ? getPreloadableFonts(\n        ctx.renderOpts.nextFontManifest,\n        layoutOrPagePath,\n        injectedFontPreloadTagsWithCurrentLayout\n      )\n    : null\n\n  if (preloadedFontFiles) {\n    if (preloadedFontFiles.length) {\n      for (let i = 0; i < preloadedFontFiles.length; i++) {\n        const fontFilename = preloadedFontFiles[i]\n        const ext = /\\.(woff|woff2|eot|ttf|otf)$/.exec(fontFilename)![1]\n        const type = `font/${ext}`\n        const href = `${ctx.assetPrefix}/_next/${encodeURIPath(fontFilename)}${getAssetQueryString(ctx, false)}`\n\n        preloadCallbacks.push(() => {\n          ctx.componentMod.preloadFont(\n            href,\n            type,\n            ctx.renderOpts.crossOrigin,\n            ctx.nonce\n          )\n        })\n      }\n    } else {\n      try {\n        let url = new URL(ctx.assetPrefix)\n        preloadCallbacks.push(() => {\n          ctx.componentMod.preconnect(url.origin, 'anonymous', ctx.nonce)\n        })\n      } catch (error) {\n        // assetPrefix must not be a fully qualified domain name. We assume\n        // we should preconnect to same origin instead\n        preloadCallbacks.push(() => {\n          ctx.componentMod.preconnect('/', 'anonymous', ctx.nonce)\n        })\n      }\n    }\n  }\n\n  const styles = renderCssResource(styleTags, ctx, preloadCallbacks)\n\n  const scripts = scriptTags\n    ? scriptTags.map((href, index) => {\n        const fullSrc = `${ctx.assetPrefix}/_next/${encodeURIPath(\n          href\n        )}${getAssetQueryString(ctx, true)}`\n\n        return (\n          <script\n            src={fullSrc}\n            async={true}\n            key={`script-${index}`}\n            nonce={ctx.nonce}\n          />\n        )\n      })\n    : []\n\n  return styles.length || scripts.length ? [...styles, ...scripts] : null\n}\n"], "names": ["getLayerAssets", "ctx", "layoutOrPagePath", "injectedCSS", "injectedCSSWithCurrentLayout", "injectedJS", "injectedJSWithCurrentLayout", "injectedFontPreloadTags", "injectedFontPreloadTagsWithCurrentLayout", "preloadCallbacks", "styles", "styleTags", "scripts", "scriptTags", "getLinkAndScriptTags", "clientReferenceManifest", "preloadedFontFiles", "getPreloadableFonts", "renderOpts", "nextFontManifest", "length", "i", "fontFilename", "ext", "exec", "type", "href", "assetPrefix", "encodeURIPath", "getAssetQueryString", "push", "componentMod", "preloadFont", "crossOrigin", "nonce", "url", "URL", "preconnect", "origin", "error", "renderCssResource", "map", "index", "fullSrc", "script", "src", "async"], "mappings": ";;;;+BASgBA;;;eAAAA;;;;8DATE;uCACmB;qCACD;qCAEA;+BACN;mCAEI;;;;;;AAE3B,SAASA,eAAe,EAC7BC,GAAG,EACHC,gBAAgB,EAChBC,aAAaC,4BAA4B,EACzCC,YAAYC,2BAA2B,EACvCC,yBAAyBC,wCAAwC,EACjEC,gBAAgB,EAQjB;IACC,MAAM,EAAEC,QAAQC,SAAS,EAAEC,SAASC,UAAU,EAAE,GAAGX,mBAC/CY,IAAAA,2CAAoB,EAClBb,IAAIc,uBAAuB,EAC3Bb,kBACAE,8BACAE,6BACA,QAEF;QAAEI,QAAQ,EAAE;QAAEE,SAAS,EAAE;IAAC;IAE9B,MAAMI,qBAAqBd,mBACvBe,IAAAA,wCAAmB,EACjBhB,IAAIiB,UAAU,CAACC,gBAAgB,EAC/BjB,kBACAM,4CAEF;IAEJ,IAAIQ,oBAAoB;QACtB,IAAIA,mBAAmBI,MAAM,EAAE;YAC7B,IAAK,IAAIC,IAAI,GAAGA,IAAIL,mBAAmBI,MAAM,EAAEC,IAAK;gBAClD,MAAMC,eAAeN,kBAAkB,CAACK,EAAE;gBAC1C,MAAME,MAAM,8BAA8BC,IAAI,CAACF,aAAc,CAAC,EAAE;gBAChE,MAAMG,OAAO,CAAC,KAAK,EAAEF,KAAK;gBAC1B,MAAMG,OAAO,GAAGzB,IAAI0B,WAAW,CAAC,OAAO,EAAEC,IAAAA,4BAAa,EAACN,gBAAgBO,IAAAA,wCAAmB,EAAC5B,KAAK,QAAQ;gBAExGQ,iBAAiBqB,IAAI,CAAC;oBACpB7B,IAAI8B,YAAY,CAACC,WAAW,CAC1BN,MACAD,MACAxB,IAAIiB,UAAU,CAACe,WAAW,EAC1BhC,IAAIiC,KAAK;gBAEb;YACF;QACF,OAAO;YACL,IAAI;gBACF,IAAIC,MAAM,IAAIC,IAAInC,IAAI0B,WAAW;gBACjClB,iBAAiBqB,IAAI,CAAC;oBACpB7B,IAAI8B,YAAY,CAACM,UAAU,CAACF,IAAIG,MAAM,EAAE,aAAarC,IAAIiC,KAAK;gBAChE;YACF,EAAE,OAAOK,OAAO;gBACd,mEAAmE;gBACnE,8CAA8C;gBAC9C9B,iBAAiBqB,IAAI,CAAC;oBACpB7B,IAAI8B,YAAY,CAACM,UAAU,CAAC,KAAK,aAAapC,IAAIiC,KAAK;gBACzD;YACF;QACF;IACF;IAEA,MAAMxB,SAAS8B,IAAAA,oCAAiB,EAAC7B,WAAWV,KAAKQ;IAEjD,MAAMG,UAAUC,aACZA,WAAW4B,GAAG,CAAC,CAACf,MAAMgB;QACpB,MAAMC,UAAU,GAAG1C,IAAI0B,WAAW,CAAC,OAAO,EAAEC,IAAAA,4BAAa,EACvDF,QACEG,IAAAA,wCAAmB,EAAC5B,KAAK,OAAO;QAEpC,qBACE,qBAAC2C;YACCC,KAAKF;YACLG,OAAO;YAEPZ,OAAOjC,IAAIiC,KAAK;WADX,CAAC,OAAO,EAAEQ,OAAO;IAI5B,KACA,EAAE;IAEN,OAAOhC,OAAOU,MAAM,IAAIR,QAAQQ,MAAM,GAAG;WAAIV;WAAWE;KAAQ,GAAG;AACrE", "ignoreList": [0]}