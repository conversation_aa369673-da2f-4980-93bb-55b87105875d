@echo off
echo 🚀 Starting Authentication Web App
echo ==================================

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js found

REM Start backend
echo 📦 Starting backend...
cd backend

REM Install dependencies if node_modules doesn't exist
if not exist "node_modules" (
    echo 📥 Installing backend dependencies...
    npm install
)

REM Generate Prisma client
echo 🔧 Generating Prisma client...
npx prisma generate

REM Check if database exists and create tables
echo 🗄️  Setting up database...
npx prisma migrate dev --name init

REM Start backend in background
echo 🚀 Starting backend server...
start "Backend Server" cmd /k "npm run start:dev"

REM Wait a moment for backend to start
timeout /t 5 /nobreak >nul

REM Start frontend
echo 🎨 Starting frontend...
cd ..\frontend

REM Install dependencies if node_modules doesn't exist
if not exist "node_modules" (
    echo 📥 Installing frontend dependencies...
    npm install
)

REM Start frontend
echo 🚀 Starting frontend server...
start "Frontend Server" cmd /k "npm run dev"

echo.
echo 🎉 Application started successfully!
echo ==================================
echo 📱 Frontend: http://localhost:3000
echo 🔧 Backend:  http://localhost:3001
echo.
echo Press any key to exit...
pause >nul
