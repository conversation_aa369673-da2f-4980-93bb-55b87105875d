{"version": 3, "sources": ["../../../../src/shared/lib/turbopack/manifest-loader.ts"], "sourcesContent": ["import type {\n  EdgeFunctionDefinition,\n  MiddlewareManifest,\n} from '../../../build/webpack/plugins/middleware-plugin'\nimport type {\n  StatsAsset,\n  StatsChunk,\n  StatsChunkGroup,\n  StatsModule,\n  StatsCompilation as WebpackStats,\n} from 'webpack'\nimport type { BuildManifest } from '../../../server/get-page-files'\nimport type { AppBuildManifest } from '../../../build/webpack/plugins/app-build-manifest-plugin'\nimport type { PagesManifest } from '../../../build/webpack/plugins/pages-manifest-plugin'\nimport type { ActionManifest } from '../../../build/webpack/plugins/flight-client-entry-plugin'\nimport type { NextFontManifest } from '../../../build/webpack/plugins/next-font-manifest-plugin'\nimport type { REACT_LOADABLE_MANIFEST } from '../constants'\nimport {\n  APP_BUILD_MANIFEST,\n  APP_PATHS_MANIFEST,\n  BUILD_MANIFEST,\n  INTERCEPTION_ROUTE_REWRITE_MANIFEST,\n  MIDDLEWARE_BUILD_MANIFEST,\n  MIDDLEWARE_MANIFEST,\n  NEXT_FONT_MANIFEST,\n  PAGES_MANIFEST,\n  SERVER_REFERENCE_MANIFEST,\n  TURBOPACK_CLIENT_BUILD_MANIFEST,\n  TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST,\n  WEBPACK_STATS,\n} from '../constants'\nimport { join, posix } from 'path'\nimport { readFile } from 'fs/promises'\nimport type { SetupOpts } from '../../../server/lib/router-utils/setup-dev-bundler'\nimport { deleteCache } from '../../../server/dev/require-cache'\nimport { writeFileAtomic } from '../../../lib/fs/write-atomic'\nimport { isInterceptionRouteRewrite } from '../../../lib/generate-interception-routes-rewrites'\nimport {\n  type ClientBuildManifest,\n  normalizeRewritesForBuildManifest,\n  srcEmptySsgManifest,\n  processRoute,\n  createEdgeRuntimeManifest,\n} from '../../../build/webpack/plugins/build-manifest-plugin'\nimport getAssetPathFromRoute from '../router/utils/get-asset-path-from-route'\nimport { getEntryKey, type EntryKey } from './entry-key'\nimport type { CustomRoutes } from '../../../lib/load-custom-routes'\nimport { getSortedRoutes } from '../router/utils'\nimport { existsSync } from 'fs'\nimport {\n  addMetadataIdToRoute,\n  addRouteSuffix,\n  removeRouteSuffix,\n} from '../../../server/dev/turbopack-utils'\nimport { tryToParsePath } from '../../../lib/try-to-parse-path'\nimport { safePathToRegexp } from '../router/utils/route-match-utils'\nimport type { Entrypoints } from '../../../build/swc/types'\n\ninterface InstrumentationDefinition {\n  files: string[]\n  name: 'instrumentation'\n}\n\ntype TurbopackMiddlewareManifest = MiddlewareManifest & {\n  instrumentation?: InstrumentationDefinition\n}\n\ntype ManifestName =\n  | typeof MIDDLEWARE_MANIFEST\n  | typeof BUILD_MANIFEST\n  | typeof APP_BUILD_MANIFEST\n  | typeof PAGES_MANIFEST\n  | typeof WEBPACK_STATS\n  | typeof APP_PATHS_MANIFEST\n  | `${typeof SERVER_REFERENCE_MANIFEST}.json`\n  | `${typeof NEXT_FONT_MANIFEST}.json`\n  | typeof REACT_LOADABLE_MANIFEST\n  | typeof TURBOPACK_CLIENT_BUILD_MANIFEST\n\nconst getManifestPath = (\n  page: string,\n  distDir: string,\n  name: ManifestName,\n  type: string,\n  firstCall: boolean\n) => {\n  let manifestPath = posix.join(\n    distDir,\n    `server`,\n    type,\n    type === 'middleware' || type === 'instrumentation'\n      ? ''\n      : type === 'app'\n        ? page\n        : getAssetPathFromRoute(page),\n    name\n  )\n\n  if (firstCall) {\n    const isSitemapRoute = /[\\\\/]sitemap(.xml)?\\/route$/.test(page)\n    // Check the ambiguity of /sitemap and /sitemap.xml\n    if (isSitemapRoute && !existsSync(manifestPath)) {\n      manifestPath = getManifestPath(\n        page.replace(/\\/sitemap\\/route$/, '/sitemap.xml/route'),\n        distDir,\n        name,\n        type,\n        false\n      )\n    }\n    // existsSync is faster than using the async version\n    if (!existsSync(manifestPath) && page.endsWith('/route')) {\n      // TODO: Improve implementation of metadata routes, currently it requires this extra check for the variants of the files that can be written.\n      let metadataPage = addRouteSuffix(\n        addMetadataIdToRoute(removeRouteSuffix(page))\n      )\n      manifestPath = getManifestPath(metadataPage, distDir, name, type, false)\n    }\n  }\n\n  return manifestPath\n}\n\nasync function readPartialManifest<T>(\n  distDir: string,\n  name: ManifestName,\n  pageName: string,\n  type: 'pages' | 'app' | 'middleware' | 'instrumentation' = 'pages'\n): Promise<T> {\n  const page = pageName\n  const manifestPath = getManifestPath(page, distDir, name, type, true)\n  return JSON.parse(await readFile(posix.join(manifestPath), 'utf-8')) as T\n}\n\nexport class TurbopackManifestLoader {\n  private actionManifests: Map<EntryKey, ActionManifest> = new Map()\n  private appBuildManifests: Map<EntryKey, AppBuildManifest> = new Map()\n  private appPathsManifests: Map<EntryKey, PagesManifest> = new Map()\n  private buildManifests: Map<EntryKey, BuildManifest> = new Map()\n  private clientBuildManifests: Map<EntryKey, ClientBuildManifest> = new Map()\n  private fontManifests: Map<EntryKey, NextFontManifest> = new Map()\n  private middlewareManifests: Map<EntryKey, TurbopackMiddlewareManifest> =\n    new Map()\n  private pagesManifests: Map<string, PagesManifest> = new Map()\n  private webpackStats: Map<EntryKey, WebpackStats> = new Map()\n  private encryptionKey: string\n\n  private readonly distDir: string\n  private readonly buildId: string\n\n  constructor({\n    distDir,\n    buildId,\n    encryptionKey,\n  }: {\n    buildId: string\n    distDir: string\n    encryptionKey: string\n  }) {\n    this.distDir = distDir\n    this.buildId = buildId\n    this.encryptionKey = encryptionKey\n  }\n\n  delete(key: EntryKey) {\n    this.actionManifests.delete(key)\n    this.appBuildManifests.delete(key)\n    this.appPathsManifests.delete(key)\n    this.buildManifests.delete(key)\n    this.clientBuildManifests.delete(key)\n    this.fontManifests.delete(key)\n    this.middlewareManifests.delete(key)\n    this.pagesManifests.delete(key)\n    this.webpackStats.delete(key)\n  }\n\n  async loadActionManifest(pageName: string): Promise<void> {\n    this.actionManifests.set(\n      getEntryKey('app', 'server', pageName),\n      await readPartialManifest(\n        this.distDir,\n        `${SERVER_REFERENCE_MANIFEST}.json`,\n        pageName,\n        'app'\n      )\n    )\n  }\n\n  private async mergeActionManifests(manifests: Iterable<ActionManifest>) {\n    type ActionEntries = ActionManifest['edge' | 'node']\n    const manifest: ActionManifest = {\n      node: {},\n      edge: {},\n      encryptionKey: this.encryptionKey,\n    }\n\n    function mergeActionIds(\n      actionEntries: ActionEntries,\n      other: ActionEntries\n    ): void {\n      for (const key in other) {\n        const action = (actionEntries[key] ??= {\n          workers: {},\n          layer: {},\n        })\n        action.filename = other[key].filename\n        action.exportedName = other[key].exportedName\n        Object.assign(action.workers, other[key].workers)\n        Object.assign(action.layer, other[key].layer)\n      }\n    }\n\n    for (const m of manifests) {\n      mergeActionIds(manifest.node, m.node)\n      mergeActionIds(manifest.edge, m.edge)\n    }\n    for (const key in manifest.node) {\n      const entry = manifest.node[key]\n      entry.workers = sortObjectByKey(entry.workers)\n      entry.layer = sortObjectByKey(entry.layer)\n    }\n    for (const key in manifest.edge) {\n      const entry = manifest.edge[key]\n      entry.workers = sortObjectByKey(entry.workers)\n      entry.layer = sortObjectByKey(entry.layer)\n    }\n\n    return manifest\n  }\n\n  private async writeActionManifest(): Promise<void> {\n    const actionManifest = await this.mergeActionManifests(\n      this.actionManifests.values()\n    )\n    const actionManifestJsonPath = join(\n      this.distDir,\n      'server',\n      `${SERVER_REFERENCE_MANIFEST}.json`\n    )\n    const actionManifestJsPath = join(\n      this.distDir,\n      'server',\n      `${SERVER_REFERENCE_MANIFEST}.js`\n    )\n    const json = JSON.stringify(actionManifest, null, 2)\n    deleteCache(actionManifestJsonPath)\n    deleteCache(actionManifestJsPath)\n    await writeFileAtomic(actionManifestJsonPath, json)\n    await writeFileAtomic(\n      actionManifestJsPath,\n      `self.__RSC_SERVER_MANIFEST=${JSON.stringify(json)}`\n    )\n  }\n\n  async loadAppBuildManifest(pageName: string): Promise<void> {\n    this.appBuildManifests.set(\n      getEntryKey('app', 'server', pageName),\n      await readPartialManifest(\n        this.distDir,\n        APP_BUILD_MANIFEST,\n        pageName,\n        'app'\n      )\n    )\n  }\n\n  private mergeAppBuildManifests(manifests: Iterable<AppBuildManifest>) {\n    const manifest: AppBuildManifest = {\n      pages: {},\n    }\n    for (const m of manifests) {\n      Object.assign(manifest.pages, m.pages)\n    }\n    manifest.pages = sortObjectByKey(manifest.pages)\n    return manifest\n  }\n\n  private async writeAppBuildManifest(): Promise<void> {\n    const appBuildManifest = this.mergeAppBuildManifests(\n      this.appBuildManifests.values()\n    )\n    const appBuildManifestPath = join(this.distDir, APP_BUILD_MANIFEST)\n    deleteCache(appBuildManifestPath)\n    await writeFileAtomic(\n      appBuildManifestPath,\n      JSON.stringify(appBuildManifest, null, 2)\n    )\n  }\n\n  async loadAppPathsManifest(pageName: string): Promise<void> {\n    this.appPathsManifests.set(\n      getEntryKey('app', 'server', pageName),\n      await readPartialManifest(\n        this.distDir,\n        APP_PATHS_MANIFEST,\n        pageName,\n        'app'\n      )\n    )\n  }\n\n  private async writeAppPathsManifest(): Promise<void> {\n    const appPathsManifest = this.mergePagesManifests(\n      this.appPathsManifests.values()\n    )\n    const appPathsManifestPath = join(\n      this.distDir,\n      'server',\n      APP_PATHS_MANIFEST\n    )\n    deleteCache(appPathsManifestPath)\n    await writeFileAtomic(\n      appPathsManifestPath,\n      JSON.stringify(appPathsManifest, null, 2)\n    )\n  }\n\n  private async writeWebpackStats(): Promise<void> {\n    const webpackStats = this.mergeWebpackStats(this.webpackStats.values())\n    const path = join(this.distDir, 'server', WEBPACK_STATS)\n    deleteCache(path)\n    await writeFileAtomic(path, JSON.stringify(webpackStats, null, 2))\n  }\n\n  async loadBuildManifest(\n    pageName: string,\n    type: 'app' | 'pages' = 'pages'\n  ): Promise<void> {\n    this.buildManifests.set(\n      getEntryKey(type, 'server', pageName),\n      await readPartialManifest(this.distDir, BUILD_MANIFEST, pageName, type)\n    )\n  }\n\n  async loadClientBuildManifest(\n    pageName: string,\n    type: 'app' | 'pages' = 'pages'\n  ): Promise<void> {\n    this.clientBuildManifests.set(\n      getEntryKey(type, 'server', pageName),\n      await readPartialManifest(\n        this.distDir,\n        TURBOPACK_CLIENT_BUILD_MANIFEST,\n        pageName,\n        type\n      )\n    )\n  }\n\n  async loadWebpackStats(\n    pageName: string,\n    type: 'app' | 'pages' = 'pages'\n  ): Promise<void> {\n    this.webpackStats.set(\n      getEntryKey(type, 'client', pageName),\n      await readPartialManifest(this.distDir, WEBPACK_STATS, pageName, type)\n    )\n  }\n\n  private mergeWebpackStats(statsFiles: Iterable<WebpackStats>): WebpackStats {\n    const entrypoints: Record<string, StatsChunkGroup> = {}\n    const assets: Map<string, StatsAsset> = new Map()\n    const chunks: Map<string | number, StatsChunk> = new Map()\n    const modules: Map<string | number, StatsModule> = new Map()\n\n    for (const statsFile of statsFiles) {\n      if (statsFile.entrypoints) {\n        for (const [k, v] of Object.entries(statsFile.entrypoints)) {\n          if (!entrypoints[k]) {\n            entrypoints[k] = v\n          }\n        }\n      }\n\n      if (statsFile.assets) {\n        for (const asset of statsFile.assets) {\n          if (!assets.has(asset.name)) {\n            assets.set(asset.name, asset)\n          }\n        }\n      }\n\n      if (statsFile.chunks) {\n        for (const chunk of statsFile.chunks) {\n          if (!chunks.has(chunk.id!)) {\n            chunks.set(chunk.id!, chunk)\n          }\n        }\n      }\n\n      if (statsFile.modules) {\n        for (const module of statsFile.modules) {\n          const id = module.id\n          if (id != null) {\n            // Merge the chunk list for the module. This can vary across endpoints.\n            const existing = modules.get(id)\n            if (existing == null) {\n              modules.set(id, module)\n            } else if (module.chunks != null && existing.chunks != null) {\n              for (const chunk of module.chunks) {\n                if (!existing.chunks.includes(chunk)) {\n                  existing.chunks.push(chunk)\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n\n    return {\n      version: 'Turbopack',\n      entrypoints,\n      assets: [...assets.values()],\n      chunks: [...chunks.values()],\n      modules: [...modules.values()],\n    }\n  }\n\n  private mergeBuildManifests(manifests: Iterable<BuildManifest>) {\n    const manifest: Partial<BuildManifest> & Pick<BuildManifest, 'pages'> = {\n      pages: {\n        '/_app': [],\n      },\n      // Something in next.js depends on these to exist even for app dir rendering\n      devFiles: [],\n      ampDevFiles: [],\n      polyfillFiles: [],\n      lowPriorityFiles: [\n        `static/${this.buildId}/_ssgManifest.js`,\n        `static/${this.buildId}/_buildManifest.js`,\n      ],\n      rootMainFiles: [],\n      ampFirstPages: [],\n    }\n    for (const m of manifests) {\n      Object.assign(manifest.pages, m.pages)\n      if (m.rootMainFiles.length) manifest.rootMainFiles = m.rootMainFiles\n      // polyfillFiles should always be the same, so we can overwrite instead of actually merging\n      if (m.polyfillFiles.length) manifest.polyfillFiles = m.polyfillFiles\n    }\n    manifest.pages = sortObjectByKey(manifest.pages) as BuildManifest['pages']\n    return manifest\n  }\n\n  private mergeClientBuildManifests(\n    rewrites: CustomRoutes['rewrites'],\n    manifests: Iterable<ClientBuildManifest>,\n    sortedPageKeys: string[]\n  ): ClientBuildManifest {\n    const manifest = {\n      __rewrites: normalizeRewritesForBuildManifest(rewrites) as any,\n      sortedPages: sortedPageKeys,\n    }\n    for (const m of manifests) {\n      Object.assign(manifest, m)\n    }\n    return sortObjectByKey(manifest)\n  }\n\n  private async writeBuildManifest(\n    entrypoints: Entrypoints,\n    devRewrites: SetupOpts['fsChecker']['rewrites'] | undefined,\n    productionRewrites: CustomRoutes['rewrites'] | undefined\n  ): Promise<void> {\n    const rewrites = productionRewrites ?? {\n      ...devRewrites,\n      beforeFiles: (devRewrites?.beforeFiles ?? []).map(processRoute),\n      afterFiles: (devRewrites?.afterFiles ?? []).map(processRoute),\n      fallback: (devRewrites?.fallback ?? []).map(processRoute),\n    }\n    const buildManifest = this.mergeBuildManifests(this.buildManifests.values())\n    const buildManifestPath = join(this.distDir, BUILD_MANIFEST)\n    const middlewareBuildManifestPath = join(\n      this.distDir,\n      'server',\n      `${MIDDLEWARE_BUILD_MANIFEST}.js`\n    )\n    const interceptionRewriteManifestPath = join(\n      this.distDir,\n      'server',\n      `${INTERCEPTION_ROUTE_REWRITE_MANIFEST}.js`\n    )\n    deleteCache(buildManifestPath)\n    deleteCache(middlewareBuildManifestPath)\n    deleteCache(interceptionRewriteManifestPath)\n    await writeFileAtomic(\n      buildManifestPath,\n      JSON.stringify(buildManifest, null, 2)\n    )\n    await writeFileAtomic(\n      middlewareBuildManifestPath,\n      // we use globalThis here because middleware can be node\n      // which doesn't have \"self\"\n      createEdgeRuntimeManifest(buildManifest)\n    )\n\n    const interceptionRewrites = JSON.stringify(\n      rewrites.beforeFiles.filter(isInterceptionRouteRewrite)\n    )\n\n    await writeFileAtomic(\n      interceptionRewriteManifestPath,\n      `self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST=${JSON.stringify(\n        interceptionRewrites\n      )};`\n    )\n\n    const pagesKeys = [...entrypoints.page.keys()]\n    if (entrypoints.global.app) {\n      pagesKeys.push('/_app')\n    }\n    if (entrypoints.global.error) {\n      pagesKeys.push('/_error')\n    }\n\n    const sortedPageKeys = getSortedRoutes(pagesKeys)\n    const clientBuildManifest = this.mergeClientBuildManifests(\n      rewrites,\n      this.clientBuildManifests.values(),\n      sortedPageKeys\n    )\n    const clientBuildManifestJs = `self.__BUILD_MANIFEST = ${JSON.stringify(\n      clientBuildManifest,\n      null,\n      2\n    )};self.__BUILD_MANIFEST_CB && self.__BUILD_MANIFEST_CB()`\n    await writeFileAtomic(\n      join(this.distDir, 'static', this.buildId, '_buildManifest.js'),\n      clientBuildManifestJs\n    )\n    await writeFileAtomic(\n      join(this.distDir, 'static', this.buildId, '_ssgManifest.js'),\n      srcEmptySsgManifest\n    )\n  }\n\n  private async writeClientMiddlewareManifest(): Promise<void> {\n    const middlewareManifest = this.mergeMiddlewareManifests(\n      this.middlewareManifests.values()\n    )\n\n    const matchers = middlewareManifest?.middleware['/']?.matchers || []\n\n    const clientMiddlewareManifestPath = join(\n      this.distDir,\n      'static',\n      this.buildId,\n      `${TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST}`\n    )\n    deleteCache(clientMiddlewareManifestPath)\n    await writeFileAtomic(\n      clientMiddlewareManifestPath,\n      JSON.stringify(matchers, null, 2)\n    )\n  }\n\n  private async writeFallbackBuildManifest(): Promise<void> {\n    const fallbackBuildManifest = this.mergeBuildManifests(\n      [\n        this.buildManifests.get(getEntryKey('pages', 'server', '_app')),\n        this.buildManifests.get(getEntryKey('pages', 'server', '_error')),\n      ].filter(Boolean) as BuildManifest[]\n    )\n    const fallbackBuildManifestPath = join(\n      this.distDir,\n      `fallback-${BUILD_MANIFEST}`\n    )\n    deleteCache(fallbackBuildManifestPath)\n    await writeFileAtomic(\n      fallbackBuildManifestPath,\n      JSON.stringify(fallbackBuildManifest, null, 2)\n    )\n  }\n\n  async loadFontManifest(\n    pageName: string,\n    type: 'app' | 'pages' = 'pages'\n  ): Promise<void> {\n    this.fontManifests.set(\n      getEntryKey(type, 'server', pageName),\n      await readPartialManifest(\n        this.distDir,\n        `${NEXT_FONT_MANIFEST}.json`,\n        pageName,\n        type\n      )\n    )\n  }\n\n  private mergeFontManifests(manifests: Iterable<NextFontManifest>) {\n    const manifest: NextFontManifest = {\n      app: {},\n      appUsingSizeAdjust: false,\n      pages: {},\n      pagesUsingSizeAdjust: false,\n    }\n    for (const m of manifests) {\n      Object.assign(manifest.app, m.app)\n      Object.assign(manifest.pages, m.pages)\n\n      manifest.appUsingSizeAdjust =\n        manifest.appUsingSizeAdjust || m.appUsingSizeAdjust\n      manifest.pagesUsingSizeAdjust =\n        manifest.pagesUsingSizeAdjust || m.pagesUsingSizeAdjust\n    }\n    manifest.app = sortObjectByKey(manifest.app)\n    manifest.pages = sortObjectByKey(manifest.pages)\n    return manifest\n  }\n\n  private async writeNextFontManifest(): Promise<void> {\n    const fontManifest = this.mergeFontManifests(this.fontManifests.values())\n    const json = JSON.stringify(fontManifest, null, 2)\n\n    const fontManifestJsonPath = join(\n      this.distDir,\n      'server',\n      `${NEXT_FONT_MANIFEST}.json`\n    )\n    const fontManifestJsPath = join(\n      this.distDir,\n      'server',\n      `${NEXT_FONT_MANIFEST}.js`\n    )\n    deleteCache(fontManifestJsonPath)\n    deleteCache(fontManifestJsPath)\n    await writeFileAtomic(fontManifestJsonPath, json)\n    await writeFileAtomic(\n      fontManifestJsPath,\n      `self.__NEXT_FONT_MANIFEST=${JSON.stringify(json)}`\n    )\n  }\n\n  /**\n   * @returns If the manifest was written or not\n   */\n  async loadMiddlewareManifest(\n    pageName: string,\n    type: 'pages' | 'app' | 'middleware' | 'instrumentation'\n  ): Promise<boolean> {\n    const middlewareManifestPath = getManifestPath(\n      pageName,\n      this.distDir,\n      MIDDLEWARE_MANIFEST,\n      type,\n      true\n    )\n\n    // middlewareManifest is actually \"edge manifest\" and not all routes are edge runtime. If it is not written we skip it.\n    if (!existsSync(middlewareManifestPath)) {\n      return false\n    }\n\n    this.middlewareManifests.set(\n      getEntryKey(\n        type === 'middleware' || type === 'instrumentation' ? 'root' : type,\n        'server',\n        pageName\n      ),\n      await readPartialManifest(\n        this.distDir,\n        MIDDLEWARE_MANIFEST,\n        pageName,\n        type\n      )\n    )\n\n    return true\n  }\n\n  getMiddlewareManifest(key: EntryKey) {\n    return this.middlewareManifests.get(key)\n  }\n\n  deleteMiddlewareManifest(key: EntryKey) {\n    return this.middlewareManifests.delete(key)\n  }\n\n  private mergeMiddlewareManifests(\n    manifests: Iterable<TurbopackMiddlewareManifest>\n  ): MiddlewareManifest {\n    const manifest: MiddlewareManifest = {\n      version: 3,\n      middleware: {},\n      sortedMiddleware: [],\n      functions: {},\n    }\n    let instrumentation: InstrumentationDefinition | undefined = undefined\n    for (const m of manifests) {\n      Object.assign(manifest.functions, m.functions)\n      Object.assign(manifest.middleware, m.middleware)\n      if (m.instrumentation) {\n        instrumentation = m.instrumentation\n      }\n    }\n    manifest.functions = sortObjectByKey(manifest.functions)\n    manifest.middleware = sortObjectByKey(manifest.middleware)\n    const updateFunctionDefinition = (\n      fun: EdgeFunctionDefinition\n    ): EdgeFunctionDefinition => {\n      return {\n        ...fun,\n        files: [...(instrumentation?.files ?? []), ...fun.files],\n      }\n    }\n    for (const key of Object.keys(manifest.middleware)) {\n      const value = manifest.middleware[key]\n      manifest.middleware[key] = updateFunctionDefinition(value)\n    }\n    for (const key of Object.keys(manifest.functions)) {\n      const value = manifest.functions[key]\n      manifest.functions[key] = updateFunctionDefinition(value)\n    }\n    for (const fun of Object.values(manifest.functions).concat(\n      Object.values(manifest.middleware)\n    )) {\n      for (const matcher of fun.matchers) {\n        if (!matcher.regexp) {\n          matcher.regexp = safePathToRegexp(matcher.originalSource, [], {\n            delimiter: '/',\n            sensitive: false,\n            strict: true,\n          }).source.replaceAll('\\\\/', '/')\n        }\n      }\n    }\n    manifest.sortedMiddleware = Object.keys(manifest.middleware)\n\n    return manifest\n  }\n\n  private async writeMiddlewareManifest(): Promise<void> {\n    const middlewareManifest = this.mergeMiddlewareManifests(\n      this.middlewareManifests.values()\n    )\n\n    // Normalize regexes as it uses path-to-regexp\n    for (const key in middlewareManifest.middleware) {\n      middlewareManifest.middleware[key].matchers.forEach((matcher) => {\n        if (!matcher.regexp.startsWith('^')) {\n          const parsedPage = tryToParsePath(matcher.regexp)\n          if (parsedPage.error || !parsedPage.regexStr) {\n            throw new Error(`Invalid source: ${matcher.regexp}`)\n          }\n          matcher.regexp = parsedPage.regexStr\n        }\n      })\n    }\n\n    const middlewareManifestPath = join(\n      this.distDir,\n      'server',\n      MIDDLEWARE_MANIFEST\n    )\n    deleteCache(middlewareManifestPath)\n    await writeFileAtomic(\n      middlewareManifestPath,\n      JSON.stringify(middlewareManifest, null, 2)\n    )\n  }\n\n  async loadPagesManifest(pageName: string): Promise<void> {\n    this.pagesManifests.set(\n      getEntryKey('pages', 'server', pageName),\n      await readPartialManifest(this.distDir, PAGES_MANIFEST, pageName)\n    )\n  }\n\n  private mergePagesManifests(manifests: Iterable<PagesManifest>) {\n    const manifest: PagesManifest = {}\n    for (const m of manifests) {\n      Object.assign(manifest, m)\n    }\n    return sortObjectByKey(manifest)\n  }\n\n  private async writePagesManifest(): Promise<void> {\n    const pagesManifest = this.mergePagesManifests(this.pagesManifests.values())\n    const pagesManifestPath = join(this.distDir, 'server', PAGES_MANIFEST)\n    deleteCache(pagesManifestPath)\n    await writeFileAtomic(\n      pagesManifestPath,\n      JSON.stringify(pagesManifest, null, 2)\n    )\n  }\n\n  async writeManifests({\n    devRewrites,\n    productionRewrites,\n    entrypoints,\n  }: {\n    devRewrites: SetupOpts['fsChecker']['rewrites'] | undefined\n    productionRewrites: CustomRoutes['rewrites'] | undefined\n    entrypoints: Entrypoints\n  }) {\n    await this.writeActionManifest()\n    await this.writeAppBuildManifest()\n    await this.writeAppPathsManifest()\n    await this.writeBuildManifest(entrypoints, devRewrites, productionRewrites)\n    await this.writeFallbackBuildManifest()\n    await this.writeMiddlewareManifest()\n    await this.writeClientMiddlewareManifest()\n    await this.writeNextFontManifest()\n    await this.writePagesManifest()\n\n    if (process.env.TURBOPACK_STATS != null) {\n      await this.writeWebpackStats()\n    }\n  }\n}\n\nfunction sortObjectByKey(obj: Record<string, any>) {\n  return Object.keys(obj)\n    .sort()\n    .reduce(\n      (acc, key) => {\n        acc[key] = obj[key]\n        return acc\n      },\n      {} as Record<string, any>\n    )\n}\n"], "names": ["TurbopackManifestLoader", "getManifestPath", "page", "distDir", "name", "type", "firstCall", "manifestPath", "posix", "join", "getAssetPathFromRoute", "isSitemapRoute", "test", "existsSync", "replace", "endsWith", "metadataPage", "addRouteSuffix", "addMetadataIdToRoute", "removeRouteSuffix", "readPartialManifest", "pageName", "JSON", "parse", "readFile", "delete", "key", "actionManifests", "appBuildManifests", "appPathsManifests", "buildManifests", "clientBuildManifests", "fontManifests", "middlewareManifests", "pagesManifests", "webpackStats", "loadActionManifest", "set", "getEntry<PERSON>ey", "SERVER_REFERENCE_MANIFEST", "mergeActionManifests", "manifests", "manifest", "node", "edge", "<PERSON><PERSON><PERSON>", "mergeActionIds", "actionEntries", "other", "action", "workers", "layer", "filename", "exportedName", "Object", "assign", "m", "entry", "sortObjectByKey", "writeActionManifest", "actionManifest", "values", "actionManifestJsonPath", "actionManifestJsPath", "json", "stringify", "deleteCache", "writeFileAtomic", "loadAppBuildManifest", "APP_BUILD_MANIFEST", "mergeAppBuildManifests", "pages", "writeAppBuildManifest", "appBuildManifest", "appBuildManifestPath", "loadAppPathsManifest", "APP_PATHS_MANIFEST", "writeAppPathsManifest", "appPathsManifest", "mergePagesManifests", "appPathsManifestPath", "writeWebpackStats", "mergeWebpackStats", "path", "WEBPACK_STATS", "loadBuildManifest", "BUILD_MANIFEST", "loadClientBuildManifest", "TURBOPACK_CLIENT_BUILD_MANIFEST", "loadWebpackStats", "statsFiles", "entrypoints", "assets", "Map", "chunks", "modules", "statsFile", "k", "v", "entries", "asset", "has", "chunk", "id", "module", "existing", "get", "includes", "push", "version", "mergeBuildManifests", "devFiles", "ampDevFiles", "polyfillFiles", "lowPriorityFiles", "buildId", "rootMainFiles", "ampFirstPages", "length", "mergeClientBuildManifests", "rewrites", "sortedPageKeys", "__rewrites", "normalizeRewritesForBuildManifest", "sortedPages", "writeBuildManifest", "devRewrites", "productionRewrites", "beforeFiles", "map", "processRoute", "afterFiles", "fallback", "buildManifest", "buildManifestPath", "middlewareBuildManifestPath", "MIDDLEWARE_BUILD_MANIFEST", "interceptionRewriteManifestPath", "INTERCEPTION_ROUTE_REWRITE_MANIFEST", "createEdgeRuntimeManifest", "interceptionRewrites", "filter", "isInterceptionRouteRewrite", "pagesKeys", "keys", "global", "app", "error", "getSortedRoutes", "clientBuildManifest", "clientBuildManifestJs", "srcEmptySsgManifest", "writeClientMiddlewareManifest", "middlewareManifest", "mergeMiddlewareManifests", "matchers", "middleware", "clientMiddlewareManifestPath", "TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST", "writeFallbackBuildManifest", "fallbackBuildManifest", "Boolean", "fallbackBuildManifestPath", "loadFontManifest", "NEXT_FONT_MANIFEST", "mergeFontManifests", "appUsingSizeAdjust", "pagesUsingSizeAdjust", "writeNextFontManifest", "fontManifest", "fontManifestJsonPath", "fontManifestJsPath", "loadMiddlewareManifest", "middlewareManifestPath", "MIDDLEWARE_MANIFEST", "getMiddlewareManifest", "deleteMiddlewareManifest", "sortedMiddleware", "functions", "instrumentation", "undefined", "updateFunctionDefinition", "fun", "files", "value", "concat", "matcher", "regexp", "safePathToRegexp", "originalSource", "delimiter", "sensitive", "strict", "source", "replaceAll", "writeMiddlewareManifest", "for<PERSON>ach", "startsWith", "parsedPage", "tryToParsePath", "regexStr", "Error", "loadPagesManifest", "PAGES_MANIFEST", "writePagesManifest", "pagesManifest", "pagesManifestPath", "writeManifests", "process", "env", "TURBOPACK_STATS", "constructor", "obj", "sort", "reduce", "acc"], "mappings": ";;;;+BAsIaA;;;eAAAA;;;;2BAxGN;sBACqB;0BACH;8BAEG;6BACI;oDACW;qCAOpC;gFAC2B;0BACS;uBAEX;oBACL;gCAKpB;gCACwB;iCACE;AAwBjC,MAAMC,kBAAkB,CACtBC,MACAC,SACAC,MACAC,MACAC;IAEA,IAAIC,eAAeC,WAAK,CAACC,IAAI,CAC3BN,SACC,UACDE,MACAA,SAAS,gBAAgBA,SAAS,oBAC9B,KACAA,SAAS,QACPH,OACAQ,IAAAA,8BAAqB,EAACR,OAC5BE;IAGF,IAAIE,WAAW;QACb,MAAMK,iBAAiB,8BAA8BC,IAAI,CAACV;QAC1D,mDAAmD;QACnD,IAAIS,kBAAkB,CAACE,IAAAA,cAAU,EAACN,eAAe;YAC/CA,eAAeN,gBACbC,KAAKY,OAAO,CAAC,qBAAqB,uBAClCX,SACAC,MACAC,MACA;QAEJ;QACA,oDAAoD;QACpD,IAAI,CAACQ,IAAAA,cAAU,EAACN,iBAAiBL,KAAKa,QAAQ,CAAC,WAAW;YACxD,6IAA6I;YAC7I,IAAIC,eAAeC,IAAAA,8BAAc,EAC/BC,IAAAA,oCAAoB,EAACC,IAAAA,iCAAiB,EAACjB;YAEzCK,eAAeN,gBAAgBe,cAAcb,SAASC,MAAMC,MAAM;QACpE;IACF;IAEA,OAAOE;AACT;AAEA,eAAea,oBACbjB,OAAe,EACfC,IAAkB,EAClBiB,QAAgB,EAChBhB,IAAkE;IAAlEA,IAAAA,iBAAAA,OAA2D;IAE3D,MAAMH,OAAOmB;IACb,MAAMd,eAAeN,gBAAgBC,MAAMC,SAASC,MAAMC,MAAM;IAChE,OAAOiB,KAAKC,KAAK,CAAC,MAAMC,IAAAA,kBAAQ,EAAChB,WAAK,CAACC,IAAI,CAACF,eAAe;AAC7D;AAEO,MAAMP;IA8BXyB,OAAOC,GAAa,EAAE;QACpB,IAAI,CAACC,eAAe,CAACF,MAAM,CAACC;QAC5B,IAAI,CAACE,iBAAiB,CAACH,MAAM,CAACC;QAC9B,IAAI,CAACG,iBAAiB,CAACJ,MAAM,CAACC;QAC9B,IAAI,CAACI,cAAc,CAACL,MAAM,CAACC;QAC3B,IAAI,CAACK,oBAAoB,CAACN,MAAM,CAACC;QACjC,IAAI,CAACM,aAAa,CAACP,MAAM,CAACC;QAC1B,IAAI,CAACO,mBAAmB,CAACR,MAAM,CAACC;QAChC,IAAI,CAACQ,cAAc,CAACT,MAAM,CAACC;QAC3B,IAAI,CAACS,YAAY,CAACV,MAAM,CAACC;IAC3B;IAEA,MAAMU,mBAAmBf,QAAgB,EAAiB;QACxD,IAAI,CAACM,eAAe,CAACU,GAAG,CACtBC,IAAAA,qBAAW,EAAC,OAAO,UAAUjB,WAC7B,MAAMD,oBACJ,IAAI,CAACjB,OAAO,EACZ,AAAC,KAAEoC,oCAAyB,GAAC,SAC7BlB,UACA;IAGN;IAEA,MAAcmB,qBAAqBC,SAAmC,EAAE;QAEtE,MAAMC,WAA2B;YAC/BC,MAAM,CAAC;YACPC,MAAM,CAAC;YACPC,eAAe,IAAI,CAACA,aAAa;QACnC;QAEA,SAASC,eACPC,aAA4B,EAC5BC,KAAoB;YAEpB,IAAK,MAAMtB,OAAOsB,MAAO;oBACPD,gBAAcrB;;gBAA9B,MAAMuB,SAAUF,MAAAA,iBAAAA,cAAa,CAACrB,OAAAA,IAAI,gBAAlBqB,cAAa,CAACrB,KAAI,GAAK;oBACrCwB,SAAS,CAAC;oBACVC,OAAO,CAAC;gBACV;gBACAF,OAAOG,QAAQ,GAAGJ,KAAK,CAACtB,IAAI,CAAC0B,QAAQ;gBACrCH,OAAOI,YAAY,GAAGL,KAAK,CAACtB,IAAI,CAAC2B,YAAY;gBAC7CC,OAAOC,MAAM,CAACN,OAAOC,OAAO,EAAEF,KAAK,CAACtB,IAAI,CAACwB,OAAO;gBAChDI,OAAOC,MAAM,CAACN,OAAOE,KAAK,EAAEH,KAAK,CAACtB,IAAI,CAACyB,KAAK;YAC9C;QACF;QAEA,KAAK,MAAMK,KAAKf,UAAW;YACzBK,eAAeJ,SAASC,IAAI,EAAEa,EAAEb,IAAI;YACpCG,eAAeJ,SAASE,IAAI,EAAEY,EAAEZ,IAAI;QACtC;QACA,IAAK,MAAMlB,OAAOgB,SAASC,IAAI,CAAE;YAC/B,MAAMc,QAAQf,SAASC,IAAI,CAACjB,IAAI;YAChC+B,MAAMP,OAAO,GAAGQ,gBAAgBD,MAAMP,OAAO;YAC7CO,MAAMN,KAAK,GAAGO,gBAAgBD,MAAMN,KAAK;QAC3C;QACA,IAAK,MAAMzB,OAAOgB,SAASE,IAAI,CAAE;YAC/B,MAAMa,QAAQf,SAASE,IAAI,CAAClB,IAAI;YAChC+B,MAAMP,OAAO,GAAGQ,gBAAgBD,MAAMP,OAAO;YAC7CO,MAAMN,KAAK,GAAGO,gBAAgBD,MAAMN,KAAK;QAC3C;QAEA,OAAOT;IACT;IAEA,MAAciB,sBAAqC;QACjD,MAAMC,iBAAiB,MAAM,IAAI,CAACpB,oBAAoB,CACpD,IAAI,CAACb,eAAe,CAACkC,MAAM;QAE7B,MAAMC,yBAAyBrD,IAAAA,UAAI,EACjC,IAAI,CAACN,OAAO,EACZ,UACA,AAAC,KAAEoC,oCAAyB,GAAC;QAE/B,MAAMwB,uBAAuBtD,IAAAA,UAAI,EAC/B,IAAI,CAACN,OAAO,EACZ,UACA,AAAC,KAAEoC,oCAAyB,GAAC;QAE/B,MAAMyB,OAAO1C,KAAK2C,SAAS,CAACL,gBAAgB,MAAM;QAClDM,IAAAA,yBAAW,EAACJ;QACZI,IAAAA,yBAAW,EAACH;QACZ,MAAMI,IAAAA,4BAAe,EAACL,wBAAwBE;QAC9C,MAAMG,IAAAA,4BAAe,EACnBJ,sBACA,AAAC,gCAA6BzC,KAAK2C,SAAS,CAACD;IAEjD;IAEA,MAAMI,qBAAqB/C,QAAgB,EAAiB;QAC1D,IAAI,CAACO,iBAAiB,CAACS,GAAG,CACxBC,IAAAA,qBAAW,EAAC,OAAO,UAAUjB,WAC7B,MAAMD,oBACJ,IAAI,CAACjB,OAAO,EACZkE,6BAAkB,EAClBhD,UACA;IAGN;IAEQiD,uBAAuB7B,SAAqC,EAAE;QACpE,MAAMC,WAA6B;YACjC6B,OAAO,CAAC;QACV;QACA,KAAK,MAAMf,KAAKf,UAAW;YACzBa,OAAOC,MAAM,CAACb,SAAS6B,KAAK,EAAEf,EAAEe,KAAK;QACvC;QACA7B,SAAS6B,KAAK,GAAGb,gBAAgBhB,SAAS6B,KAAK;QAC/C,OAAO7B;IACT;IAEA,MAAc8B,wBAAuC;QACnD,MAAMC,mBAAmB,IAAI,CAACH,sBAAsB,CAClD,IAAI,CAAC1C,iBAAiB,CAACiC,MAAM;QAE/B,MAAMa,uBAAuBjE,IAAAA,UAAI,EAAC,IAAI,CAACN,OAAO,EAAEkE,6BAAkB;QAClEH,IAAAA,yBAAW,EAACQ;QACZ,MAAMP,IAAAA,4BAAe,EACnBO,sBACApD,KAAK2C,SAAS,CAACQ,kBAAkB,MAAM;IAE3C;IAEA,MAAME,qBAAqBtD,QAAgB,EAAiB;QAC1D,IAAI,CAACQ,iBAAiB,CAACQ,GAAG,CACxBC,IAAAA,qBAAW,EAAC,OAAO,UAAUjB,WAC7B,MAAMD,oBACJ,IAAI,CAACjB,OAAO,EACZyE,6BAAkB,EAClBvD,UACA;IAGN;IAEA,MAAcwD,wBAAuC;QACnD,MAAMC,mBAAmB,IAAI,CAACC,mBAAmB,CAC/C,IAAI,CAAClD,iBAAiB,CAACgC,MAAM;QAE/B,MAAMmB,uBAAuBvE,IAAAA,UAAI,EAC/B,IAAI,CAACN,OAAO,EACZ,UACAyE,6BAAkB;QAEpBV,IAAAA,yBAAW,EAACc;QACZ,MAAMb,IAAAA,4BAAe,EACnBa,sBACA1D,KAAK2C,SAAS,CAACa,kBAAkB,MAAM;IAE3C;IAEA,MAAcG,oBAAmC;QAC/C,MAAM9C,eAAe,IAAI,CAAC+C,iBAAiB,CAAC,IAAI,CAAC/C,YAAY,CAAC0B,MAAM;QACpE,MAAMsB,OAAO1E,IAAAA,UAAI,EAAC,IAAI,CAACN,OAAO,EAAE,UAAUiF,wBAAa;QACvDlB,IAAAA,yBAAW,EAACiB;QACZ,MAAMhB,IAAAA,4BAAe,EAACgB,MAAM7D,KAAK2C,SAAS,CAAC9B,cAAc,MAAM;IACjE;IAEA,MAAMkD,kBACJhE,QAAgB,EAChBhB,IAA+B,EAChB;QADfA,IAAAA,iBAAAA,OAAwB;QAExB,IAAI,CAACyB,cAAc,CAACO,GAAG,CACrBC,IAAAA,qBAAW,EAACjC,MAAM,UAAUgB,WAC5B,MAAMD,oBAAoB,IAAI,CAACjB,OAAO,EAAEmF,yBAAc,EAAEjE,UAAUhB;IAEtE;IAEA,MAAMkF,wBACJlE,QAAgB,EAChBhB,IAA+B,EAChB;QADfA,IAAAA,iBAAAA,OAAwB;QAExB,IAAI,CAAC0B,oBAAoB,CAACM,GAAG,CAC3BC,IAAAA,qBAAW,EAACjC,MAAM,UAAUgB,WAC5B,MAAMD,oBACJ,IAAI,CAACjB,OAAO,EACZqF,0CAA+B,EAC/BnE,UACAhB;IAGN;IAEA,MAAMoF,iBACJpE,QAAgB,EAChBhB,IAA+B,EAChB;QADfA,IAAAA,iBAAAA,OAAwB;QAExB,IAAI,CAAC8B,YAAY,CAACE,GAAG,CACnBC,IAAAA,qBAAW,EAACjC,MAAM,UAAUgB,WAC5B,MAAMD,oBAAoB,IAAI,CAACjB,OAAO,EAAEiF,wBAAa,EAAE/D,UAAUhB;IAErE;IAEQ6E,kBAAkBQ,UAAkC,EAAgB;QAC1E,MAAMC,cAA+C,CAAC;QACtD,MAAMC,SAAkC,IAAIC;QAC5C,MAAMC,SAA2C,IAAID;QACrD,MAAME,UAA6C,IAAIF;QAEvD,KAAK,MAAMG,aAAaN,WAAY;YAClC,IAAIM,UAAUL,WAAW,EAAE;gBACzB,KAAK,MAAM,CAACM,GAAGC,EAAE,IAAI5C,OAAO6C,OAAO,CAACH,UAAUL,WAAW,EAAG;oBAC1D,IAAI,CAACA,WAAW,CAACM,EAAE,EAAE;wBACnBN,WAAW,CAACM,EAAE,GAAGC;oBACnB;gBACF;YACF;YAEA,IAAIF,UAAUJ,MAAM,EAAE;gBACpB,KAAK,MAAMQ,SAASJ,UAAUJ,MAAM,CAAE;oBACpC,IAAI,CAACA,OAAOS,GAAG,CAACD,MAAMhG,IAAI,GAAG;wBAC3BwF,OAAOvD,GAAG,CAAC+D,MAAMhG,IAAI,EAAEgG;oBACzB;gBACF;YACF;YAEA,IAAIJ,UAAUF,MAAM,EAAE;gBACpB,KAAK,MAAMQ,SAASN,UAAUF,MAAM,CAAE;oBACpC,IAAI,CAACA,OAAOO,GAAG,CAACC,MAAMC,EAAE,GAAI;wBAC1BT,OAAOzD,GAAG,CAACiE,MAAMC,EAAE,EAAGD;oBACxB;gBACF;YACF;YAEA,IAAIN,UAAUD,OAAO,EAAE;gBACrB,KAAK,MAAMS,UAAUR,UAAUD,OAAO,CAAE;oBACtC,MAAMQ,KAAKC,OAAOD,EAAE;oBACpB,IAAIA,MAAM,MAAM;wBACd,uEAAuE;wBACvE,MAAME,WAAWV,QAAQW,GAAG,CAACH;wBAC7B,IAAIE,YAAY,MAAM;4BACpBV,QAAQ1D,GAAG,CAACkE,IAAIC;wBAClB,OAAO,IAAIA,OAAOV,MAAM,IAAI,QAAQW,SAASX,MAAM,IAAI,MAAM;4BAC3D,KAAK,MAAMQ,SAASE,OAAOV,MAAM,CAAE;gCACjC,IAAI,CAACW,SAASX,MAAM,CAACa,QAAQ,CAACL,QAAQ;oCACpCG,SAASX,MAAM,CAACc,IAAI,CAACN;gCACvB;4BACF;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAO;YACLO,SAAS;YACTlB;YACAC,QAAQ;mBAAIA,OAAO/B,MAAM;aAAG;YAC5BiC,QAAQ;mBAAIA,OAAOjC,MAAM;aAAG;YAC5BkC,SAAS;mBAAIA,QAAQlC,MAAM;aAAG;QAChC;IACF;IAEQiD,oBAAoBrE,SAAkC,EAAE;QAC9D,MAAMC,WAAkE;YACtE6B,OAAO;gBACL,SAAS,EAAE;YACb;YACA,4EAA4E;YAC5EwC,UAAU,EAAE;YACZC,aAAa,EAAE;YACfC,eAAe,EAAE;YACjBC,kBAAkB;gBACf,YAAS,IAAI,CAACC,OAAO,GAAC;gBACtB,YAAS,IAAI,CAACA,OAAO,GAAC;aACxB;YACDC,eAAe,EAAE;YACjBC,eAAe,EAAE;QACnB;QACA,KAAK,MAAM7D,KAAKf,UAAW;YACzBa,OAAOC,MAAM,CAACb,SAAS6B,KAAK,EAAEf,EAAEe,KAAK;YACrC,IAAIf,EAAE4D,aAAa,CAACE,MAAM,EAAE5E,SAAS0E,aAAa,GAAG5D,EAAE4D,aAAa;YACpE,2FAA2F;YAC3F,IAAI5D,EAAEyD,aAAa,CAACK,MAAM,EAAE5E,SAASuE,aAAa,GAAGzD,EAAEyD,aAAa;QACtE;QACAvE,SAAS6B,KAAK,GAAGb,gBAAgBhB,SAAS6B,KAAK;QAC/C,OAAO7B;IACT;IAEQ6E,0BACNC,QAAkC,EAClC/E,SAAwC,EACxCgF,cAAwB,EACH;QACrB,MAAM/E,WAAW;YACfgF,YAAYC,IAAAA,sDAAiC,EAACH;YAC9CI,aAAaH;QACf;QACA,KAAK,MAAMjE,KAAKf,UAAW;YACzBa,OAAOC,MAAM,CAACb,UAAUc;QAC1B;QACA,OAAOE,gBAAgBhB;IACzB;IAEA,MAAcmF,mBACZlC,WAAwB,EACxBmC,WAA2D,EAC3DC,kBAAwD,EACzC;YAGCD,0BACDA,yBACFA;QAJb,MAAMN,WAAWO,6BAAAA,qBAAsB;YACrC,GAAGD,WAAW;YACdE,aAAa,AAACF,CAAAA,CAAAA,2BAAAA,+BAAAA,YAAaE,WAAW,YAAxBF,2BAA4B,EAAE,AAAD,EAAGG,GAAG,CAACC,iCAAY;YAC9DC,YAAY,AAACL,CAAAA,CAAAA,0BAAAA,+BAAAA,YAAaK,UAAU,YAAvBL,0BAA2B,EAAE,AAAD,EAAGG,GAAG,CAACC,iCAAY;YAC5DE,UAAU,AAACN,CAAAA,CAAAA,wBAAAA,+BAAAA,YAAaM,QAAQ,YAArBN,wBAAyB,EAAE,AAAD,EAAGG,GAAG,CAACC,iCAAY;QAC1D;QACA,MAAMG,gBAAgB,IAAI,CAACvB,mBAAmB,CAAC,IAAI,CAAChF,cAAc,CAAC+B,MAAM;QACzE,MAAMyE,oBAAoB7H,IAAAA,UAAI,EAAC,IAAI,CAACN,OAAO,EAAEmF,yBAAc;QAC3D,MAAMiD,8BAA8B9H,IAAAA,UAAI,EACtC,IAAI,CAACN,OAAO,EACZ,UACA,AAAC,KAAEqI,oCAAyB,GAAC;QAE/B,MAAMC,kCAAkChI,IAAAA,UAAI,EAC1C,IAAI,CAACN,OAAO,EACZ,UACA,AAAC,KAAEuI,8CAAmC,GAAC;QAEzCxE,IAAAA,yBAAW,EAACoE;QACZpE,IAAAA,yBAAW,EAACqE;QACZrE,IAAAA,yBAAW,EAACuE;QACZ,MAAMtE,IAAAA,4BAAe,EACnBmE,mBACAhH,KAAK2C,SAAS,CAACoE,eAAe,MAAM;QAEtC,MAAMlE,IAAAA,4BAAe,EACnBoE,6BACA,wDAAwD;QACxD,4BAA4B;QAC5BI,IAAAA,8CAAyB,EAACN;QAG5B,MAAMO,uBAAuBtH,KAAK2C,SAAS,CACzCuD,SAASQ,WAAW,CAACa,MAAM,CAACC,8DAA0B;QAGxD,MAAM3E,IAAAA,4BAAe,EACnBsE,iCACA,AAAC,gDAA6CnH,KAAK2C,SAAS,CAC1D2E,wBACA;QAGJ,MAAMG,YAAY;eAAIpD,YAAYzF,IAAI,CAAC8I,IAAI;SAAG;QAC9C,IAAIrD,YAAYsD,MAAM,CAACC,GAAG,EAAE;YAC1BH,UAAUnC,IAAI,CAAC;QACjB;QACA,IAAIjB,YAAYsD,MAAM,CAACE,KAAK,EAAE;YAC5BJ,UAAUnC,IAAI,CAAC;QACjB;QAEA,MAAMa,iBAAiB2B,IAAAA,sBAAe,EAACL;QACvC,MAAMM,sBAAsB,IAAI,CAAC9B,yBAAyB,CACxDC,UACA,IAAI,CAACzF,oBAAoB,CAAC8B,MAAM,IAChC4D;QAEF,MAAM6B,wBAAwB,AAAC,6BAA0BhI,KAAK2C,SAAS,CACrEoF,qBACA,MACA,KACA;QACF,MAAMlF,IAAAA,4BAAe,EACnB1D,IAAAA,UAAI,EAAC,IAAI,CAACN,OAAO,EAAE,UAAU,IAAI,CAACgH,OAAO,EAAE,sBAC3CmC;QAEF,MAAMnF,IAAAA,4BAAe,EACnB1D,IAAAA,UAAI,EAAC,IAAI,CAACN,OAAO,EAAE,UAAU,IAAI,CAACgH,OAAO,EAAE,oBAC3CoC,wCAAmB;IAEvB;IAEA,MAAcC,gCAA+C;YAK1CC;QAJjB,MAAMA,qBAAqB,IAAI,CAACC,wBAAwB,CACtD,IAAI,CAACzH,mBAAmB,CAAC4B,MAAM;QAGjC,MAAM8F,WAAWF,CAAAA,uCAAAA,kCAAAA,mBAAoBG,UAAU,CAAC,IAAI,qBAAnCH,gCAAqCE,QAAQ,KAAI,EAAE;QAEpE,MAAME,+BAA+BpJ,IAAAA,UAAI,EACvC,IAAI,CAACN,OAAO,EACZ,UACA,IAAI,CAACgH,OAAO,EACZ,AAAC,KAAE2C,+CAAoC;QAEzC5F,IAAAA,yBAAW,EAAC2F;QACZ,MAAM1F,IAAAA,4BAAe,EACnB0F,8BACAvI,KAAK2C,SAAS,CAAC0F,UAAU,MAAM;IAEnC;IAEA,MAAcI,6BAA4C;QACxD,MAAMC,wBAAwB,IAAI,CAAClD,mBAAmB,CACpD;YACE,IAAI,CAAChF,cAAc,CAAC4E,GAAG,CAACpE,IAAAA,qBAAW,EAAC,SAAS,UAAU;YACvD,IAAI,CAACR,cAAc,CAAC4E,GAAG,CAACpE,IAAAA,qBAAW,EAAC,SAAS,UAAU;SACxD,CAACuG,MAAM,CAACoB;QAEX,MAAMC,4BAA4BzJ,IAAAA,UAAI,EACpC,IAAI,CAACN,OAAO,EACZ,AAAC,cAAWmF,yBAAc;QAE5BpB,IAAAA,yBAAW,EAACgG;QACZ,MAAM/F,IAAAA,4BAAe,EACnB+F,2BACA5I,KAAK2C,SAAS,CAAC+F,uBAAuB,MAAM;IAEhD;IAEA,MAAMG,iBACJ9I,QAAgB,EAChBhB,IAA+B,EAChB;QADfA,IAAAA,iBAAAA,OAAwB;QAExB,IAAI,CAAC2B,aAAa,CAACK,GAAG,CACpBC,IAAAA,qBAAW,EAACjC,MAAM,UAAUgB,WAC5B,MAAMD,oBACJ,IAAI,CAACjB,OAAO,EACZ,AAAC,KAAEiK,6BAAkB,GAAC,SACtB/I,UACAhB;IAGN;IAEQgK,mBAAmB5H,SAAqC,EAAE;QAChE,MAAMC,WAA6B;YACjCwG,KAAK,CAAC;YACNoB,oBAAoB;YACpB/F,OAAO,CAAC;YACRgG,sBAAsB;QACxB;QACA,KAAK,MAAM/G,KAAKf,UAAW;YACzBa,OAAOC,MAAM,CAACb,SAASwG,GAAG,EAAE1F,EAAE0F,GAAG;YACjC5F,OAAOC,MAAM,CAACb,SAAS6B,KAAK,EAAEf,EAAEe,KAAK;YAErC7B,SAAS4H,kBAAkB,GACzB5H,SAAS4H,kBAAkB,IAAI9G,EAAE8G,kBAAkB;YACrD5H,SAAS6H,oBAAoB,GAC3B7H,SAAS6H,oBAAoB,IAAI/G,EAAE+G,oBAAoB;QAC3D;QACA7H,SAASwG,GAAG,GAAGxF,gBAAgBhB,SAASwG,GAAG;QAC3CxG,SAAS6B,KAAK,GAAGb,gBAAgBhB,SAAS6B,KAAK;QAC/C,OAAO7B;IACT;IAEA,MAAc8H,wBAAuC;QACnD,MAAMC,eAAe,IAAI,CAACJ,kBAAkB,CAAC,IAAI,CAACrI,aAAa,CAAC6B,MAAM;QACtE,MAAMG,OAAO1C,KAAK2C,SAAS,CAACwG,cAAc,MAAM;QAEhD,MAAMC,uBAAuBjK,IAAAA,UAAI,EAC/B,IAAI,CAACN,OAAO,EACZ,UACA,AAAC,KAAEiK,6BAAkB,GAAC;QAExB,MAAMO,qBAAqBlK,IAAAA,UAAI,EAC7B,IAAI,CAACN,OAAO,EACZ,UACA,AAAC,KAAEiK,6BAAkB,GAAC;QAExBlG,IAAAA,yBAAW,EAACwG;QACZxG,IAAAA,yBAAW,EAACyG;QACZ,MAAMxG,IAAAA,4BAAe,EAACuG,sBAAsB1G;QAC5C,MAAMG,IAAAA,4BAAe,EACnBwG,oBACA,AAAC,+BAA4BrJ,KAAK2C,SAAS,CAACD;IAEhD;IAEA;;GAEC,GACD,MAAM4G,uBACJvJ,QAAgB,EAChBhB,IAAwD,EACtC;QAClB,MAAMwK,yBAAyB5K,gBAC7BoB,UACA,IAAI,CAAClB,OAAO,EACZ2K,8BAAmB,EACnBzK,MACA;QAGF,uHAAuH;QACvH,IAAI,CAACQ,IAAAA,cAAU,EAACgK,yBAAyB;YACvC,OAAO;QACT;QAEA,IAAI,CAAC5I,mBAAmB,CAACI,GAAG,CAC1BC,IAAAA,qBAAW,EACTjC,SAAS,gBAAgBA,SAAS,oBAAoB,SAASA,MAC/D,UACAgB,WAEF,MAAMD,oBACJ,IAAI,CAACjB,OAAO,EACZ2K,8BAAmB,EACnBzJ,UACAhB;QAIJ,OAAO;IACT;IAEA0K,sBAAsBrJ,GAAa,EAAE;QACnC,OAAO,IAAI,CAACO,mBAAmB,CAACyE,GAAG,CAAChF;IACtC;IAEAsJ,yBAAyBtJ,GAAa,EAAE;QACtC,OAAO,IAAI,CAACO,mBAAmB,CAACR,MAAM,CAACC;IACzC;IAEQgI,yBACNjH,SAAgD,EAC5B;QACpB,MAAMC,WAA+B;YACnCmE,SAAS;YACT+C,YAAY,CAAC;YACbqB,kBAAkB,EAAE;YACpBC,WAAW,CAAC;QACd;QACA,IAAIC,kBAAyDC;QAC7D,KAAK,MAAM5H,KAAKf,UAAW;YACzBa,OAAOC,MAAM,CAACb,SAASwI,SAAS,EAAE1H,EAAE0H,SAAS;YAC7C5H,OAAOC,MAAM,CAACb,SAASkH,UAAU,EAAEpG,EAAEoG,UAAU;YAC/C,IAAIpG,EAAE2H,eAAe,EAAE;gBACrBA,kBAAkB3H,EAAE2H,eAAe;YACrC;QACF;QACAzI,SAASwI,SAAS,GAAGxH,gBAAgBhB,SAASwI,SAAS;QACvDxI,SAASkH,UAAU,GAAGlG,gBAAgBhB,SAASkH,UAAU;QACzD,MAAMyB,2BAA2B,CAC/BC;gBAIcH;YAFd,OAAO;gBACL,GAAGG,GAAG;gBACNC,OAAO;uBAAKJ,CAAAA,yBAAAA,mCAAAA,gBAAiBI,KAAK,YAAtBJ,yBAA0B,EAAE;uBAAMG,IAAIC,KAAK;iBAAC;YAC1D;QACF;QACA,KAAK,MAAM7J,OAAO4B,OAAO0F,IAAI,CAACtG,SAASkH,UAAU,EAAG;YAClD,MAAM4B,QAAQ9I,SAASkH,UAAU,CAAClI,IAAI;YACtCgB,SAASkH,UAAU,CAAClI,IAAI,GAAG2J,yBAAyBG;QACtD;QACA,KAAK,MAAM9J,OAAO4B,OAAO0F,IAAI,CAACtG,SAASwI,SAAS,EAAG;YACjD,MAAMM,QAAQ9I,SAASwI,SAAS,CAACxJ,IAAI;YACrCgB,SAASwI,SAAS,CAACxJ,IAAI,GAAG2J,yBAAyBG;QACrD;QACA,KAAK,MAAMF,OAAOhI,OAAOO,MAAM,CAACnB,SAASwI,SAAS,EAAEO,MAAM,CACxDnI,OAAOO,MAAM,CAACnB,SAASkH,UAAU,GAChC;YACD,KAAK,MAAM8B,WAAWJ,IAAI3B,QAAQ,CAAE;gBAClC,IAAI,CAAC+B,QAAQC,MAAM,EAAE;oBACnBD,QAAQC,MAAM,GAAGC,IAAAA,iCAAgB,EAACF,QAAQG,cAAc,EAAE,EAAE,EAAE;wBAC5DC,WAAW;wBACXC,WAAW;wBACXC,QAAQ;oBACV,GAAGC,MAAM,CAACC,UAAU,CAAC,OAAO;gBAC9B;YACF;QACF;QACAxJ,SAASuI,gBAAgB,GAAG3H,OAAO0F,IAAI,CAACtG,SAASkH,UAAU;QAE3D,OAAOlH;IACT;IAEA,MAAcyJ,0BAAyC;QACrD,MAAM1C,qBAAqB,IAAI,CAACC,wBAAwB,CACtD,IAAI,CAACzH,mBAAmB,CAAC4B,MAAM;QAGjC,8CAA8C;QAC9C,IAAK,MAAMnC,OAAO+H,mBAAmBG,UAAU,CAAE;YAC/CH,mBAAmBG,UAAU,CAAClI,IAAI,CAACiI,QAAQ,CAACyC,OAAO,CAAC,CAACV;gBACnD,IAAI,CAACA,QAAQC,MAAM,CAACU,UAAU,CAAC,MAAM;oBACnC,MAAMC,aAAaC,IAAAA,8BAAc,EAACb,QAAQC,MAAM;oBAChD,IAAIW,WAAWnD,KAAK,IAAI,CAACmD,WAAWE,QAAQ,EAAE;wBAC5C,MAAM,qBAA8C,CAA9C,IAAIC,MAAM,AAAC,qBAAkBf,QAAQC,MAAM,GAA3C,qBAAA;mCAAA;wCAAA;0CAAA;wBAA6C;oBACrD;oBACAD,QAAQC,MAAM,GAAGW,WAAWE,QAAQ;gBACtC;YACF;QACF;QAEA,MAAM3B,yBAAyBpK,IAAAA,UAAI,EACjC,IAAI,CAACN,OAAO,EACZ,UACA2K,8BAAmB;QAErB5G,IAAAA,yBAAW,EAAC2G;QACZ,MAAM1G,IAAAA,4BAAe,EACnB0G,wBACAvJ,KAAK2C,SAAS,CAACwF,oBAAoB,MAAM;IAE7C;IAEA,MAAMiD,kBAAkBrL,QAAgB,EAAiB;QACvD,IAAI,CAACa,cAAc,CAACG,GAAG,CACrBC,IAAAA,qBAAW,EAAC,SAAS,UAAUjB,WAC/B,MAAMD,oBAAoB,IAAI,CAACjB,OAAO,EAAEwM,yBAAc,EAAEtL;IAE5D;IAEQ0D,oBAAoBtC,SAAkC,EAAE;QAC9D,MAAMC,WAA0B,CAAC;QACjC,KAAK,MAAMc,KAAKf,UAAW;YACzBa,OAAOC,MAAM,CAACb,UAAUc;QAC1B;QACA,OAAOE,gBAAgBhB;IACzB;IAEA,MAAckK,qBAAoC;QAChD,MAAMC,gBAAgB,IAAI,CAAC9H,mBAAmB,CAAC,IAAI,CAAC7C,cAAc,CAAC2B,MAAM;QACzE,MAAMiJ,oBAAoBrM,IAAAA,UAAI,EAAC,IAAI,CAACN,OAAO,EAAE,UAAUwM,yBAAc;QACrEzI,IAAAA,yBAAW,EAAC4I;QACZ,MAAM3I,IAAAA,4BAAe,EACnB2I,mBACAxL,KAAK2C,SAAS,CAAC4I,eAAe,MAAM;IAExC;IAEA,MAAME,eAAe,KAQpB,EAAE;QARkB,IAAA,EACnBjF,WAAW,EACXC,kBAAkB,EAClBpC,WAAW,EAKZ,GARoB;QASnB,MAAM,IAAI,CAAChC,mBAAmB;QAC9B,MAAM,IAAI,CAACa,qBAAqB;QAChC,MAAM,IAAI,CAACK,qBAAqB;QAChC,MAAM,IAAI,CAACgD,kBAAkB,CAAClC,aAAamC,aAAaC;QACxD,MAAM,IAAI,CAACgC,0BAA0B;QACrC,MAAM,IAAI,CAACoC,uBAAuB;QAClC,MAAM,IAAI,CAAC3C,6BAA6B;QACxC,MAAM,IAAI,CAACgB,qBAAqB;QAChC,MAAM,IAAI,CAACoC,kBAAkB;QAE7B,IAAII,QAAQC,GAAG,CAACC,eAAe,IAAI,MAAM;YACvC,MAAM,IAAI,CAACjI,iBAAiB;QAC9B;IACF;IAnpBAkI,YAAY,EACVhN,OAAO,EACPgH,OAAO,EACPtE,aAAa,EAKd,CAAE;aAvBKlB,kBAAiD,IAAIkE;aACrDjE,oBAAqD,IAAIiE;aACzDhE,oBAAkD,IAAIgE;aACtD/D,iBAA+C,IAAI+D;aACnD9D,uBAA2D,IAAI8D;aAC/D7D,gBAAiD,IAAI6D;aACrD5D,sBACN,IAAI4D;aACE3D,iBAA6C,IAAI2D;aACjD1D,eAA4C,IAAI0D;QAetD,IAAI,CAAC1F,OAAO,GAAGA;QACf,IAAI,CAACgH,OAAO,GAAGA;QACf,IAAI,CAACtE,aAAa,GAAGA;IACvB;AAwoBF;AAEA,SAASa,gBAAgB0J,GAAwB;IAC/C,OAAO9J,OAAO0F,IAAI,CAACoE,KAChBC,IAAI,GACJC,MAAM,CACL,CAACC,KAAK7L;QACJ6L,GAAG,CAAC7L,IAAI,GAAG0L,GAAG,CAAC1L,IAAI;QACnB,OAAO6L;IACT,GACA,CAAC;AAEP", "ignoreList": [0]}