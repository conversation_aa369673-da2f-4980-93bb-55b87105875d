{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/next-types-plugin/index.ts"], "sourcesContent": ["import type { Rewrite, Redirect } from '../../../../lib/load-custom-routes'\n\nimport fs from 'fs/promises'\nimport { webpack, sources } from 'next/dist/compiled/webpack/webpack'\nimport path from 'path'\n\nimport { WEBPACK_LAYERS } from '../../../../lib/constants'\nimport { denormalizePagePath } from '../../../../shared/lib/page-path/denormalize-page-path'\nimport { ensureLeadingSlash } from '../../../../shared/lib/page-path/ensure-leading-slash'\nimport { normalizePathSep } from '../../../../shared/lib/page-path/normalize-path-sep'\nimport { HTTP_METHODS } from '../../../../server/web/http'\nimport { isDynamicRoute } from '../../../../shared/lib/router/utils'\nimport { normalizeAppPath } from '../../../../shared/lib/router/utils/app-paths'\nimport { getPageFromPath } from '../../../entries'\nimport type { PageExtensions } from '../../../page-extensions-type'\nimport { getProxiedPluginState } from '../../../build-context'\nimport type { CacheLife } from '../../../../server/use-cache/cache-life'\n\nconst PLUGIN_NAME = 'NextTypesPlugin'\n\ntype Rewrites = {\n  fallback: Rewrite[]\n  afterFiles: Rewrite[]\n  beforeFiles: Rewrite[]\n}\n\ninterface Options {\n  dir: string\n  distDir: string\n  appDir: string\n  dev: boolean\n  isEdgeServer: boolean\n  pageExtensions: PageExtensions\n  cacheLifeConfig: undefined | { [profile: string]: CacheLife }\n  originalRewrites: Rewrites | undefined\n  originalRedirects: Redirect[] | undefined\n}\n\nfunction createTypeGuardFile(\n  fullPath: string,\n  relativePath: string,\n  options: {\n    type: 'layout' | 'page' | 'route'\n    slots?: string[]\n  }\n) {\n  return `// File: ${fullPath}\nimport * as entry from '${relativePath}.js'\n${\n  options.type === 'route'\n    ? `import type { NextRequest } from 'next/server.js'`\n    : `import type { ResolvingMetadata, ResolvingViewport } from 'next/dist/lib/metadata/types/metadata-interface.js'`\n}\n\ntype TEntry = typeof import('${relativePath}.js')\n\ntype SegmentParams<T extends Object = any> = T extends Record<string, any>\n  ? { [K in keyof T]: T[K] extends string ? string | string[] | undefined : never }\n  : T\n\n// Check that the entry is a valid entry\ncheckFields<Diff<{\n  ${\n    options.type === 'route'\n      ? HTTP_METHODS.map((method) => `${method}?: Function`).join('\\n  ')\n      : 'default: Function'\n  }\n  config?: {}\n  generateStaticParams?: Function\n  revalidate?: RevalidateRange<TEntry> | false\n  dynamic?: 'auto' | 'force-dynamic' | 'error' | 'force-static'\n  dynamicParams?: boolean\n  fetchCache?: 'auto' | 'force-no-store' | 'only-no-store' | 'default-no-store' | 'default-cache' | 'only-cache' | 'force-cache'\n  preferredRegion?: 'auto' | 'global' | 'home' | string | string[]\n  runtime?: 'nodejs' | 'experimental-edge' | 'edge'\n  maxDuration?: number\n  ${\n    options.type === 'route'\n      ? ''\n      : `\n  metadata?: any\n  generateMetadata?: Function\n  viewport?: any\n  generateViewport?: Function\n  experimental_ppr?: boolean\n  `\n  }\n}, TEntry, ''>>()\n\n${options.type === 'route' ? `type RouteContext = { params: Promise<SegmentParams> }` : ''}\n${\n  options.type === 'route'\n    ? HTTP_METHODS.map(\n        (method) => `// Check the prop type of the entry function\nif ('${method}' in entry) {\n  checkFields<\n    Diff<\n      ParamCheck<Request | NextRequest>,\n      {\n        __tag__: '${method}'\n        __param_position__: 'first'\n        __param_type__: FirstArg<MaybeField<TEntry, '${method}'>>\n      },\n      '${method}'\n    >\n  >()\n  checkFields<\n    Diff<\n      ParamCheck<RouteContext>,\n      {\n        __tag__: '${method}'\n        __param_position__: 'second'\n        __param_type__: SecondArg<MaybeField<TEntry, '${method}'>>\n      },\n      '${method}'\n    >\n  >()\n  ${\n    ''\n    // Adding void to support never return type without explicit return:\n    // e.g. notFound() will interrupt the execution but the handler return type is inferred as void.\n    // x-ref: https://github.com/microsoft/TypeScript/issues/16608#issuecomment-309327984\n  }\n  checkFields<\n    Diff<\n      {\n        __tag__: '${method}',\n        __return_type__: Response | void | never | Promise<Response | void | never>\n      },\n      {\n        __tag__: '${method}',\n        __return_type__: ReturnType<MaybeField<TEntry, '${method}'>>\n      },\n      '${method}'\n    >\n  >()\n}\n`\n      ).join('')\n    : `// Check the prop type of the entry function\ncheckFields<Diff<${\n        options.type === 'page' ? 'PageProps' : 'LayoutProps'\n      }, FirstArg<TEntry['default']>, 'default'>>()\n\n// Check the arguments and return type of the generateMetadata function\nif ('generateMetadata' in entry) {\n  checkFields<Diff<${\n    options.type === 'page' ? 'PageProps' : 'LayoutProps'\n  }, FirstArg<MaybeField<TEntry, 'generateMetadata'>>, 'generateMetadata'>>()\n  checkFields<Diff<ResolvingMetadata, SecondArg<MaybeField<TEntry, 'generateMetadata'>>, 'generateMetadata'>>()\n}\n\n// Check the arguments and return type of the generateViewport function\nif ('generateViewport' in entry) {\n  checkFields<Diff<${\n    options.type === 'page' ? 'PageProps' : 'LayoutProps'\n  }, FirstArg<MaybeField<TEntry, 'generateViewport'>>, 'generateViewport'>>()\n  checkFields<Diff<ResolvingViewport, SecondArg<MaybeField<TEntry, 'generateViewport'>>, 'generateViewport'>>()\n}\n`\n}\n// Check the arguments and return type of the generateStaticParams function\nif ('generateStaticParams' in entry) {\n  checkFields<Diff<{ params: SegmentParams }, FirstArg<MaybeField<TEntry, 'generateStaticParams'>>, 'generateStaticParams'>>()\n  checkFields<Diff<{ __tag__: 'generateStaticParams', __return_type__: any[] | Promise<any[]> }, { __tag__: 'generateStaticParams', __return_type__: ReturnType<MaybeField<TEntry, 'generateStaticParams'>> }>>()\n}\n\nexport interface PageProps {\n  params?: Promise<SegmentParams>\n  searchParams?: Promise<any>\n}\nexport interface LayoutProps {\n  children?: React.ReactNode\n${\n  options.slots\n    ? options.slots.map((slot) => `  ${slot}: React.ReactNode`).join('\\n')\n    : ''\n}\n  params?: Promise<SegmentParams>\n}\n\n// =============\n// Utility types\ntype RevalidateRange<T> = T extends { revalidate: any } ? NonNegative<T['revalidate']> : never\n\n// If T is unknown or any, it will be an empty {} type. Otherwise, it will be the same as Omit<T, keyof Base>.\ntype OmitWithTag<T, K extends keyof any, _M> = Omit<T, K>\ntype Diff<Base, T extends Base, Message extends string = ''> = 0 extends (1 & T) ? {} : OmitWithTag<T, keyof Base, Message>\n\ntype FirstArg<T extends Function> = T extends (...args: [infer T, any]) => any ? unknown extends T ? any : T : never\ntype SecondArg<T extends Function> = T extends (...args: [any, infer T]) => any ? unknown extends T ? any : T : never\ntype MaybeField<T, K extends string> = T extends { [k in K]: infer G } ? G extends Function ? G : never : never\n\n${\n  options.type === 'route'\n    ? `type ParamCheck<T> = {\n  __tag__: string\n  __param_position__: string\n  __param_type__: T\n}`\n    : ''\n}\n\nfunction checkFields<_ extends { [k in keyof any]: never }>() {}\n\n// https://github.com/sindresorhus/type-fest\ntype Numeric = number | bigint\ntype Zero = 0 | 0n\ntype Negative<T extends Numeric> = T extends Zero ? never : \\`\\${T}\\` extends \\`-\\${string}\\` ? T : never\ntype NonNegative<T extends Numeric> = T extends Zero ? T : Negative<T> extends never ? T : '__invalid_negative_number__'\n`\n}\n\nasync function collectNamedSlots(layoutPath: string) {\n  const layoutDir = path.dirname(layoutPath)\n  const items = await fs.readdir(layoutDir, { withFileTypes: true })\n  const slots = []\n  for (const item of items) {\n    if (\n      item.isDirectory() &&\n      item.name.startsWith('@') &&\n      // `@children slots are matched to the children prop, and should not be handled separately for type-checking\n      item.name !== '@children'\n    ) {\n      slots.push(item.name.slice(1))\n    }\n  }\n  return slots\n}\n\n// By exposing the static route types separately as string literals,\n// editors can provide autocompletion for them. However it's currently not\n// possible to provide the same experience for dynamic routes.\n\nconst pluginState = getProxiedPluginState({\n  collectedRootParams: {} as Record<string, string[]>,\n  routeTypes: {\n    edge: {\n      static: [],\n      dynamic: [],\n    },\n    node: {\n      static: [],\n      dynamic: [],\n    },\n    extra: {\n      static: [],\n      dynamic: [],\n    },\n  } as Record<\n    'edge' | 'node' | 'extra',\n    Record<'static' | 'dynamic', string[]>\n  >,\n})\n\nfunction formatRouteToRouteType(route: string) {\n  const isDynamic = isDynamicRoute(route)\n  if (isDynamic) {\n    route = route\n      .split('/')\n      .map((part) => {\n        if (part.startsWith('[') && part.endsWith(']')) {\n          if (part.startsWith('[...')) {\n            // /[...slug]\n            return `\\${CatchAllSlug<T>}`\n          } else if (part.startsWith('[[...') && part.endsWith(']]')) {\n            // /[[...slug]]\n            return `\\${OptionalCatchAllSlug<T>}`\n          }\n          // /[slug]\n          return `\\${SafeSlug<T>}`\n        }\n        return part\n      })\n      .join('/')\n  }\n\n  return {\n    isDynamic,\n    routeType: route,\n  }\n}\n\nfunction formatTimespan(seconds: number): string {\n  if (seconds > 0) {\n    if (seconds === 18748800) {\n      return '1 month'\n    }\n    if (seconds === 18144000) {\n      return '1 month'\n    }\n    if (seconds === 604800) {\n      return '1 week'\n    }\n    if (seconds === 86400) {\n      return '1 day'\n    }\n    if (seconds === 3600) {\n      return '1 hour'\n    }\n    if (seconds === 60) {\n      return '1 minute'\n    }\n    if (seconds % 18748800 === 0) {\n      return seconds / 18748800 + ' months'\n    }\n    if (seconds % 18144000 === 0) {\n      return seconds / 18144000 + ' months'\n    }\n    if (seconds % 604800 === 0) {\n      return seconds / 604800 + ' weeks'\n    }\n    if (seconds % 86400 === 0) {\n      return seconds / 86400 + ' days'\n    }\n    if (seconds % 3600 === 0) {\n      return seconds / 3600 + ' hours'\n    }\n    if (seconds % 60 === 0) {\n      return seconds / 60 + ' minutes'\n    }\n  }\n  return seconds + ' seconds'\n}\n\nfunction formatTimespanWithSeconds(seconds: undefined | number): string {\n  if (seconds === undefined) {\n    return 'default'\n  }\n  if (seconds >= 0xfffffffe) {\n    return 'never'\n  }\n  const text = seconds + ' seconds'\n  const descriptive = formatTimespan(seconds)\n  if (descriptive === text) {\n    return text\n  }\n  return text + ' (' + descriptive + ')'\n}\n\nfunction getRootParamsFromLayouts(layouts: Record<string, string[]>) {\n  // Sort layouts by depth (descending)\n  const sortedLayouts = Object.entries(layouts).sort(\n    (a, b) => b[0].split('/').length - a[0].split('/').length\n  )\n\n  if (!sortedLayouts.length) {\n    return []\n  }\n\n  // we assume the shorted layout path is the root layout\n  let rootLayout = sortedLayouts[sortedLayouts.length - 1][0]\n\n  let rootParams = new Set<string>()\n  let isMultipleRootLayouts = false\n\n  for (const [layoutPath, params] of sortedLayouts) {\n    const allSegmentsAreDynamic = layoutPath\n      .split('/')\n      .slice(1, -1)\n      // match dynamic params but not catch-all or optional catch-all\n      .every((segment) => /^\\[[^[.\\]]+\\]$/.test(segment))\n\n    if (allSegmentsAreDynamic) {\n      if (isSubpath(rootLayout, layoutPath)) {\n        // Current path is a subpath of the root layout, update root\n        rootLayout = layoutPath\n        rootParams = new Set(params)\n      } else {\n        // Found another potential root layout\n        isMultipleRootLayouts = true\n        // Add any new params\n        for (const param of params) {\n          rootParams.add(param)\n        }\n      }\n    }\n  }\n\n  // Create result array\n  const result = Array.from(rootParams).map((param) => ({\n    param,\n    optional: isMultipleRootLayouts,\n  }))\n\n  return result\n}\n\nfunction isSubpath(parentLayoutPath: string, potentialChildLayoutPath: string) {\n  // we strip off the `layout` part of the path as those will always conflict with being a subpath\n  const parentSegments = parentLayoutPath.split('/').slice(1, -1)\n  const childSegments = potentialChildLayoutPath.split('/').slice(1, -1)\n\n  // child segments should be shorter or equal to parent segments to be a subpath\n  if (childSegments.length > parentSegments.length || !childSegments.length)\n    return false\n\n  // Verify all segment values are equal\n  return childSegments.every(\n    (childSegment, index) => childSegment === parentSegments[index]\n  )\n}\n\nfunction createServerDefinitions(\n  rootParams: { param: string; optional: boolean }[]\n) {\n  return `\n  declare module 'next/server' {\n\n    import type { AsyncLocalStorage as NodeAsyncLocalStorage } from 'async_hooks'\n    declare global {\n      var AsyncLocalStorage: typeof NodeAsyncLocalStorage\n    }\n    export { NextFetchEvent } from 'next/dist/server/web/spec-extension/fetch-event'\n    export { NextRequest } from 'next/dist/server/web/spec-extension/request'\n    export { NextResponse } from 'next/dist/server/web/spec-extension/response'\n    export { NextMiddleware, MiddlewareConfig } from 'next/dist/server/web/types'\n    export { userAgentFromString } from 'next/dist/server/web/spec-extension/user-agent'\n    export { userAgent } from 'next/dist/server/web/spec-extension/user-agent'\n    export { URLPattern } from 'next/dist/compiled/@edge-runtime/primitives/url'\n    export { ImageResponse } from 'next/dist/server/web/spec-extension/image-response'\n    export type { ImageResponseOptions } from 'next/dist/compiled/@vercel/og/types'\n    export { after } from 'next/dist/server/after'\n    export { connection } from 'next/dist/server/request/connection'\n    export type { UnsafeUnwrappedSearchParams } from 'next/dist/server/request/search-params'\n    export type { UnsafeUnwrappedParams } from 'next/dist/server/request/params'\n    export function unstable_rootParams(): Promise<{ ${rootParams\n      .map(\n        ({ param, optional }) =>\n          // ensure params with dashes are valid keys\n          `${param.includes('-') ? `'${param}'` : param}${optional ? '?' : ''}: string`\n      )\n      .join(', ')} }>\n  }\n  `\n}\n\nfunction createCustomCacheLifeDefinitions(cacheLife: {\n  [profile: string]: CacheLife\n}) {\n  let overloads = ''\n\n  const profileNames = Object.keys(cacheLife)\n  for (let i = 0; i < profileNames.length; i++) {\n    const profileName = profileNames[i]\n    const profile = cacheLife[profileName]\n    if (typeof profile !== 'object' || profile === null) {\n      continue\n    }\n\n    let description = ''\n\n    if (profile.stale === undefined) {\n      description += `\n     * This cache may be stale on clients for the default stale time of the scope before checking with the server.`\n    } else if (profile.stale >= 0xfffffffe) {\n      description += `\n     * This cache may be stale on clients indefinitely before checking with the server.`\n    } else {\n      description += `\n     * This cache may be stale on clients for ${formatTimespan(profile.stale)} before checking with the server.`\n    }\n    if (\n      profile.revalidate !== undefined &&\n      profile.expire !== undefined &&\n      profile.revalidate >= profile.expire\n    ) {\n      description += `\n     * This cache will expire after ${formatTimespan(profile.expire)}. The next request will recompute it.`\n    } else {\n      if (profile.revalidate === undefined) {\n        description += `\n     * It will inherit the default revalidate time of its scope since it does not define its own.`\n      } else if (profile.revalidate >= 0xfffffffe) {\n        // Nothing to mention.\n      } else {\n        description += `\n     * If the server receives a new request after ${formatTimespan(profile.revalidate)}, start revalidating new values in the background.`\n      }\n      if (profile.expire === undefined) {\n        description += `\n     * It will inherit the default expiration time of its scope since it does not define its own.`\n      } else if (profile.expire >= 0xfffffffe) {\n        description += `\n     * It lives for the maximum age of the server cache. If this entry has no traffic for a while, it may serve an old value the next request.`\n      } else {\n        description += `\n     * If this entry has no traffic for ${formatTimespan(profile.expire)} it will expire. The next request will recompute it.`\n      }\n    }\n\n    overloads += `\n    /**\n     * Cache this \\`\"use cache\"\\` for a timespan defined by the \\`${JSON.stringify(profileName)}\\` profile.\n     * \\`\\`\\`\n     *   stale:      ${formatTimespanWithSeconds(profile.stale)}\n     *   revalidate: ${formatTimespanWithSeconds(profile.revalidate)}\n     *   expire:     ${formatTimespanWithSeconds(profile.expire)}\n     * \\`\\`\\`\n     * ${description}\n     */\n    export function unstable_cacheLife(profile: ${JSON.stringify(profileName)}): void\n    `\n  }\n\n  overloads += `\n    /**\n     * Cache this \\`\"use cache\"\\` using a custom timespan.\n     * \\`\\`\\`\n     *   stale: ... // seconds\n     *   revalidate: ... // seconds\n     *   expire: ... // seconds\n     * \\`\\`\\`\n     *\n     * This is similar to Cache-Control: max-age=\\`stale\\`,s-max-age=\\`revalidate\\`,stale-while-revalidate=\\`expire-revalidate\\`\n     *\n     * If a value is left out, the lowest of other cacheLife() calls or the default, is used instead.\n     */\n    export function unstable_cacheLife(profile: {\n      /**\n       * This cache may be stale on clients for ... seconds before checking with the server.\n       */\n      stale?: number,\n      /**\n       * If the server receives a new request after ... seconds, start revalidating new values in the background.\n       */\n      revalidate?: number,\n      /**\n       * If this entry has no traffic for ... seconds it will expire. The next request will recompute it.\n       */\n      expire?: number\n    }): void\n  `\n\n  // Redefine the cacheLife() accepted arguments.\n  return `// Type definitions for Next.js cacheLife configs\n\ndeclare module 'next/cache' {\n  export { unstable_cache } from 'next/dist/server/web/spec-extension/unstable-cache'\n  export {\n    revalidateTag,\n    revalidatePath,\n    unstable_expireTag,\n    unstable_expirePath,\n  } from 'next/dist/server/web/spec-extension/revalidate'\n  export { unstable_noStore } from 'next/dist/server/web/spec-extension/unstable-no-store'\n\n  ${overloads}\n\n  export { cacheTag as unstable_cacheTag } from 'next/dist/server/use-cache/cache-tag'\n}\n`\n}\n\nconst appTypesBasePath = path.join('types', 'app')\n\nexport class NextTypesPlugin {\n  dir: string\n  distDir: string\n  appDir: string\n  dev: boolean\n  isEdgeServer: boolean\n  pageExtensions: string[]\n  pagesDir: string\n  cacheLifeConfig: undefined | { [profile: string]: CacheLife }\n  distDirAbsolutePath: string\n\n  constructor(options: Options) {\n    this.dir = options.dir\n    this.distDir = options.distDir\n    this.appDir = options.appDir\n    this.dev = options.dev\n    this.isEdgeServer = options.isEdgeServer\n    this.pageExtensions = options.pageExtensions\n    this.pagesDir = path.join(this.appDir, '..', 'pages')\n    this.cacheLifeConfig = options.cacheLifeConfig\n    this.distDirAbsolutePath = path.join(this.dir, this.distDir)\n  }\n\n  getRelativePathFromAppTypesDir(moduleRelativePathToAppDir: string) {\n    const moduleAbsolutePath = path.join(\n      this.appDir,\n      moduleRelativePathToAppDir\n    )\n\n    const moduleInAppTypesAbsolutePath = path.join(\n      this.distDirAbsolutePath,\n      appTypesBasePath,\n      moduleRelativePathToAppDir\n    )\n\n    return path.relative(\n      moduleInAppTypesAbsolutePath + '/..',\n      moduleAbsolutePath\n    )\n  }\n\n  collectPage(filePath: string) {\n    const isApp = filePath.startsWith(this.appDir + path.sep)\n    const isPages = !isApp && filePath.startsWith(this.pagesDir + path.sep)\n\n    if (!isApp && !isPages) {\n      return\n    }\n\n    // Filter out non-page and non-route files in app dir\n    if (isApp && !/[/\\\\](?:page|route)\\.[^.]+$/.test(filePath)) {\n      return\n    }\n\n    // Filter out non-page files in pages dir\n    if (\n      isPages &&\n      /[/\\\\](?:_app|_document|_error|404|500)\\.[^.]+$/.test(filePath)\n    ) {\n      return\n    }\n\n    let route = (isApp ? normalizeAppPath : denormalizePagePath)(\n      ensureLeadingSlash(\n        getPageFromPath(\n          path.relative(isApp ? this.appDir : this.pagesDir, filePath),\n          this.pageExtensions\n        )\n      )\n    )\n\n    const { isDynamic, routeType } = formatRouteToRouteType(route)\n\n    pluginState.routeTypes[this.isEdgeServer ? 'edge' : 'node'][\n      isDynamic ? 'dynamic' : 'static'\n    ].push(routeType)\n  }\n\n  apply(compiler: webpack.Compiler) {\n    // From asset root to dist root\n    const assetDirRelative = this.dev\n      ? '..'\n      : this.isEdgeServer\n        ? '..'\n        : '../..'\n\n    const handleModule = async (\n      mod: webpack.NormalModule,\n      compilation: webpack.Compilation\n    ) => {\n      if (!mod.resource) return\n\n      const pageExtensionsRegex = new RegExp(\n        `\\\\.(${this.pageExtensions.join('|')})$`\n      )\n\n      if (!pageExtensionsRegex.test(mod.resource)) return\n\n      if (!mod.resource.startsWith(this.appDir + path.sep)) {\n        if (!this.dev) {\n          if (mod.resource.startsWith(this.pagesDir + path.sep)) {\n            this.collectPage(mod.resource)\n          }\n        }\n        return\n      }\n      if (mod.layer !== WEBPACK_LAYERS.reactServerComponents) return\n\n      // skip for /app/_private dir convention\n      // matches <app-dir>/**/_*\n      const IS_PRIVATE = /(?:\\/[^/]+)*\\/_.*$/.test(\n        mod.resource.replace(this.appDir, '')\n      )\n      if (IS_PRIVATE) return\n\n      const IS_LAYOUT = /[/\\\\]layout\\.[^./\\\\]+$/.test(mod.resource)\n      const IS_PAGE = !IS_LAYOUT && /[/\\\\]page\\.[^.]+$/.test(mod.resource)\n      const IS_ROUTE = !IS_PAGE && /[/\\\\]route\\.[^.]+$/.test(mod.resource)\n      const IS_IMPORTABLE = /\\.(js|jsx|ts|tsx|mjs|cjs)$/.test(mod.resource)\n      const relativePathToApp = path.relative(this.appDir, mod.resource)\n\n      if (!this.dev) {\n        if (IS_PAGE || IS_ROUTE) {\n          this.collectPage(mod.resource)\n        }\n      }\n\n      const typePath = path.join(\n        appTypesBasePath,\n        relativePathToApp.replace(pageExtensionsRegex, '.ts')\n      )\n      const relativeImportPath = normalizePathSep(\n        path\n          .join(this.getRelativePathFromAppTypesDir(relativePathToApp))\n          .replace(pageExtensionsRegex, '')\n      )\n\n      const assetPath = path.join(assetDirRelative, typePath)\n\n      // Typescript won’t allow relative-importing (for example) a .mdx file using the .js extension\n      // so for now we only generate “type guard files” for files that typescript can transform\n      if (!IS_IMPORTABLE) return\n\n      if (IS_LAYOUT) {\n        const rootLayoutPath = normalizeAppPath(\n          ensureLeadingSlash(\n            getPageFromPath(\n              path.relative(this.appDir, mod.resource),\n              this.pageExtensions\n            )\n          )\n        )\n\n        const foundParams = Array.from(\n          rootLayoutPath.matchAll(/\\[(.*?)\\]/g),\n          (match) => match[1]\n        )\n\n        pluginState.collectedRootParams[rootLayoutPath] = foundParams\n\n        const slots = await collectNamedSlots(mod.resource)\n        compilation.emitAsset(\n          assetPath,\n          new sources.RawSource(\n            createTypeGuardFile(mod.resource, relativeImportPath, {\n              type: 'layout',\n              slots,\n            })\n          ) as unknown as webpack.sources.RawSource\n        )\n      } else if (IS_PAGE) {\n        compilation.emitAsset(\n          assetPath,\n          new sources.RawSource(\n            createTypeGuardFile(mod.resource, relativeImportPath, {\n              type: 'page',\n            })\n          ) as unknown as webpack.sources.RawSource\n        )\n      } else if (IS_ROUTE) {\n        compilation.emitAsset(\n          assetPath,\n          new sources.RawSource(\n            createTypeGuardFile(mod.resource, relativeImportPath, {\n              type: 'route',\n            })\n          ) as unknown as webpack.sources.RawSource\n        )\n      }\n    }\n\n    compiler.hooks.compilation.tap(PLUGIN_NAME, (compilation) => {\n      compilation.hooks.processAssets.tapAsync(\n        {\n          name: PLUGIN_NAME,\n          stage: webpack.Compilation.PROCESS_ASSETS_STAGE_OPTIMIZE_HASH,\n        },\n        async (_, callback) => {\n          const promises: Promise<any>[] = []\n\n          // Clear routes\n          if (this.isEdgeServer) {\n            pluginState.routeTypes.edge.dynamic = []\n            pluginState.routeTypes.edge.static = []\n          } else {\n            pluginState.routeTypes.node.dynamic = []\n            pluginState.routeTypes.node.static = []\n          }\n\n          compilation.chunkGroups.forEach((chunkGroup) => {\n            chunkGroup.chunks.forEach((chunk) => {\n              if (!chunk.name) return\n\n              // Here we only track page and route chunks.\n              if (\n                !chunk.name.startsWith('pages/') &&\n                !(\n                  chunk.name.startsWith('app/') &&\n                  (chunk.name.endsWith('/page') ||\n                    chunk.name.endsWith('/route'))\n                )\n              ) {\n                return\n              }\n\n              const chunkModules =\n                compilation.chunkGraph.getChunkModulesIterable(\n                  chunk\n                ) as Iterable<webpack.NormalModule>\n              for (const mod of chunkModules) {\n                promises.push(handleModule(mod, compilation))\n\n                // If this is a concatenation, register each child to the parent ID.\n                const anyModule = mod as unknown as {\n                  modules: webpack.NormalModule[]\n                }\n                if (anyModule.modules) {\n                  anyModule.modules.forEach((concatenatedMod) => {\n                    promises.push(handleModule(concatenatedMod, compilation))\n                  })\n                }\n              }\n            })\n          })\n\n          await Promise.all(promises)\n\n          const rootParams = getRootParamsFromLayouts(\n            pluginState.collectedRootParams\n          )\n          // If we discovered rootParams, we'll override the `next/server` types\n          // since we're able to determine the root params at build time.\n          if (rootParams.length > 0) {\n            const serverTypesPath = path.join(\n              assetDirRelative,\n              'types/server.d.ts'\n            )\n\n            compilation.emitAsset(\n              serverTypesPath,\n              new sources.RawSource(\n                createServerDefinitions(rootParams)\n              ) as unknown as webpack.sources.RawSource\n            )\n          }\n\n          // Support `\"moduleResolution\": \"Node16\" | \"NodeNext\"` with `\"type\": \"module\"`\n\n          const packageJsonAssetPath = path.join(\n            assetDirRelative,\n            'types/package.json'\n          )\n\n          compilation.emitAsset(\n            packageJsonAssetPath,\n            new sources.RawSource(\n              '{\"type\": \"module\"}'\n            ) as unknown as webpack.sources.RawSource\n          )\n\n          if (this.cacheLifeConfig) {\n            const cacheLifeAssetPath = path.join(\n              assetDirRelative,\n              'types/cache-life.d.ts'\n            )\n\n            compilation.emitAsset(\n              cacheLifeAssetPath,\n              new sources.RawSource(\n                createCustomCacheLifeDefinitions(this.cacheLifeConfig)\n              ) as unknown as webpack.sources.RawSource\n            )\n          }\n\n          callback()\n        }\n      )\n    })\n  }\n}\n"], "names": ["fs", "webpack", "sources", "path", "WEBPACK_LAYERS", "denormalizePagePath", "ensureLeadingSlash", "normalizePathSep", "HTTP_METHODS", "isDynamicRoute", "normalizeAppPath", "getPageFromPath", "getProxiedPluginState", "PLUGIN_NAME", "createTypeGuardFile", "fullPath", "relativePath", "options", "type", "map", "method", "join", "slots", "slot", "collectNamedSlots", "<PERSON><PERSON><PERSON>", "layoutDir", "dirname", "items", "readdir", "withFileTypes", "item", "isDirectory", "name", "startsWith", "push", "slice", "pluginState", "collectedRootParams", "routeTypes", "edge", "static", "dynamic", "node", "extra", "formatRouteToRouteType", "route", "isDynamic", "split", "part", "endsWith", "routeType", "formatTimespan", "seconds", "formatTimespanWithSeconds", "undefined", "text", "descriptive", "getRootParamsFromLayouts", "layouts", "sortedLayouts", "Object", "entries", "sort", "a", "b", "length", "rootLayout", "rootParams", "Set", "isMultipleRootLayouts", "params", "allSegmentsAreDynamic", "every", "segment", "test", "isSubpath", "param", "add", "result", "Array", "from", "optional", "parentLayoutPath", "potentialChildLayoutPath", "parentSegments", "childSegments", "childSegment", "index", "createServerDefinitions", "includes", "createCustomCacheLifeDefinitions", "cacheLife", "overloads", "profileNames", "keys", "i", "profileName", "profile", "description", "stale", "revalidate", "expire", "JSON", "stringify", "appTypesBasePath", "NextTypesPlugin", "constructor", "dir", "distDir", "appDir", "dev", "isEdgeServer", "pageExtensions", "pagesDir", "cacheLifeConfig", "distDirAbsolutePath", "getRelativePathFromAppTypesDir", "moduleRelativePathToAppDir", "moduleAbsolutePath", "moduleInAppTypesAbsolutePath", "relative", "collectPage", "filePath", "isApp", "sep", "isPages", "apply", "compiler", "assetDirRelative", "handleModule", "mod", "compilation", "resource", "pageExtensionsRegex", "RegExp", "layer", "reactServerComponents", "IS_PRIVATE", "replace", "IS_LAYOUT", "IS_PAGE", "IS_ROUTE", "IS_IMPORTABLE", "relativePathToApp", "typePath", "relativeImportPath", "assetPath", "rootLayoutPath", "foundParams", "matchAll", "match", "emitAsset", "RawSource", "hooks", "tap", "processAssets", "tapAsync", "stage", "Compilation", "PROCESS_ASSETS_STAGE_OPTIMIZE_HASH", "_", "callback", "promises", "chunkGroups", "for<PERSON>ach", "chunkGroup", "chunks", "chunk", "chunkModules", "chunkGraph", "getChunkModulesIterable", "anyModule", "modules", "concatenatedMod", "Promise", "all", "serverTypesPath", "packageJsonAssetPath", "cacheLifeAssetPath"], "mappings": "AAEA,OAAOA,QAAQ,cAAa;AAC5B,SAASC,OAAO,EAAEC,OAAO,QAAQ,qCAAoC;AACrE,OAAOC,UAAU,OAAM;AAEvB,SAASC,cAAc,QAAQ,4BAA2B;AAC1D,SAASC,mBAAmB,QAAQ,yDAAwD;AAC5F,SAASC,kBAAkB,QAAQ,wDAAuD;AAC1F,SAASC,gBAAgB,QAAQ,sDAAqD;AACtF,SAASC,YAAY,QAAQ,8BAA6B;AAC1D,SAASC,cAAc,QAAQ,sCAAqC;AACpE,SAASC,gBAAgB,QAAQ,gDAA+C;AAChF,SAASC,eAAe,QAAQ,mBAAkB;AAElD,SAASC,qBAAqB,QAAQ,yBAAwB;AAG9D,MAAMC,cAAc;AAoBpB,SAASC,oBACPC,QAAgB,EAChBC,YAAoB,EACpBC,OAGC;IAED,OAAO,CAAC,SAAS,EAAEF,SAAS;wBACN,EAAEC,aAAa;AACvC,EACEC,QAAQC,IAAI,KAAK,UACb,CAAC,iDAAiD,CAAC,GACnD,CAAC,8GAA8G,CAAC,CACrH;;6BAE4B,EAAEF,aAAa;;;;;;;;EAQ1C,EACEC,QAAQC,IAAI,KAAK,UACbV,aAAaW,GAAG,CAAC,CAACC,SAAW,GAAGA,OAAO,WAAW,CAAC,EAAEC,IAAI,CAAC,UAC1D,oBACL;;;;;;;;;;EAUD,EACEJ,QAAQC,IAAI,KAAK,UACb,KACA,CAAC;;;;;;EAMP,CAAC,CACA;;;AAGH,EAAED,QAAQC,IAAI,KAAK,UAAU,CAAC,sDAAsD,CAAC,GAAG,GAAG;AAC3F,EACED,QAAQC,IAAI,KAAK,UACbV,aAAaW,GAAG,CACd,CAACC,SAAW,CAAC;KAChB,EAAEA,OAAO;;;;;kBAKI,EAAEA,OAAO;;qDAE0B,EAAEA,OAAO;;OAEvD,EAAEA,OAAO;;;;;;;kBAOE,EAAEA,OAAO;;sDAE2B,EAAEA,OAAO;;OAExD,EAAEA,OAAO;;;EAGd,EACE,GAID;;;;kBAIe,EAAEA,OAAO;;;;kBAIT,EAAEA,OAAO;wDAC6B,EAAEA,OAAO;;OAE1D,EAAEA,OAAO;;;;AAIhB,CAAC,EACOC,IAAI,CAAC,MACP,CAAC;iBACU,EACTJ,QAAQC,IAAI,KAAK,SAAS,cAAc,cACzC;;;;mBAIY,EACfD,QAAQC,IAAI,KAAK,SAAS,cAAc,cACzC;;;;;;mBAMgB,EACfD,QAAQC,IAAI,KAAK,SAAS,cAAc,cACzC;;;AAGH,CAAC,CACA;;;;;;;;;;;;;AAaD,EACED,QAAQK,KAAK,GACTL,QAAQK,KAAK,CAACH,GAAG,CAAC,CAACI,OAAS,CAAC,EAAE,EAAEA,KAAK,iBAAiB,CAAC,EAAEF,IAAI,CAAC,QAC/D,GACL;;;;;;;;;;;;;;;;AAgBD,EACEJ,QAAQC,IAAI,KAAK,UACb,CAAC;;;;CAIN,CAAC,GACI,GACL;;;;;;;;;AASD,CAAC;AACD;AAEA,eAAeM,kBAAkBC,UAAkB;IACjD,MAAMC,YAAYvB,KAAKwB,OAAO,CAACF;IAC/B,MAAMG,QAAQ,MAAM5B,GAAG6B,OAAO,CAACH,WAAW;QAAEI,eAAe;IAAK;IAChE,MAAMR,QAAQ,EAAE;IAChB,KAAK,MAAMS,QAAQH,MAAO;QACxB,IACEG,KAAKC,WAAW,MAChBD,KAAKE,IAAI,CAACC,UAAU,CAAC,QACrB,4GAA4G;QAC5GH,KAAKE,IAAI,KAAK,aACd;YACAX,MAAMa,IAAI,CAACJ,KAAKE,IAAI,CAACG,KAAK,CAAC;QAC7B;IACF;IACA,OAAOd;AACT;AAEA,oEAAoE;AACpE,0EAA0E;AAC1E,8DAA8D;AAE9D,MAAMe,cAAczB,sBAAsB;IACxC0B,qBAAqB,CAAC;IACtBC,YAAY;QACVC,MAAM;YACJC,QAAQ,EAAE;YACVC,SAAS,EAAE;QACb;QACAC,MAAM;YACJF,QAAQ,EAAE;YACVC,SAAS,EAAE;QACb;QACAE,OAAO;YACLH,QAAQ,EAAE;YACVC,SAAS,EAAE;QACb;IACF;AAIF;AAEA,SAASG,uBAAuBC,KAAa;IAC3C,MAAMC,YAAYtC,eAAeqC;IACjC,IAAIC,WAAW;QACbD,QAAQA,MACLE,KAAK,CAAC,KACN7B,GAAG,CAAC,CAAC8B;YACJ,IAAIA,KAAKf,UAAU,CAAC,QAAQe,KAAKC,QAAQ,CAAC,MAAM;gBAC9C,IAAID,KAAKf,UAAU,CAAC,SAAS;oBAC3B,aAAa;oBACb,OAAO,CAAC,mBAAmB,CAAC;gBAC9B,OAAO,IAAIe,KAAKf,UAAU,CAAC,YAAYe,KAAKC,QAAQ,CAAC,OAAO;oBAC1D,eAAe;oBACf,OAAO,CAAC,2BAA2B,CAAC;gBACtC;gBACA,UAAU;gBACV,OAAO,CAAC,eAAe,CAAC;YAC1B;YACA,OAAOD;QACT,GACC5B,IAAI,CAAC;IACV;IAEA,OAAO;QACL0B;QACAI,WAAWL;IACb;AACF;AAEA,SAASM,eAAeC,OAAe;IACrC,IAAIA,UAAU,GAAG;QACf,IAAIA,YAAY,UAAU;YACxB,OAAO;QACT;QACA,IAAIA,YAAY,UAAU;YACxB,OAAO;QACT;QACA,IAAIA,YAAY,QAAQ;YACtB,OAAO;QACT;QACA,IAAIA,YAAY,OAAO;YACrB,OAAO;QACT;QACA,IAAIA,YAAY,MAAM;YACpB,OAAO;QACT;QACA,IAAIA,YAAY,IAAI;YAClB,OAAO;QACT;QACA,IAAIA,UAAU,aAAa,GAAG;YAC5B,OAAOA,UAAU,WAAW;QAC9B;QACA,IAAIA,UAAU,aAAa,GAAG;YAC5B,OAAOA,UAAU,WAAW;QAC9B;QACA,IAAIA,UAAU,WAAW,GAAG;YAC1B,OAAOA,UAAU,SAAS;QAC5B;QACA,IAAIA,UAAU,UAAU,GAAG;YACzB,OAAOA,UAAU,QAAQ;QAC3B;QACA,IAAIA,UAAU,SAAS,GAAG;YACxB,OAAOA,UAAU,OAAO;QAC1B;QACA,IAAIA,UAAU,OAAO,GAAG;YACtB,OAAOA,UAAU,KAAK;QACxB;IACF;IACA,OAAOA,UAAU;AACnB;AAEA,SAASC,0BAA0BD,OAA2B;IAC5D,IAAIA,YAAYE,WAAW;QACzB,OAAO;IACT;IACA,IAAIF,WAAW,YAAY;QACzB,OAAO;IACT;IACA,MAAMG,OAAOH,UAAU;IACvB,MAAMI,cAAcL,eAAeC;IACnC,IAAII,gBAAgBD,MAAM;QACxB,OAAOA;IACT;IACA,OAAOA,OAAO,OAAOC,cAAc;AACrC;AAEA,SAASC,yBAAyBC,OAAiC;IACjE,qCAAqC;IACrC,MAAMC,gBAAgBC,OAAOC,OAAO,CAACH,SAASI,IAAI,CAChD,CAACC,GAAGC,IAAMA,CAAC,CAAC,EAAE,CAACjB,KAAK,CAAC,KAAKkB,MAAM,GAAGF,CAAC,CAAC,EAAE,CAAChB,KAAK,CAAC,KAAKkB,MAAM;IAG3D,IAAI,CAACN,cAAcM,MAAM,EAAE;QACzB,OAAO,EAAE;IACX;IAEA,uDAAuD;IACvD,IAAIC,aAAaP,aAAa,CAACA,cAAcM,MAAM,GAAG,EAAE,CAAC,EAAE;IAE3D,IAAIE,aAAa,IAAIC;IACrB,IAAIC,wBAAwB;IAE5B,KAAK,MAAM,CAAC7C,YAAY8C,OAAO,IAAIX,cAAe;QAChD,MAAMY,wBAAwB/C,WAC3BuB,KAAK,CAAC,KACNZ,KAAK,CAAC,GAAG,CAAC,EACX,+DAA+D;SAC9DqC,KAAK,CAAC,CAACC,UAAY,iBAAiBC,IAAI,CAACD;QAE5C,IAAIF,uBAAuB;YACzB,IAAII,UAAUT,YAAY1C,aAAa;gBACrC,4DAA4D;gBAC5D0C,aAAa1C;gBACb2C,aAAa,IAAIC,IAAIE;YACvB,OAAO;gBACL,sCAAsC;gBACtCD,wBAAwB;gBACxB,qBAAqB;gBACrB,KAAK,MAAMO,SAASN,OAAQ;oBAC1BH,WAAWU,GAAG,CAACD;gBACjB;YACF;QACF;IACF;IAEA,sBAAsB;IACtB,MAAME,SAASC,MAAMC,IAAI,CAACb,YAAYjD,GAAG,CAAC,CAAC0D,QAAW,CAAA;YACpDA;YACAK,UAAUZ;QACZ,CAAA;IAEA,OAAOS;AACT;AAEA,SAASH,UAAUO,gBAAwB,EAAEC,wBAAgC;IAC3E,gGAAgG;IAChG,MAAMC,iBAAiBF,iBAAiBnC,KAAK,CAAC,KAAKZ,KAAK,CAAC,GAAG,CAAC;IAC7D,MAAMkD,gBAAgBF,yBAAyBpC,KAAK,CAAC,KAAKZ,KAAK,CAAC,GAAG,CAAC;IAEpE,+EAA+E;IAC/E,IAAIkD,cAAcpB,MAAM,GAAGmB,eAAenB,MAAM,IAAI,CAACoB,cAAcpB,MAAM,EACvE,OAAO;IAET,sCAAsC;IACtC,OAAOoB,cAAcb,KAAK,CACxB,CAACc,cAAcC,QAAUD,iBAAiBF,cAAc,CAACG,MAAM;AAEnE;AAEA,SAASC,wBACPrB,UAAkD;IAElD,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;qDAoB2C,EAAEA,WAChDjD,GAAG,CACF,CAAC,EAAE0D,KAAK,EAAEK,QAAQ,EAAE,GAClB,2CAA2C;QAC3C,GAAGL,MAAMa,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAEb,MAAM,CAAC,CAAC,GAAGA,QAAQK,WAAW,MAAM,GAAG,QAAQ,CAAC,EAEhF7D,IAAI,CAAC,MAAM;;EAEhB,CAAC;AACH;AAEA,SAASsE,iCAAiCC,SAEzC;IACC,IAAIC,YAAY;IAEhB,MAAMC,eAAejC,OAAOkC,IAAI,CAACH;IACjC,IAAK,IAAII,IAAI,GAAGA,IAAIF,aAAa5B,MAAM,EAAE8B,IAAK;QAC5C,MAAMC,cAAcH,YAAY,CAACE,EAAE;QACnC,MAAME,UAAUN,SAAS,CAACK,YAAY;QACtC,IAAI,OAAOC,YAAY,YAAYA,YAAY,MAAM;YACnD;QACF;QAEA,IAAIC,cAAc;QAElB,IAAID,QAAQE,KAAK,KAAK7C,WAAW;YAC/B4C,eAAe,CAAC;kHAC4F,CAAC;QAC/G,OAAO,IAAID,QAAQE,KAAK,IAAI,YAAY;YACtCD,eAAe,CAAC;uFACiE,CAAC;QACpF,OAAO;YACLA,eAAe,CAAC;8CACwB,EAAE/C,eAAe8C,QAAQE,KAAK,EAAE,iCAAiC,CAAC;QAC5G;QACA,IACEF,QAAQG,UAAU,KAAK9C,aACvB2C,QAAQI,MAAM,KAAK/C,aACnB2C,QAAQG,UAAU,IAAIH,QAAQI,MAAM,EACpC;YACAH,eAAe,CAAC;oCACc,EAAE/C,eAAe8C,QAAQI,MAAM,EAAE,qCAAqC,CAAC;QACvG,OAAO;YACL,IAAIJ,QAAQG,UAAU,KAAK9C,WAAW;gBACpC4C,eAAe,CAAC;iGACyE,CAAC;YAC5F,OAAO,IAAID,QAAQG,UAAU,IAAI,YAAY;YAC3C,sBAAsB;YACxB,OAAO;gBACLF,eAAe,CAAC;kDAC0B,EAAE/C,eAAe8C,QAAQG,UAAU,EAAE,kDAAkD,CAAC;YACpI;YACA,IAAIH,QAAQI,MAAM,KAAK/C,WAAW;gBAChC4C,eAAe,CAAC;iGACyE,CAAC;YAC5F,OAAO,IAAID,QAAQI,MAAM,IAAI,YAAY;gBACvCH,eAAe,CAAC;8IACsH,CAAC;YACzI,OAAO;gBACLA,eAAe,CAAC;wCACgB,EAAE/C,eAAe8C,QAAQI,MAAM,EAAE,oDAAoD,CAAC;YACxH;QACF;QAEAT,aAAa,CAAC;;kEAEgD,EAAEU,KAAKC,SAAS,CAACP,aAAa;;qBAE3E,EAAE3C,0BAA0B4C,QAAQE,KAAK,EAAE;qBAC3C,EAAE9C,0BAA0B4C,QAAQG,UAAU,EAAE;qBAChD,EAAE/C,0BAA0B4C,QAAQI,MAAM,EAAE;;OAE1D,EAAEH,YAAY;;gDAE2B,EAAEI,KAAKC,SAAS,CAACP,aAAa;IAC1E,CAAC;IACH;IAEAJ,aAAa,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;EA2Bd,CAAC;IAED,+CAA+C;IAC/C,OAAO,CAAC;;;;;;;;;;;;EAYR,EAAEA,UAAU;;;;AAId,CAAC;AACD;AAEA,MAAMY,mBAAmBtG,KAAKkB,IAAI,CAAC,SAAS;AAE5C,OAAO,MAAMqF;IAWXC,YAAY1F,OAAgB,CAAE;QAC5B,IAAI,CAAC2F,GAAG,GAAG3F,QAAQ2F,GAAG;QACtB,IAAI,CAACC,OAAO,GAAG5F,QAAQ4F,OAAO;QAC9B,IAAI,CAACC,MAAM,GAAG7F,QAAQ6F,MAAM;QAC5B,IAAI,CAACC,GAAG,GAAG9F,QAAQ8F,GAAG;QACtB,IAAI,CAACC,YAAY,GAAG/F,QAAQ+F,YAAY;QACxC,IAAI,CAACC,cAAc,GAAGhG,QAAQgG,cAAc;QAC5C,IAAI,CAACC,QAAQ,GAAG/G,KAAKkB,IAAI,CAAC,IAAI,CAACyF,MAAM,EAAE,MAAM;QAC7C,IAAI,CAACK,eAAe,GAAGlG,QAAQkG,eAAe;QAC9C,IAAI,CAACC,mBAAmB,GAAGjH,KAAKkB,IAAI,CAAC,IAAI,CAACuF,GAAG,EAAE,IAAI,CAACC,OAAO;IAC7D;IAEAQ,+BAA+BC,0BAAkC,EAAE;QACjE,MAAMC,qBAAqBpH,KAAKkB,IAAI,CAClC,IAAI,CAACyF,MAAM,EACXQ;QAGF,MAAME,+BAA+BrH,KAAKkB,IAAI,CAC5C,IAAI,CAAC+F,mBAAmB,EACxBX,kBACAa;QAGF,OAAOnH,KAAKsH,QAAQ,CAClBD,+BAA+B,OAC/BD;IAEJ;IAEAG,YAAYC,QAAgB,EAAE;QAC5B,MAAMC,QAAQD,SAASzF,UAAU,CAAC,IAAI,CAAC4E,MAAM,GAAG3G,KAAK0H,GAAG;QACxD,MAAMC,UAAU,CAACF,SAASD,SAASzF,UAAU,CAAC,IAAI,CAACgF,QAAQ,GAAG/G,KAAK0H,GAAG;QAEtE,IAAI,CAACD,SAAS,CAACE,SAAS;YACtB;QACF;QAEA,qDAAqD;QACrD,IAAIF,SAAS,CAAC,8BAA8BjD,IAAI,CAACgD,WAAW;YAC1D;QACF;QAEA,yCAAyC;QACzC,IACEG,WACA,iDAAiDnD,IAAI,CAACgD,WACtD;YACA;QACF;QAEA,IAAI7E,QAAQ,AAAC8E,CAAAA,QAAQlH,mBAAmBL,mBAAkB,EACxDC,mBACEK,gBACER,KAAKsH,QAAQ,CAACG,QAAQ,IAAI,CAACd,MAAM,GAAG,IAAI,CAACI,QAAQ,EAAES,WACnD,IAAI,CAACV,cAAc;QAKzB,MAAM,EAAElE,SAAS,EAAEI,SAAS,EAAE,GAAGN,uBAAuBC;QAExDT,YAAYE,UAAU,CAAC,IAAI,CAACyE,YAAY,GAAG,SAAS,OAAO,CACzDjE,YAAY,YAAY,SACzB,CAACZ,IAAI,CAACgB;IACT;IAEA4E,MAAMC,QAA0B,EAAE;QAChC,+BAA+B;QAC/B,MAAMC,mBAAmB,IAAI,CAAClB,GAAG,GAC7B,OACA,IAAI,CAACC,YAAY,GACf,OACA;QAEN,MAAMkB,eAAe,OACnBC,KACAC;YAEA,IAAI,CAACD,IAAIE,QAAQ,EAAE;YAEnB,MAAMC,sBAAsB,IAAIC,OAC9B,CAAC,IAAI,EAAE,IAAI,CAACtB,cAAc,CAAC5F,IAAI,CAAC,KAAK,EAAE,CAAC;YAG1C,IAAI,CAACiH,oBAAoB3D,IAAI,CAACwD,IAAIE,QAAQ,GAAG;YAE7C,IAAI,CAACF,IAAIE,QAAQ,CAACnG,UAAU,CAAC,IAAI,CAAC4E,MAAM,GAAG3G,KAAK0H,GAAG,GAAG;gBACpD,IAAI,CAAC,IAAI,CAACd,GAAG,EAAE;oBACb,IAAIoB,IAAIE,QAAQ,CAACnG,UAAU,CAAC,IAAI,CAACgF,QAAQ,GAAG/G,KAAK0H,GAAG,GAAG;wBACrD,IAAI,CAACH,WAAW,CAACS,IAAIE,QAAQ;oBAC/B;gBACF;gBACA;YACF;YACA,IAAIF,IAAIK,KAAK,KAAKpI,eAAeqI,qBAAqB,EAAE;YAExD,wCAAwC;YACxC,0BAA0B;YAC1B,MAAMC,aAAa,qBAAqB/D,IAAI,CAC1CwD,IAAIE,QAAQ,CAACM,OAAO,CAAC,IAAI,CAAC7B,MAAM,EAAE;YAEpC,IAAI4B,YAAY;YAEhB,MAAME,YAAY,yBAAyBjE,IAAI,CAACwD,IAAIE,QAAQ;YAC5D,MAAMQ,UAAU,CAACD,aAAa,oBAAoBjE,IAAI,CAACwD,IAAIE,QAAQ;YACnE,MAAMS,WAAW,CAACD,WAAW,qBAAqBlE,IAAI,CAACwD,IAAIE,QAAQ;YACnE,MAAMU,gBAAgB,6BAA6BpE,IAAI,CAACwD,IAAIE,QAAQ;YACpE,MAAMW,oBAAoB7I,KAAKsH,QAAQ,CAAC,IAAI,CAACX,MAAM,EAAEqB,IAAIE,QAAQ;YAEjE,IAAI,CAAC,IAAI,CAACtB,GAAG,EAAE;gBACb,IAAI8B,WAAWC,UAAU;oBACvB,IAAI,CAACpB,WAAW,CAACS,IAAIE,QAAQ;gBAC/B;YACF;YAEA,MAAMY,WAAW9I,KAAKkB,IAAI,CACxBoF,kBACAuC,kBAAkBL,OAAO,CAACL,qBAAqB;YAEjD,MAAMY,qBAAqB3I,iBACzBJ,KACGkB,IAAI,CAAC,IAAI,CAACgG,8BAA8B,CAAC2B,oBACzCL,OAAO,CAACL,qBAAqB;YAGlC,MAAMa,YAAYhJ,KAAKkB,IAAI,CAAC4G,kBAAkBgB;YAE9C,8FAA8F;YAC9F,yFAAyF;YACzF,IAAI,CAACF,eAAe;YAEpB,IAAIH,WAAW;gBACb,MAAMQ,iBAAiB1I,iBACrBJ,mBACEK,gBACER,KAAKsH,QAAQ,CAAC,IAAI,CAACX,MAAM,EAAEqB,IAAIE,QAAQ,GACvC,IAAI,CAACpB,cAAc;gBAKzB,MAAMoC,cAAcrE,MAAMC,IAAI,CAC5BmE,eAAeE,QAAQ,CAAC,eACxB,CAACC,QAAUA,KAAK,CAAC,EAAE;gBAGrBlH,YAAYC,mBAAmB,CAAC8G,eAAe,GAAGC;gBAElD,MAAM/H,QAAQ,MAAME,kBAAkB2G,IAAIE,QAAQ;gBAClDD,YAAYoB,SAAS,CACnBL,WACA,IAAIjJ,QAAQuJ,SAAS,CACnB3I,oBAAoBqH,IAAIE,QAAQ,EAAEa,oBAAoB;oBACpDhI,MAAM;oBACNI;gBACF;YAGN,OAAO,IAAIuH,SAAS;gBAClBT,YAAYoB,SAAS,CACnBL,WACA,IAAIjJ,QAAQuJ,SAAS,CACnB3I,oBAAoBqH,IAAIE,QAAQ,EAAEa,oBAAoB;oBACpDhI,MAAM;gBACR;YAGN,OAAO,IAAI4H,UAAU;gBACnBV,YAAYoB,SAAS,CACnBL,WACA,IAAIjJ,QAAQuJ,SAAS,CACnB3I,oBAAoBqH,IAAIE,QAAQ,EAAEa,oBAAoB;oBACpDhI,MAAM;gBACR;YAGN;QACF;QAEA8G,SAAS0B,KAAK,CAACtB,WAAW,CAACuB,GAAG,CAAC9I,aAAa,CAACuH;YAC3CA,YAAYsB,KAAK,CAACE,aAAa,CAACC,QAAQ,CACtC;gBACE5H,MAAMpB;gBACNiJ,OAAO7J,QAAQ8J,WAAW,CAACC,kCAAkC;YAC/D,GACA,OAAOC,GAAGC;gBACR,MAAMC,WAA2B,EAAE;gBAEnC,eAAe;gBACf,IAAI,IAAI,CAACnD,YAAY,EAAE;oBACrB3E,YAAYE,UAAU,CAACC,IAAI,CAACE,OAAO,GAAG,EAAE;oBACxCL,YAAYE,UAAU,CAACC,IAAI,CAACC,MAAM,GAAG,EAAE;gBACzC,OAAO;oBACLJ,YAAYE,UAAU,CAACI,IAAI,CAACD,OAAO,GAAG,EAAE;oBACxCL,YAAYE,UAAU,CAACI,IAAI,CAACF,MAAM,GAAG,EAAE;gBACzC;gBAEA2F,YAAYgC,WAAW,CAACC,OAAO,CAAC,CAACC;oBAC/BA,WAAWC,MAAM,CAACF,OAAO,CAAC,CAACG;wBACzB,IAAI,CAACA,MAAMvI,IAAI,EAAE;wBAEjB,4CAA4C;wBAC5C,IACE,CAACuI,MAAMvI,IAAI,CAACC,UAAU,CAAC,aACvB,CACEsI,CAAAA,MAAMvI,IAAI,CAACC,UAAU,CAAC,WACrBsI,CAAAA,MAAMvI,IAAI,CAACiB,QAAQ,CAAC,YACnBsH,MAAMvI,IAAI,CAACiB,QAAQ,CAAC,SAAQ,CAAC,GAEjC;4BACA;wBACF;wBAEA,MAAMuH,eACJrC,YAAYsC,UAAU,CAACC,uBAAuB,CAC5CH;wBAEJ,KAAK,MAAMrC,OAAOsC,aAAc;4BAC9BN,SAAShI,IAAI,CAAC+F,aAAaC,KAAKC;4BAEhC,oEAAoE;4BACpE,MAAMwC,YAAYzC;4BAGlB,IAAIyC,UAAUC,OAAO,EAAE;gCACrBD,UAAUC,OAAO,CAACR,OAAO,CAAC,CAACS;oCACzBX,SAAShI,IAAI,CAAC+F,aAAa4C,iBAAiB1C;gCAC9C;4BACF;wBACF;oBACF;gBACF;gBAEA,MAAM2C,QAAQC,GAAG,CAACb;gBAElB,MAAM/F,aAAaV,yBACjBrB,YAAYC,mBAAmB;gBAEjC,sEAAsE;gBACtE,+DAA+D;gBAC/D,IAAI8B,WAAWF,MAAM,GAAG,GAAG;oBACzB,MAAM+G,kBAAkB9K,KAAKkB,IAAI,CAC/B4G,kBACA;oBAGFG,YAAYoB,SAAS,CACnByB,iBACA,IAAI/K,QAAQuJ,SAAS,CACnBhE,wBAAwBrB;gBAG9B;gBAEA,8EAA8E;gBAE9E,MAAM8G,uBAAuB/K,KAAKkB,IAAI,CACpC4G,kBACA;gBAGFG,YAAYoB,SAAS,CACnB0B,sBACA,IAAIhL,QAAQuJ,SAAS,CACnB;gBAIJ,IAAI,IAAI,CAACtC,eAAe,EAAE;oBACxB,MAAMgE,qBAAqBhL,KAAKkB,IAAI,CAClC4G,kBACA;oBAGFG,YAAYoB,SAAS,CACnB2B,oBACA,IAAIjL,QAAQuJ,SAAS,CACnB9D,iCAAiC,IAAI,CAACwB,eAAe;gBAG3D;gBAEA+C;YACF;QAEJ;IACF;AACF", "ignoreList": [0]}