'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { quotesAPI, Quote } from '@/services/api';

export default function HomePage() {
  const router = useRouter();
  const { user, logout, isLoading } = useAuth();
  const [quote, setQuote] = useState<Quote | null>(null);
  const [countdown, setCountdown] = useState(5);
  const [isQuoteLoading, setIsQuoteLoading] = useState(false);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isLoading && !user) {
      router.push('/login');
    }
  }, [user, isLoading, router]);

  // Fetch a new quote
  const fetchNewQuote = async () => {
    setIsQuoteLoading(true);
    try {
      const response = await quotesAPI.getRandomQuote();
      setQuote(response.data);
    } catch (error) {
      console.error('Failed to fetch quote:', error);
    } finally {
      setIsQuoteLoading(false);
    }
  };

  // Initialize with first quote
  useEffect(() => {
    if (user) {
      fetchNewQuote();
    }
  }, [user]);

  // Set up the 5-second timer with countdown
  useEffect(() => {
    if (!user || !quote) return;

    const interval = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          fetchNewQuote();
          return 5;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [user, quote]);

  const handleLogout = () => {
    logout();
    router.push('/login');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-xl">Loading...</div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect to login
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Navigation */}
      <nav className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900">
                Welcome, {user.firstName}!
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href="/profile"
                className="text-gray-700 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium"
              >
                Profile
              </Link>
              <button
                onClick={handleLogout}
                className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-3xl font-extrabold text-gray-900 mb-8">
            Daily Motivation
          </h2>
          
          {/* Quote Display */}
          <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
            {isQuoteLoading ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-lg text-gray-500">Loading new quote...</div>
              </div>
            ) : quote ? (
              <div className="space-y-4">
                <blockquote className="text-xl md:text-2xl font-medium text-gray-900 leading-relaxed">
                  "{quote.quote}"
                </blockquote>
                <div className="text-sm text-gray-500">
                  Updated: {new Date(quote.timestamp).toLocaleTimeString()}
                </div>
              </div>
            ) : (
              <div className="text-lg text-gray-500">No quote available</div>
            )}
          </div>

          {/* Countdown Timer */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-center space-x-4">
              <div className="text-lg font-medium text-gray-700">
                Next quote in:
              </div>
              <div className="bg-indigo-100 text-indigo-800 px-4 py-2 rounded-full font-bold text-xl">
                {countdown}s
              </div>
            </div>
            <div className="mt-4">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-indigo-600 h-2 rounded-full transition-all duration-1000 ease-linear"
                  style={{ width: `${((5 - countdown) / 5) * 100}%` }}
                ></div>
              </div>
            </div>
          </div>

          {/* Manual Refresh Button */}
          <div className="mt-8">
            <button
              onClick={fetchNewQuote}
              disabled={isQuoteLoading}
              className="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-md font-medium disabled:opacity-50"
            >
              {isQuoteLoading ? 'Loading...' : 'Get New Quote'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
