{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/empty-projects/augment-small-project/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { User } from '@/services/api';\n\ninterface AuthContextType {\n  user: User | null;\n  token: string | null;\n  login: (token: string, user: User) => void;\n  logout: () => void;\n  updateUser: (user: User) => void;\n  isLoading: boolean;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [token, setToken] = useState<string | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    // Check for stored auth data on mount\n    const storedToken = localStorage.getItem('token');\n    const storedUser = localStorage.getItem('user');\n\n    if (storedToken && storedUser) {\n      setToken(storedToken);\n      setUser(JSON.parse(storedUser));\n    }\n    setIsLoading(false);\n  }, []);\n\n  const login = (newToken: string, newUser: User) => {\n    setToken(newToken);\n    setUser(newUser);\n    localStorage.setItem('token', newToken);\n    localStorage.setItem('user', JSON.stringify(newUser));\n  };\n\n  const logout = () => {\n    setToken(null);\n    setUser(null);\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n  };\n\n  const updateUser = (updatedUser: User) => {\n    setUser(updatedUser);\n    localStorage.setItem('user', JSON.stringify(updatedUser));\n  };\n\n  const value = {\n    user,\n    token,\n    login,\n    logout,\n    updateUser,\n    isLoading,\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n};\n"], "names": [], "mappings": ";;;;;;;AAEA;AAFA;;;AAcA,MAAM,4BAAc,IAAA,sNAAa,EAA8B;AAExD,MAAM,UAAU;IACrB,MAAM,UAAU,IAAA,mNAAU,EAAC;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,MAAM,eAAwD,CAAC,EAAE,QAAQ,EAAE;IAChF,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,iNAAQ,EAAc;IAC9C,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,iNAAQ,EAAgB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iNAAQ,EAAC;IAE3C,IAAA,kNAAS,EAAC;QACR,sCAAsC;QACtC,MAAM,cAAc,aAAa,OAAO,CAAC;QACzC,MAAM,aAAa,aAAa,OAAO,CAAC;QAExC,IAAI,eAAe,YAAY;YAC7B,SAAS;YACT,QAAQ,KAAK,KAAK,CAAC;QACrB;QACA,aAAa;IACf,GAAG,EAAE;IAEL,MAAM,QAAQ,CAAC,UAAkB;QAC/B,SAAS;QACT,QAAQ;QACR,aAAa,OAAO,CAAC,SAAS;QAC9B,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;IAC9C;IAEA,MAAM,SAAS;QACb,SAAS;QACT,QAAQ;QACR,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;IAC1B;IAEA,MAAM,aAAa,CAAC;QAClB,QAAQ;QACR,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;IAC9C;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/empty-projects/augment-small-project/frontend/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 99, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/empty-projects/augment-small-project/frontend/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/empty-projects/augment-small-project/frontend/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}]}