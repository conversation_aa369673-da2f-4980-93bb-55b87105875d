{"version": 3, "sources": ["../../../src/server/dev/middleware-webpack.ts"], "sourcesContent": ["import { findSourceMap, type SourceMap } from 'module'\nimport path from 'path'\nimport { fileURLToPath, pathToFileURL } from 'url'\nimport { SourceMapConsumer } from 'next/dist/compiled/source-map08'\nimport { getSourceMapFromFile } from './get-source-map-from-file'\nimport {\n  devirtualizeReactServerURL,\n  findApplicableSourceMapPayload,\n  sourceMapIgnoreListsEverything,\n  type BasicSourceMapPayload,\n  type ModernSourceMapPayload,\n} from '../lib/source-maps'\nimport { openFileInEditor } from '../../next-devtools/server/launch-editor'\nimport {\n  getOriginalCodeFrame,\n  ignoreListAnonymousStackFramesIfSandwiched,\n  type StackFrame,\n  type IgnorableStackFrame,\n  type OriginalStackFrameResponse,\n  type OriginalStackFramesRequest,\n  type OriginalStackFramesResponse,\n} from '../../next-devtools/server/shared'\nimport { middlewareResponse } from '../../next-devtools/server/middleware-response'\n\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type webpack from 'webpack'\nimport type {\n  NullableMappedPosition,\n  RawSourceMap,\n} from 'next/dist/compiled/source-map08'\nimport { formatFrameSourceFile } from '../../next-devtools/shared/webpack-module-path'\nimport type { MappedPosition } from 'source-map'\nimport { inspect } from 'util'\n\nfunction shouldIgnoreSource(sourceURL: string): boolean {\n  return (\n    sourceURL.includes('node_modules') ||\n    // Only relevant for when Next.js is symlinked e.g. in the Next.js monorepo\n    sourceURL.includes('next/dist') ||\n    sourceURL.startsWith('node:')\n  )\n}\n\ntype IgnoredSources = Array<{ url: string; ignored: boolean }>\n\ntype SourceAttributes = {\n  sourcePosition: NullableMappedPosition\n  sourceContent: string | null\n}\n\ntype Source =\n  | {\n      type: 'file'\n      sourceMap: BasicSourceMapPayload\n      ignoredSources: IgnoredSources\n      moduleURL: string\n    }\n  | {\n      type: 'bundle'\n      sourceMap: BasicSourceMapPayload\n      ignoredSources: IgnoredSources\n      compilation: webpack.Compilation\n      moduleId: string\n      moduleURL: string\n    }\n\nfunction getModuleById(\n  id: string | undefined,\n  compilation: webpack.Compilation\n) {\n  const { chunkGraph, modules } = compilation\n\n  return [...modules].find((module) => chunkGraph.getModuleId(module) === id)\n}\n\nfunction findModuleNotFoundFromError(errorMessage: string | undefined) {\n  return errorMessage?.match(/'([^']+)' module/)?.[1]\n}\n\nfunction getSourcePath(source: string) {\n  if (source.startsWith('file://')) {\n    return fileURLToPath(source)\n  }\n  return source.replace(/^(webpack:\\/\\/\\/|webpack:\\/\\/|webpack:\\/\\/_N_E\\/)/, '')\n}\n\n/**\n * @returns 1-based lines and 0-based columns\n */\nasync function findOriginalSourcePositionAndContent(\n  sourceMap: ModernSourceMapPayload,\n  position: { line1: number | null; column1: number | null }\n): Promise<SourceAttributes | null> {\n  let consumer: SourceMapConsumer\n  try {\n    consumer = await new SourceMapConsumer(sourceMap)\n  } catch (cause) {\n    console.error(\n      new Error(\n        `${sourceMap.file}: Invalid source map. Only conformant source maps can be used to find the original code.`,\n        { cause }\n      )\n    )\n    return null\n  }\n\n  try {\n    const sourcePosition = consumer.originalPositionFor({\n      line: position.line1 ?? 1,\n      // 0-based columns out requires 0-based columns in.\n      column: (position.column1 ?? 1) - 1,\n    })\n\n    if (!sourcePosition.source) {\n      return null\n    }\n\n    const sourceContent: string | null =\n      consumer.sourceContentFor(\n        sourcePosition.source,\n        /* returnNullOnMissing */ true\n      ) ?? null\n\n    return {\n      sourcePosition,\n      sourceContent,\n    }\n  } finally {\n    consumer.destroy()\n  }\n}\n\nexport function getIgnoredSources(\n  sourceMap: RawSourceMap & { ignoreList?: number[] }\n): IgnoredSources {\n  const ignoreList = new Set<number>(sourceMap.ignoreList ?? [])\n  const moduleFilenames = sourceMap?.sources ?? []\n\n  for (let index = 0; index < moduleFilenames.length; index++) {\n    // bundlerFilePath case: webpack://./app/page.tsx\n    const webpackSourceURL = moduleFilenames[index]\n    // Format the path to the normal file path\n    const formattedFilePath = formatFrameSourceFile(webpackSourceURL)\n    if (shouldIgnoreSource(formattedFilePath)) {\n      ignoreList.add(index)\n    }\n  }\n\n  const ignoredSources = sourceMap.sources.map((source, index) => {\n    return {\n      url: source,\n      ignored: ignoreList.has(sourceMap.sources.indexOf(source)),\n      content: sourceMap.sourcesContent?.[index] ?? null,\n    }\n  })\n  return ignoredSources\n}\n\nfunction isIgnoredSource(\n  source: Source,\n  sourcePosition: MappedPosition | NullableMappedPosition\n) {\n  if (sourcePosition.source == null) {\n    return true\n  }\n  for (const ignoredSource of source.ignoredSources) {\n    if (ignoredSource.ignored && ignoredSource.url === sourcePosition.source) {\n      return true\n    }\n  }\n\n  return false\n}\n\nfunction findOriginalSourcePositionAndContentFromCompilation(\n  moduleId: string | undefined,\n  importedModule: string,\n  compilation: webpack.Compilation\n): SourceAttributes | null {\n  const module = getModuleById(moduleId, compilation)\n  return module?.buildInfo?.importLocByPath?.get(importedModule) ?? null\n}\n\nexport async function createOriginalStackFrame({\n  ignoredByDefault,\n  source,\n  rootDirectory,\n  frame,\n  errorMessage,\n}: {\n  /** setting this to true will not consult ignoreList */\n  ignoredByDefault: boolean\n  source: Source\n  rootDirectory: string\n  frame: StackFrame\n  errorMessage?: string\n}): Promise<OriginalStackFrameResponse | null> {\n  const moduleNotFound = findModuleNotFoundFromError(errorMessage)\n  const result = await (() => {\n    if (moduleNotFound) {\n      if (source.type === 'file') {\n        return undefined\n      }\n\n      return findOriginalSourcePositionAndContentFromCompilation(\n        source.moduleId,\n        moduleNotFound,\n        source.compilation\n      )\n    }\n    return findOriginalSourcePositionAndContent(source.sourceMap, frame)\n  })()\n\n  if (!result) {\n    return null\n  }\n  const { sourcePosition, sourceContent } = result\n\n  if (!sourcePosition.source) {\n    return null\n  }\n\n  const ignored =\n    ignoredByDefault ||\n    isIgnoredSource(source, sourcePosition) ||\n    // If the source file is externals, should be excluded even it's not ignored source.\n    // e.g. webpack://next/dist/.. needs to be ignored\n    shouldIgnoreSource(source.moduleURL)\n\n  const sourcePath = getSourcePath(\n    // When sourcePosition.source is the loader path the modulePath is generally better.\n    (sourcePosition.source!.includes('|')\n      ? source.moduleURL\n      : sourcePosition.source) || source.moduleURL\n  )\n  const filePath = path.resolve(rootDirectory, sourcePath)\n  const resolvedFilePath = path.relative(rootDirectory, filePath)\n\n  const traced: IgnorableStackFrame = {\n    file: resolvedFilePath,\n    line1: sourcePosition.line,\n    column1: sourcePosition.column === null ? null : sourcePosition.column + 1,\n    methodName:\n      // We ignore the sourcemapped name since it won't be the correct name.\n      // The callsite will point to the column of the variable name instead of the\n      // name of the enclosing function.\n      // TODO(NDX-531): Spy on prepareStackTrace to get the enclosing line number for method name mapping.\n      // default is not a valid identifier in JS so webpack uses a custom variable when it's an unnamed default export\n      // Resolve it back to `default` for the method name if the source position didn't have the method.\n      frame.methodName\n        ?.replace('__WEBPACK_DEFAULT_EXPORT__', 'default')\n        ?.replace('__webpack_exports__.', ''),\n    arguments: [],\n    ignored,\n  }\n\n  return {\n    originalStackFrame: traced,\n    originalCodeFrame: getOriginalCodeFrame(traced, sourceContent),\n  }\n}\n\nasync function getSourceMapFromCompilation(\n  id: string,\n  compilation: webpack.Compilation\n): Promise<RawSourceMap | undefined> {\n  try {\n    const module = getModuleById(id, compilation)\n\n    if (!module) {\n      return undefined\n    }\n\n    // @ts-expect-error The types for `CodeGenerationResults.get` require a\n    // runtime to be passed as second argument, but apparently it also works\n    // without it.\n    const codeGenerationResult = compilation.codeGenerationResults.get(module)\n    const source = codeGenerationResult?.sources.get('javascript')\n\n    return source?.map() ?? undefined\n  } catch (err) {\n    console.error(`Failed to lookup module by ID (\"${id}\"):`, err)\n    return undefined\n  }\n}\n\nasync function getSource(\n  frame: {\n    file: string | null\n    line1: number | null\n    column1: number | null\n  },\n  options: {\n    getCompilations: () => webpack.Compilation[]\n  }\n): Promise<Source | undefined> {\n  let sourceURL = frame.file ?? ''\n  const { getCompilations } = options\n\n  sourceURL = devirtualizeReactServerURL(sourceURL)\n\n  let nativeSourceMap: SourceMap | undefined\n  try {\n    nativeSourceMap = findSourceMap(sourceURL)\n  } catch (cause) {\n    throw new Error(\n      `${sourceURL}: Invalid source map. Only conformant source maps can be used to find the original code.`,\n      { cause }\n    )\n  }\n\n  if (nativeSourceMap !== undefined) {\n    const sourceMapPayload = nativeSourceMap.payload\n    return {\n      type: 'file',\n      sourceMap: findApplicableSourceMapPayload(\n        (frame.line1 ?? 1) - 1,\n        (frame.column1 ?? 1) - 1,\n        sourceMapPayload\n      )!,\n\n      ignoredSources: getIgnoredSources(\n        // @ts-expect-error -- TODO: Support IndexSourceMap\n        sourceMapPayload\n      ),\n      moduleURL: sourceURL,\n    }\n  }\n\n  if (path.isAbsolute(sourceURL)) {\n    sourceURL = pathToFileURL(sourceURL).href\n  }\n\n  if (sourceURL.startsWith('file:')) {\n    const sourceMap = await getSourceMapFromFile(sourceURL)\n    return sourceMap\n      ? {\n          type: 'file',\n          sourceMap,\n          ignoredSources: getIgnoredSources(sourceMap),\n          moduleURL: sourceURL,\n        }\n      : undefined\n  }\n\n  // webpack-internal:///./src/hello.tsx => ./src/hello.tsx\n  // webpack://_N_E/./src/hello.tsx => ./src/hello.tsx\n  const moduleId = sourceURL\n    .replace(/^(webpack-internal:\\/\\/\\/|webpack:\\/\\/(_N_E\\/)?)/, '')\n    .replace(/\\?\\d+$/, '')\n\n  // (rsc)/./src/hello.tsx => ./src/hello.tsx\n  const moduleURL = moduleId.replace(/^(\\(.*\\)\\/?)/, '')\n\n  for (const compilation of getCompilations()) {\n    const sourceMap = await getSourceMapFromCompilation(moduleId, compilation)\n\n    if (sourceMap) {\n      const ignoredSources = getIgnoredSources(sourceMap)\n      return {\n        type: 'bundle',\n        sourceMap,\n        compilation,\n        moduleId,\n        moduleURL,\n        ignoredSources,\n      }\n    }\n  }\n\n  return undefined\n}\n\nexport async function getOriginalStackFrames({\n  isServer,\n  isEdgeServer,\n  isAppDirectory,\n  frames,\n  clientStats,\n  serverStats,\n  edgeServerStats,\n  rootDirectory,\n}: {\n  isServer: boolean\n  isEdgeServer: boolean\n  isAppDirectory: boolean\n  frames: readonly StackFrame[]\n  clientStats: () => webpack.Stats | null\n  serverStats: () => webpack.Stats | null\n  edgeServerStats: () => webpack.Stats | null\n  rootDirectory: string\n}): Promise<OriginalStackFramesResponse> {\n  const frameResponses = await Promise.all(\n    frames.map(\n      (frame): Promise<OriginalStackFramesResponse[number]> =>\n        getOriginalStackFrame({\n          isServer,\n          isEdgeServer,\n          isAppDirectory,\n          frame,\n          clientStats,\n          serverStats,\n          edgeServerStats,\n          rootDirectory,\n        }).then(\n          (value) => {\n            return {\n              status: 'fulfilled',\n              value,\n            }\n          },\n          (reason) => {\n            return {\n              status: 'rejected',\n              reason: inspect(reason, { colors: false }),\n            }\n          }\n        )\n    )\n  )\n\n  ignoreListAnonymousStackFramesIfSandwiched(frameResponses)\n\n  return frameResponses\n}\n\nasync function getOriginalStackFrame({\n  isServer,\n  isEdgeServer,\n  isAppDirectory,\n  frame,\n  clientStats,\n  serverStats,\n  edgeServerStats,\n  rootDirectory,\n}: {\n  isServer: boolean\n  isEdgeServer: boolean\n  isAppDirectory: boolean\n  frame: StackFrame\n  clientStats: () => webpack.Stats | null\n  serverStats: () => webpack.Stats | null\n  edgeServerStats: () => webpack.Stats | null\n  rootDirectory: string\n}): Promise<OriginalStackFrameResponse> {\n  const filename = frame.file ?? ''\n  const source = await getSource(frame, {\n    getCompilations: () => {\n      const compilations: webpack.Compilation[] = []\n\n      // Try Client Compilation first. In `pages` we leverage\n      // `isClientError` to check. In `app` it depends on if it's a server\n      // / client component and when the code throws. E.g. during HTML\n      // rendering it's the server/edge compilation.\n      if ((!isEdgeServer && !isServer) || isAppDirectory) {\n        const compilation = clientStats()?.compilation\n\n        if (compilation) {\n          compilations.push(compilation)\n        }\n      }\n\n      // Try Server Compilation. In `pages` this could be something\n      // imported in getServerSideProps/getStaticProps as the code for\n      // those is tree-shaken. In `app` this finds server components and\n      // code that was imported from a server component. It also covers\n      // when client component code throws during HTML rendering.\n      if (isServer || isAppDirectory) {\n        const compilation = serverStats()?.compilation\n\n        if (compilation) {\n          compilations.push(compilation)\n        }\n      }\n\n      // Try Edge Server Compilation. Both cases are the same as Server\n      // Compilation, main difference is that it covers `runtime: 'edge'`\n      // pages/app routes.\n      if (isEdgeServer || isAppDirectory) {\n        const compilation = edgeServerStats()?.compilation\n\n        if (compilation) {\n          compilations.push(compilation)\n        }\n      }\n\n      return compilations\n    },\n  })\n\n  let defaultNormalizedStackFrameLocation = frame.file\n  if (\n    defaultNormalizedStackFrameLocation !== null &&\n    defaultNormalizedStackFrameLocation.startsWith('file://')\n  ) {\n    defaultNormalizedStackFrameLocation = path.relative(\n      rootDirectory,\n      fileURLToPath(defaultNormalizedStackFrameLocation)\n    )\n  }\n  // This stack frame is used for the one that couldn't locate the source or source mapped frame\n  const defaultStackFrame: IgnorableStackFrame = {\n    file: defaultNormalizedStackFrameLocation,\n    line1: frame.line1,\n    column1: frame.column1,\n    methodName: frame.methodName,\n    ignored: shouldIgnoreSource(filename),\n    arguments: [],\n  }\n  if (!source) {\n    // return original stack frame with no source map\n    return {\n      originalStackFrame: defaultStackFrame,\n      originalCodeFrame: null,\n    }\n  }\n  defaultStackFrame.ignored ||= sourceMapIgnoreListsEverything(source.sourceMap)\n\n  const originalStackFrameResponse = await createOriginalStackFrame({\n    ignoredByDefault: defaultStackFrame.ignored,\n    frame,\n    source,\n    rootDirectory,\n  })\n\n  if (!originalStackFrameResponse) {\n    return {\n      originalStackFrame: defaultStackFrame,\n      originalCodeFrame: null,\n    }\n  }\n\n  return originalStackFrameResponse\n}\n\nexport function getOverlayMiddleware(options: {\n  rootDirectory: string\n  isSrcDir: boolean\n  clientStats: () => webpack.Stats | null\n  serverStats: () => webpack.Stats | null\n  edgeServerStats: () => webpack.Stats | null\n}) {\n  const { rootDirectory, isSrcDir, clientStats, serverStats, edgeServerStats } =\n    options\n\n  return async function (\n    req: IncomingMessage,\n    res: ServerResponse,\n    next: () => void\n  ): Promise<void> {\n    const { pathname, searchParams } = new URL(`http://n${req.url}`)\n\n    if (pathname === '/__nextjs_original-stack-frames') {\n      if (req.method !== 'POST') {\n        return middlewareResponse.badRequest(res)\n      }\n\n      const body = await new Promise<string>((resolve, reject) => {\n        let data = ''\n        req.on('data', (chunk) => {\n          data += chunk\n        })\n        req.on('end', () => resolve(data))\n        req.on('error', reject)\n      })\n\n      try {\n        const { frames, isServer, isEdgeServer, isAppDirectory } = JSON.parse(\n          body\n        ) as OriginalStackFramesRequest\n\n        return middlewareResponse.json(\n          res,\n          await getOriginalStackFrames({\n            isServer,\n            isEdgeServer,\n            isAppDirectory,\n            frames,\n            clientStats,\n            serverStats,\n            edgeServerStats,\n            rootDirectory,\n          })\n        )\n      } catch (err) {\n        return middlewareResponse.badRequest(res)\n      }\n    } else if (pathname === '/__nextjs_launch-editor') {\n      const frame = {\n        file: searchParams.get('file') as string,\n        methodName: searchParams.get('methodName') as string,\n        line1: parseInt(searchParams.get('line1') ?? '1', 10) || 1,\n        column1: parseInt(searchParams.get('column1') ?? '1', 10) || 1,\n        arguments: searchParams.getAll('arguments').filter(Boolean),\n      } satisfies StackFrame\n\n      if (!frame.file) return middlewareResponse.badRequest(res)\n\n      let openEditorResult\n      const isAppRelativePath = searchParams.get('isAppRelativePath') === '1'\n      if (isAppRelativePath) {\n        const relativeFilePath = searchParams.get('file') || ''\n        const appPath = path.join(\n          'app',\n          isSrcDir ? 'src' : '',\n          relativeFilePath\n        )\n        openEditorResult = await openFileInEditor(appPath, 1, 1, rootDirectory)\n      } else {\n        // TODO: How do we differentiate layers and actual file paths with round brackets?\n        // frame files may start with their webpack layer, like (middleware)/middleware.js\n        const filePath = frame.file.replace(/^\\([^)]+\\)\\//, '')\n        openEditorResult = await openFileInEditor(\n          filePath,\n          frame.line1,\n          frame.column1 ?? 1,\n          rootDirectory\n        )\n      }\n      if (openEditorResult.error) {\n        console.error('Failed to launch editor:', openEditorResult.error)\n        return middlewareResponse.internalServerError(\n          res,\n          openEditorResult.error\n        )\n      }\n      if (!openEditorResult.found) {\n        return middlewareResponse.notFound(res)\n      }\n      return middlewareResponse.noContent(res)\n    }\n\n    return next()\n  }\n}\n\nexport function getSourceMapMiddleware(options: {\n  clientStats: () => webpack.Stats | null\n  serverStats: () => webpack.Stats | null\n  edgeServerStats: () => webpack.Stats | null\n}) {\n  const { clientStats, serverStats, edgeServerStats } = options\n\n  return async function (\n    req: IncomingMessage,\n    res: ServerResponse,\n    next: () => void\n  ): Promise<void> {\n    const { pathname, searchParams } = new URL(`http://n${req.url}`)\n\n    if (pathname !== '/__nextjs_source-map') {\n      return next()\n    }\n\n    const filename = searchParams.get('filename')\n\n    if (!filename) {\n      return middlewareResponse.badRequest(res)\n    }\n\n    let source: Source | undefined\n\n    try {\n      source = await getSource(\n        {\n          file: filename,\n          // Webpack doesn't use Index Source Maps\n          line1: null,\n          column1: null,\n        },\n        {\n          getCompilations: () => {\n            const compilations: webpack.Compilation[] = []\n\n            for (const stats of [\n              clientStats(),\n              serverStats(),\n              edgeServerStats(),\n            ]) {\n              if (stats?.compilation) {\n                compilations.push(stats.compilation)\n              }\n            }\n\n            return compilations\n          },\n        }\n      )\n    } catch (error) {\n      return middlewareResponse.internalServerError(res, error)\n    }\n\n    if (!source) {\n      return middlewareResponse.noContent(res)\n    }\n\n    return middlewareResponse.json(res, source.sourceMap)\n  }\n}\n"], "names": ["findSourceMap", "path", "fileURLToPath", "pathToFileURL", "SourceMapConsumer", "getSourceMapFromFile", "devirtualizeReactServerURL", "findApplicableSourceMapPayload", "sourceMapIgnoreListsEverything", "openFileInEditor", "getOriginalCodeFrame", "ignoreListAnonymousStackFramesIfSandwiched", "middlewareResponse", "formatFrameSourceFile", "inspect", "shouldIgnoreSource", "sourceURL", "includes", "startsWith", "getModuleById", "id", "compilation", "chunkGraph", "modules", "find", "module", "getModuleId", "findModuleNotFoundFromError", "errorMessage", "match", "getSourcePath", "source", "replace", "findOriginalSourcePositionAndContent", "sourceMap", "position", "consumer", "cause", "console", "error", "Error", "file", "sourcePosition", "originalPositionFor", "line", "line1", "column", "column1", "sourceContent", "sourceContentFor", "destroy", "getIgnoredSources", "ignoreList", "Set", "moduleFilenames", "sources", "index", "length", "webpackSourceURL", "formattedFilePath", "add", "ignoredSources", "map", "url", "ignored", "has", "indexOf", "content", "sourcesContent", "isIgnoredSource", "ignoredSource", "findOriginalSourcePositionAndContentFromCompilation", "moduleId", "importedModule", "buildInfo", "importLocByPath", "get", "createOriginalStackFrame", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rootDirectory", "frame", "moduleNotFound", "result", "type", "undefined", "moduleURL", "sourcePath", "filePath", "resolve", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "relative", "traced", "methodName", "arguments", "originalStackFrame", "originalCodeFrame", "getSourceMapFromCompilation", "codeGenerationResult", "codeGenerationResults", "err", "getSource", "options", "getCompilations", "nativeSourceMap", "sourceMapPayload", "payload", "isAbsolute", "href", "getOriginalStackFrames", "isServer", "isEdgeServer", "isAppDirectory", "frames", "clientStats", "serverStats", "edgeServerStats", "frameResponses", "Promise", "all", "getOriginalStackFrame", "then", "value", "status", "reason", "colors", "filename", "compilations", "push", "defaultNormalizedStackFrameLocation", "defaultStackFrame", "originalStackFrameResponse", "getOverlayMiddleware", "isSrcDir", "req", "res", "next", "pathname", "searchParams", "URL", "method", "badRequest", "body", "reject", "data", "on", "chunk", "JSON", "parse", "json", "parseInt", "getAll", "filter", "Boolean", "openEditorResult", "isAppRelativePath", "relativeFilePath", "appPath", "join", "internalServerError", "found", "notFound", "noContent", "getSourceMapMiddleware", "stats"], "mappings": "AAAA,SAASA,aAAa,QAAwB,SAAQ;AACtD,OAAOC,UAAU,OAAM;AACvB,SAASC,aAAa,EAAEC,aAAa,QAAQ,MAAK;AAClD,SAASC,iBAAiB,QAAQ,kCAAiC;AACnE,SAASC,oBAAoB,QAAQ,6BAA4B;AACjE,SACEC,0BAA0B,EAC1BC,8BAA8B,EAC9BC,8BAA8B,QAGzB,qBAAoB;AAC3B,SAASC,gBAAgB,QAAQ,2CAA0C;AAC3E,SACEC,oBAAoB,EACpBC,0CAA0C,QAMrC,oCAAmC;AAC1C,SAASC,kBAAkB,QAAQ,iDAAgD;AAQnF,SAASC,qBAAqB,QAAQ,iDAAgD;AAEtF,SAASC,OAAO,QAAQ,OAAM;AAE9B,SAASC,mBAAmBC,SAAiB;IAC3C,OACEA,UAAUC,QAAQ,CAAC,mBACnB,2EAA2E;IAC3ED,UAAUC,QAAQ,CAAC,gBACnBD,UAAUE,UAAU,CAAC;AAEzB;AAyBA,SAASC,cACPC,EAAsB,EACtBC,WAAgC;IAEhC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAE,GAAGF;IAEhC,OAAO;WAAIE;KAAQ,CAACC,IAAI,CAAC,CAACC,SAAWH,WAAWI,WAAW,CAACD,YAAYL;AAC1E;AAEA,SAASO,4BAA4BC,YAAgC;QAC5DA;IAAP,OAAOA,iCAAAA,sBAAAA,aAAcC,KAAK,CAAC,wCAApBD,mBAAyC,CAAC,EAAE;AACrD;AAEA,SAASE,cAAcC,MAAc;IACnC,IAAIA,OAAOb,UAAU,CAAC,YAAY;QAChC,OAAOhB,cAAc6B;IACvB;IACA,OAAOA,OAAOC,OAAO,CAAC,qDAAqD;AAC7E;AAEA;;CAEC,GACD,eAAeC,qCACbC,SAAiC,EACjCC,QAA0D;IAE1D,IAAIC;IACJ,IAAI;QACFA,WAAW,MAAM,IAAIhC,kBAAkB8B;IACzC,EAAE,OAAOG,OAAO;QACdC,QAAQC,KAAK,CACX,qBAGC,CAHD,IAAIC,MACF,GAAGN,UAAUO,IAAI,CAAC,wFAAwF,CAAC,EAC3G;YAAEJ;QAAM,IAFV,qBAAA;mBAAA;wBAAA;0BAAA;QAGA;QAEF,OAAO;IACT;IAEA,IAAI;QACF,MAAMK,iBAAiBN,SAASO,mBAAmB,CAAC;YAClDC,MAAMT,SAASU,KAAK,IAAI;YACxB,mDAAmD;YACnDC,QAAQ,AAACX,CAAAA,SAASY,OAAO,IAAI,CAAA,IAAK;QACpC;QAEA,IAAI,CAACL,eAAeX,MAAM,EAAE;YAC1B,OAAO;QACT;QAEA,MAAMiB,gBACJZ,SAASa,gBAAgB,CACvBP,eAAeX,MAAM,EACrB,uBAAuB,GAAG,SACvB;QAEP,OAAO;YACLW;YACAM;QACF;IACF,SAAU;QACRZ,SAASc,OAAO;IAClB;AACF;AAEA,OAAO,SAASC,kBACdjB,SAAmD;IAEnD,MAAMkB,aAAa,IAAIC,IAAYnB,UAAUkB,UAAU,IAAI,EAAE;IAC7D,MAAME,kBAAkBpB,CAAAA,6BAAAA,UAAWqB,OAAO,KAAI,EAAE;IAEhD,IAAK,IAAIC,QAAQ,GAAGA,QAAQF,gBAAgBG,MAAM,EAAED,QAAS;QAC3D,iDAAiD;QACjD,MAAME,mBAAmBJ,eAAe,CAACE,MAAM;QAC/C,0CAA0C;QAC1C,MAAMG,oBAAoB9C,sBAAsB6C;QAChD,IAAI3C,mBAAmB4C,oBAAoB;YACzCP,WAAWQ,GAAG,CAACJ;QACjB;IACF;IAEA,MAAMK,iBAAiB3B,UAAUqB,OAAO,CAACO,GAAG,CAAC,CAAC/B,QAAQyB;YAIzCtB;QAHX,OAAO;YACL6B,KAAKhC;YACLiC,SAASZ,WAAWa,GAAG,CAAC/B,UAAUqB,OAAO,CAACW,OAAO,CAACnC;YAClDoC,SAASjC,EAAAA,4BAAAA,UAAUkC,cAAc,qBAAxBlC,yBAA0B,CAACsB,MAAM,KAAI;QAChD;IACF;IACA,OAAOK;AACT;AAEA,SAASQ,gBACPtC,MAAc,EACdW,cAAuD;IAEvD,IAAIA,eAAeX,MAAM,IAAI,MAAM;QACjC,OAAO;IACT;IACA,KAAK,MAAMuC,iBAAiBvC,OAAO8B,cAAc,CAAE;QACjD,IAAIS,cAAcN,OAAO,IAAIM,cAAcP,GAAG,KAAKrB,eAAeX,MAAM,EAAE;YACxE,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA,SAASwC,oDACPC,QAA4B,EAC5BC,cAAsB,EACtBpD,WAAgC;QAGzBI,mCAAAA;IADP,MAAMA,SAASN,cAAcqD,UAAUnD;IACvC,OAAOI,CAAAA,2BAAAA,oBAAAA,OAAQiD,SAAS,sBAAjBjD,oCAAAA,kBAAmBkD,eAAe,qBAAlClD,kCAAoCmD,GAAG,CAACH,oBAAmB;AACpE;AAEA,OAAO,eAAeI,yBAAyB,EAC7CC,gBAAgB,EAChB/C,MAAM,EACNgD,aAAa,EACbC,KAAK,EACLpD,YAAY,EAQb;QA+CK,sEAAsE;IACtE,4EAA4E;IAC5E,kCAAkC;IAClC,oGAAoG;IACpG,gHAAgH;IAChH,kGAAkG;IAClGoD,2BAAAA;IApDJ,MAAMC,iBAAiBtD,4BAA4BC;IACnD,MAAMsD,SAAS,MAAM,AAAC,CAAA;QACpB,IAAID,gBAAgB;YAClB,IAAIlD,OAAOoD,IAAI,KAAK,QAAQ;gBAC1B,OAAOC;YACT;YAEA,OAAOb,oDACLxC,OAAOyC,QAAQ,EACfS,gBACAlD,OAAOV,WAAW;QAEtB;QACA,OAAOY,qCAAqCF,OAAOG,SAAS,EAAE8C;IAChE,CAAA;IAEA,IAAI,CAACE,QAAQ;QACX,OAAO;IACT;IACA,MAAM,EAAExC,cAAc,EAAEM,aAAa,EAAE,GAAGkC;IAE1C,IAAI,CAACxC,eAAeX,MAAM,EAAE;QAC1B,OAAO;IACT;IAEA,MAAMiC,UACJc,oBACAT,gBAAgBtC,QAAQW,mBACxB,oFAAoF;IACpF,kDAAkD;IAClD3B,mBAAmBgB,OAAOsD,SAAS;IAErC,MAAMC,aAAaxD,cAEjB,AADA,oFAAoF;IACnFY,CAAAA,eAAeX,MAAM,CAAEd,QAAQ,CAAC,OAC7Bc,OAAOsD,SAAS,GAChB3C,eAAeX,MAAM,AAAD,KAAMA,OAAOsD,SAAS;IAEhD,MAAME,WAAWtF,KAAKuF,OAAO,CAACT,eAAeO;IAC7C,MAAMG,mBAAmBxF,KAAKyF,QAAQ,CAACX,eAAeQ;IAEtD,MAAMI,SAA8B;QAClClD,MAAMgD;QACN5C,OAAOH,eAAeE,IAAI;QAC1BG,SAASL,eAAeI,MAAM,KAAK,OAAO,OAAOJ,eAAeI,MAAM,GAAG;QACzE8C,UAAU,GAORZ,oBAAAA,MAAMY,UAAU,sBAAhBZ,4BAAAA,kBACIhD,OAAO,CAAC,8BAA8B,+BAD1CgD,0BAEIhD,OAAO,CAAC,wBAAwB;QACtC6D,WAAW,EAAE;QACb7B;IACF;IAEA,OAAO;QACL8B,oBAAoBH;QACpBI,mBAAmBrF,qBAAqBiF,QAAQ3C;IAClD;AACF;AAEA,eAAegD,4BACb5E,EAAU,EACVC,WAAgC;IAEhC,IAAI;QACF,MAAMI,SAASN,cAAcC,IAAIC;QAEjC,IAAI,CAACI,QAAQ;YACX,OAAO2D;QACT;QAEA,uEAAuE;QACvE,wEAAwE;QACxE,cAAc;QACd,MAAMa,uBAAuB5E,YAAY6E,qBAAqB,CAACtB,GAAG,CAACnD;QACnE,MAAMM,SAASkE,wCAAAA,qBAAsB1C,OAAO,CAACqB,GAAG,CAAC;QAEjD,OAAO7C,CAAAA,0BAAAA,OAAQ+B,GAAG,OAAMsB;IAC1B,EAAE,OAAOe,KAAK;QACZ7D,QAAQC,KAAK,CAAC,CAAC,gCAAgC,EAAEnB,GAAG,GAAG,CAAC,EAAE+E;QAC1D,OAAOf;IACT;AACF;AAEA,eAAegB,UACbpB,KAIC,EACDqB,OAEC;IAED,IAAIrF,YAAYgE,MAAMvC,IAAI,IAAI;IAC9B,MAAM,EAAE6D,eAAe,EAAE,GAAGD;IAE5BrF,YAAYV,2BAA2BU;IAEvC,IAAIuF;IACJ,IAAI;QACFA,kBAAkBvG,cAAcgB;IAClC,EAAE,OAAOqB,OAAO;QACd,MAAM,qBAGL,CAHK,IAAIG,MACR,GAAGxB,UAAU,wFAAwF,CAAC,EACtG;YAAEqB;QAAM,IAFJ,qBAAA;mBAAA;wBAAA;0BAAA;QAGN;IACF;IAEA,IAAIkE,oBAAoBnB,WAAW;QACjC,MAAMoB,mBAAmBD,gBAAgBE,OAAO;QAChD,OAAO;YACLtB,MAAM;YACNjD,WAAW3B,+BACT,AAACyE,CAAAA,MAAMnC,KAAK,IAAI,CAAA,IAAK,GACrB,AAACmC,CAAAA,MAAMjC,OAAO,IAAI,CAAA,IAAK,GACvByD;YAGF3C,gBAAgBV,kBACd,mDAAmD;YACnDqD;YAEFnB,WAAWrE;QACb;IACF;IAEA,IAAIf,KAAKyG,UAAU,CAAC1F,YAAY;QAC9BA,YAAYb,cAAca,WAAW2F,IAAI;IAC3C;IAEA,IAAI3F,UAAUE,UAAU,CAAC,UAAU;QACjC,MAAMgB,YAAY,MAAM7B,qBAAqBW;QAC7C,OAAOkB,YACH;YACEiD,MAAM;YACNjD;YACA2B,gBAAgBV,kBAAkBjB;YAClCmD,WAAWrE;QACb,IACAoE;IACN;IAEA,yDAAyD;IACzD,oDAAoD;IACpD,MAAMZ,WAAWxD,UACdgB,OAAO,CAAC,oDAAoD,IAC5DA,OAAO,CAAC,UAAU;IAErB,2CAA2C;IAC3C,MAAMqD,YAAYb,SAASxC,OAAO,CAAC,gBAAgB;IAEnD,KAAK,MAAMX,eAAeiF,kBAAmB;QAC3C,MAAMpE,YAAY,MAAM8D,4BAA4BxB,UAAUnD;QAE9D,IAAIa,WAAW;YACb,MAAM2B,iBAAiBV,kBAAkBjB;YACzC,OAAO;gBACLiD,MAAM;gBACNjD;gBACAb;gBACAmD;gBACAa;gBACAxB;YACF;QACF;IACF;IAEA,OAAOuB;AACT;AAEA,OAAO,eAAewB,uBAAuB,EAC3CC,QAAQ,EACRC,YAAY,EACZC,cAAc,EACdC,MAAM,EACNC,WAAW,EACXC,WAAW,EACXC,eAAe,EACfpC,aAAa,EAUd;IACC,MAAMqC,iBAAiB,MAAMC,QAAQC,GAAG,CACtCN,OAAOlD,GAAG,CACR,CAACkB,QACCuC,sBAAsB;YACpBV;YACAC;YACAC;YACA/B;YACAiC;YACAC;YACAC;YACApC;QACF,GAAGyC,IAAI,CACL,CAACC;YACC,OAAO;gBACLC,QAAQ;gBACRD;YACF;QACF,GACA,CAACE;YACC,OAAO;gBACLD,QAAQ;gBACRC,QAAQ7G,QAAQ6G,QAAQ;oBAAEC,QAAQ;gBAAM;YAC1C;QACF;IAKRjH,2CAA2CyG;IAE3C,OAAOA;AACT;AAEA,eAAeG,sBAAsB,EACnCV,QAAQ,EACRC,YAAY,EACZC,cAAc,EACd/B,KAAK,EACLiC,WAAW,EACXC,WAAW,EACXC,eAAe,EACfpC,aAAa,EAUd;IACC,MAAM8C,WAAW7C,MAAMvC,IAAI,IAAI;IAC/B,MAAMV,SAAS,MAAMqE,UAAUpB,OAAO;QACpCsB,iBAAiB;YACf,MAAMwB,eAAsC,EAAE;YAE9C,uDAAuD;YACvD,oEAAoE;YACpE,gEAAgE;YAChE,8CAA8C;YAC9C,IAAI,AAAC,CAAChB,gBAAgB,CAACD,YAAaE,gBAAgB;oBAC9BE;gBAApB,MAAM5F,eAAc4F,eAAAA,kCAAAA,aAAe5F,WAAW;gBAE9C,IAAIA,aAAa;oBACfyG,aAAaC,IAAI,CAAC1G;gBACpB;YACF;YAEA,6DAA6D;YAC7D,gEAAgE;YAChE,kEAAkE;YAClE,iEAAiE;YACjE,2DAA2D;YAC3D,IAAIwF,YAAYE,gBAAgB;oBACVG;gBAApB,MAAM7F,eAAc6F,eAAAA,kCAAAA,aAAe7F,WAAW;gBAE9C,IAAIA,aAAa;oBACfyG,aAAaC,IAAI,CAAC1G;gBACpB;YACF;YAEA,iEAAiE;YACjE,mEAAmE;YACnE,oBAAoB;YACpB,IAAIyF,gBAAgBC,gBAAgB;oBACdI;gBAApB,MAAM9F,eAAc8F,mBAAAA,sCAAAA,iBAAmB9F,WAAW;gBAElD,IAAIA,aAAa;oBACfyG,aAAaC,IAAI,CAAC1G;gBACpB;YACF;YAEA,OAAOyG;QACT;IACF;IAEA,IAAIE,sCAAsChD,MAAMvC,IAAI;IACpD,IACEuF,wCAAwC,QACxCA,oCAAoC9G,UAAU,CAAC,YAC/C;QACA8G,sCAAsC/H,KAAKyF,QAAQ,CACjDX,eACA7E,cAAc8H;IAElB;IACA,8FAA8F;IAC9F,MAAMC,oBAAyC;QAC7CxF,MAAMuF;QACNnF,OAAOmC,MAAMnC,KAAK;QAClBE,SAASiC,MAAMjC,OAAO;QACtB6C,YAAYZ,MAAMY,UAAU;QAC5B5B,SAASjD,mBAAmB8G;QAC5BhC,WAAW,EAAE;IACf;IACA,IAAI,CAAC9D,QAAQ;QACX,iDAAiD;QACjD,OAAO;YACL+D,oBAAoBmC;YACpBlC,mBAAmB;QACrB;IACF;IACAkC,kBAAkBjE,OAAO,KAAKxD,+BAA+BuB,OAAOG,SAAS;IAE7E,MAAMgG,6BAA6B,MAAMrD,yBAAyB;QAChEC,kBAAkBmD,kBAAkBjE,OAAO;QAC3CgB;QACAjD;QACAgD;IACF;IAEA,IAAI,CAACmD,4BAA4B;QAC/B,OAAO;YACLpC,oBAAoBmC;YACpBlC,mBAAmB;QACrB;IACF;IAEA,OAAOmC;AACT;AAEA,OAAO,SAASC,qBAAqB9B,OAMpC;IACC,MAAM,EAAEtB,aAAa,EAAEqD,QAAQ,EAAEnB,WAAW,EAAEC,WAAW,EAAEC,eAAe,EAAE,GAC1Ed;IAEF,OAAO,eACLgC,GAAoB,EACpBC,GAAmB,EACnBC,IAAgB;QAEhB,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAE,GAAG,IAAIC,IAAI,CAAC,QAAQ,EAAEL,IAAItE,GAAG,EAAE;QAE/D,IAAIyE,aAAa,mCAAmC;YAClD,IAAIH,IAAIM,MAAM,KAAK,QAAQ;gBACzB,OAAO/H,mBAAmBgI,UAAU,CAACN;YACvC;YAEA,MAAMO,OAAO,MAAM,IAAIxB,QAAgB,CAAC7B,SAASsD;gBAC/C,IAAIC,OAAO;gBACXV,IAAIW,EAAE,CAAC,QAAQ,CAACC;oBACdF,QAAQE;gBACV;gBACAZ,IAAIW,EAAE,CAAC,OAAO,IAAMxD,QAAQuD;gBAC5BV,IAAIW,EAAE,CAAC,SAASF;YAClB;YAEA,IAAI;gBACF,MAAM,EAAE9B,MAAM,EAAEH,QAAQ,EAAEC,YAAY,EAAEC,cAAc,EAAE,GAAGmC,KAAKC,KAAK,CACnEN;gBAGF,OAAOjI,mBAAmBwI,IAAI,CAC5Bd,KACA,MAAM1B,uBAAuB;oBAC3BC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACApC;gBACF;YAEJ,EAAE,OAAOoB,KAAK;gBACZ,OAAOvF,mBAAmBgI,UAAU,CAACN;YACvC;QACF,OAAO,IAAIE,aAAa,2BAA2B;YACjD,MAAMxD,QAAQ;gBACZvC,MAAMgG,aAAa7D,GAAG,CAAC;gBACvBgB,YAAY6C,aAAa7D,GAAG,CAAC;gBAC7B/B,OAAOwG,SAASZ,aAAa7D,GAAG,CAAC,YAAY,KAAK,OAAO;gBACzD7B,SAASsG,SAASZ,aAAa7D,GAAG,CAAC,cAAc,KAAK,OAAO;gBAC7DiB,WAAW4C,aAAaa,MAAM,CAAC,aAAaC,MAAM,CAACC;YACrD;YAEA,IAAI,CAACxE,MAAMvC,IAAI,EAAE,OAAO7B,mBAAmBgI,UAAU,CAACN;YAEtD,IAAImB;YACJ,MAAMC,oBAAoBjB,aAAa7D,GAAG,CAAC,yBAAyB;YACpE,IAAI8E,mBAAmB;gBACrB,MAAMC,mBAAmBlB,aAAa7D,GAAG,CAAC,WAAW;gBACrD,MAAMgF,UAAU3J,KAAK4J,IAAI,CACvB,OACAzB,WAAW,QAAQ,IACnBuB;gBAEFF,mBAAmB,MAAMhJ,iBAAiBmJ,SAAS,GAAG,GAAG7E;YAC3D,OAAO;gBACL,kFAAkF;gBAClF,kFAAkF;gBAClF,MAAMQ,WAAWP,MAAMvC,IAAI,CAACT,OAAO,CAAC,gBAAgB;gBACpDyH,mBAAmB,MAAMhJ,iBACvB8E,UACAP,MAAMnC,KAAK,EACXmC,MAAMjC,OAAO,IAAI,GACjBgC;YAEJ;YACA,IAAI0E,iBAAiBlH,KAAK,EAAE;gBAC1BD,QAAQC,KAAK,CAAC,4BAA4BkH,iBAAiBlH,KAAK;gBAChE,OAAO3B,mBAAmBkJ,mBAAmB,CAC3CxB,KACAmB,iBAAiBlH,KAAK;YAE1B;YACA,IAAI,CAACkH,iBAAiBM,KAAK,EAAE;gBAC3B,OAAOnJ,mBAAmBoJ,QAAQ,CAAC1B;YACrC;YACA,OAAO1H,mBAAmBqJ,SAAS,CAAC3B;QACtC;QAEA,OAAOC;IACT;AACF;AAEA,OAAO,SAAS2B,uBAAuB7D,OAItC;IACC,MAAM,EAAEY,WAAW,EAAEC,WAAW,EAAEC,eAAe,EAAE,GAAGd;IAEtD,OAAO,eACLgC,GAAoB,EACpBC,GAAmB,EACnBC,IAAgB;QAEhB,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAE,GAAG,IAAIC,IAAI,CAAC,QAAQ,EAAEL,IAAItE,GAAG,EAAE;QAE/D,IAAIyE,aAAa,wBAAwB;YACvC,OAAOD;QACT;QAEA,MAAMV,WAAWY,aAAa7D,GAAG,CAAC;QAElC,IAAI,CAACiD,UAAU;YACb,OAAOjH,mBAAmBgI,UAAU,CAACN;QACvC;QAEA,IAAIvG;QAEJ,IAAI;YACFA,SAAS,MAAMqE,UACb;gBACE3D,MAAMoF;gBACN,wCAAwC;gBACxChF,OAAO;gBACPE,SAAS;YACX,GACA;gBACEuD,iBAAiB;oBACf,MAAMwB,eAAsC,EAAE;oBAE9C,KAAK,MAAMqC,SAAS;wBAClBlD;wBACAC;wBACAC;qBACD,CAAE;wBACD,IAAIgD,yBAAAA,MAAO9I,WAAW,EAAE;4BACtByG,aAAaC,IAAI,CAACoC,MAAM9I,WAAW;wBACrC;oBACF;oBAEA,OAAOyG;gBACT;YACF;QAEJ,EAAE,OAAOvF,OAAO;YACd,OAAO3B,mBAAmBkJ,mBAAmB,CAACxB,KAAK/F;QACrD;QAEA,IAAI,CAACR,QAAQ;YACX,OAAOnB,mBAAmBqJ,SAAS,CAAC3B;QACtC;QAEA,OAAO1H,mBAAmBwI,IAAI,CAACd,KAAKvG,OAAOG,SAAS;IACtD;AACF", "ignoreList": [0]}