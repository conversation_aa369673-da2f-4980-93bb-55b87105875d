{"version": 3, "sources": ["../../../src/server/request/root-params.ts"], "sourcesContent": ["import { InvariantError } from '../../shared/lib/invariant-error'\nimport {\n  postponeWithTracking,\n  throwToInterruptStaticGeneration,\n} from '../app-render/dynamic-rendering'\nimport {\n  workAsyncStorage,\n  type WorkStore,\n} from '../app-render/work-async-storage.external'\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStoreLegacy,\n  type PrerenderStorePPR,\n  type StaticPrerenderStore,\n} from '../app-render/work-unit-async-storage.external'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport type { FallbackRouteParams } from './fallback-params'\nimport type { Params, ParamValue } from './params'\nimport {\n  describeStringPropertyAccess,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\nimport { actionAsyncStorage } from '../app-render/action-async-storage.external'\nimport { warnOnce } from '../../build/output/log'\n\ninterface CacheLifetime {}\nconst CachedParams = new WeakMap<CacheLifetime, Promise<Params>>()\n\n/**\n * @deprecated import specific root params from `next/root-params` instead.\n */\nexport async function unstable_rootParams(): Promise<Params> {\n  warnOnce(\n    '`unstable_rootParams()` is deprecated and will be removed in an upcoming major release. Import specific root params from `next/root-params` instead.'\n  )\n  const workStore = workAsyncStorage.getStore()\n  if (!workStore) {\n    throw new InvariantError('Missing workStore in unstable_rootParams')\n  }\n\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (!workUnitStore) {\n    throw new Error(\n      `Route ${workStore.route} used \\`unstable_rootParams()\\` in Pages Router. This API is only available within App Router.`\n    )\n  }\n\n  switch (workUnitStore.type) {\n    case 'cache':\n    case 'unstable-cache': {\n      throw new Error(\n        `Route ${workStore.route} used \\`unstable_rootParams()\\` inside \\`\"use cache\"\\` or \\`unstable_cache\\`. Support for this API inside cache scopes is planned for a future version of Next.js.`\n      )\n    }\n    case 'prerender':\n    case 'prerender-client':\n    case 'prerender-ppr':\n    case 'prerender-legacy':\n      return createPrerenderRootParams(\n        workUnitStore.rootParams,\n        workStore,\n        workUnitStore\n      )\n    case 'private-cache':\n    case 'prerender-runtime':\n    case 'request':\n      return Promise.resolve(workUnitStore.rootParams)\n    default:\n      return workUnitStore satisfies never\n  }\n}\n\nfunction createPrerenderRootParams(\n  underlyingParams: Params,\n  workStore: WorkStore,\n  prerenderStore: StaticPrerenderStore\n): Promise<Params> {\n  switch (prerenderStore.type) {\n    case 'prerender-client': {\n      const exportName = '`unstable_rootParams`'\n      throw new InvariantError(\n        `${exportName} must not be used within a client component. Next.js should be preventing ${exportName} from being included in client components statically, but did not in this case.`\n      )\n    }\n    case 'prerender': {\n      const fallbackParams = prerenderStore.fallbackRouteParams\n      if (fallbackParams) {\n        for (const key in underlyingParams) {\n          if (fallbackParams.has(key)) {\n            const cachedParams = CachedParams.get(underlyingParams)\n            if (cachedParams) {\n              return cachedParams\n            }\n\n            const promise = makeHangingPromise<Params>(\n              prerenderStore.renderSignal,\n              workStore.route,\n              '`unstable_rootParams`'\n            )\n            CachedParams.set(underlyingParams, promise)\n\n            return promise\n          }\n        }\n      }\n      break\n    }\n    case 'prerender-ppr': {\n      const fallbackParams = prerenderStore.fallbackRouteParams\n      if (fallbackParams) {\n        for (const key in underlyingParams) {\n          if (fallbackParams.has(key)) {\n            // We have fallback params at this level so we need to make an erroring\n            // params object which will postpone if you access the fallback params\n            return makeErroringRootParams(\n              underlyingParams,\n              fallbackParams,\n              workStore,\n              prerenderStore\n            )\n          }\n        }\n      }\n      break\n    }\n    case 'prerender-legacy':\n      break\n    default:\n      prerenderStore satisfies never\n  }\n\n  // We don't have any fallback params so we have an entirely static safe params object\n  return Promise.resolve(underlyingParams)\n}\n\nfunction makeErroringRootParams(\n  underlyingParams: Params,\n  fallbackParams: FallbackRouteParams,\n  workStore: WorkStore,\n  prerenderStore: PrerenderStorePPR | PrerenderStoreLegacy\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const augmentedUnderlying = { ...underlyingParams }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(augmentedUnderlying)\n  CachedParams.set(underlyingParams, promise)\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      if (fallbackParams.has(prop)) {\n        Object.defineProperty(augmentedUnderlying, prop, {\n          get() {\n            const expression = describeStringPropertyAccess(\n              'unstable_rootParams',\n              prop\n            )\n            // In most dynamic APIs we also throw if `dynamic = \"error\"` however\n            // for params is only dynamic when we're generating a fallback shell\n            // and even when `dynamic = \"error\"` we still support generating dynamic\n            // fallback shells\n            // TODO remove this comment when cacheComponents is the default since there\n            // will be no `dynamic = \"error\"`\n            if (prerenderStore.type === 'prerender-ppr') {\n              // PPR Prerender (no cacheComponents)\n              postponeWithTracking(\n                workStore.route,\n                expression,\n                prerenderStore.dynamicTracking\n              )\n            } else {\n              // Legacy Prerender\n              throwToInterruptStaticGeneration(\n                expression,\n                workStore,\n                prerenderStore\n              )\n            }\n          },\n          enumerable: true,\n        })\n      } else {\n        ;(promise as any)[prop] = underlyingParams[prop]\n      }\n    }\n  })\n\n  return promise\n}\n\n/**\n * Used for the compiler-generated `next/root-params` module.\n * @internal\n */\nexport function getRootParam(paramName: string): Promise<ParamValue> {\n  const apiName = `\\`import('next/root-params').${paramName}()\\``\n\n  const workStore = workAsyncStorage.getStore()\n  if (!workStore) {\n    throw new InvariantError(`Missing workStore in ${apiName}`)\n  }\n\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (!workUnitStore) {\n    throw new Error(\n      `Route ${workStore.route} used ${apiName} outside of a Server Component. This is not allowed.`\n    )\n  }\n\n  const actionStore = actionAsyncStorage.getStore()\n  if (actionStore) {\n    if (actionStore.isAppRoute) {\n      // TODO(root-params): add support for route handlers\n      throw new Error(\n        `Route ${workStore.route} used ${apiName} inside a Route Handler. Support for this API in Route Handlers is planned for a future version of Next.js.`\n      )\n    }\n    if (actionStore.isAction && workUnitStore.phase === 'action') {\n      // Actions are not fundamentally tied to a route (even if they're always submitted from some page),\n      // so root params would be inconsistent if an action is called from multiple roots.\n      // Make sure we check if the phase is \"action\" - we should not error in the rerender\n      // after an action revalidates or updates cookies (which will still have `actionStore.isAction === true`)\n      throw new Error(\n        `${apiName} was used inside a Server Action. This is not supported. Functions from 'next/root-params' can only be called in the context of a route.`\n      )\n    }\n  }\n\n  switch (workUnitStore.type) {\n    case 'unstable-cache':\n    case 'cache': {\n      throw new Error(\n        `Route ${workStore.route} used ${apiName} inside \\`\"use cache\"\\` or \\`unstable_cache\\`. Support for this API inside cache scopes is planned for a future version of Next.js.`\n      )\n    }\n    case 'prerender':\n    case 'prerender-client':\n    case 'prerender-ppr':\n    case 'prerender-legacy': {\n      return createPrerenderRootParamPromise(\n        paramName,\n        workStore,\n        workUnitStore,\n        apiName\n      )\n    }\n    case 'private-cache':\n    case 'prerender-runtime':\n    case 'request': {\n      break\n    }\n    default: {\n      workUnitStore satisfies never\n    }\n  }\n  return Promise.resolve(workUnitStore.rootParams[paramName])\n}\n\nfunction createPrerenderRootParamPromise(\n  paramName: string,\n  workStore: WorkStore,\n  prerenderStore: StaticPrerenderStore,\n  apiName: string\n): Promise<ParamValue> {\n  switch (prerenderStore.type) {\n    case 'prerender-client': {\n      throw new InvariantError(\n        `${apiName} must not be used within a client component. Next.js should be preventing ${apiName} from being included in client components statically, but did not in this case.`\n      )\n    }\n    case 'prerender':\n    case 'prerender-legacy':\n    case 'prerender-ppr':\n    default:\n  }\n\n  const underlyingParams = prerenderStore.rootParams\n\n  switch (prerenderStore.type) {\n    case 'prerender': {\n      // We are in a dynamicIO prerender.\n      // The param is a fallback, so it should be treated as dynamic.\n      if (\n        prerenderStore.fallbackRouteParams &&\n        prerenderStore.fallbackRouteParams.has(paramName)\n      ) {\n        return makeHangingPromise<ParamValue>(\n          prerenderStore.renderSignal,\n          workStore.route,\n          apiName\n        )\n      }\n      break\n    }\n    case 'prerender-ppr': {\n      // We aren't in a dynamicIO prerender, but the param is a fallback,\n      // so we need to make an erroring params object which will postpone/error if you access it\n      if (\n        prerenderStore.fallbackRouteParams &&\n        prerenderStore.fallbackRouteParams.has(paramName)\n      ) {\n        return makeErroringRootParamPromise(\n          paramName,\n          workStore,\n          prerenderStore,\n          apiName\n        )\n      }\n      break\n    }\n    case 'prerender-legacy': {\n      // legacy prerenders can't have fallback params\n      break\n    }\n    default: {\n      prerenderStore satisfies never\n    }\n  }\n\n  // If the param is not a fallback param, we just return the statically available value.\n  return Promise.resolve(underlyingParams[paramName])\n}\n\n/** Deliberately async -- we want to create a rejected promise, not error synchronously. */\nasync function makeErroringRootParamPromise(\n  paramName: string,\n  workStore: WorkStore,\n  prerenderStore: PrerenderStorePPR | PrerenderStoreLegacy,\n  apiName: string\n): Promise<ParamValue> {\n  const expression = describeStringPropertyAccess(apiName, paramName)\n  // In most dynamic APIs, we also throw if `dynamic = \"error\"`.\n  // However, root params are only dynamic when we're generating a fallback shell,\n  // and even with `dynamic = \"error\"` we still support generating dynamic fallback shells.\n  // TODO: remove this comment when dynamicIO is the default since there will be no `dynamic = \"error\"`\n  switch (prerenderStore.type) {\n    case 'prerender-ppr': {\n      return postponeWithTracking(\n        workStore.route,\n        expression,\n        prerenderStore.dynamicTracking\n      )\n    }\n    case 'prerender-legacy': {\n      return throwToInterruptStaticGeneration(\n        expression,\n        workStore,\n        prerenderStore\n      )\n    }\n    default: {\n      prerenderStore satisfies never\n    }\n  }\n}\n"], "names": ["getRootParam", "unstable_rootParams", "C<PERSON>d<PERSON><PERSON><PERSON>", "WeakMap", "warnOnce", "workStore", "workAsyncStorage", "getStore", "InvariantError", "workUnitStore", "workUnitAsyncStorage", "Error", "route", "type", "createPrerenderRootParams", "rootParams", "Promise", "resolve", "underlyingParams", "prerenderStore", "exportName", "fallbackP<PERSON><PERSON>", "fallbackRouteParams", "key", "has", "cachedParams", "get", "promise", "makeHangingPromise", "renderSignal", "set", "makeErroringRootParams", "augmentedUnderlying", "Object", "keys", "for<PERSON>ach", "prop", "wellKnownProperties", "defineProperty", "expression", "describeStringPropertyAccess", "postponeWithTracking", "dynamicTracking", "throwToInterruptStaticGeneration", "enumerable", "paramName", "apiName", "actionStore", "actionAsyncStorage", "isAppRoute", "isAction", "phase", "createPrerenderRootParamPromise", "makeErroringRootParamPromise"], "mappings": ";;;;;;;;;;;;;;;IA4MgBA,YAAY;eAAZA;;IA7KMC,mBAAmB;eAAnBA;;;gCA/BS;kCAIxB;0CAIA;8CAMA;uCAC4B;8BAM5B;4CAC4B;qBACV;AAGzB,MAAMC,eAAe,IAAIC;AAKlB,eAAeF;IACpBG,IAAAA,aAAQ,EACN;IAEF,MAAMC,YAAYC,0CAAgB,CAACC,QAAQ;IAC3C,IAAI,CAACF,WAAW;QACd,MAAM,qBAA8D,CAA9D,IAAIG,8BAAc,CAAC,6CAAnB,qBAAA;mBAAA;wBAAA;0BAAA;QAA6D;IACrE;IAEA,MAAMC,gBAAgBC,kDAAoB,CAACH,QAAQ;IAEnD,IAAI,CAACE,eAAe;QAClB,MAAM,qBAEL,CAFK,IAAIE,MACR,CAAC,MAAM,EAAEN,UAAUO,KAAK,CAAC,8FAA8F,CAAC,GADpH,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,OAAQH,cAAcI,IAAI;QACxB,KAAK;QACL,KAAK;YAAkB;gBACrB,MAAM,qBAEL,CAFK,IAAIF,MACR,CAAC,MAAM,EAAEN,UAAUO,KAAK,CAAC,kKAAkK,CAAC,GADxL,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACA,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAOE,0BACLL,cAAcM,UAAU,EACxBV,WACAI;QAEJ,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAOO,QAAQC,OAAO,CAACR,cAAcM,UAAU;QACjD;YACE,OAAON;IACX;AACF;AAEA,SAASK,0BACPI,gBAAwB,EACxBb,SAAoB,EACpBc,cAAoC;IAEpC,OAAQA,eAAeN,IAAI;QACzB,KAAK;YAAoB;gBACvB,MAAMO,aAAa;gBACnB,MAAM,qBAEL,CAFK,IAAIZ,8BAAc,CACtB,GAAGY,WAAW,0EAA0E,EAAEA,WAAW,+EAA+E,CAAC,GADjL,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACA,KAAK;YAAa;gBAChB,MAAMC,iBAAiBF,eAAeG,mBAAmB;gBACzD,IAAID,gBAAgB;oBAClB,IAAK,MAAME,OAAOL,iBAAkB;wBAClC,IAAIG,eAAeG,GAAG,CAACD,MAAM;4BAC3B,MAAME,eAAevB,aAAawB,GAAG,CAACR;4BACtC,IAAIO,cAAc;gCAChB,OAAOA;4BACT;4BAEA,MAAME,UAAUC,IAAAA,yCAAkB,EAChCT,eAAeU,YAAY,EAC3BxB,UAAUO,KAAK,EACf;4BAEFV,aAAa4B,GAAG,CAACZ,kBAAkBS;4BAEnC,OAAOA;wBACT;oBACF;gBACF;gBACA;YACF;QACA,KAAK;YAAiB;gBACpB,MAAMN,iBAAiBF,eAAeG,mBAAmB;gBACzD,IAAID,gBAAgB;oBAClB,IAAK,MAAME,OAAOL,iBAAkB;wBAClC,IAAIG,eAAeG,GAAG,CAACD,MAAM;4BAC3B,uEAAuE;4BACvE,sEAAsE;4BACtE,OAAOQ,uBACLb,kBACAG,gBACAhB,WACAc;wBAEJ;oBACF;gBACF;gBACA;YACF;QACA,KAAK;YACH;QACF;YACEA;IACJ;IAEA,qFAAqF;IACrF,OAAOH,QAAQC,OAAO,CAACC;AACzB;AAEA,SAASa,uBACPb,gBAAwB,EACxBG,cAAmC,EACnChB,SAAoB,EACpBc,cAAwD;IAExD,MAAMM,eAAevB,aAAawB,GAAG,CAACR;IACtC,IAAIO,cAAc;QAChB,OAAOA;IACT;IAEA,MAAMO,sBAAsB;QAAE,GAAGd,gBAAgB;IAAC;IAElD,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAMS,UAAUX,QAAQC,OAAO,CAACe;IAChC9B,aAAa4B,GAAG,CAACZ,kBAAkBS;IAEnCM,OAAOC,IAAI,CAAChB,kBAAkBiB,OAAO,CAAC,CAACC;QACrC,IAAIC,iCAAmB,CAACb,GAAG,CAACY,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;YACL,IAAIf,eAAeG,GAAG,CAACY,OAAO;gBAC5BH,OAAOK,cAAc,CAACN,qBAAqBI,MAAM;oBAC/CV;wBACE,MAAMa,aAAaC,IAAAA,0CAA4B,EAC7C,uBACAJ;wBAEF,oEAAoE;wBACpE,oEAAoE;wBACpE,wEAAwE;wBACxE,kBAAkB;wBAClB,2EAA2E;wBAC3E,iCAAiC;wBACjC,IAAIjB,eAAeN,IAAI,KAAK,iBAAiB;4BAC3C,qCAAqC;4BACrC4B,IAAAA,sCAAoB,EAClBpC,UAAUO,KAAK,EACf2B,YACApB,eAAeuB,eAAe;wBAElC,OAAO;4BACL,mBAAmB;4BACnBC,IAAAA,kDAAgC,EAC9BJ,YACAlC,WACAc;wBAEJ;oBACF;oBACAyB,YAAY;gBACd;YACF,OAAO;;gBACHjB,OAAe,CAACS,KAAK,GAAGlB,gBAAgB,CAACkB,KAAK;YAClD;QACF;IACF;IAEA,OAAOT;AACT;AAMO,SAAS3B,aAAa6C,SAAiB;IAC5C,MAAMC,UAAU,CAAC,6BAA6B,EAAED,UAAU,IAAI,CAAC;IAE/D,MAAMxC,YAAYC,0CAAgB,CAACC,QAAQ;IAC3C,IAAI,CAACF,WAAW;QACd,MAAM,qBAAqD,CAArD,IAAIG,8BAAc,CAAC,CAAC,qBAAqB,EAAEsC,SAAS,GAApD,qBAAA;mBAAA;wBAAA;0BAAA;QAAoD;IAC5D;IAEA,MAAMrC,gBAAgBC,kDAAoB,CAACH,QAAQ;IACnD,IAAI,CAACE,eAAe;QAClB,MAAM,qBAEL,CAFK,IAAIE,MACR,CAAC,MAAM,EAAEN,UAAUO,KAAK,CAAC,MAAM,EAAEkC,QAAQ,oDAAoD,CAAC,GAD1F,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMC,cAAcC,8CAAkB,CAACzC,QAAQ;IAC/C,IAAIwC,aAAa;QACf,IAAIA,YAAYE,UAAU,EAAE;YAC1B,oDAAoD;YACpD,MAAM,qBAEL,CAFK,IAAItC,MACR,CAAC,MAAM,EAAEN,UAAUO,KAAK,CAAC,MAAM,EAAEkC,QAAQ,2GAA2G,CAAC,GADjJ,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,IAAIC,YAAYG,QAAQ,IAAIzC,cAAc0C,KAAK,KAAK,UAAU;YAC5D,mGAAmG;YACnG,mFAAmF;YACnF,oFAAoF;YACpF,yGAAyG;YACzG,MAAM,qBAEL,CAFK,IAAIxC,MACR,GAAGmC,QAAQ,wIAAwI,CAAC,GADhJ,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;IAEA,OAAQrC,cAAcI,IAAI;QACxB,KAAK;QACL,KAAK;YAAS;gBACZ,MAAM,qBAEL,CAFK,IAAIF,MACR,CAAC,MAAM,EAAEN,UAAUO,KAAK,CAAC,MAAM,EAAEkC,QAAQ,mIAAmI,CAAC,GADzK,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACA,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAoB;gBACvB,OAAOM,gCACLP,WACAxC,WACAI,eACAqC;YAEJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAW;gBACd;YACF;QACA;YAAS;gBACPrC;YACF;IACF;IACA,OAAOO,QAAQC,OAAO,CAACR,cAAcM,UAAU,CAAC8B,UAAU;AAC5D;AAEA,SAASO,gCACPP,SAAiB,EACjBxC,SAAoB,EACpBc,cAAoC,EACpC2B,OAAe;IAEf,OAAQ3B,eAAeN,IAAI;QACzB,KAAK;YAAoB;gBACvB,MAAM,qBAEL,CAFK,IAAIL,8BAAc,CACtB,GAAGsC,QAAQ,0EAA0E,EAAEA,QAAQ,+EAA+E,CAAC,GAD3K,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACA,KAAK;QACL,KAAK;QACL,KAAK;QACL;IACF;IAEA,MAAM5B,mBAAmBC,eAAeJ,UAAU;IAElD,OAAQI,eAAeN,IAAI;QACzB,KAAK;YAAa;gBAChB,mCAAmC;gBACnC,+DAA+D;gBAC/D,IACEM,eAAeG,mBAAmB,IAClCH,eAAeG,mBAAmB,CAACE,GAAG,CAACqB,YACvC;oBACA,OAAOjB,IAAAA,yCAAkB,EACvBT,eAAeU,YAAY,EAC3BxB,UAAUO,KAAK,EACfkC;gBAEJ;gBACA;YACF;QACA,KAAK;YAAiB;gBACpB,mEAAmE;gBACnE,0FAA0F;gBAC1F,IACE3B,eAAeG,mBAAmB,IAClCH,eAAeG,mBAAmB,CAACE,GAAG,CAACqB,YACvC;oBACA,OAAOQ,6BACLR,WACAxC,WACAc,gBACA2B;gBAEJ;gBACA;YACF;QACA,KAAK;YAAoB;gBAEvB;YACF;QACA;YAAS;gBACP3B;YACF;IACF;IAEA,uFAAuF;IACvF,OAAOH,QAAQC,OAAO,CAACC,gBAAgB,CAAC2B,UAAU;AACpD;AAEA,yFAAyF,GACzF,eAAeQ,6BACbR,SAAiB,EACjBxC,SAAoB,EACpBc,cAAwD,EACxD2B,OAAe;IAEf,MAAMP,aAAaC,IAAAA,0CAA4B,EAACM,SAASD;IACzD,8DAA8D;IAC9D,gFAAgF;IAChF,yFAAyF;IACzF,qGAAqG;IACrG,OAAQ1B,eAAeN,IAAI;QACzB,KAAK;YAAiB;gBACpB,OAAO4B,IAAAA,sCAAoB,EACzBpC,UAAUO,KAAK,EACf2B,YACApB,eAAeuB,eAAe;YAElC;QACA,KAAK;YAAoB;gBACvB,OAAOC,IAAAA,kDAAgC,EACrCJ,YACAlC,WACAc;YAEJ;QACA;YAAS;gBACPA;YACF;IACF;AACF", "ignoreList": [0]}