{"version": 3, "sources": ["../../../src/export/routes/pages.ts"], "sourcesContent": ["import type { ExportRouteResult } from '../types'\nimport type {\n  PagesRenderContext,\n  PagesSharedContext,\n  RenderOpts,\n} from '../../server/render'\nimport type { LoadComponentsReturnType } from '../../server/load-components'\nimport type { AmpValidation } from '../types'\nimport type { NextParsedUrlQuery } from '../../server/request-meta'\nimport type { Params } from '../../server/request/params'\n\nimport RenderResult from '../../server/render-result'\nimport { join } from 'path'\nimport type {\n  MockedRequest,\n  MockedResponse,\n} from '../../server/lib/mock-request'\nimport { isInAmpMode } from '../../shared/lib/amp-mode'\nimport {\n  HTML_CONTENT_TYPE_HEADER,\n  NEXT_DATA_SUFFIX,\n  SERVER_PROPS_EXPORT_ERROR,\n} from '../../lib/constants'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { FileType, fileExists } from '../../lib/file-exists'\nimport { lazyRenderPagesPage } from '../../server/route-modules/pages/module.render'\nimport type { MultiFileWriter } from '../../lib/multi-file-writer'\nimport {\n  getAmpValidatorInstance,\n  getBundledAmpValidatorFilepath,\n} from '../helpers/get-amp-html-validator'\n\n/**\n * Renders & exports a page associated with the /pages directory\n */\nexport async function exportPagesPage(\n  req: MockedRequest,\n  res: MockedResponse,\n  path: string,\n  page: string,\n  query: NextParsedUrlQuery,\n  params: Params | undefined,\n  htmlFilepath: string,\n  htmlFilename: string,\n  ampPath: string,\n  subFolders: boolean,\n  outDir: string,\n  ampValidatorPath: string | undefined,\n  pagesDataDir: string,\n  buildExport: boolean,\n  isDynamic: boolean,\n  sharedContext: PagesSharedContext,\n  renderContext: PagesRenderContext,\n  hasOrigQueryValues: boolean,\n  renderOpts: RenderOpts,\n  components: LoadComponentsReturnType,\n  fileWriter: MultiFileWriter\n): Promise<ExportRouteResult | undefined> {\n  const ampState = {\n    ampFirst: components.pageConfig?.amp === true,\n    hasQuery: Boolean(query.amp),\n    hybrid: components.pageConfig?.amp === 'hybrid',\n  }\n\n  if (!ampValidatorPath) {\n    ampValidatorPath = getBundledAmpValidatorFilepath()\n  }\n\n  const inAmpMode = isInAmpMode(ampState)\n  const hybridAmp = ampState.hybrid\n\n  if (components.getServerSideProps) {\n    throw new Error(`Error for page ${page}: ${SERVER_PROPS_EXPORT_ERROR}`)\n  }\n\n  // for non-dynamic SSG pages we should have already\n  // prerendered the file\n  if (!buildExport && components.getStaticProps && !isDynamic) {\n    return\n  }\n\n  // Pages router merges page params (e.g. [lang]) with query params\n  // primarily to support them both being accessible on `useRouter().query`.\n  // If we extracted dynamic params from the path, we need to merge them\n  // back into the query object.\n  const searchAndDynamicParams = {\n    ...query,\n    ...params,\n  }\n\n  if (components.getStaticProps && !htmlFilepath.endsWith('.html')) {\n    // make sure it ends with .html if the name contains a dot\n    htmlFilepath += '.html'\n    htmlFilename += '.html'\n  }\n\n  let renderResult: RenderResult | undefined\n\n  if (typeof components.Component === 'string') {\n    renderResult = RenderResult.fromStatic(\n      components.Component,\n      HTML_CONTENT_TYPE_HEADER\n    )\n\n    if (hasOrigQueryValues) {\n      throw new Error(\n        `\\nError: you provided query values for ${path} which is an auto-exported page. These can not be applied since the page can no longer be re-rendered on the server. To disable auto-export for this page add \\`getInitialProps\\`\\n`\n      )\n    }\n  } else {\n    /**\n     * This sets environment variable to be used at the time of SSR by head.tsx.\n     * Using this from process.env allows targeting SSR by calling\n     * `process.env.__NEXT_OPTIMIZE_CSS`.\n     */\n    if (renderOpts.optimizeCss) {\n      process.env.__NEXT_OPTIMIZE_CSS = JSON.stringify(true)\n    }\n    try {\n      renderResult = await lazyRenderPagesPage(\n        req,\n        res,\n        page,\n        searchAndDynamicParams,\n        renderOpts,\n        sharedContext,\n        renderContext\n      )\n    } catch (err) {\n      if (!isBailoutToCSRError(err)) throw err\n    }\n  }\n\n  const ssgNotFound = renderResult?.metadata.isNotFound\n\n  const ampValidations: AmpValidation[] = []\n\n  const validateAmp = async (\n    rawAmpHtml: string,\n    ampPageName: string,\n    validatorPath: string | undefined\n  ) => {\n    const validator = await getAmpValidatorInstance(validatorPath)\n    const result = validator.validateString(rawAmpHtml)\n    const errors = result.errors.filter((error) => {\n      if (error.severity === 'ERROR') {\n        // Unclear yet if these actually prevent the page from being indexed by the AMP cache.\n        // These are coming from React so all we can do is ignore them for now.\n\n        // <link rel=\"expect\" blocking=\"render\" />\n        // https://github.com/ampproject/amphtml/issues/40279\n        if (\n          error.code === 'DISALLOWED_ATTR' &&\n          error.params[0] === 'blocking' &&\n          error.params[1] === 'link'\n        ) {\n          return false\n        }\n        // <template> without type\n        // https://github.com/ampproject/amphtml/issues/40280\n        if (\n          error.code === 'MANDATORY_ATTR_MISSING' &&\n          error.params[0] === 'type' &&\n          error.params[1] === 'template'\n        ) {\n          return false\n        }\n        // <template> without type\n        // https://github.com/ampproject/amphtml/issues/40280\n        if (\n          error.code === 'MISSING_REQUIRED_EXTENSION' &&\n          error.params[0] === 'template' &&\n          error.params[1] === 'amp-mustache'\n        ) {\n          return false\n        }\n        return true\n      }\n      return false\n    })\n    const warnings = result.errors.filter((e) => e.severity !== 'ERROR')\n\n    if (warnings.length || errors.length) {\n      ampValidations.push({\n        page: ampPageName,\n        result: {\n          errors,\n          warnings,\n        },\n      })\n    }\n  }\n\n  const html =\n    renderResult && !renderResult.isNull ? renderResult.toUnchunkedString() : ''\n\n  let ampRenderResult: RenderResult | undefined\n\n  if (inAmpMode && !renderOpts.ampSkipValidation) {\n    if (!ssgNotFound) {\n      await validateAmp(html, path, ampValidatorPath)\n    }\n  } else if (hybridAmp) {\n    const ampHtmlFilename = subFolders\n      ? join(ampPath, 'index.html')\n      : `${ampPath}.html`\n\n    const ampHtmlFilepath = join(outDir, ampHtmlFilename)\n\n    const exists = await fileExists(ampHtmlFilepath, FileType.File)\n    if (!exists) {\n      try {\n        ampRenderResult = await lazyRenderPagesPage(\n          req,\n          res,\n          page,\n          { ...searchAndDynamicParams, amp: '1' },\n          renderOpts,\n          sharedContext,\n          renderContext\n        )\n      } catch (err) {\n        if (!isBailoutToCSRError(err)) throw err\n      }\n\n      const ampHtml =\n        ampRenderResult && !ampRenderResult.isNull\n          ? ampRenderResult.toUnchunkedString()\n          : ''\n      if (!renderOpts.ampSkipValidation) {\n        await validateAmp(ampHtml, page + '?amp=1', ampValidatorPath)\n      }\n\n      fileWriter.append(ampHtmlFilepath, ampHtml)\n    }\n  }\n\n  const metadata = renderResult?.metadata || ampRenderResult?.metadata || {}\n  if (metadata.pageData) {\n    const dataFile = join(\n      pagesDataDir,\n      htmlFilename.replace(/\\.html$/, NEXT_DATA_SUFFIX)\n    )\n\n    fileWriter.append(dataFile, JSON.stringify(metadata.pageData))\n\n    if (hybridAmp) {\n      fileWriter.append(\n        dataFile.replace(/\\.json$/, '.amp.json'),\n        JSON.stringify(metadata.pageData)\n      )\n    }\n  }\n\n  if (!ssgNotFound) {\n    // don't attempt writing to disk if getStaticProps returned not found\n    fileWriter.append(htmlFilepath, html)\n  }\n\n  return {\n    ampValidations,\n    cacheControl: metadata.cacheControl ?? {\n      revalidate: false,\n      expire: undefined,\n    },\n    ssgNotFound,\n  }\n}\n"], "names": ["RenderResult", "join", "isInAmpMode", "HTML_CONTENT_TYPE_HEADER", "NEXT_DATA_SUFFIX", "SERVER_PROPS_EXPORT_ERROR", "isBailoutToCSRError", "FileType", "fileExists", "lazyRenderPagesPage", "getAmpValidatorInstance", "getBundledAmpValidatorFilepath", "exportPagesPage", "req", "res", "path", "page", "query", "params", "htmlFilepath", "htmlFilename", "ampPath", "subFolders", "outDir", "ampValidator<PERSON>ath", "pagesDataDir", "buildExport", "isDynamic", "sharedContext", "renderContext", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "renderOpts", "components", "fileWriter", "ampState", "ampFirs<PERSON>", "pageConfig", "amp", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "hybrid", "inAmpMode", "hybridAmp", "getServerSideProps", "Error", "getStaticProps", "searchAndDynamicParams", "endsWith", "renderResult", "Component", "fromStatic", "optimizeCss", "process", "env", "__NEXT_OPTIMIZE_CSS", "JSON", "stringify", "err", "ssgNotFound", "metadata", "isNotFound", "ampValidations", "validateAmp", "rawAmpHtml", "ampPageName", "validatorPath", "validator", "result", "validateString", "errors", "filter", "error", "severity", "code", "warnings", "e", "length", "push", "html", "isNull", "toUnchunkedString", "ampRenderResult", "ampSkipValidation", "ampHtmlFilename", "ampHtmlFilepath", "exists", "File", "ampHtml", "append", "pageData", "dataFile", "replace", "cacheControl", "revalidate", "expire", "undefined"], "mappings": "AAWA,OAAOA,kBAAkB,6BAA4B;AACrD,SAASC,IAAI,QAAQ,OAAM;AAK3B,SAASC,WAAW,QAAQ,4BAA2B;AACvD,SACEC,wBAAwB,EACxBC,gBAAgB,EAChBC,yBAAyB,QACpB,sBAAqB;AAC5B,SAASC,mBAAmB,QAAQ,+CAA8C;AAClF,SAASC,QAAQ,EAAEC,UAAU,QAAQ,wBAAuB;AAC5D,SAASC,mBAAmB,QAAQ,iDAAgD;AAEpF,SACEC,uBAAuB,EACvBC,8BAA8B,QACzB,oCAAmC;AAE1C;;CAEC,GACD,OAAO,eAAeC,gBACpBC,GAAkB,EAClBC,GAAmB,EACnBC,IAAY,EACZC,IAAY,EACZC,KAAyB,EACzBC,MAA0B,EAC1BC,YAAoB,EACpBC,YAAoB,EACpBC,OAAe,EACfC,UAAmB,EACnBC,MAAc,EACdC,gBAAoC,EACpCC,YAAoB,EACpBC,WAAoB,EACpBC,SAAkB,EAClBC,aAAiC,EACjCC,aAAiC,EACjCC,kBAA2B,EAC3BC,UAAsB,EACtBC,UAAoC,EACpCC,UAA2B;QAGfD,wBAEFA;IAHV,MAAME,WAAW;QACfC,UAAUH,EAAAA,yBAAAA,WAAWI,UAAU,qBAArBJ,uBAAuBK,GAAG,MAAK;QACzCC,UAAUC,QAAQtB,MAAMoB,GAAG;QAC3BG,QAAQR,EAAAA,0BAAAA,WAAWI,UAAU,qBAArBJ,wBAAuBK,GAAG,MAAK;IACzC;IAEA,IAAI,CAACb,kBAAkB;QACrBA,mBAAmBb;IACrB;IAEA,MAAM8B,YAAYvC,YAAYgC;IAC9B,MAAMQ,YAAYR,SAASM,MAAM;IAEjC,IAAIR,WAAWW,kBAAkB,EAAE;QACjC,MAAM,qBAAiE,CAAjE,IAAIC,MAAM,CAAC,eAAe,EAAE5B,KAAK,EAAE,EAAEX,2BAA2B,GAAhE,qBAAA;mBAAA;wBAAA;0BAAA;QAAgE;IACxE;IAEA,mDAAmD;IACnD,uBAAuB;IACvB,IAAI,CAACqB,eAAeM,WAAWa,cAAc,IAAI,CAAClB,WAAW;QAC3D;IACF;IAEA,kEAAkE;IAClE,0EAA0E;IAC1E,sEAAsE;IACtE,8BAA8B;IAC9B,MAAMmB,yBAAyB;QAC7B,GAAG7B,KAAK;QACR,GAAGC,MAAM;IACX;IAEA,IAAIc,WAAWa,cAAc,IAAI,CAAC1B,aAAa4B,QAAQ,CAAC,UAAU;QAChE,0DAA0D;QAC1D5B,gBAAgB;QAChBC,gBAAgB;IAClB;IAEA,IAAI4B;IAEJ,IAAI,OAAOhB,WAAWiB,SAAS,KAAK,UAAU;QAC5CD,eAAehD,aAAakD,UAAU,CACpClB,WAAWiB,SAAS,EACpB9C;QAGF,IAAI2B,oBAAoB;YACtB,MAAM,qBAEL,CAFK,IAAIc,MACR,CAAC,uCAAuC,EAAE7B,KAAK,mLAAmL,CAAC,GAD/N,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF,OAAO;QACL;;;;KAIC,GACD,IAAIgB,WAAWoB,WAAW,EAAE;YAC1BC,QAAQC,GAAG,CAACC,mBAAmB,GAAGC,KAAKC,SAAS,CAAC;QACnD;QACA,IAAI;YACFR,eAAe,MAAMvC,oBACnBI,KACAC,KACAE,MACA8B,wBACAf,YACAH,eACAC;QAEJ,EAAE,OAAO4B,KAAK;YACZ,IAAI,CAACnD,oBAAoBmD,MAAM,MAAMA;QACvC;IACF;IAEA,MAAMC,cAAcV,gCAAAA,aAAcW,QAAQ,CAACC,UAAU;IAErD,MAAMC,iBAAkC,EAAE;IAE1C,MAAMC,cAAc,OAClBC,YACAC,aACAC;QAEA,MAAMC,YAAY,MAAMxD,wBAAwBuD;QAChD,MAAME,SAASD,UAAUE,cAAc,CAACL;QACxC,MAAMM,SAASF,OAAOE,MAAM,CAACC,MAAM,CAAC,CAACC;YACnC,IAAIA,MAAMC,QAAQ,KAAK,SAAS;gBAC9B,sFAAsF;gBACtF,uEAAuE;gBAEvE,0CAA0C;gBAC1C,qDAAqD;gBACrD,IACED,MAAME,IAAI,KAAK,qBACfF,MAAMrD,MAAM,CAAC,EAAE,KAAK,cACpBqD,MAAMrD,MAAM,CAAC,EAAE,KAAK,QACpB;oBACA,OAAO;gBACT;gBACA,0BAA0B;gBAC1B,qDAAqD;gBACrD,IACEqD,MAAME,IAAI,KAAK,4BACfF,MAAMrD,MAAM,CAAC,EAAE,KAAK,UACpBqD,MAAMrD,MAAM,CAAC,EAAE,KAAK,YACpB;oBACA,OAAO;gBACT;gBACA,0BAA0B;gBAC1B,qDAAqD;gBACrD,IACEqD,MAAME,IAAI,KAAK,gCACfF,MAAMrD,MAAM,CAAC,EAAE,KAAK,cACpBqD,MAAMrD,MAAM,CAAC,EAAE,KAAK,gBACpB;oBACA,OAAO;gBACT;gBACA,OAAO;YACT;YACA,OAAO;QACT;QACA,MAAMwD,WAAWP,OAAOE,MAAM,CAACC,MAAM,CAAC,CAACK,IAAMA,EAAEH,QAAQ,KAAK;QAE5D,IAAIE,SAASE,MAAM,IAAIP,OAAOO,MAAM,EAAE;YACpCf,eAAegB,IAAI,CAAC;gBAClB7D,MAAMgD;gBACNG,QAAQ;oBACNE;oBACAK;gBACF;YACF;QACF;IACF;IAEA,MAAMI,OACJ9B,gBAAgB,CAACA,aAAa+B,MAAM,GAAG/B,aAAagC,iBAAiB,KAAK;IAE5E,IAAIC;IAEJ,IAAIxC,aAAa,CAACV,WAAWmD,iBAAiB,EAAE;QAC9C,IAAI,CAACxB,aAAa;YAChB,MAAMI,YAAYgB,MAAM/D,MAAMS;QAChC;IACF,OAAO,IAAIkB,WAAW;QACpB,MAAMyC,kBAAkB7D,aACpBrB,KAAKoB,SAAS,gBACd,GAAGA,QAAQ,KAAK,CAAC;QAErB,MAAM+D,kBAAkBnF,KAAKsB,QAAQ4D;QAErC,MAAME,SAAS,MAAM7E,WAAW4E,iBAAiB7E,SAAS+E,IAAI;QAC9D,IAAI,CAACD,QAAQ;YACX,IAAI;gBACFJ,kBAAkB,MAAMxE,oBACtBI,KACAC,KACAE,MACA;oBAAE,GAAG8B,sBAAsB;oBAAET,KAAK;gBAAI,GACtCN,YACAH,eACAC;YAEJ,EAAE,OAAO4B,KAAK;gBACZ,IAAI,CAACnD,oBAAoBmD,MAAM,MAAMA;YACvC;YAEA,MAAM8B,UACJN,mBAAmB,CAACA,gBAAgBF,MAAM,GACtCE,gBAAgBD,iBAAiB,KACjC;YACN,IAAI,CAACjD,WAAWmD,iBAAiB,EAAE;gBACjC,MAAMpB,YAAYyB,SAASvE,OAAO,UAAUQ;YAC9C;YAEAS,WAAWuD,MAAM,CAACJ,iBAAiBG;QACrC;IACF;IAEA,MAAM5B,WAAWX,CAAAA,gCAAAA,aAAcW,QAAQ,MAAIsB,mCAAAA,gBAAiBtB,QAAQ,KAAI,CAAC;IACzE,IAAIA,SAAS8B,QAAQ,EAAE;QACrB,MAAMC,WAAWzF,KACfwB,cACAL,aAAauE,OAAO,CAAC,WAAWvF;QAGlC6B,WAAWuD,MAAM,CAACE,UAAUnC,KAAKC,SAAS,CAACG,SAAS8B,QAAQ;QAE5D,IAAI/C,WAAW;YACbT,WAAWuD,MAAM,CACfE,SAASC,OAAO,CAAC,WAAW,cAC5BpC,KAAKC,SAAS,CAACG,SAAS8B,QAAQ;QAEpC;IACF;IAEA,IAAI,CAAC/B,aAAa;QAChB,qEAAqE;QACrEzB,WAAWuD,MAAM,CAACrE,cAAc2D;IAClC;IAEA,OAAO;QACLjB;QACA+B,cAAcjC,SAASiC,YAAY,IAAI;YACrCC,YAAY;YACZC,QAAQC;QACV;QACArC;IACF;AACF", "ignoreList": [0]}