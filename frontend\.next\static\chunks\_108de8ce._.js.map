{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/empty-projects/augment-small-project/frontend/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\n\nexport default function Home() {\n  const router = useRouter();\n  const { user, isLoading } = useAuth();\n\n  useEffect(() => {\n    if (!isLoading) {\n      if (user) {\n        router.push('/home');\n      } else {\n        router.push('/login');\n      }\n    }\n  }, [user, isLoading, router]);\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center\">\n      <div className=\"text-xl\">Loading...</div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,SAAS,IAAA,kJAAS;IACxB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,IAAA,6IAAO;IAEnC,IAAA,0KAAS;0BAAC;YACR,IAAI,CAAC,WAAW;gBACd,IAAI,MAAM;oBACR,OAAO,IAAI,CAAC;gBACd,OAAO;oBACL,OAAO,IAAI,CAAC;gBACd;YACF;QACF;yBAAG;QAAC;QAAM;QAAW;KAAO;IAE5B,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBAAU;;;;;;;;;;;AAG/B;GAnBwB;;QACP,kJAAS;QACI,6IAAO;;;KAFb", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/empty-projects/augment-small-project/frontend/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}