{"version": 3, "sources": ["../../../src/server/async-storage/work-store.ts"], "sourcesContent": ["import type { WorkStore } from '../app-render/work-async-storage.external'\nimport type { IncrementalCache } from '../lib/incremental-cache'\nimport type { RenderOpts } from '../app-render/types'\nimport type { FetchMetric } from '../base-http'\nimport type { RequestLifecycleOpts } from '../base-server'\nimport type { AppSegmentConfig } from '../../build/segment-config/app/app-segment-config'\nimport type { CacheLife } from '../use-cache/cache-life'\n\nimport { AfterContext } from '../after/after-context'\n\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport { createLazyResult, type LazyResult } from '../lib/lazy-result'\nimport { getCacheHandlerEntries } from '../use-cache/handlers'\nimport { createSnapshot } from '../app-render/async-local-storage'\n\nexport type WorkStoreContext = {\n  /**\n   * The page that is being rendered. This relates to the path to the page file.\n   */\n  page: string\n\n  isPrefetchRequest?: boolean\n  renderOpts: {\n    cacheLifeProfiles?: { [profile: string]: CacheLife }\n    incrementalCache?: IncrementalCache\n    isOnDemandRevalidate?: boolean\n    fetchCache?: AppSegmentConfig['fetchCache']\n    isPossibleServerAction?: boolean\n    pendingWaitUntil?: Promise<any>\n    experimental: Pick<\n      RenderOpts['experimental'],\n      'isRoutePPREnabled' | 'cacheComponents' | 'authInterrupts'\n    >\n\n    /**\n     * Fetch metrics attached in patch-fetch.ts\n     **/\n    fetchMetrics?: FetchMetric[]\n\n    /**\n     * A hack around accessing the store value outside the context of the\n     * request.\n     *\n     * @internal\n     * @deprecated should only be used as a temporary workaround\n     */\n    // TODO: remove this when we resolve accessing the store outside the execution context\n    store?: WorkStore\n  } & Pick<\n    // Pull some properties from RenderOpts so that the docs are also\n    // mirrored.\n    RenderOpts,\n    | 'assetPrefix'\n    | 'supportsDynamicResponse'\n    | 'shouldWaitOnAllReady'\n    | 'isRevalidate'\n    | 'nextExport'\n    | 'isDraftMode'\n    | 'isDebugDynamicAccesses'\n    | 'dev'\n    | 'hasReadableErrorStacks'\n  > &\n    RequestLifecycleOpts &\n    Partial<Pick<RenderOpts, 'reactLoadableManifest'>>\n\n  /**\n   * The build ID of the current build.\n   */\n  buildId: string\n\n  // Tags that were previously revalidated (e.g. by a redirecting server action)\n  // and have already been sent to cache handlers.\n  previouslyRevalidatedTags: string[]\n}\n\nexport function createWorkStore({\n  page,\n  renderOpts,\n  isPrefetchRequest,\n  buildId,\n  previouslyRevalidatedTags,\n}: WorkStoreContext): WorkStore {\n  /**\n   * Rules of Static & Dynamic HTML:\n   *\n   *    1.) We must generate static HTML unless the caller explicitly opts\n   *        in to dynamic HTML support.\n   *\n   *    2.) If dynamic HTML support is requested, we must honor that request\n   *        or throw an error. It is the sole responsibility of the caller to\n   *        ensure they aren't e.g. requesting dynamic HTML for an AMP page.\n   *\n   *    3.) If the request is in draft mode, we must generate dynamic HTML.\n   *\n   *    4.) If the request is a server action, we must generate dynamic HTML.\n   *\n   * These rules help ensure that other existing features like request caching,\n   * coalescing, and ISR continue working as intended.\n   */\n  const isStaticGeneration =\n    !renderOpts.shouldWaitOnAllReady &&\n    !renderOpts.supportsDynamicResponse &&\n    !renderOpts.isDraftMode &&\n    !renderOpts.isPossibleServerAction\n\n  const isDevelopment = renderOpts.dev ?? false\n\n  const shouldTrackFetchMetrics =\n    isDevelopment ||\n    // The only times we want to track fetch metrics outside of development is\n    // when we are performing a static generation and we either are in debug\n    // mode, or tracking fetch metrics was specifically opted into.\n    (isStaticGeneration &&\n      (!!process.env.NEXT_DEBUG_BUILD ||\n        process.env.NEXT_SSG_FETCH_METRICS === '1'))\n\n  const store: WorkStore = {\n    isStaticGeneration,\n    page,\n    route: normalizeAppPath(page),\n    incrementalCache:\n      // we fallback to a global incremental cache for edge-runtime locally\n      // so that it can access the fs cache without mocks\n      renderOpts.incrementalCache || (globalThis as any).__incrementalCache,\n    cacheLifeProfiles: renderOpts.cacheLifeProfiles,\n    isRevalidate: renderOpts.isRevalidate,\n    isBuildTimePrerendering: renderOpts.nextExport,\n    hasReadableErrorStacks: renderOpts.hasReadableErrorStacks,\n    fetchCache: renderOpts.fetchCache,\n    isOnDemandRevalidate: renderOpts.isOnDemandRevalidate,\n\n    isDraftMode: renderOpts.isDraftMode,\n\n    isPrefetchRequest,\n    buildId,\n    reactLoadableManifest: renderOpts?.reactLoadableManifest || {},\n    assetPrefix: renderOpts?.assetPrefix || '',\n\n    afterContext: createAfterContext(renderOpts),\n    cacheComponentsEnabled: renderOpts.experimental.cacheComponents,\n    dev: isDevelopment,\n    previouslyRevalidatedTags,\n    refreshTagsByCacheKind: createRefreshTagsByCacheKind(),\n    runInCleanSnapshot: createSnapshot(),\n    shouldTrackFetchMetrics,\n  }\n\n  // TODO: remove this when we resolve accessing the store outside the execution context\n  renderOpts.store = store\n\n  return store\n}\n\nfunction createAfterContext(renderOpts: RequestLifecycleOpts): AfterContext {\n  const { waitUntil, onClose, onAfterTaskError } = renderOpts\n  return new AfterContext({\n    waitUntil,\n    onClose,\n    onTaskError: onAfterTaskError,\n  })\n}\n\n/**\n * Creates a map with lazy results that refresh tags for the respective cache\n * kind when they're awaited for the first time.\n */\nfunction createRefreshTagsByCacheKind(): Map<string, LazyResult<void>> {\n  const refreshTagsByCacheKind = new Map<string, LazyResult<void>>()\n  const cacheHandlers = getCacheHandlerEntries()\n\n  if (cacheHandlers) {\n    for (const [kind, cacheHandler] of cacheHandlers) {\n      if ('refreshTags' in cacheHandler) {\n        refreshTagsByCacheKind.set(\n          kind,\n          createLazyResult(async () => cacheHandler.refreshTags())\n        )\n      }\n    }\n  }\n\n  return refreshTagsByCacheKind\n}\n"], "names": ["createWorkStore", "page", "renderOpts", "isPrefetchRequest", "buildId", "previouslyRevalidatedTags", "isStaticGeneration", "shouldWaitOnAllReady", "supportsDynamicResponse", "isDraftMode", "isPossibleServerAction", "isDevelopment", "dev", "shouldTrackFetchMetrics", "process", "env", "NEXT_DEBUG_BUILD", "NEXT_SSG_FETCH_METRICS", "store", "route", "normalizeAppPath", "incrementalCache", "globalThis", "__incrementalCache", "cacheLifeProfiles", "isRevalidate", "isBuildTimePrerendering", "nextExport", "hasReadableErrorStacks", "fetchCache", "isOnDemandRevalidate", "reactLoadableManifest", "assetPrefix", "afterContext", "createAfterContext", "cacheComponentsEnabled", "experimental", "cacheComponents", "refreshTagsByCacheKind", "createRefreshTagsByCacheKind", "runInCleanSnapshot", "createSnapshot", "waitUntil", "onClose", "onAfterTaskError", "AfterContext", "onTaskError", "Map", "cacheHandlers", "getCacheHandlerEntries", "kind", "cache<PERSON><PERSON><PERSON>", "set", "createLazyResult", "refreshTags"], "mappings": ";;;;+BA2EgBA;;;eAAAA;;;8BAnEa;0BAEI;4BACiB;0BACX;mCACR;AA8DxB,SAASA,gBAAgB,EAC9BC,IAAI,EACJC,UAAU,EACVC,iBAAiB,EACjBC,OAAO,EACPC,yBAAyB,EACR;IACjB;;;;;;;;;;;;;;;;GAgBC,GACD,MAAMC,qBACJ,CAACJ,WAAWK,oBAAoB,IAChC,CAACL,WAAWM,uBAAuB,IACnC,CAACN,WAAWO,WAAW,IACvB,CAACP,WAAWQ,sBAAsB;IAEpC,MAAMC,gBAAgBT,WAAWU,GAAG,IAAI;IAExC,MAAMC,0BACJF,iBACA,0EAA0E;IAC1E,wEAAwE;IACxE,+DAA+D;IAC9DL,sBACE,CAAA,CAAC,CAACQ,QAAQC,GAAG,CAACC,gBAAgB,IAC7BF,QAAQC,GAAG,CAACE,sBAAsB,KAAK,GAAE;IAE/C,MAAMC,QAAmB;QACvBZ;QACAL;QACAkB,OAAOC,IAAAA,0BAAgB,EAACnB;QACxBoB,kBACE,qEAAqE;QACrE,mDAAmD;QACnDnB,WAAWmB,gBAAgB,IAAI,AAACC,WAAmBC,kBAAkB;QACvEC,mBAAmBtB,WAAWsB,iBAAiB;QAC/CC,cAAcvB,WAAWuB,YAAY;QACrCC,yBAAyBxB,WAAWyB,UAAU;QAC9CC,wBAAwB1B,WAAW0B,sBAAsB;QACzDC,YAAY3B,WAAW2B,UAAU;QACjCC,sBAAsB5B,WAAW4B,oBAAoB;QAErDrB,aAAaP,WAAWO,WAAW;QAEnCN;QACAC;QACA2B,uBAAuB7B,CAAAA,8BAAAA,WAAY6B,qBAAqB,KAAI,CAAC;QAC7DC,aAAa9B,CAAAA,8BAAAA,WAAY8B,WAAW,KAAI;QAExCC,cAAcC,mBAAmBhC;QACjCiC,wBAAwBjC,WAAWkC,YAAY,CAACC,eAAe;QAC/DzB,KAAKD;QACLN;QACAiC,wBAAwBC;QACxBC,oBAAoBC,IAAAA,iCAAc;QAClC5B;IACF;IAEA,sFAAsF;IACtFX,WAAWgB,KAAK,GAAGA;IAEnB,OAAOA;AACT;AAEA,SAASgB,mBAAmBhC,UAAgC;IAC1D,MAAM,EAAEwC,SAAS,EAAEC,OAAO,EAAEC,gBAAgB,EAAE,GAAG1C;IACjD,OAAO,IAAI2C,0BAAY,CAAC;QACtBH;QACAC;QACAG,aAAaF;IACf;AACF;AAEA;;;CAGC,GACD,SAASL;IACP,MAAMD,yBAAyB,IAAIS;IACnC,MAAMC,gBAAgBC,IAAAA,gCAAsB;IAE5C,IAAID,eAAe;QACjB,KAAK,MAAM,CAACE,MAAMC,aAAa,IAAIH,cAAe;YAChD,IAAI,iBAAiBG,cAAc;gBACjCb,uBAAuBc,GAAG,CACxBF,MACAG,IAAAA,4BAAgB,EAAC,UAAYF,aAAaG,WAAW;YAEzD;QACF;IACF;IAEA,OAAOhB;AACT", "ignoreList": [0]}