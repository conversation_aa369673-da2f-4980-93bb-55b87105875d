{"version": 3, "sources": ["../../../src/next-devtools/shared/devtools-config-schema.ts"], "sourcesContent": ["import type { DevToolsConfig } from '../dev-overlay/shared'\nimport { z } from 'next/dist/compiled/zod'\n\nexport const devToolsConfigSchema: z.ZodType<DevToolsConfig> = z.object({\n  theme: z.enum(['light', 'dark', 'system']).optional(),\n  disableDevIndicator: z.boolean().optional(),\n  devToolsPosition: z\n    .enum(['top-left', 'top-right', 'bottom-left', 'bottom-right'])\n    .optional(),\n  devToolsPanelPosition: z\n    .record(\n      z.string(),\n      z.enum(['top-left', 'top-right', 'bottom-left', 'bottom-right'])\n    )\n    .optional(),\n  devToolsPanelSize: z\n    .record(z.string(), z.object({ width: z.number(), height: z.number() }))\n    .optional(),\n  scale: z.number().optional(),\n  hideShortcut: z.string().nullable().optional(),\n})\n"], "names": ["z", "devToolsConfigSchema", "object", "theme", "enum", "optional", "disableDevIndicator", "boolean", "devToolsPosition", "devToolsPanelPosition", "record", "string", "devToolsPanelSize", "width", "number", "height", "scale", "hideShortcut", "nullable"], "mappings": "AACA,SAASA,CAAC,QAAQ,yBAAwB;AAE1C,OAAO,MAAMC,uBAAkDD,EAAEE,MAAM,CAAC;IACtEC,OAAOH,EAAEI,IAAI,CAAC;QAAC;QAAS;QAAQ;KAAS,EAAEC,QAAQ;IACnDC,qBAAqBN,EAAEO,OAAO,GAAGF,QAAQ;IACzCG,kBAAkBR,EACfI,IAAI,CAAC;QAAC;QAAY;QAAa;QAAe;KAAe,EAC7DC,QAAQ;IACXI,uBAAuBT,EACpBU,MAAM,CACLV,EAAEW,MAAM,IACRX,EAAEI,IAAI,CAAC;QAAC;QAAY;QAAa;QAAe;KAAe,GAEhEC,QAAQ;IACXO,mBAAmBZ,EAChBU,MAAM,CAACV,EAAEW,MAAM,IAAIX,EAAEE,MAAM,CAAC;QAAEW,OAAOb,EAAEc,MAAM;QAAIC,QAAQf,EAAEc,MAAM;IAAG,IACpET,QAAQ;IACXW,OAAOhB,EAAEc,MAAM,GAAGT,QAAQ;IAC1BY,cAAcjB,EAAEW,MAAM,GAAGO,QAAQ,GAAGb,QAAQ;AAC9C,GAAE", "ignoreList": [0]}