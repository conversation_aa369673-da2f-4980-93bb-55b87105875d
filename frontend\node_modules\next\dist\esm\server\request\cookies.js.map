{"version": 3, "sources": ["../../../src/server/request/cookies.ts"], "sourcesContent": ["import {\n  type ReadonlyRequestCookies,\n  type ResponseCookies,\n  areCookiesMutableInCurrentPhase,\n  RequestCookiesAdapter,\n} from '../web/spec-extension/adapters/request-cookies'\nimport { RequestCookies } from '../web/spec-extension/cookies'\nimport {\n  workAsyncStorage,\n  type WorkStore,\n} from '../app-render/work-async-storage.external'\nimport {\n  throwForMissingRequestStore,\n  workUnitAsyncStorage,\n  type PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\nimport {\n  delayUntilRuntimeStage,\n  postponeWithTracking,\n  throwToInterruptStaticGeneration,\n  trackDynamicDataInDynamicRender,\n  trackSynchronousRequestDataAccessInDev,\n} from '../app-render/dynamic-rendering'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport {\n  makeDevtoolsIOAwarePromise,\n  makeHangingPromise,\n} from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport { isRequestAPICallableInsideAfter } from './utils'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { ReflectAdapter } from '../web/spec-extension/adapters/reflect'\n\n/**\n * In this version of Next.js `cookies()` returns a Promise however you can still reference the properties of the underlying cookies object\n * synchronously to facilitate migration. The `UnsafeUnwrappedCookies` type is added to your code by a codemod that attempts to automatically\n * updates callsites to reflect the new Promise return type. There are some cases where `cookies()` cannot be automatically converted, namely\n * when it is used inside a synchronous function and we can't be sure the function can be made async automatically. In these cases we add an\n * explicit type case to `UnsafeUnwrappedCookies` to enable typescript to allow for the synchronous usage only where it is actually necessary.\n *\n * You should should update these callsites to either be async functions where the `cookies()` value can be awaited or you should call `cookies()`\n * from outside and await the return value before passing it into this function.\n *\n * You can find instances that require manual migration by searching for `UnsafeUnwrappedCookies` in your codebase or by search for a comment that\n * starts with `@next-codemod-error`.\n *\n * In a future version of Next.js `cookies()` will only return a Promise and you will not be able to access the underlying cookies object directly\n * without awaiting the return value first. When this change happens the type `UnsafeUnwrappedCookies` will be updated to reflect that is it no longer\n * usable.\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedCookies = ReadonlyRequestCookies\n\nexport function cookies(): Promise<ReadonlyRequestCookies> {\n  const callingExpression = 'cookies'\n  const workStore = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (workStore) {\n    if (\n      workUnitStore &&\n      workUnitStore.phase === 'after' &&\n      !isRequestAPICallableInsideAfter()\n    ) {\n      throw new Error(\n        // TODO(after): clarify that this only applies to pages?\n        `Route ${workStore.route} used \"cookies\" inside \"after(...)\". This is not supported. If you need this data inside an \"after\" callback, use \"cookies\" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`\n      )\n    }\n\n    if (workStore.forceStatic) {\n      // When using forceStatic we override all other logic and always just return an empty\n      // cookies object without tracking\n      const underlyingCookies = createEmptyCookies()\n      return makeUntrackedExoticCookies(underlyingCookies)\n    }\n\n    if (workStore.dynamicShouldError) {\n      throw new StaticGenBailoutError(\n        `Route ${workStore.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`cookies\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n      )\n    }\n\n    if (workUnitStore) {\n      switch (workUnitStore.type) {\n        case 'cache':\n          const error = new Error(\n            `Route ${workStore.route} used \"cookies\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"cookies\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n          )\n          Error.captureStackTrace(error, cookies)\n          workStore.invalidDynamicUsageError ??= error\n          throw error\n        case 'unstable-cache':\n          throw new Error(\n            `Route ${workStore.route} used \"cookies\" inside a function cached with \"unstable_cache(...)\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"cookies\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`\n          )\n        case 'prerender':\n          return makeHangingCookies(workStore, workUnitStore)\n        case 'prerender-client':\n          const exportName = '`cookies`'\n          throw new InvariantError(\n            `${exportName} must not be used within a client component. Next.js should be preventing ${exportName} from being included in client components statically, but did not in this case.`\n          )\n        case 'prerender-ppr':\n          // We need track dynamic access here eagerly to keep continuity with\n          // how cookies has worked in PPR without cacheComponents.\n          return postponeWithTracking(\n            workStore.route,\n            callingExpression,\n            workUnitStore.dynamicTracking\n          )\n        case 'prerender-legacy':\n          // We track dynamic access here so we don't need to wrap the cookies\n          // in individual property access tracking.\n          return throwToInterruptStaticGeneration(\n            callingExpression,\n            workStore,\n            workUnitStore\n          )\n        case 'prerender-runtime':\n          return delayUntilRuntimeStage(\n            workUnitStore,\n            makeUntrackedCookies(workUnitStore.cookies)\n          )\n        case 'private-cache':\n          if (process.env.__NEXT_CACHE_COMPONENTS) {\n            return makeUntrackedCookies(workUnitStore.cookies)\n          }\n\n          return makeUntrackedExoticCookies(workUnitStore.cookies)\n        case 'request':\n          trackDynamicDataInDynamicRender(workUnitStore)\n\n          let underlyingCookies: ReadonlyRequestCookies\n\n          if (areCookiesMutableInCurrentPhase(workUnitStore)) {\n            // We can't conditionally return different types here based on the context.\n            // To avoid confusion, we always return the readonly type here.\n            underlyingCookies =\n              workUnitStore.userspaceMutableCookies as unknown as ReadonlyRequestCookies\n          } else {\n            underlyingCookies = workUnitStore.cookies\n          }\n\n          if (process.env.NODE_ENV === 'development') {\n            // Semantically we only need the dev tracking when running in `next dev`\n            // but since you would never use next dev with production NODE_ENV we use this\n            // as a proxy so we can statically exclude this code from production builds.\n            if (process.env.__NEXT_CACHE_COMPONENTS) {\n              return makeUntrackedCookiesWithDevWarnings(\n                underlyingCookies,\n                workStore?.route\n              )\n            }\n\n            return makeUntrackedExoticCookiesWithDevWarnings(\n              underlyingCookies,\n              workStore?.route\n            )\n          } else {\n            if (process.env.__NEXT_CACHE_COMPONENTS) {\n              return makeUntrackedCookies(underlyingCookies)\n            }\n\n            return makeUntrackedExoticCookies(underlyingCookies)\n          }\n        default:\n          workUnitStore satisfies never\n      }\n    }\n  }\n\n  // If we end up here, there was no work store or work unit store present.\n  throwForMissingRequestStore(callingExpression)\n}\n\nfunction createEmptyCookies(): ReadonlyRequestCookies {\n  return RequestCookiesAdapter.seal(new RequestCookies(new Headers({})))\n}\n\ninterface CacheLifetime {}\nconst CachedCookies = new WeakMap<\n  CacheLifetime,\n  Promise<ReadonlyRequestCookies>\n>()\n\nfunction makeHangingCookies(\n  workStore: WorkStore,\n  prerenderStore: PrerenderStoreModern\n): Promise<ReadonlyRequestCookies> {\n  const cachedPromise = CachedCookies.get(prerenderStore)\n  if (cachedPromise) {\n    return cachedPromise\n  }\n\n  const promise = makeHangingPromise<ReadonlyRequestCookies>(\n    prerenderStore.renderSignal,\n    workStore.route,\n    '`cookies()`'\n  )\n  CachedCookies.set(prerenderStore, promise)\n\n  return promise\n}\n\nfunction makeUntrackedCookies(\n  underlyingCookies: ReadonlyRequestCookies\n): Promise<ReadonlyRequestCookies> {\n  const cachedCookies = CachedCookies.get(underlyingCookies)\n  if (cachedCookies) {\n    return cachedCookies\n  }\n\n  const promise = Promise.resolve(underlyingCookies)\n  CachedCookies.set(underlyingCookies, promise)\n\n  return promise\n}\n\nfunction makeUntrackedExoticCookies(\n  underlyingCookies: ReadonlyRequestCookies\n): Promise<ReadonlyRequestCookies> {\n  const cachedCookies = CachedCookies.get(underlyingCookies)\n  if (cachedCookies) {\n    return cachedCookies\n  }\n\n  const promise = Promise.resolve(underlyingCookies)\n  CachedCookies.set(underlyingCookies, promise)\n\n  Object.defineProperties(promise, {\n    [Symbol.iterator]: {\n      value: underlyingCookies[Symbol.iterator]\n        ? underlyingCookies[Symbol.iterator].bind(underlyingCookies)\n        : // TODO this is a polyfill for when the underlying type is ResponseCookies\n          // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n          // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n          // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n          // has extra properties not available on RequestCookie instances.\n          polyfilledResponseCookiesIterator.bind(underlyingCookies),\n    },\n    size: {\n      get(): number {\n        return underlyingCookies.size\n      },\n    },\n    get: {\n      value: underlyingCookies.get.bind(underlyingCookies),\n    },\n    getAll: {\n      value: underlyingCookies.getAll.bind(underlyingCookies),\n    },\n    has: {\n      value: underlyingCookies.has.bind(underlyingCookies),\n    },\n    set: {\n      value: underlyingCookies.set.bind(underlyingCookies),\n    },\n    delete: {\n      value: underlyingCookies.delete.bind(underlyingCookies),\n    },\n    clear: {\n      value:\n        // @ts-expect-error clear is defined in RequestCookies implementation but not in the type\n        typeof underlyingCookies.clear === 'function'\n          ? // @ts-expect-error clear is defined in RequestCookies implementation but not in the type\n            underlyingCookies.clear.bind(underlyingCookies)\n          : // TODO this is a polyfill for when the underlying type is ResponseCookies\n            // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n            // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n            // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n            // has extra properties not available on RequestCookie instances.\n            polyfilledResponseCookiesClear.bind(underlyingCookies, promise),\n    },\n    toString: {\n      value: underlyingCookies.toString.bind(underlyingCookies),\n    },\n  } satisfies CookieExtensions)\n\n  return promise\n}\n\nfunction makeUntrackedExoticCookiesWithDevWarnings(\n  underlyingCookies: ReadonlyRequestCookies,\n  route?: string\n): Promise<ReadonlyRequestCookies> {\n  const cachedCookies = CachedCookies.get(underlyingCookies)\n  if (cachedCookies) {\n    return cachedCookies\n  }\n\n  const promise = makeDevtoolsIOAwarePromise(underlyingCookies)\n  CachedCookies.set(underlyingCookies, promise)\n\n  Object.defineProperties(promise, {\n    [Symbol.iterator]: {\n      value: function () {\n        const expression = '`...cookies()` or similar iteration'\n        syncIODev(route, expression)\n        return underlyingCookies[Symbol.iterator]\n          ? underlyingCookies[Symbol.iterator].apply(\n              underlyingCookies,\n              arguments as any\n            )\n          : // TODO this is a polyfill for when the underlying type is ResponseCookies\n            // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n            // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n            // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n            // has extra properties not available on RequestCookie instances.\n            polyfilledResponseCookiesIterator.call(underlyingCookies)\n      },\n      writable: false,\n    },\n    size: {\n      get(): number {\n        const expression = '`cookies().size`'\n        syncIODev(route, expression)\n        return underlyingCookies.size\n      },\n    },\n    get: {\n      value: function get() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().get()`'\n        } else {\n          expression = `\\`cookies().get(${describeNameArg(arguments[0])})\\``\n        }\n        syncIODev(route, expression)\n        return underlyingCookies.get.apply(underlyingCookies, arguments as any)\n      },\n      writable: false,\n    },\n    getAll: {\n      value: function getAll() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().getAll()`'\n        } else {\n          expression = `\\`cookies().getAll(${describeNameArg(arguments[0])})\\``\n        }\n        syncIODev(route, expression)\n        return underlyingCookies.getAll.apply(\n          underlyingCookies,\n          arguments as any\n        )\n      },\n      writable: false,\n    },\n    has: {\n      value: function get() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().has()`'\n        } else {\n          expression = `\\`cookies().has(${describeNameArg(arguments[0])})\\``\n        }\n        syncIODev(route, expression)\n        return underlyingCookies.has.apply(underlyingCookies, arguments as any)\n      },\n      writable: false,\n    },\n    set: {\n      value: function set() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().set()`'\n        } else {\n          const arg = arguments[0]\n          if (arg) {\n            expression = `\\`cookies().set(${describeNameArg(arg)}, ...)\\``\n          } else {\n            expression = '`cookies().set(...)`'\n          }\n        }\n        syncIODev(route, expression)\n        return underlyingCookies.set.apply(underlyingCookies, arguments as any)\n      },\n      writable: false,\n    },\n    delete: {\n      value: function () {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().delete()`'\n        } else if (arguments.length === 1) {\n          expression = `\\`cookies().delete(${describeNameArg(arguments[0])})\\``\n        } else {\n          expression = `\\`cookies().delete(${describeNameArg(arguments[0])}, ...)\\``\n        }\n        syncIODev(route, expression)\n        return underlyingCookies.delete.apply(\n          underlyingCookies,\n          arguments as any\n        )\n      },\n      writable: false,\n    },\n    clear: {\n      value: function clear() {\n        const expression = '`cookies().clear()`'\n        syncIODev(route, expression)\n        // @ts-ignore clear is defined in RequestCookies implementation but not in the type\n        return typeof underlyingCookies.clear === 'function'\n          ? // @ts-ignore clear is defined in RequestCookies implementation but not in the type\n            underlyingCookies.clear.apply(underlyingCookies, arguments)\n          : // TODO this is a polyfill for when the underlying type is ResponseCookies\n            // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n            // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n            // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n            // has extra properties not available on RequestCookie instances.\n            polyfilledResponseCookiesClear.call(underlyingCookies, promise)\n      },\n      writable: false,\n    },\n    toString: {\n      value: function toString() {\n        const expression = '`cookies().toString()` or implicit casting'\n        syncIODev(route, expression)\n        return underlyingCookies.toString.apply(\n          underlyingCookies,\n          arguments as any\n        )\n      },\n      writable: false,\n    },\n  } satisfies CookieExtensions)\n\n  return promise\n}\n\n// Similar to `makeUntrackedExoticCookiesWithDevWarnings`, but just logging the\n// sync access without actually defining the cookies properties on the promise.\nfunction makeUntrackedCookiesWithDevWarnings(\n  underlyingCookies: ReadonlyRequestCookies,\n  route?: string\n): Promise<ReadonlyRequestCookies> {\n  const cachedCookies = CachedCookies.get(underlyingCookies)\n  if (cachedCookies) {\n    return cachedCookies\n  }\n\n  const promise = makeDevtoolsIOAwarePromise(underlyingCookies)\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      switch (prop) {\n        case Symbol.iterator: {\n          warnForSyncAccess(route, '`...cookies()` or similar iteration')\n          break\n        }\n        case 'size':\n        case 'get':\n        case 'getAll':\n        case 'has':\n        case 'set':\n        case 'delete':\n        case 'clear':\n        case 'toString': {\n          warnForSyncAccess(route, `\\`cookies().${prop}\\``)\n          break\n        }\n        default: {\n          // We only warn for well-defined properties of the cookies object.\n        }\n      }\n\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n  })\n\n  CachedCookies.set(underlyingCookies, proxiedPromise)\n\n  return proxiedPromise\n}\n\nfunction describeNameArg(arg: unknown) {\n  return typeof arg === 'object' &&\n    arg !== null &&\n    typeof (arg as any).name === 'string'\n    ? `'${(arg as any).name}'`\n    : typeof arg === 'string'\n      ? `'${arg}'`\n      : '...'\n}\n\nfunction syncIODev(route: string | undefined, expression: string) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'request':\n        if (workUnitStore.prerenderPhase === true) {\n          // When we're rendering dynamically in dev, we need to advance out of\n          // the Prerender environment when we read Request data synchronously.\n          trackSynchronousRequestDataAccessInDev(workUnitStore)\n        }\n        break\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-runtime':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n\n  // In all cases we warn normally\n  warnForSyncAccess(route, expression)\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createCookiesAccessError\n)\n\nfunction createCookiesAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`cookies()\\` should be awaited before using its value. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction polyfilledResponseCookiesIterator(\n  this: ResponseCookies\n): ReturnType<ReadonlyRequestCookies[typeof Symbol.iterator]> {\n  return this.getAll()\n    .map((c) => [c.name, c] as [string, any])\n    .values()\n}\n\nfunction polyfilledResponseCookiesClear(\n  this: ResponseCookies,\n  returnable: Promise<ReadonlyRequestCookies>\n): typeof returnable {\n  for (const cookie of this.getAll()) {\n    this.delete(cookie.name)\n  }\n  return returnable\n}\n\ntype CookieExtensions = {\n  [K in keyof ReadonlyRequestCookies | 'clear']: unknown\n}\n"], "names": ["areCookiesMutableInCurrentPhase", "RequestCookiesAdapter", "RequestCookies", "workAsyncStorage", "throwForMissingRequestStore", "workUnitAsyncStorage", "delayUntilRuntimeStage", "postponeWithTracking", "throwToInterruptStaticGeneration", "trackDynamicDataInDynamicRender", "trackSynchronousRequestDataAccessInDev", "StaticGenBailoutError", "makeDevtoolsIOAwarePromise", "makeHangingPromise", "createDedupedByCallsiteServerErrorLoggerDev", "isRequestAPICallableInsideAfter", "InvariantError", "ReflectAdapter", "cookies", "callingExpression", "workStore", "getStore", "workUnitStore", "phase", "Error", "route", "forceStatic", "underlyingCookies", "createEmptyCookies", "makeUntrackedExoticCookies", "dynamicShouldError", "type", "error", "captureStackTrace", "invalidDynamicUsageError", "makeHangingCookies", "exportName", "dynamicTracking", "makeUntrackedCookies", "process", "env", "__NEXT_CACHE_COMPONENTS", "userspaceMutableCookies", "NODE_ENV", "makeUntrackedCookiesWithDevWarnings", "makeUntrackedExoticCookiesWithDevWarnings", "seal", "Headers", "CachedCookies", "WeakMap", "prerenderStore", "cachedPromise", "get", "promise", "renderSignal", "set", "cachedCookies", "Promise", "resolve", "Object", "defineProperties", "Symbol", "iterator", "value", "bind", "polyfilledResponseCookiesIterator", "size", "getAll", "has", "delete", "clear", "polyfilledResponseCookiesClear", "toString", "expression", "syncIODev", "apply", "arguments", "call", "writable", "length", "describeNameArg", "arg", "proxiedPromise", "Proxy", "target", "prop", "receiver", "warnForSyncAccess", "name", "prerenderPhase", "createCookiesAccessError", "prefix", "map", "c", "values", "returnable", "cookie"], "mappings": "AAAA,SAGEA,+BAA+B,EAC/BC,qBAAqB,QAChB,iDAAgD;AACvD,SAASC,cAAc,QAAQ,gCAA+B;AAC9D,SACEC,gBAAgB,QAEX,4CAA2C;AAClD,SACEC,2BAA2B,EAC3BC,oBAAoB,QAEf,iDAAgD;AACvD,SACEC,sBAAsB,EACtBC,oBAAoB,EACpBC,gCAAgC,EAChCC,+BAA+B,EAC/BC,sCAAsC,QACjC,kCAAiC;AACxC,SAASC,qBAAqB,QAAQ,oDAAmD;AACzF,SACEC,0BAA0B,EAC1BC,kBAAkB,QACb,6BAA4B;AACnC,SAASC,2CAA2C,QAAQ,oDAAmD;AAC/G,SAASC,+BAA+B,QAAQ,UAAS;AACzD,SAASC,cAAc,QAAQ,mCAAkC;AACjE,SAASC,cAAc,QAAQ,yCAAwC;AAyBvE,OAAO,SAASC;IACd,MAAMC,oBAAoB;IAC1B,MAAMC,YAAYjB,iBAAiBkB,QAAQ;IAC3C,MAAMC,gBAAgBjB,qBAAqBgB,QAAQ;IAEnD,IAAID,WAAW;QACb,IACEE,iBACAA,cAAcC,KAAK,KAAK,WACxB,CAACR,mCACD;YACA,MAAM,qBAGL,CAHK,IAAIS,MACR,wDAAwD;YACxD,CAAC,MAAM,EAAEJ,UAAUK,KAAK,CAAC,yOAAyO,CAAC,GAF/P,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QAEA,IAAIL,UAAUM,WAAW,EAAE;YACzB,qFAAqF;YACrF,kCAAkC;YAClC,MAAMC,oBAAoBC;YAC1B,OAAOC,2BAA2BF;QACpC;QAEA,IAAIP,UAAUU,kBAAkB,EAAE;YAChC,MAAM,qBAEL,CAFK,IAAInB,sBACR,CAAC,MAAM,EAAES,UAAUK,KAAK,CAAC,iNAAiN,CAAC,GADvO,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIH,eAAe;YACjB,OAAQA,cAAcS,IAAI;gBACxB,KAAK;oBACH,MAAMC,QAAQ,qBAEb,CAFa,IAAIR,MAChB,CAAC,MAAM,EAAEJ,UAAUK,KAAK,CAAC,0UAA0U,CAAC,GADxV,qBAAA;+BAAA;oCAAA;sCAAA;oBAEd;oBACAD,MAAMS,iBAAiB,CAACD,OAAOd;oBAC/BE,UAAUc,wBAAwB,KAAKF;oBACvC,MAAMA;gBACR,KAAK;oBACH,MAAM,qBAEL,CAFK,IAAIR,MACR,CAAC,MAAM,EAAEJ,UAAUK,KAAK,CAAC,mXAAmX,CAAC,GADzY,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF,KAAK;oBACH,OAAOU,mBAAmBf,WAAWE;gBACvC,KAAK;oBACH,MAAMc,aAAa;oBACnB,MAAM,qBAEL,CAFK,IAAIpB,eACR,GAAGoB,WAAW,0EAA0E,EAAEA,WAAW,+EAA+E,CAAC,GADjL,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF,KAAK;oBACH,oEAAoE;oBACpE,yDAAyD;oBACzD,OAAO7B,qBACLa,UAAUK,KAAK,EACfN,mBACAG,cAAce,eAAe;gBAEjC,KAAK;oBACH,oEAAoE;oBACpE,0CAA0C;oBAC1C,OAAO7B,iCACLW,mBACAC,WACAE;gBAEJ,KAAK;oBACH,OAAOhB,uBACLgB,eACAgB,qBAAqBhB,cAAcJ,OAAO;gBAE9C,KAAK;oBACH,IAAIqB,QAAQC,GAAG,CAACC,uBAAuB,EAAE;wBACvC,OAAOH,qBAAqBhB,cAAcJ,OAAO;oBACnD;oBAEA,OAAOW,2BAA2BP,cAAcJ,OAAO;gBACzD,KAAK;oBACHT,gCAAgCa;oBAEhC,IAAIK;oBAEJ,IAAI3B,gCAAgCsB,gBAAgB;wBAClD,2EAA2E;wBAC3E,+DAA+D;wBAC/DK,oBACEL,cAAcoB,uBAAuB;oBACzC,OAAO;wBACLf,oBAAoBL,cAAcJ,OAAO;oBAC3C;oBAEA,IAAIqB,QAAQC,GAAG,CAACG,QAAQ,KAAK,eAAe;wBAC1C,wEAAwE;wBACxE,8EAA8E;wBAC9E,4EAA4E;wBAC5E,IAAIJ,QAAQC,GAAG,CAACC,uBAAuB,EAAE;4BACvC,OAAOG,oCACLjB,mBACAP,6BAAAA,UAAWK,KAAK;wBAEpB;wBAEA,OAAOoB,0CACLlB,mBACAP,6BAAAA,UAAWK,KAAK;oBAEpB,OAAO;wBACL,IAAIc,QAAQC,GAAG,CAACC,uBAAuB,EAAE;4BACvC,OAAOH,qBAAqBX;wBAC9B;wBAEA,OAAOE,2BAA2BF;oBACpC;gBACF;oBACEL;YACJ;QACF;IACF;IAEA,yEAAyE;IACzElB,4BAA4Be;AAC9B;AAEA,SAASS;IACP,OAAO3B,sBAAsB6C,IAAI,CAAC,IAAI5C,eAAe,IAAI6C,QAAQ,CAAC;AACpE;AAGA,MAAMC,gBAAgB,IAAIC;AAK1B,SAASd,mBACPf,SAAoB,EACpB8B,cAAoC;IAEpC,MAAMC,gBAAgBH,cAAcI,GAAG,CAACF;IACxC,IAAIC,eAAe;QACjB,OAAOA;IACT;IAEA,MAAME,UAAUxC,mBACdqC,eAAeI,YAAY,EAC3BlC,UAAUK,KAAK,EACf;IAEFuB,cAAcO,GAAG,CAACL,gBAAgBG;IAElC,OAAOA;AACT;AAEA,SAASf,qBACPX,iBAAyC;IAEzC,MAAM6B,gBAAgBR,cAAcI,GAAG,CAACzB;IACxC,IAAI6B,eAAe;QACjB,OAAOA;IACT;IAEA,MAAMH,UAAUI,QAAQC,OAAO,CAAC/B;IAChCqB,cAAcO,GAAG,CAAC5B,mBAAmB0B;IAErC,OAAOA;AACT;AAEA,SAASxB,2BACPF,iBAAyC;IAEzC,MAAM6B,gBAAgBR,cAAcI,GAAG,CAACzB;IACxC,IAAI6B,eAAe;QACjB,OAAOA;IACT;IAEA,MAAMH,UAAUI,QAAQC,OAAO,CAAC/B;IAChCqB,cAAcO,GAAG,CAAC5B,mBAAmB0B;IAErCM,OAAOC,gBAAgB,CAACP,SAAS;QAC/B,CAACQ,OAAOC,QAAQ,CAAC,EAAE;YACjBC,OAAOpC,iBAAiB,CAACkC,OAAOC,QAAQ,CAAC,GACrCnC,iBAAiB,CAACkC,OAAOC,QAAQ,CAAC,CAACE,IAAI,CAACrC,qBAExC,qGAAqG;YACrG,iHAAiH;YACjH,oHAAoH;YACpH,iEAAiE;YACjEsC,kCAAkCD,IAAI,CAACrC;QAC7C;QACAuC,MAAM;YACJd;gBACE,OAAOzB,kBAAkBuC,IAAI;YAC/B;QACF;QACAd,KAAK;YACHW,OAAOpC,kBAAkByB,GAAG,CAACY,IAAI,CAACrC;QACpC;QACAwC,QAAQ;YACNJ,OAAOpC,kBAAkBwC,MAAM,CAACH,IAAI,CAACrC;QACvC;QACAyC,KAAK;YACHL,OAAOpC,kBAAkByC,GAAG,CAACJ,IAAI,CAACrC;QACpC;QACA4B,KAAK;YACHQ,OAAOpC,kBAAkB4B,GAAG,CAACS,IAAI,CAACrC;QACpC;QACA0C,QAAQ;YACNN,OAAOpC,kBAAkB0C,MAAM,CAACL,IAAI,CAACrC;QACvC;QACA2C,OAAO;YACLP,OACE,yFAAyF;YACzF,OAAOpC,kBAAkB2C,KAAK,KAAK,aAE/B3C,kBAAkB2C,KAAK,CAACN,IAAI,CAACrC,qBAE7B,qGAAqG;YACrG,iHAAiH;YACjH,oHAAoH;YACpH,iEAAiE;YACjE4C,+BAA+BP,IAAI,CAACrC,mBAAmB0B;QAC/D;QACAmB,UAAU;YACRT,OAAOpC,kBAAkB6C,QAAQ,CAACR,IAAI,CAACrC;QACzC;IACF;IAEA,OAAO0B;AACT;AAEA,SAASR,0CACPlB,iBAAyC,EACzCF,KAAc;IAEd,MAAM+B,gBAAgBR,cAAcI,GAAG,CAACzB;IACxC,IAAI6B,eAAe;QACjB,OAAOA;IACT;IAEA,MAAMH,UAAUzC,2BAA2Be;IAC3CqB,cAAcO,GAAG,CAAC5B,mBAAmB0B;IAErCM,OAAOC,gBAAgB,CAACP,SAAS;QAC/B,CAACQ,OAAOC,QAAQ,CAAC,EAAE;YACjBC,OAAO;gBACL,MAAMU,aAAa;gBACnBC,UAAUjD,OAAOgD;gBACjB,OAAO9C,iBAAiB,CAACkC,OAAOC,QAAQ,CAAC,GACrCnC,iBAAiB,CAACkC,OAAOC,QAAQ,CAAC,CAACa,KAAK,CACtChD,mBACAiD,aAGF,qGAAqG;gBACrG,iHAAiH;gBACjH,oHAAoH;gBACpH,iEAAiE;gBACjEX,kCAAkCY,IAAI,CAAClD;YAC7C;YACAmD,UAAU;QACZ;QACAZ,MAAM;YACJd;gBACE,MAAMqB,aAAa;gBACnBC,UAAUjD,OAAOgD;gBACjB,OAAO9C,kBAAkBuC,IAAI;YAC/B;QACF;QACAd,KAAK;YACHW,OAAO,SAASX;gBACd,IAAIqB;gBACJ,IAAIG,UAAUG,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO;oBACLA,aAAa,CAAC,gBAAgB,EAAEO,gBAAgBJ,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACpE;gBACAF,UAAUjD,OAAOgD;gBACjB,OAAO9C,kBAAkByB,GAAG,CAACuB,KAAK,CAAChD,mBAAmBiD;YACxD;YACAE,UAAU;QACZ;QACAX,QAAQ;YACNJ,OAAO,SAASI;gBACd,IAAIM;gBACJ,IAAIG,UAAUG,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO;oBACLA,aAAa,CAAC,mBAAmB,EAAEO,gBAAgBJ,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACvE;gBACAF,UAAUjD,OAAOgD;gBACjB,OAAO9C,kBAAkBwC,MAAM,CAACQ,KAAK,CACnChD,mBACAiD;YAEJ;YACAE,UAAU;QACZ;QACAV,KAAK;YACHL,OAAO,SAASX;gBACd,IAAIqB;gBACJ,IAAIG,UAAUG,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO;oBACLA,aAAa,CAAC,gBAAgB,EAAEO,gBAAgBJ,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACpE;gBACAF,UAAUjD,OAAOgD;gBACjB,OAAO9C,kBAAkByC,GAAG,CAACO,KAAK,CAAChD,mBAAmBiD;YACxD;YACAE,UAAU;QACZ;QACAvB,KAAK;YACHQ,OAAO,SAASR;gBACd,IAAIkB;gBACJ,IAAIG,UAAUG,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO;oBACL,MAAMQ,MAAML,SAAS,CAAC,EAAE;oBACxB,IAAIK,KAAK;wBACPR,aAAa,CAAC,gBAAgB,EAAEO,gBAAgBC,KAAK,QAAQ,CAAC;oBAChE,OAAO;wBACLR,aAAa;oBACf;gBACF;gBACAC,UAAUjD,OAAOgD;gBACjB,OAAO9C,kBAAkB4B,GAAG,CAACoB,KAAK,CAAChD,mBAAmBiD;YACxD;YACAE,UAAU;QACZ;QACAT,QAAQ;YACNN,OAAO;gBACL,IAAIU;gBACJ,IAAIG,UAAUG,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO,IAAIG,UAAUG,MAAM,KAAK,GAAG;oBACjCN,aAAa,CAAC,mBAAmB,EAAEO,gBAAgBJ,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACvE,OAAO;oBACLH,aAAa,CAAC,mBAAmB,EAAEO,gBAAgBJ,SAAS,CAAC,EAAE,EAAE,QAAQ,CAAC;gBAC5E;gBACAF,UAAUjD,OAAOgD;gBACjB,OAAO9C,kBAAkB0C,MAAM,CAACM,KAAK,CACnChD,mBACAiD;YAEJ;YACAE,UAAU;QACZ;QACAR,OAAO;YACLP,OAAO,SAASO;gBACd,MAAMG,aAAa;gBACnBC,UAAUjD,OAAOgD;gBACjB,mFAAmF;gBACnF,OAAO,OAAO9C,kBAAkB2C,KAAK,KAAK,aAEtC3C,kBAAkB2C,KAAK,CAACK,KAAK,CAAChD,mBAAmBiD,aAEjD,qGAAqG;gBACrG,iHAAiH;gBACjH,oHAAoH;gBACpH,iEAAiE;gBACjEL,+BAA+BM,IAAI,CAAClD,mBAAmB0B;YAC7D;YACAyB,UAAU;QACZ;QACAN,UAAU;YACRT,OAAO,SAASS;gBACd,MAAMC,aAAa;gBACnBC,UAAUjD,OAAOgD;gBACjB,OAAO9C,kBAAkB6C,QAAQ,CAACG,KAAK,CACrChD,mBACAiD;YAEJ;YACAE,UAAU;QACZ;IACF;IAEA,OAAOzB;AACT;AAEA,+EAA+E;AAC/E,+EAA+E;AAC/E,SAAST,oCACPjB,iBAAyC,EACzCF,KAAc;IAEd,MAAM+B,gBAAgBR,cAAcI,GAAG,CAACzB;IACxC,IAAI6B,eAAe;QACjB,OAAOA;IACT;IAEA,MAAMH,UAAUzC,2BAA2Be;IAE3C,MAAMuD,iBAAiB,IAAIC,MAAM9B,SAAS;QACxCD,KAAIgC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,OAAQD;gBACN,KAAKxB,OAAOC,QAAQ;oBAAE;wBACpByB,kBAAkB9D,OAAO;wBACzB;oBACF;gBACA,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBAAY;wBACf8D,kBAAkB9D,OAAO,CAAC,YAAY,EAAE4D,KAAK,EAAE,CAAC;wBAChD;oBACF;gBACA;oBAAS;oBACP,kEAAkE;oBACpE;YACF;YAEA,OAAOpE,eAAemC,GAAG,CAACgC,QAAQC,MAAMC;QAC1C;IACF;IAEAtC,cAAcO,GAAG,CAAC5B,mBAAmBuD;IAErC,OAAOA;AACT;AAEA,SAASF,gBAAgBC,GAAY;IACnC,OAAO,OAAOA,QAAQ,YACpBA,QAAQ,QACR,OAAO,AAACA,IAAYO,IAAI,KAAK,WAC3B,CAAC,CAAC,EAAE,AAACP,IAAYO,IAAI,CAAC,CAAC,CAAC,GACxB,OAAOP,QAAQ,WACb,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,GACV;AACR;AAEA,SAASP,UAAUjD,KAAyB,EAAEgD,UAAkB;IAC9D,MAAMnD,gBAAgBjB,qBAAqBgB,QAAQ;IAEnD,IAAIC,eAAe;QACjB,OAAQA,cAAcS,IAAI;YACxB,KAAK;gBACH,IAAIT,cAAcmE,cAAc,KAAK,MAAM;oBACzC,qEAAqE;oBACrE,qEAAqE;oBACrE/E,uCAAuCY;gBACzC;gBACA;YACF,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH;YACF;gBACEA;QACJ;IACF;IAEA,gCAAgC;IAChCiE,kBAAkB9D,OAAOgD;AAC3B;AAEA,MAAMc,oBAAoBzE,4CACxB4E;AAGF,SAASA,yBACPjE,KAAyB,EACzBgD,UAAkB;IAElB,MAAMkB,SAASlE,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,qBAIN,CAJM,IAAID,MACT,GAAGmE,OAAO,KAAK,EAAElB,WAAW,EAAE,CAAC,GAC7B,CAAC,wDAAwD,CAAC,GAC1D,CAAC,8DAA8D,CAAC,GAH7D,qBAAA;eAAA;oBAAA;sBAAA;IAIP;AACF;AAEA,SAASR;IAGP,OAAO,IAAI,CAACE,MAAM,GACfyB,GAAG,CAAC,CAACC,IAAM;YAACA,EAAEL,IAAI;YAAEK;SAAE,EACtBC,MAAM;AACX;AAEA,SAASvB,+BAEPwB,UAA2C;IAE3C,KAAK,MAAMC,UAAU,IAAI,CAAC7B,MAAM,GAAI;QAClC,IAAI,CAACE,MAAM,CAAC2B,OAAOR,IAAI;IACzB;IACA,OAAOO;AACT", "ignoreList": [0]}