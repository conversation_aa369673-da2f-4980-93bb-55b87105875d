{"version": 3, "sources": ["../../src/lib/find-root.ts"], "sourcesContent": ["import { dirname } from 'path'\nimport findUp from 'next/dist/compiled/find-up'\nimport * as Log from '../build/output/log'\n\nexport function findRootLockFile(cwd: string) {\n  return findUp.sync(\n    [\n      'pnpm-lock.yaml',\n      'package-lock.json',\n      'yarn.lock',\n      'bun.lock',\n      'bun.lockb',\n    ],\n    {\n      cwd,\n    }\n  )\n}\n\nexport function findRootDir(cwd: string): string {\n  const lockFile = findRootLockFile(cwd)\n  if (!lockFile) return cwd\n\n  const lockFiles = [lockFile]\n  while (true) {\n    const lastLockFile = lockFiles[lockFiles.length - 1]\n    const currentDir = dirname(lastLockFile)\n    const parentDir = dirname(currentDir)\n\n    // dirname('/')==='/' so if we happen to reach the FS root (as might happen in a container we need to quit to avoid looping forever\n    if (parentDir === currentDir) break\n\n    const newLockFile = findRootLockFile(parentDir)\n\n    if (!newLockFile) break\n\n    lockFiles.push(newLockFile)\n  }\n\n  // Only warn if not in a build worker to avoid duplicate warnings\n  if (typeof process.send !== 'function' && lockFiles.length > 1) {\n    const additionalLockFiles = lockFiles\n      .slice(0, -1)\n      .map((str) => `\\n   * ${str}`)\n      .join('')\n\n    if (process.env.TURBOPACK) {\n      Log.warnOnce(\n        `Warning: Next.js inferred your workspace root, but it may not be correct.\\n` +\n          ` We detected multiple lockfiles and selected the directory of ${lockFiles[lockFiles.length - 1]} as the root directory.\\n` +\n          ` To silence this warning, set \\`turbopack.root\\` in your Next.js config, or consider ` +\n          `removing one of the lockfiles if it's not needed.\\n` +\n          `   See https://nextjs.org/docs/app/api-reference/config/next-config-js/turbopack#root-directory for more information.\\n` +\n          ` Detected additional lockfiles: ${additionalLockFiles}\\n`\n      )\n    } else {\n      Log.warnOnce(\n        `Warning: Next.js inferred your workspace root, but it may not be correct.\\n` +\n          ` We detected multiple lockfiles and selected the directory of ${lockFiles[lockFiles.length - 1]} as the root directory.\\n` +\n          ` To silence this warning, set \\`outputFileTracingRoot\\` in your Next.js config, or consider ` +\n          `removing one of the lockfiles if it's not needed.\\n` +\n          `   See https://nextjs.org/docs/app/api-reference/config/next-config-js/output#caveats for more information.\\n` +\n          ` Detected additional lockfiles: ${additionalLockFiles}\\n`\n      )\n    }\n  }\n\n  return dirname(lockFiles[lockFiles.length - 1])\n}\n"], "names": ["findRootDir", "findRootLockFile", "cwd", "findUp", "sync", "lockFile", "lockFiles", "lastLockFile", "length", "currentDir", "dirname", "parentDir", "newLockFile", "push", "process", "send", "additionalLockFiles", "slice", "map", "str", "join", "env", "TURBOPACK", "Log", "warnOnce"], "mappings": ";;;;;;;;;;;;;;;IAmBgBA,WAAW;eAAXA;;IAfAC,gBAAgB;eAAhBA;;;sBAJQ;+DACL;6DACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEd,SAASA,iBAAiBC,GAAW;IAC1C,OAAOC,eAAM,CAACC,IAAI,CAChB;QACE;QACA;QACA;QACA;QACA;KACD,EACD;QACEF;IACF;AAEJ;AAEO,SAASF,YAAYE,GAAW;IACrC,MAAMG,WAAWJ,iBAAiBC;IAClC,IAAI,CAACG,UAAU,OAAOH;IAEtB,MAAMI,YAAY;QAACD;KAAS;IAC5B,MAAO,KAAM;QACX,MAAME,eAAeD,SAAS,CAACA,UAAUE,MAAM,GAAG,EAAE;QACpD,MAAMC,aAAaC,IAAAA,aAAO,EAACH;QAC3B,MAAMI,YAAYD,IAAAA,aAAO,EAACD;QAE1B,mIAAmI;QACnI,IAAIE,cAAcF,YAAY;QAE9B,MAAMG,cAAcX,iBAAiBU;QAErC,IAAI,CAACC,aAAa;QAElBN,UAAUO,IAAI,CAACD;IACjB;IAEA,iEAAiE;IACjE,IAAI,OAAOE,QAAQC,IAAI,KAAK,cAAcT,UAAUE,MAAM,GAAG,GAAG;QAC9D,MAAMQ,sBAAsBV,UACzBW,KAAK,CAAC,GAAG,CAAC,GACVC,GAAG,CAAC,CAACC,MAAQ,CAAC,OAAO,EAAEA,KAAK,EAC5BC,IAAI,CAAC;QAER,IAAIN,QAAQO,GAAG,CAACC,SAAS,EAAE;YACzBC,KAAIC,QAAQ,CACV,CAAC,2EAA2E,CAAC,GAC3E,CAAC,8DAA8D,EAAElB,SAAS,CAACA,UAAUE,MAAM,GAAG,EAAE,CAAC,yBAAyB,CAAC,GAC3H,CAAC,qFAAqF,CAAC,GACvF,CAAC,mDAAmD,CAAC,GACrD,CAAC,uHAAuH,CAAC,GACzH,CAAC,gCAAgC,EAAEQ,oBAAoB,EAAE,CAAC;QAEhE,OAAO;YACLO,KAAIC,QAAQ,CACV,CAAC,2EAA2E,CAAC,GAC3E,CAAC,8DAA8D,EAAElB,SAAS,CAACA,UAAUE,MAAM,GAAG,EAAE,CAAC,yBAAyB,CAAC,GAC3H,CAAC,4FAA4F,CAAC,GAC9F,CAAC,mDAAmD,CAAC,GACrD,CAAC,6GAA6G,CAAC,GAC/G,CAAC,gCAAgC,EAAEQ,oBAAoB,EAAE,CAAC;QAEhE;IACF;IAEA,OAAON,IAAAA,aAAO,EAACJ,SAAS,CAACA,UAAUE,MAAM,GAAG,EAAE;AAChD", "ignoreList": [0]}