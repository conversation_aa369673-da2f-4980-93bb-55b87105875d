import { PrismaService } from '../prisma/prisma.service';
import { UpdateUserDto } from './dto/update-user.dto';
export declare class UsersService {
    private prisma;
    constructor(prisma: PrismaService);
    getProfile(userId: string): Promise<{
        firstName: string;
        lastName: string;
        email: string;
        id: string;
        profilePicture: string | null;
        createdAt: Date;
        updatedAt: Date;
    } | null>;
    updateProfile(userId: string, updateUserDto: UpdateUserDto): Promise<{
        message: string;
        user: {
            firstName: string;
            lastName: string;
            email: string;
            id: string;
            profilePicture: string | null;
            updatedAt: Date;
        };
    }>;
}
