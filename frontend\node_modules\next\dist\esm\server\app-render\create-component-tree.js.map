{"version": 3, "sources": ["../../../src/server/app-render/create-component-tree.tsx"], "sourcesContent": ["import type { CacheNodeSeedData, PreloadCallbacks } from './types'\nimport React from 'react'\nimport {\n  isClientReference,\n  isUseCacheFunction,\n} from '../../lib/client-and-server-references'\nimport { getLayoutOrPageModule } from '../lib/app-dir-module'\nimport type { LoaderTree } from '../lib/app-dir-module'\nimport { interopDefault } from './interop-default'\nimport { parseLoaderTree } from './parse-loader-tree'\nimport type { AppRenderContext, GetDynamicParamFromSegment } from './app-render'\nimport { createComponentStylesAndScripts } from './create-component-styles-and-scripts'\nimport { getLayerAssets } from './get-layer-assets'\nimport { hasLoadingComponentInTree } from './has-loading-component-in-tree'\nimport { validateRevalidate } from '../lib/patch-fetch'\nimport { PARALLEL_ROUTE_DEFAULT_PATH } from '../../client/components/builtin/default'\nimport { getTracer } from '../lib/trace/tracer'\nimport { NextNodeServerSpan } from '../lib/trace/constants'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport type { LoadingModuleData } from '../../shared/lib/app-router-context.shared-runtime'\nimport type { Params } from '../request/params'\nimport { workUnitAsyncStorage } from './work-unit-async-storage.external'\nimport { OUTLET_BOUNDARY_NAME } from '../../lib/framework/boundary-constants'\nimport type {\n  UseCacheLayoutComponentProps,\n  UseCachePageComponentProps,\n} from '../use-cache/use-cache-wrapper'\nimport { DEFAULT_SEGMENT_KEY } from '../../shared/lib/segment'\nimport {\n  BOUNDARY_PREFIX,\n  BOUNDARY_SUFFIX,\n  BUILTIN_PREFIX,\n  getConventionPathByType,\n  isNextjsBuiltinFilePath,\n} from './segment-explorer-path'\n\n/**\n * Use the provided loader tree to create the React Component tree.\n */\nexport function createComponentTree(props: {\n  loaderTree: LoaderTree\n  parentParams: Params\n  rootLayoutIncluded: boolean\n  injectedCSS: Set<string>\n  injectedJS: Set<string>\n  injectedFontPreloadTags: Set<string>\n  getMetadataReady: () => Promise<void>\n  getViewportReady: () => Promise<void>\n  ctx: AppRenderContext\n  missingSlots?: Set<string>\n  preloadCallbacks: PreloadCallbacks\n  authInterrupts: boolean\n  StreamingMetadataOutlet: React.ComponentType | null\n}): Promise<CacheNodeSeedData> {\n  return getTracer().trace(\n    NextNodeServerSpan.createComponentTree,\n    {\n      spanName: 'build component tree',\n    },\n    () => createComponentTreeInternal(props, true)\n  )\n}\n\nfunction errorMissingDefaultExport(\n  pagePath: string,\n  convention: string\n): never {\n  const normalizedPagePath = pagePath === '/' ? '' : pagePath\n  throw new Error(\n    `The default export is not a React Component in \"${normalizedPagePath}/${convention}\"`\n  )\n}\n\nconst cacheNodeKey = 'c'\n\nasync function createComponentTreeInternal(\n  {\n    loaderTree: tree,\n    parentParams,\n    rootLayoutIncluded,\n    injectedCSS,\n    injectedJS,\n    injectedFontPreloadTags,\n    getViewportReady,\n    getMetadataReady,\n    ctx,\n    missingSlots,\n    preloadCallbacks,\n    authInterrupts,\n    StreamingMetadataOutlet,\n  }: {\n    loaderTree: LoaderTree\n    parentParams: Params\n    rootLayoutIncluded: boolean\n    injectedCSS: Set<string>\n    injectedJS: Set<string>\n    injectedFontPreloadTags: Set<string>\n    getViewportReady: () => Promise<void>\n    getMetadataReady: () => Promise<void>\n    ctx: AppRenderContext\n    missingSlots?: Set<string>\n    preloadCallbacks: PreloadCallbacks\n    authInterrupts: boolean\n    StreamingMetadataOutlet: React.ComponentType | null\n  },\n  isRoot: boolean\n): Promise<CacheNodeSeedData> {\n  const {\n    renderOpts: { nextConfigOutput, experimental },\n    workStore,\n    componentMod: {\n      SegmentViewNode,\n      HTTPAccessFallbackBoundary,\n      LayoutRouter,\n      RenderFromTemplateContext,\n      OutletBoundary,\n      ClientPageRoot,\n      ClientSegmentRoot,\n      createServerSearchParamsForServerPage,\n      createPrerenderSearchParamsForClientPage,\n      createServerParamsForServerSegment,\n      createPrerenderParamsForClientSegment,\n      serverHooks: { DynamicServerError },\n      Postpone,\n    },\n    pagePath,\n    getDynamicParamFromSegment,\n    isPrefetch,\n    query,\n  } = ctx\n\n  const { page, conventionPath, segment, modules, parallelRoutes } =\n    parseLoaderTree(tree)\n\n  const {\n    layout,\n    template,\n    error,\n    loading,\n    'not-found': notFound,\n    forbidden,\n    unauthorized,\n  } = modules\n\n  const injectedCSSWithCurrentLayout = new Set(injectedCSS)\n  const injectedJSWithCurrentLayout = new Set(injectedJS)\n  const injectedFontPreloadTagsWithCurrentLayout = new Set(\n    injectedFontPreloadTags\n  )\n\n  const layerAssets = getLayerAssets({\n    preloadCallbacks,\n    ctx,\n    layoutOrPagePath: conventionPath,\n    injectedCSS: injectedCSSWithCurrentLayout,\n    injectedJS: injectedJSWithCurrentLayout,\n    injectedFontPreloadTags: injectedFontPreloadTagsWithCurrentLayout,\n  })\n\n  const [Template, templateStyles, templateScripts] = template\n    ? await createComponentStylesAndScripts({\n        ctx,\n        filePath: template[1],\n        getComponent: template[0],\n        injectedCSS: injectedCSSWithCurrentLayout,\n        injectedJS: injectedJSWithCurrentLayout,\n      })\n    : [React.Fragment]\n\n  const [ErrorComponent, errorStyles, errorScripts] = error\n    ? await createComponentStylesAndScripts({\n        ctx,\n        filePath: error[1],\n        getComponent: error[0],\n        injectedCSS: injectedCSSWithCurrentLayout,\n        injectedJS: injectedJSWithCurrentLayout,\n      })\n    : []\n\n  const [Loading, loadingStyles, loadingScripts] = loading\n    ? await createComponentStylesAndScripts({\n        ctx,\n        filePath: loading[1],\n        getComponent: loading[0],\n        injectedCSS: injectedCSSWithCurrentLayout,\n        injectedJS: injectedJSWithCurrentLayout,\n      })\n    : []\n\n  const isLayout = typeof layout !== 'undefined'\n  const isPage = typeof page !== 'undefined'\n  const { mod: layoutOrPageMod, modType } = await getTracer().trace(\n    NextNodeServerSpan.getLayoutOrPageModule,\n    {\n      hideSpan: !(isLayout || isPage),\n      spanName: 'resolve segment modules',\n      attributes: {\n        'next.segment': segment,\n      },\n    },\n    () => getLayoutOrPageModule(tree)\n  )\n\n  /**\n   * Checks if the current segment is a root layout.\n   */\n  const rootLayoutAtThisLevel = isLayout && !rootLayoutIncluded\n  /**\n   * Checks if the current segment or any level above it has a root layout.\n   */\n  const rootLayoutIncludedAtThisLevelOrAbove =\n    rootLayoutIncluded || rootLayoutAtThisLevel\n\n  const [NotFound, notFoundStyles] = notFound\n    ? await createComponentStylesAndScripts({\n        ctx,\n        filePath: notFound[1],\n        getComponent: notFound[0],\n        injectedCSS: injectedCSSWithCurrentLayout,\n        injectedJS: injectedJSWithCurrentLayout,\n      })\n    : []\n\n  const [Forbidden, forbiddenStyles] =\n    authInterrupts && forbidden\n      ? await createComponentStylesAndScripts({\n          ctx,\n          filePath: forbidden[1],\n          getComponent: forbidden[0],\n          injectedCSS: injectedCSSWithCurrentLayout,\n          injectedJS: injectedJSWithCurrentLayout,\n        })\n      : []\n\n  const [Unauthorized, unauthorizedStyles] =\n    authInterrupts && unauthorized\n      ? await createComponentStylesAndScripts({\n          ctx,\n          filePath: unauthorized[1],\n          getComponent: unauthorized[0],\n          injectedCSS: injectedCSSWithCurrentLayout,\n          injectedJS: injectedJSWithCurrentLayout,\n        })\n      : []\n\n  let dynamic = layoutOrPageMod?.dynamic\n\n  if (nextConfigOutput === 'export') {\n    if (!dynamic || dynamic === 'auto') {\n      dynamic = 'error'\n    } else if (dynamic === 'force-dynamic') {\n      // force-dynamic is always incompatible with 'export'. We must interrupt the build\n      throw new StaticGenBailoutError(\n        `Page with \\`dynamic = \"force-dynamic\"\\` couldn't be exported. \\`output: \"export\"\\` requires all pages be renderable statically because there is no runtime server to dynamically render routes in this output format. Learn more: https://nextjs.org/docs/app/building-your-application/deploying/static-exports`\n      )\n    }\n  }\n\n  if (typeof dynamic === 'string') {\n    // the nested most config wins so we only force-static\n    // if it's configured above any parent that configured\n    // otherwise\n    if (dynamic === 'error') {\n      workStore.dynamicShouldError = true\n    } else if (dynamic === 'force-dynamic') {\n      workStore.forceDynamic = true\n\n      // TODO: (PPR) remove this bailout once PPR is the default\n      if (workStore.isStaticGeneration && !experimental.isRoutePPREnabled) {\n        // If the postpone API isn't available, we can't postpone the render and\n        // therefore we can't use the dynamic API.\n        const err = new DynamicServerError(\n          `Page with \\`dynamic = \"force-dynamic\"\\` won't be rendered statically.`\n        )\n        workStore.dynamicUsageDescription = err.message\n        workStore.dynamicUsageStack = err.stack\n        throw err\n      }\n    } else {\n      workStore.dynamicShouldError = false\n      workStore.forceStatic = dynamic === 'force-static'\n    }\n  }\n\n  if (typeof layoutOrPageMod?.fetchCache === 'string') {\n    workStore.fetchCache = layoutOrPageMod?.fetchCache\n  }\n\n  if (typeof layoutOrPageMod?.revalidate !== 'undefined') {\n    validateRevalidate(layoutOrPageMod?.revalidate, workStore.route)\n  }\n\n  if (typeof layoutOrPageMod?.revalidate === 'number') {\n    const defaultRevalidate = layoutOrPageMod.revalidate as number\n\n    const workUnitStore = workUnitAsyncStorage.getStore()\n\n    if (workUnitStore) {\n      switch (workUnitStore.type) {\n        case 'prerender':\n        case 'prerender-runtime':\n        case 'prerender-legacy':\n        case 'prerender-ppr':\n          if (workUnitStore.revalidate > defaultRevalidate) {\n            workUnitStore.revalidate = defaultRevalidate\n          }\n          break\n        case 'request':\n          // A request store doesn't have a revalidate property.\n          break\n        // createComponentTree is not called for these stores:\n        case 'cache':\n        case 'private-cache':\n        case 'prerender-client':\n        case 'unstable-cache':\n          break\n        default:\n          workUnitStore satisfies never\n      }\n    }\n\n    if (\n      !workStore.forceStatic &&\n      workStore.isStaticGeneration &&\n      defaultRevalidate === 0 &&\n      // If the postpone API isn't available, we can't postpone the render and\n      // therefore we can't use the dynamic API.\n      !experimental.isRoutePPREnabled\n    ) {\n      const dynamicUsageDescription = `revalidate: 0 configured ${segment}`\n      workStore.dynamicUsageDescription = dynamicUsageDescription\n\n      throw new DynamicServerError(dynamicUsageDescription)\n    }\n  }\n\n  const isStaticGeneration = workStore.isStaticGeneration\n\n  // Assume the segment we're rendering contains only partial data if PPR is\n  // enabled and this is a statically generated response. This is used by the\n  // client Segment Cache after a prefetch to determine if it can skip the\n  // second request to fill in the dynamic data.\n  //\n  // It's OK for this to be `true` when the data is actually fully static, but\n  // it's not OK for this to be `false` when the data possibly contains holes.\n  // Although the value here is overly pessimistic, for prefetches, it will be\n  // replaced by a more specific value when the data is later processed into\n  // per-segment responses (see collect-segment-data.tsx)\n  //\n  // For dynamic requests, this must always be `false` because dynamic responses\n  // are never partial.\n  const isPossiblyPartialResponse =\n    isStaticGeneration && experimental.isRoutePPREnabled === true\n\n  const LayoutOrPage: React.ComponentType<any> | undefined = layoutOrPageMod\n    ? interopDefault(layoutOrPageMod)\n    : undefined\n\n  /**\n   * The React Component to render.\n   */\n  let MaybeComponent = LayoutOrPage\n\n  if (process.env.NODE_ENV === 'development') {\n    const { isValidElementType } =\n      require('next/dist/compiled/react-is') as typeof import('next/dist/compiled/react-is')\n    if (\n      typeof MaybeComponent !== 'undefined' &&\n      !isValidElementType(MaybeComponent)\n    ) {\n      errorMissingDefaultExport(pagePath, modType ?? 'page')\n    }\n\n    if (\n      typeof ErrorComponent !== 'undefined' &&\n      !isValidElementType(ErrorComponent)\n    ) {\n      errorMissingDefaultExport(pagePath, 'error')\n    }\n\n    if (typeof Loading !== 'undefined' && !isValidElementType(Loading)) {\n      errorMissingDefaultExport(pagePath, 'loading')\n    }\n\n    if (typeof NotFound !== 'undefined' && !isValidElementType(NotFound)) {\n      errorMissingDefaultExport(pagePath, 'not-found')\n    }\n\n    if (typeof Forbidden !== 'undefined' && !isValidElementType(Forbidden)) {\n      errorMissingDefaultExport(pagePath, 'forbidden')\n    }\n\n    if (\n      typeof Unauthorized !== 'undefined' &&\n      !isValidElementType(Unauthorized)\n    ) {\n      errorMissingDefaultExport(pagePath, 'unauthorized')\n    }\n  }\n\n  // Handle dynamic segment params.\n  const segmentParam = getDynamicParamFromSegment(segment)\n\n  // Create object holding the parent params and current params\n  let currentParams: Params = parentParams\n  if (segmentParam && segmentParam.value !== null) {\n    currentParams = {\n      ...parentParams,\n      [segmentParam.param]: segmentParam.value,\n    }\n  }\n\n  // Resolve the segment param\n  const actualSegment = segmentParam ? segmentParam.treeSegment : segment\n  const isSegmentViewEnabled =\n    process.env.NODE_ENV === 'development' &&\n    ctx.renderOpts.devtoolSegmentExplorer\n  const dir =\n    (process.env.NEXT_RUNTIME === 'edge'\n      ? process.env.__NEXT_EDGE_PROJECT_DIR\n      : ctx.renderOpts.dir) || ''\n\n  // Use the same condition to render metadataOutlet as metadata\n  const metadataOutlet = StreamingMetadataOutlet ? (\n    <StreamingMetadataOutlet />\n  ) : (\n    <MetadataOutlet ready={getMetadataReady} />\n  )\n\n  const [notFoundElement, notFoundFilePath] =\n    await createBoundaryConventionElement({\n      ctx,\n      conventionName: 'not-found',\n      Component: NotFound,\n      styles: notFoundStyles,\n      tree,\n    })\n\n  const [forbiddenElement] = await createBoundaryConventionElement({\n    ctx,\n    conventionName: 'forbidden',\n    Component: Forbidden,\n    styles: forbiddenStyles,\n    tree,\n  })\n\n  const [unauthorizedElement] = await createBoundaryConventionElement({\n    ctx,\n    conventionName: 'unauthorized',\n    Component: Unauthorized,\n    styles: unauthorizedStyles,\n    tree,\n  })\n\n  // TODO: Combine this `map` traversal with the loop below that turns the array\n  // into an object.\n  const parallelRouteMap = await Promise.all(\n    Object.keys(parallelRoutes).map(\n      async (\n        parallelRouteKey\n      ): Promise<[string, React.ReactNode, CacheNodeSeedData | null]> => {\n        const isChildrenRouteKey = parallelRouteKey === 'children'\n        const parallelRoute = parallelRoutes[parallelRouteKey]\n\n        const notFoundComponent = isChildrenRouteKey\n          ? notFoundElement\n          : undefined\n\n        const forbiddenComponent = isChildrenRouteKey\n          ? forbiddenElement\n          : undefined\n\n        const unauthorizedComponent = isChildrenRouteKey\n          ? unauthorizedElement\n          : undefined\n\n        // if we're prefetching and that there's a Loading component, we bail out\n        // otherwise we keep rendering for the prefetch.\n        // We also want to bail out if there's no Loading component in the tree.\n        let childCacheNodeSeedData: CacheNodeSeedData | null = null\n\n        if (\n          // Before PPR, the way instant navigations work in Next.js is we\n          // prefetch everything up to the first route segment that defines a\n          // loading.tsx boundary. (We do the same if there's no loading\n          // boundary in the entire tree, because we don't want to prefetch too\n          // much) The rest of the tree is deferred until the actual navigation.\n          // It does not take into account whether the data is dynamic — even if\n          // the tree is completely static, it will still defer everything\n          // inside the loading boundary.\n          //\n          // This behavior predates PPR and is only relevant if the\n          // PPR flag is not enabled.\n          isPrefetch &&\n          (Loading || !hasLoadingComponentInTree(parallelRoute)) &&\n          // The approach with PPR is different — loading.tsx behaves like a\n          // regular Suspense boundary and has no special behavior.\n          //\n          // With PPR, we prefetch as deeply as possible, and only defer when\n          // dynamic data is accessed. If so, we only defer the nearest parent\n          // Suspense boundary of the dynamic data access, regardless of whether\n          // the boundary is defined by loading.tsx or a normal <Suspense>\n          // component in userspace.\n          //\n          // NOTE: In practice this usually means we'll end up prefetching more\n          // than we were before PPR, which may or may not be considered a\n          // performance regression by some apps. The plan is to address this\n          // before General Availability of PPR by introducing granular\n          // per-segment fetching, so we can reuse as much of the tree as\n          // possible during both prefetches and dynamic navigations. But during\n          // the beta period, we should be clear about this trade off in our\n          // communications.\n          !experimental.isRoutePPREnabled\n        ) {\n          // Don't prefetch this child. This will trigger a lazy fetch by the\n          // client router.\n        } else {\n          // Create the child component\n\n          if (process.env.NODE_ENV === 'development' && missingSlots) {\n            // When we detect the default fallback (which triggers a 404), we collect the missing slots\n            // to provide more helpful debug information during development mode.\n            const parsedTree = parseLoaderTree(parallelRoute)\n            if (\n              parsedTree.conventionPath?.endsWith(PARALLEL_ROUTE_DEFAULT_PATH)\n            ) {\n              missingSlots.add(parallelRouteKey)\n            }\n          }\n\n          const seedData = await createComponentTreeInternal(\n            {\n              loaderTree: parallelRoute,\n              parentParams: currentParams,\n              rootLayoutIncluded: rootLayoutIncludedAtThisLevelOrAbove,\n              injectedCSS: injectedCSSWithCurrentLayout,\n              injectedJS: injectedJSWithCurrentLayout,\n              injectedFontPreloadTags: injectedFontPreloadTagsWithCurrentLayout,\n              // `getMetadataReady` and `getViewportReady` are used to conditionally throw. In the case of parallel routes we will have more than one page\n              // but we only want to throw on the first one.\n              getMetadataReady: isChildrenRouteKey\n                ? getMetadataReady\n                : () => Promise.resolve(),\n              getViewportReady: isChildrenRouteKey\n                ? getViewportReady\n                : () => Promise.resolve(),\n              ctx,\n              missingSlots,\n              preloadCallbacks,\n              authInterrupts,\n              // `StreamingMetadataOutlet` is used to conditionally throw. In the case of parallel routes we will have more than one page\n              // but we only want to throw on the first one.\n              StreamingMetadataOutlet: isChildrenRouteKey\n                ? StreamingMetadataOutlet\n                : null,\n            },\n            false\n          )\n\n          childCacheNodeSeedData = seedData\n        }\n\n        const templateNode = (\n          <Template>\n            <RenderFromTemplateContext />\n          </Template>\n        )\n\n        const templateFilePath = getConventionPathByType(tree, dir, 'template')\n        const errorFilePath = getConventionPathByType(tree, dir, 'error')\n        const loadingFilePath = getConventionPathByType(tree, dir, 'loading')\n        const globalErrorFilePath = isRoot\n          ? getConventionPathByType(tree, dir, 'global-error')\n          : undefined\n\n        const wrappedErrorStyles =\n          isSegmentViewEnabled && errorFilePath ? (\n            <SegmentViewNode type=\"error\" pagePath={errorFilePath}>\n              {errorStyles}\n            </SegmentViewNode>\n          ) : (\n            errorStyles\n          )\n\n        // Add a suffix to avoid conflict with the segment view node representing rendered file.\n        // existence: not-found.tsx@boundary\n        // rendered: not-found.tsx\n        const fileNameSuffix = BOUNDARY_SUFFIX\n        const segmentViewBoundaries = isSegmentViewEnabled ? (\n          <>\n            {notFoundFilePath && (\n              <SegmentViewNode\n                type={`${BOUNDARY_PREFIX}not-found`}\n                pagePath={notFoundFilePath + fileNameSuffix}\n              />\n            )}\n            {loadingFilePath && (\n              <SegmentViewNode\n                type={`${BOUNDARY_PREFIX}loading`}\n                pagePath={loadingFilePath + fileNameSuffix}\n              />\n            )}\n            {errorFilePath && (\n              <SegmentViewNode\n                type={`${BOUNDARY_PREFIX}error`}\n                pagePath={errorFilePath + fileNameSuffix}\n              />\n            )}\n            {/* Only show global-error when it's the builtin one */}\n            {globalErrorFilePath && (\n              <SegmentViewNode\n                type={`${BOUNDARY_PREFIX}global-error`}\n                pagePath={\n                  isNextjsBuiltinFilePath(globalErrorFilePath)\n                    ? `${BUILTIN_PREFIX}global-error.js${fileNameSuffix}`\n                    : globalErrorFilePath\n                }\n              />\n            )}\n            {/* do not surface forbidden and unauthorized boundaries yet as they're unstable */}\n          </>\n        ) : null\n\n        return [\n          parallelRouteKey,\n          <LayoutRouter\n            parallelRouterKey={parallelRouteKey}\n            // TODO-APP: Add test for loading returning `undefined`. This currently can't be tested as the `webdriver()` tab will wait for the full page to load before returning.\n            error={ErrorComponent}\n            errorStyles={wrappedErrorStyles}\n            errorScripts={errorScripts}\n            template={\n              // Only render SegmentViewNode when there's an actual template\n              isSegmentViewEnabled && templateFilePath ? (\n                <SegmentViewNode type=\"template\" pagePath={templateFilePath}>\n                  {templateNode}\n                </SegmentViewNode>\n              ) : (\n                templateNode\n              )\n            }\n            templateStyles={templateStyles}\n            templateScripts={templateScripts}\n            notFound={notFoundComponent}\n            forbidden={forbiddenComponent}\n            unauthorized={unauthorizedComponent}\n            {...(isSegmentViewEnabled && { segmentViewBoundaries })}\n          />,\n          childCacheNodeSeedData,\n        ]\n      }\n    )\n  )\n\n  // Convert the parallel route map into an object after all promises have been resolved.\n  let parallelRouteProps: { [key: string]: React.ReactNode } = {}\n  let parallelRouteCacheNodeSeedData: {\n    [key: string]: CacheNodeSeedData | null\n  } = {}\n  for (const parallelRoute of parallelRouteMap) {\n    const [parallelRouteKey, parallelRouteProp, flightData] = parallelRoute\n    parallelRouteProps[parallelRouteKey] = parallelRouteProp\n    parallelRouteCacheNodeSeedData[parallelRouteKey] = flightData\n  }\n\n  let loadingElement = Loading ? <Loading key=\"l\" /> : null\n  const loadingFilePath = getConventionPathByType(tree, dir, 'loading')\n  if (isSegmentViewEnabled && loadingElement) {\n    if (loadingFilePath) {\n      loadingElement = (\n        <SegmentViewNode\n          key={cacheNodeKey + '-loading'}\n          type=\"loading\"\n          pagePath={loadingFilePath}\n        >\n          {loadingElement}\n        </SegmentViewNode>\n      )\n    }\n  }\n\n  const loadingData: LoadingModuleData = loadingElement\n    ? [loadingElement, loadingStyles, loadingScripts]\n    : null\n\n  // When the segment does not have a layout or page we still have to add the layout router to ensure the path holds the loading component\n  if (!MaybeComponent) {\n    return [\n      actualSegment,\n      <React.Fragment key={cacheNodeKey}>\n        {layerAssets}\n        {parallelRouteProps.children}\n      </React.Fragment>,\n      parallelRouteCacheNodeSeedData,\n      loadingData,\n      isPossiblyPartialResponse,\n    ]\n  }\n\n  const Component = MaybeComponent\n  // If force-dynamic is used and the current render supports postponing, we\n  // replace it with a node that will postpone the render. This ensures that the\n  // postpone is invoked during the react render phase and not during the next\n  // render phase.\n  // @TODO this does not actually do what it seems like it would or should do. The idea is that\n  // if we are rendering in a force-dynamic mode and we can postpone we should only make the segments\n  // that ask for force-dynamic to be dynamic, allowing other segments to still prerender. However\n  // because this comes after the children traversal and the static generation store is mutated every segment\n  // along the parent path of a force-dynamic segment will hit this condition effectively making the entire\n  // render force-dynamic. We should refactor this function so that we can correctly track which segments\n  // need to be dynamic\n  if (\n    workStore.isStaticGeneration &&\n    workStore.forceDynamic &&\n    experimental.isRoutePPREnabled\n  ) {\n    return [\n      actualSegment,\n      <React.Fragment key={cacheNodeKey}>\n        <Postpone\n          reason='dynamic = \"force-dynamic\" was used'\n          route={workStore.route}\n        />\n        {layerAssets}\n      </React.Fragment>,\n      parallelRouteCacheNodeSeedData,\n      loadingData,\n      true,\n    ]\n  }\n\n  const isClientComponent = isClientReference(layoutOrPageMod)\n\n  if (\n    process.env.NODE_ENV === 'development' &&\n    'params' in parallelRouteProps\n  ) {\n    // @TODO consider making this an error and running the check in build as well\n    console.error(\n      `\"params\" is a reserved prop in Layouts and Pages and cannot be used as the name of a parallel route in ${segment}`\n    )\n  }\n\n  if (isPage) {\n    const PageComponent = Component\n\n    // Assign searchParams to props if this is a page\n    let pageElement: React.ReactNode\n    if (isClientComponent) {\n      if (isStaticGeneration) {\n        const promiseOfParams =\n          createPrerenderParamsForClientSegment(currentParams)\n        const promiseOfSearchParams =\n          createPrerenderSearchParamsForClientPage(workStore)\n        pageElement = (\n          <ClientPageRoot\n            Component={PageComponent}\n            searchParams={query}\n            params={currentParams}\n            promises={[promiseOfSearchParams, promiseOfParams]}\n          />\n        )\n      } else {\n        pageElement = (\n          <ClientPageRoot\n            Component={PageComponent}\n            searchParams={query}\n            params={currentParams}\n          />\n        )\n      }\n    } else {\n      // If we are passing params to a server component Page we need to track\n      // their usage in case the current render mode tracks dynamic API usage.\n      const params = createServerParamsForServerSegment(\n        currentParams,\n        workStore\n      )\n\n      // If we are passing searchParams to a server component Page we need to\n      // track their usage in case the current render mode tracks dynamic API\n      // usage.\n      let searchParams = createServerSearchParamsForServerPage(query, workStore)\n\n      if (isUseCacheFunction(PageComponent)) {\n        const UseCachePageComponent: React.ComponentType<UseCachePageComponentProps> =\n          PageComponent\n\n        pageElement = (\n          <UseCachePageComponent\n            params={params}\n            searchParams={searchParams}\n            $$isPageComponent\n          />\n        )\n      } else {\n        pageElement = (\n          <PageComponent params={params} searchParams={searchParams} />\n        )\n      }\n    }\n\n    const isDefaultSegment = segment === DEFAULT_SEGMENT_KEY\n    const pageFilePath =\n      getConventionPathByType(tree, dir, 'page') ??\n      getConventionPathByType(tree, dir, 'defaultPage')\n    const segmentType = isDefaultSegment ? 'default' : 'page'\n    const wrappedPageElement =\n      isSegmentViewEnabled && pageFilePath ? (\n        <SegmentViewNode\n          key={cacheNodeKey + '-' + segmentType}\n          type={segmentType}\n          pagePath={pageFilePath}\n        >\n          {pageElement}\n        </SegmentViewNode>\n      ) : (\n        pageElement\n      )\n\n    return [\n      actualSegment,\n      <React.Fragment key={cacheNodeKey}>\n        {wrappedPageElement}\n        {layerAssets}\n        <OutletBoundary>\n          <MetadataOutlet ready={getViewportReady} />\n          {metadataOutlet}\n        </OutletBoundary>\n      </React.Fragment>,\n      parallelRouteCacheNodeSeedData,\n      loadingData,\n      isPossiblyPartialResponse,\n    ]\n  } else {\n    const SegmentComponent = Component\n    const isRootLayoutWithChildrenSlotAndAtLeastOneMoreSlot =\n      rootLayoutAtThisLevel &&\n      'children' in parallelRoutes &&\n      Object.keys(parallelRoutes).length > 1\n\n    let segmentNode: React.ReactNode\n\n    if (isClientComponent) {\n      let clientSegment: React.ReactNode\n\n      if (isStaticGeneration) {\n        const promiseOfParams =\n          createPrerenderParamsForClientSegment(currentParams)\n\n        clientSegment = (\n          <ClientSegmentRoot\n            Component={SegmentComponent}\n            slots={parallelRouteProps}\n            params={currentParams}\n            promise={promiseOfParams}\n          />\n        )\n      } else {\n        clientSegment = (\n          <ClientSegmentRoot\n            Component={SegmentComponent}\n            slots={parallelRouteProps}\n            params={currentParams}\n          />\n        )\n      }\n\n      if (isRootLayoutWithChildrenSlotAndAtLeastOneMoreSlot) {\n        let notfoundClientSegment: React.ReactNode\n        let forbiddenClientSegment: React.ReactNode\n        let unauthorizedClientSegment: React.ReactNode\n        // TODO-APP: This is a hack to support unmatched parallel routes, which will throw `notFound()`.\n        // This ensures that a `HTTPAccessFallbackBoundary` is available for when that happens,\n        // but it's not ideal, as it needlessly invokes the `NotFound` component and renders the `RootLayout` twice.\n        // We should instead look into handling the fallback behavior differently in development mode so that it doesn't\n        // rely on the `NotFound` behavior.\n        notfoundClientSegment = createErrorBoundaryClientSegmentRoot({\n          ErrorBoundaryComponent: NotFound,\n          errorElement: notFoundElement,\n          ClientSegmentRoot,\n          layerAssets,\n          SegmentComponent,\n          currentParams,\n        })\n        forbiddenClientSegment = createErrorBoundaryClientSegmentRoot({\n          ErrorBoundaryComponent: Forbidden,\n          errorElement: forbiddenElement,\n          ClientSegmentRoot,\n          layerAssets,\n          SegmentComponent,\n          currentParams,\n        })\n        unauthorizedClientSegment = createErrorBoundaryClientSegmentRoot({\n          ErrorBoundaryComponent: Unauthorized,\n          errorElement: unauthorizedElement,\n          ClientSegmentRoot,\n          layerAssets,\n          SegmentComponent,\n          currentParams,\n        })\n        if (\n          notfoundClientSegment ||\n          forbiddenClientSegment ||\n          unauthorizedClientSegment\n        ) {\n          segmentNode = (\n            <HTTPAccessFallbackBoundary\n              key={cacheNodeKey}\n              notFound={notfoundClientSegment}\n              forbidden={forbiddenClientSegment}\n              unauthorized={unauthorizedClientSegment}\n            >\n              {layerAssets}\n              {clientSegment}\n            </HTTPAccessFallbackBoundary>\n          )\n        } else {\n          segmentNode = (\n            <React.Fragment key={cacheNodeKey}>\n              {layerAssets}\n              {clientSegment}\n            </React.Fragment>\n          )\n        }\n      } else {\n        segmentNode = (\n          <React.Fragment key={cacheNodeKey}>\n            {layerAssets}\n            {clientSegment}\n          </React.Fragment>\n        )\n      }\n    } else {\n      const params = createServerParamsForServerSegment(\n        currentParams,\n        workStore\n      )\n\n      let serverSegment: React.ReactNode\n\n      if (isUseCacheFunction(SegmentComponent)) {\n        const UseCacheLayoutComponent: React.ComponentType<UseCacheLayoutComponentProps> =\n          SegmentComponent\n\n        serverSegment = (\n          <UseCacheLayoutComponent\n            {...parallelRouteProps}\n            params={params}\n            $$isLayoutComponent\n          />\n        )\n      } else {\n        serverSegment = (\n          <SegmentComponent {...parallelRouteProps} params={params} />\n        )\n      }\n\n      if (isRootLayoutWithChildrenSlotAndAtLeastOneMoreSlot) {\n        // TODO-APP: This is a hack to support unmatched parallel routes, which will throw `notFound()`.\n        // This ensures that a `HTTPAccessFallbackBoundary` is available for when that happens,\n        // but it's not ideal, as it needlessly invokes the `NotFound` component and renders the `RootLayout` twice.\n        // We should instead look into handling the fallback behavior differently in development mode so that it doesn't\n        // rely on the `NotFound` behavior.\n        segmentNode = (\n          <HTTPAccessFallbackBoundary\n            key={cacheNodeKey}\n            notFound={\n              notFoundElement ? (\n                <>\n                  {layerAssets}\n                  <SegmentComponent params={params}>\n                    {notFoundStyles}\n                    {notFoundElement}\n                  </SegmentComponent>\n                </>\n              ) : undefined\n            }\n          >\n            {layerAssets}\n            {serverSegment}\n          </HTTPAccessFallbackBoundary>\n        )\n      } else {\n        segmentNode = (\n          <React.Fragment key={cacheNodeKey}>\n            {layerAssets}\n            {serverSegment}\n          </React.Fragment>\n        )\n      }\n    }\n\n    const layoutFilePath = getConventionPathByType(tree, dir, 'layout')\n    const wrappedSegmentNode =\n      isSegmentViewEnabled && layoutFilePath ? (\n        <SegmentViewNode key=\"layout\" type=\"layout\" pagePath={layoutFilePath}>\n          {segmentNode}\n        </SegmentViewNode>\n      ) : (\n        segmentNode\n      )\n\n    // For layouts we just render the component\n    return [\n      actualSegment,\n      wrappedSegmentNode,\n      parallelRouteCacheNodeSeedData,\n      loadingData,\n      isPossiblyPartialResponse,\n    ]\n  }\n}\n\nasync function MetadataOutlet({\n  ready,\n}: {\n  ready: () => Promise<void> & { status?: string; value?: unknown }\n}) {\n  const r = ready()\n  // We can avoid a extra microtask by unwrapping the instrumented promise directly if available.\n  if (r.status === 'rejected') {\n    throw r.value\n  } else if (r.status !== 'fulfilled') {\n    await r\n  }\n  return null\n}\nMetadataOutlet.displayName = OUTLET_BOUNDARY_NAME\n\nfunction createErrorBoundaryClientSegmentRoot({\n  ErrorBoundaryComponent,\n  errorElement,\n  ClientSegmentRoot,\n  layerAssets,\n  SegmentComponent,\n  currentParams,\n}: {\n  ErrorBoundaryComponent: React.ComponentType<any> | undefined\n  errorElement: React.ReactNode\n  ClientSegmentRoot: React.ComponentType<any>\n  layerAssets: React.ReactNode\n  SegmentComponent: React.ComponentType<any>\n  currentParams: Params\n}) {\n  if (ErrorBoundaryComponent) {\n    const notFoundParallelRouteProps = {\n      children: errorElement,\n    }\n    return (\n      <>\n        {layerAssets}\n        <ClientSegmentRoot\n          Component={SegmentComponent}\n          slots={notFoundParallelRouteProps}\n          params={currentParams}\n        />\n      </>\n    )\n  }\n  return null\n}\n\nexport function getRootParams(\n  loaderTree: LoaderTree,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment\n): Params {\n  return getRootParamsImpl({}, loaderTree, getDynamicParamFromSegment)\n}\n\nfunction getRootParamsImpl(\n  parentParams: Params,\n  loaderTree: LoaderTree,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment\n): Params {\n  const {\n    segment,\n    modules: { layout },\n    parallelRoutes,\n  } = parseLoaderTree(loaderTree)\n\n  const segmentParam = getDynamicParamFromSegment(segment)\n\n  let currentParams: Params = parentParams\n  if (segmentParam && segmentParam.value !== null) {\n    currentParams = {\n      ...parentParams,\n      [segmentParam.param]: segmentParam.value,\n    }\n  }\n\n  const isRootLayout = typeof layout !== 'undefined'\n\n  if (isRootLayout) {\n    return currentParams\n  } else if (!parallelRoutes.children) {\n    // This should really be an error but there are bugs in Turbopack that cause\n    // the _not-found LoaderTree to not have any layouts. For rootParams sake\n    // this is somewhat irrelevant when you are not customizing the 404 page.\n    // If you are customizing 404\n    // TODO update rootParams to make all params optional if `/app/not-found.tsx` is defined\n    return currentParams\n  } else {\n    return getRootParamsImpl(\n      currentParams,\n      // We stop looking for root params as soon as we hit the first layout\n      // and it is not possible to use parallel route children above the root layout\n      // so every parallelRoutes object that this function can visit will necessarily\n      // have a single `children` prop and no others.\n      parallelRoutes.children,\n      getDynamicParamFromSegment\n    )\n  }\n}\n\nasync function createBoundaryConventionElement({\n  ctx,\n  conventionName,\n  Component,\n  styles,\n  tree,\n}: {\n  ctx: AppRenderContext\n  conventionName:\n    | 'not-found'\n    | 'error'\n    | 'loading'\n    | 'forbidden'\n    | 'unauthorized'\n  Component: React.ComponentType<any> | undefined\n  styles: React.ReactNode | undefined\n  tree: LoaderTree\n}) {\n  const isSegmentViewEnabled =\n    process.env.NODE_ENV === 'development' &&\n    ctx.renderOpts.devtoolSegmentExplorer\n  const dir =\n    (process.env.NEXT_RUNTIME === 'edge'\n      ? process.env.__NEXT_EDGE_PROJECT_DIR\n      : ctx.renderOpts.dir) || ''\n  const { SegmentViewNode } = ctx.componentMod\n  const element = Component ? (\n    <>\n      <Component />\n      {styles}\n    </>\n  ) : undefined\n\n  const pagePath = getConventionPathByType(tree, dir, conventionName)\n\n  const wrappedElement =\n    isSegmentViewEnabled && element ? (\n      <SegmentViewNode\n        key={cacheNodeKey + '-' + conventionName}\n        type={conventionName}\n        pagePath={pagePath!}\n      >\n        {element}\n      </SegmentViewNode>\n    ) : (\n      element\n    )\n\n  return [wrappedElement, pagePath] as const\n}\n"], "names": ["React", "isClientReference", "isUseCacheFunction", "getLayoutOrPageModule", "interopDefault", "parseLoaderTree", "createComponentStylesAndScripts", "getLayerAssets", "hasLoadingComponentInTree", "validateRevalidate", "PARALLEL_ROUTE_DEFAULT_PATH", "getTracer", "NextNodeServerSpan", "StaticGenBailoutError", "workUnitAsyncStorage", "OUTLET_BOUNDARY_NAME", "DEFAULT_SEGMENT_KEY", "BOUNDARY_PREFIX", "BOUNDARY_SUFFIX", "BUILTIN_PREFIX", "getConventionPathByType", "isNextjsBuiltinFilePath", "createComponentTree", "props", "trace", "spanName", "createComponentTreeInternal", "errorMissingDefaultExport", "pagePath", "convention", "normalizedPagePath", "Error", "cacheNodeKey", "loaderTree", "tree", "parentParams", "rootLayoutIncluded", "injectedCSS", "injectedJS", "injectedFontPreloadTags", "getViewportReady", "getMetadataReady", "ctx", "missingSlots", "preloadCallbacks", "authInterrupts", "StreamingMetadataOutlet", "isRoot", "renderOpts", "nextConfigOutput", "experimental", "workStore", "componentMod", "SegmentViewNode", "HTTPAccessFallbackBoundary", "LayoutRouter", "RenderFromTemplateContext", "OutletBoundary", "ClientPageRoot", "ClientSegmentRoot", "createServerSearchParamsForServerPage", "createPrerenderSearchParamsForClientPage", "createServerParamsForServerSegment", "createPrerenderParamsForClientSegment", "serverHooks", "DynamicServerError", "Postpone", "getDynamicParamFromSegment", "isPrefetch", "query", "page", "conventionPath", "segment", "modules", "parallelRoutes", "layout", "template", "error", "loading", "notFound", "forbidden", "unauthorized", "injectedCSSWithCurrentLayout", "Set", "injectedJSWithCurrentLayout", "injectedFontPreloadTagsWithCurrentLayout", "layerAssets", "layoutOrPagePath", "Template", "templateStyles", "templateScripts", "filePath", "getComponent", "Fragment", "ErrorComponent", "errorStyles", "errorScripts", "Loading", "loadingStyles", "loadingScripts", "isLayout", "isPage", "mod", "layoutOrPageMod", "modType", "hideSpan", "attributes", "rootLayoutAtThisLevel", "rootLayoutIncludedAtThisLevelOrAbove", "NotFound", "notFoundStyles", "Forbidden", "forbiddenStyles", "Unauthorized", "unauthorizedStyles", "dynamic", "dynamicShouldError", "forceDynamic", "isStaticGeneration", "isRoutePPREnabled", "err", "dynamicUsageDescription", "message", "dynamicUsageStack", "stack", "forceStatic", "fetchCache", "revalidate", "route", "defaultRevalidate", "workUnitStore", "getStore", "type", "isPossiblyPartialResponse", "LayoutOrPage", "undefined", "MaybeComponent", "process", "env", "NODE_ENV", "isValidElementType", "require", "segmentParam", "currentParams", "value", "param", "actualSegment", "treeSegment", "isSegmentViewEnabled", "devtoolSegmentExplorer", "dir", "NEXT_RUNTIME", "__NEXT_EDGE_PROJECT_DIR", "metadataOutlet", "MetadataOutlet", "ready", "notFoundElement", "notFoundFilePath", "createBoundaryConventionElement", "conventionName", "Component", "styles", "forbiddenElement", "unauthorizedElement", "parallelRouteMap", "Promise", "all", "Object", "keys", "map", "parallelRouteKey", "isChildrenRoute<PERSON>ey", "parallelRoute", "notFoundComponent", "forbiddenComponent", "unauthorizedComponent", "childCacheNodeSeedData", "parsedTree", "endsWith", "add", "seedData", "resolve", "templateNode", "templateFilePath", "errorFilePath", "loadingFilePath", "globalErrorFilePath", "wrappedErrorStyles", "fileNameSuffix", "segmentViewBoundaries", "parallel<PERSON><PERSON>er<PERSON>ey", "parallelRouteProps", "parallelRouteCacheNodeSeedData", "parallelRouteProp", "flightData", "loadingElement", "loadingData", "children", "reason", "isClientComponent", "console", "PageComponent", "pageElement", "promiseOfParams", "promiseOfSearchParams", "searchParams", "params", "promises", "UseCachePageComponent", "$$isPageComponent", "isDefaultSegment", "pageFilePath", "segmentType", "wrappedPageElement", "SegmentComponent", "isRootLayoutWithChildrenSlotAndAtLeastOneMoreSlot", "length", "segmentNode", "clientSegment", "slots", "promise", "notfoundClientSegment", "forbiddenClientSegment", "unauthorizedClientSegment", "createErrorBoundaryClientSegmentRoot", "ErrorBoundaryComponent", "errorElement", "serverSegment", "UseCacheLayoutComponent", "$$isLayoutComponent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wrappedSegmentNode", "r", "status", "displayName", "notFoundParallelRouteProps", "getRootParams", "getRootParamsImpl", "isRootLayout", "element", "wrappedElement"], "mappings": ";AACA,OAAOA,WAAW,QAAO;AACzB,SACEC,iBAAiB,EACjBC,kBAAkB,QACb,yCAAwC;AAC/C,SAASC,qBAAqB,QAAQ,wBAAuB;AAE7D,SAASC,cAAc,QAAQ,oBAAmB;AAClD,SAASC,eAAe,QAAQ,sBAAqB;AAErD,SAASC,+BAA+B,QAAQ,wCAAuC;AACvF,SAASC,cAAc,QAAQ,qBAAoB;AACnD,SAASC,yBAAyB,QAAQ,kCAAiC;AAC3E,SAASC,kBAAkB,QAAQ,qBAAoB;AACvD,SAASC,2BAA2B,QAAQ,0CAAyC;AACrF,SAASC,SAAS,QAAQ,sBAAqB;AAC/C,SAASC,kBAAkB,QAAQ,yBAAwB;AAC3D,SAASC,qBAAqB,QAAQ,oDAAmD;AAGzF,SAASC,oBAAoB,QAAQ,qCAAoC;AACzE,SAASC,oBAAoB,QAAQ,yCAAwC;AAK7E,SAASC,mBAAmB,QAAQ,2BAA0B;AAC9D,SACEC,eAAe,EACfC,eAAe,EACfC,cAAc,EACdC,uBAAuB,EACvBC,uBAAuB,QAClB,0BAAyB;AAEhC;;CAEC,GACD,OAAO,SAASC,oBAAoBC,KAcnC;IACC,OAAOZ,YAAYa,KAAK,CACtBZ,mBAAmBU,mBAAmB,EACtC;QACEG,UAAU;IACZ,GACA,IAAMC,4BAA4BH,OAAO;AAE7C;AAEA,SAASI,0BACPC,QAAgB,EAChBC,UAAkB;IAElB,MAAMC,qBAAqBF,aAAa,MAAM,KAAKA;IACnD,MAAM,qBAEL,CAFK,IAAIG,MACR,CAAC,gDAAgD,EAAED,mBAAmB,CAAC,EAAED,WAAW,CAAC,CAAC,GADlF,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEA,MAAMG,eAAe;AAErB,eAAeN,4BACb,EACEO,YAAYC,IAAI,EAChBC,YAAY,EACZC,kBAAkB,EAClBC,WAAW,EACXC,UAAU,EACVC,uBAAuB,EACvBC,gBAAgB,EAChBC,gBAAgB,EAChBC,GAAG,EACHC,YAAY,EACZC,gBAAgB,EAChBC,cAAc,EACdC,uBAAuB,EAexB,EACDC,MAAe;IAEf,MAAM,EACJC,YAAY,EAAEC,gBAAgB,EAAEC,YAAY,EAAE,EAC9CC,SAAS,EACTC,cAAc,EACZC,eAAe,EACfC,0BAA0B,EAC1BC,YAAY,EACZC,yBAAyB,EACzBC,cAAc,EACdC,cAAc,EACdC,iBAAiB,EACjBC,qCAAqC,EACrCC,wCAAwC,EACxCC,kCAAkC,EAClCC,qCAAqC,EACrCC,aAAa,EAAEC,kBAAkB,EAAE,EACnCC,QAAQ,EACT,EACDtC,QAAQ,EACRuC,0BAA0B,EAC1BC,UAAU,EACVC,KAAK,EACN,GAAG3B;IAEJ,MAAM,EAAE4B,IAAI,EAAEC,cAAc,EAAEC,OAAO,EAAEC,OAAO,EAAEC,cAAc,EAAE,GAC9DrE,gBAAgB6B;IAElB,MAAM,EACJyC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,OAAO,EACP,aAAaC,QAAQ,EACrBC,SAAS,EACTC,YAAY,EACb,GAAGR;IAEJ,MAAMS,+BAA+B,IAAIC,IAAI9C;IAC7C,MAAM+C,8BAA8B,IAAID,IAAI7C;IAC5C,MAAM+C,2CAA2C,IAAIF,IACnD5C;IAGF,MAAM+C,cAAc/E,eAAe;QACjCqC;QACAF;QACA6C,kBAAkBhB;QAClBlC,aAAa6C;QACb5C,YAAY8C;QACZ7C,yBAAyB8C;IAC3B;IAEA,MAAM,CAACG,UAAUC,gBAAgBC,gBAAgB,GAAGd,WAChD,MAAMtE,gCAAgC;QACpCoC;QACAiD,UAAUf,QAAQ,CAAC,EAAE;QACrBgB,cAAchB,QAAQ,CAAC,EAAE;QACzBvC,aAAa6C;QACb5C,YAAY8C;IACd,KACA;QAACpF,MAAM6F,QAAQ;KAAC;IAEpB,MAAM,CAACC,gBAAgBC,aAAaC,aAAa,GAAGnB,QAChD,MAAMvE,gCAAgC;QACpCoC;QACAiD,UAAUd,KAAK,CAAC,EAAE;QAClBe,cAAcf,KAAK,CAAC,EAAE;QACtBxC,aAAa6C;QACb5C,YAAY8C;IACd,KACA,EAAE;IAEN,MAAM,CAACa,SAASC,eAAeC,eAAe,GAAGrB,UAC7C,MAAMxE,gCAAgC;QACpCoC;QACAiD,UAAUb,OAAO,CAAC,EAAE;QACpBc,cAAcd,OAAO,CAAC,EAAE;QACxBzC,aAAa6C;QACb5C,YAAY8C;IACd,KACA,EAAE;IAEN,MAAMgB,WAAW,OAAOzB,WAAW;IACnC,MAAM0B,SAAS,OAAO/B,SAAS;IAC/B,MAAM,EAAEgC,KAAKC,eAAe,EAAEC,OAAO,EAAE,GAAG,MAAM7F,YAAYa,KAAK,CAC/DZ,mBAAmBT,qBAAqB,EACxC;QACEsG,UAAU,CAAEL,CAAAA,YAAYC,MAAK;QAC7B5E,UAAU;QACViF,YAAY;YACV,gBAAgBlC;QAClB;IACF,GACA,IAAMrE,sBAAsB+B;IAG9B;;GAEC,GACD,MAAMyE,wBAAwBP,YAAY,CAAChE;IAC3C;;GAEC,GACD,MAAMwE,uCACJxE,sBAAsBuE;IAExB,MAAM,CAACE,UAAUC,eAAe,GAAG/B,WAC/B,MAAMzE,gCAAgC;QACpCoC;QACAiD,UAAUZ,QAAQ,CAAC,EAAE;QACrBa,cAAcb,QAAQ,CAAC,EAAE;QACzB1C,aAAa6C;QACb5C,YAAY8C;IACd,KACA,EAAE;IAEN,MAAM,CAAC2B,WAAWC,gBAAgB,GAChCnE,kBAAkBmC,YACd,MAAM1E,gCAAgC;QACpCoC;QACAiD,UAAUX,SAAS,CAAC,EAAE;QACtBY,cAAcZ,SAAS,CAAC,EAAE;QAC1B3C,aAAa6C;QACb5C,YAAY8C;IACd,KACA,EAAE;IAER,MAAM,CAAC6B,cAAcC,mBAAmB,GACtCrE,kBAAkBoC,eACd,MAAM3E,gCAAgC;QACpCoC;QACAiD,UAAUV,YAAY,CAAC,EAAE;QACzBW,cAAcX,YAAY,CAAC,EAAE;QAC7B5C,aAAa6C;QACb5C,YAAY8C;IACd,KACA,EAAE;IAER,IAAI+B,UAAUZ,mCAAAA,gBAAiBY,OAAO;IAEtC,IAAIlE,qBAAqB,UAAU;QACjC,IAAI,CAACkE,WAAWA,YAAY,QAAQ;YAClCA,UAAU;QACZ,OAAO,IAAIA,YAAY,iBAAiB;YACtC,kFAAkF;YAClF,MAAM,qBAEL,CAFK,IAAItG,sBACR,CAAC,gTAAgT,CAAC,GAD9S,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;IAEA,IAAI,OAAOsG,YAAY,UAAU;QAC/B,sDAAsD;QACtD,sDAAsD;QACtD,YAAY;QACZ,IAAIA,YAAY,SAAS;YACvBhE,UAAUiE,kBAAkB,GAAG;QACjC,OAAO,IAAID,YAAY,iBAAiB;YACtChE,UAAUkE,YAAY,GAAG;YAEzB,0DAA0D;YAC1D,IAAIlE,UAAUmE,kBAAkB,IAAI,CAACpE,aAAaqE,iBAAiB,EAAE;gBACnE,wEAAwE;gBACxE,0CAA0C;gBAC1C,MAAMC,MAAM,qBAEX,CAFW,IAAIvD,mBACd,CAAC,qEAAqE,CAAC,GAD7D,qBAAA;2BAAA;gCAAA;kCAAA;gBAEZ;gBACAd,UAAUsE,uBAAuB,GAAGD,IAAIE,OAAO;gBAC/CvE,UAAUwE,iBAAiB,GAAGH,IAAII,KAAK;gBACvC,MAAMJ;YACR;QACF,OAAO;YACLrE,UAAUiE,kBAAkB,GAAG;YAC/BjE,UAAU0E,WAAW,GAAGV,YAAY;QACtC;IACF;IAEA,IAAI,QAAOZ,mCAAAA,gBAAiBuB,UAAU,MAAK,UAAU;QACnD3E,UAAU2E,UAAU,GAAGvB,mCAAAA,gBAAiBuB,UAAU;IACpD;IAEA,IAAI,QAAOvB,mCAAAA,gBAAiBwB,UAAU,MAAK,aAAa;QACtDtH,mBAAmB8F,mCAAAA,gBAAiBwB,UAAU,EAAE5E,UAAU6E,KAAK;IACjE;IAEA,IAAI,QAAOzB,mCAAAA,gBAAiBwB,UAAU,MAAK,UAAU;QACnD,MAAME,oBAAoB1B,gBAAgBwB,UAAU;QAEpD,MAAMG,gBAAgBpH,qBAAqBqH,QAAQ;QAEnD,IAAID,eAAe;YACjB,OAAQA,cAAcE,IAAI;gBACxB,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBACH,IAAIF,cAAcH,UAAU,GAAGE,mBAAmB;wBAChDC,cAAcH,UAAU,GAAGE;oBAC7B;oBACA;gBACF,KAAK;oBAEH;gBACF,sDAAsD;gBACtD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBACH;gBACF;oBACEC;YACJ;QACF;QAEA,IACE,CAAC/E,UAAU0E,WAAW,IACtB1E,UAAUmE,kBAAkB,IAC5BW,sBAAsB,KACtB,wEAAwE;QACxE,0CAA0C;QAC1C,CAAC/E,aAAaqE,iBAAiB,EAC/B;YACA,MAAME,0BAA0B,CAAC,yBAAyB,EAAEjD,SAAS;YACrErB,UAAUsE,uBAAuB,GAAGA;YAEpC,MAAM,qBAA+C,CAA/C,IAAIxD,mBAAmBwD,0BAAvB,qBAAA;uBAAA;4BAAA;8BAAA;YAA8C;QACtD;IACF;IAEA,MAAMH,qBAAqBnE,UAAUmE,kBAAkB;IAEvD,0EAA0E;IAC1E,2EAA2E;IAC3E,wEAAwE;IACxE,8CAA8C;IAC9C,EAAE;IACF,4EAA4E;IAC5E,4EAA4E;IAC5E,4EAA4E;IAC5E,0EAA0E;IAC1E,uDAAuD;IACvD,EAAE;IACF,8EAA8E;IAC9E,qBAAqB;IACrB,MAAMe,4BACJf,sBAAsBpE,aAAaqE,iBAAiB,KAAK;IAE3D,MAAMe,eAAqD/B,kBACvDnG,eAAemG,mBACfgC;IAEJ;;GAEC,GACD,IAAIC,iBAAiBF;IAErB,IAAIG,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,MAAM,EAAEC,kBAAkB,EAAE,GAC1BC,QAAQ;QACV,IACE,OAAOL,mBAAmB,eAC1B,CAACI,mBAAmBJ,iBACpB;YACA7G,0BAA0BC,UAAU4E,WAAW;QACjD;QAEA,IACE,OAAOV,mBAAmB,eAC1B,CAAC8C,mBAAmB9C,iBACpB;YACAnE,0BAA0BC,UAAU;QACtC;QAEA,IAAI,OAAOqE,YAAY,eAAe,CAAC2C,mBAAmB3C,UAAU;YAClEtE,0BAA0BC,UAAU;QACtC;QAEA,IAAI,OAAOiF,aAAa,eAAe,CAAC+B,mBAAmB/B,WAAW;YACpElF,0BAA0BC,UAAU;QACtC;QAEA,IAAI,OAAOmF,cAAc,eAAe,CAAC6B,mBAAmB7B,YAAY;YACtEpF,0BAA0BC,UAAU;QACtC;QAEA,IACE,OAAOqF,iBAAiB,eACxB,CAAC2B,mBAAmB3B,eACpB;YACAtF,0BAA0BC,UAAU;QACtC;IACF;IAEA,iCAAiC;IACjC,MAAMkH,eAAe3E,2BAA2BK;IAEhD,6DAA6D;IAC7D,IAAIuE,gBAAwB5G;IAC5B,IAAI2G,gBAAgBA,aAAaE,KAAK,KAAK,MAAM;QAC/CD,gBAAgB;YACd,GAAG5G,YAAY;YACf,CAAC2G,aAAaG,KAAK,CAAC,EAAEH,aAAaE,KAAK;QAC1C;IACF;IAEA,4BAA4B;IAC5B,MAAME,gBAAgBJ,eAAeA,aAAaK,WAAW,GAAG3E;IAChE,MAAM4E,uBACJX,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzBjG,IAAIM,UAAU,CAACqG,sBAAsB;IACvC,MAAMC,MACJ,AAACb,CAAAA,QAAQC,GAAG,CAACa,YAAY,KAAK,SAC1Bd,QAAQC,GAAG,CAACc,uBAAuB,GACnC9G,IAAIM,UAAU,CAACsG,GAAG,AAAD,KAAM;IAE7B,8DAA8D;IAC9D,MAAMG,iBAAiB3G,wCACrB,KAACA,6CAED,KAAC4G;QAAeC,OAAOlH;;IAGzB,MAAM,CAACmH,iBAAiBC,iBAAiB,GACvC,MAAMC,gCAAgC;QACpCpH;QACAqH,gBAAgB;QAChBC,WAAWnD;QACXoD,QAAQnD;QACR5E;IACF;IAEF,MAAM,CAACgI,iBAAiB,GAAG,MAAMJ,gCAAgC;QAC/DpH;QACAqH,gBAAgB;QAChBC,WAAWjD;QACXkD,QAAQjD;QACR9E;IACF;IAEA,MAAM,CAACiI,oBAAoB,GAAG,MAAML,gCAAgC;QAClEpH;QACAqH,gBAAgB;QAChBC,WAAW/C;QACXgD,QAAQ/C;QACRhF;IACF;IAEA,8EAA8E;IAC9E,kBAAkB;IAClB,MAAMkI,mBAAmB,MAAMC,QAAQC,GAAG,CACxCC,OAAOC,IAAI,CAAC9F,gBAAgB+F,GAAG,CAC7B,OACEC;QAEA,MAAMC,qBAAqBD,qBAAqB;QAChD,MAAME,gBAAgBlG,cAAc,CAACgG,iBAAiB;QAEtD,MAAMG,oBAAoBF,qBACtBf,kBACArB;QAEJ,MAAMuC,qBAAqBH,qBACvBT,mBACA3B;QAEJ,MAAMwC,wBAAwBJ,qBAC1BR,sBACA5B;QAEJ,yEAAyE;QACzE,gDAAgD;QAChD,wEAAwE;QACxE,IAAIyC,yBAAmD;QAEvD,IACE,gEAAgE;QAChE,mEAAmE;QACnE,8DAA8D;QAC9D,qEAAqE;QACrE,sEAAsE;QACtE,sEAAsE;QACtE,gEAAgE;QAChE,+BAA+B;QAC/B,EAAE;QACF,yDAAyD;QACzD,2BAA2B;QAC3B5G,cACC6B,CAAAA,WAAW,CAACzF,0BAA0BoK,cAAa,KACpD,kEAAkE;QAClE,yDAAyD;QACzD,EAAE;QACF,mEAAmE;QACnE,oEAAoE;QACpE,sEAAsE;QACtE,gEAAgE;QAChE,0BAA0B;QAC1B,EAAE;QACF,qEAAqE;QACrE,gEAAgE;QAChE,mEAAmE;QACnE,6DAA6D;QAC7D,+DAA+D;QAC/D,sEAAsE;QACtE,kEAAkE;QAClE,kBAAkB;QAClB,CAAC1H,aAAaqE,iBAAiB,EAC/B;QACA,mEAAmE;QACnE,iBAAiB;QACnB,OAAO;YACL,6BAA6B;YAE7B,IAAIkB,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBAAiBhG,cAAc;oBAKxDsI;gBAJF,2FAA2F;gBAC3F,qEAAqE;gBACrE,MAAMA,aAAa5K,gBAAgBuK;gBACnC,KACEK,6BAAAA,WAAW1G,cAAc,qBAAzB0G,2BAA2BC,QAAQ,CAACxK,8BACpC;oBACAiC,aAAawI,GAAG,CAACT;gBACnB;YACF;YAEA,MAAMU,WAAW,MAAM1J,4BACrB;gBACEO,YAAY2I;gBACZzI,cAAc4G;gBACd3G,oBAAoBwE;gBACpBvE,aAAa6C;gBACb5C,YAAY8C;gBACZ7C,yBAAyB8C;gBACzB,4IAA4I;gBAC5I,8CAA8C;gBAC9C5C,kBAAkBkI,qBACdlI,mBACA,IAAM4H,QAAQgB,OAAO;gBACzB7I,kBAAkBmI,qBACdnI,mBACA,IAAM6H,QAAQgB,OAAO;gBACzB3I;gBACAC;gBACAC;gBACAC;gBACA,2HAA2H;gBAC3H,8CAA8C;gBAC9CC,yBAAyB6H,qBACrB7H,0BACA;YACN,GACA;YAGFkI,yBAAyBI;QAC3B;QAEA,MAAME,6BACJ,KAAC9F;sBACC,cAAA,KAAChC;;QAIL,MAAM+H,mBAAmBnK,wBAAwBc,MAAMoH,KAAK;QAC5D,MAAMkC,gBAAgBpK,wBAAwBc,MAAMoH,KAAK;QACzD,MAAMmC,kBAAkBrK,wBAAwBc,MAAMoH,KAAK;QAC3D,MAAMoC,sBAAsB3I,SACxB3B,wBAAwBc,MAAMoH,KAAK,kBACnCf;QAEJ,MAAMoD,qBACJvC,wBAAwBoC,8BACtB,KAACnI;YAAgB+E,MAAK;YAAQxG,UAAU4J;sBACrCzF;aAGHA;QAGJ,wFAAwF;QACxF,oCAAoC;QACpC,0BAA0B;QAC1B,MAAM6F,iBAAiB1K;QACvB,MAAM2K,wBAAwBzC,qCAC5B;;gBACGS,kCACC,KAACxG;oBACC+E,MAAM,GAAGnH,gBAAgB,SAAS,CAAC;oBACnCW,UAAUiI,mBAAmB+B;;gBAGhCH,iCACC,KAACpI;oBACC+E,MAAM,GAAGnH,gBAAgB,OAAO,CAAC;oBACjCW,UAAU6J,kBAAkBG;;gBAG/BJ,+BACC,KAACnI;oBACC+E,MAAM,GAAGnH,gBAAgB,KAAK,CAAC;oBAC/BW,UAAU4J,gBAAgBI;;gBAI7BF,qCACC,KAACrI;oBACC+E,MAAM,GAAGnH,gBAAgB,YAAY,CAAC;oBACtCW,UACEP,wBAAwBqK,uBACpB,GAAGvK,eAAe,eAAe,EAAEyK,gBAAgB,GACnDF;;;aAMV;QAEJ,OAAO;YACLhB;0BACA,KAACnH;gBACCuI,mBAAmBpB;gBACnB,sKAAsK;gBACtK7F,OAAOiB;gBACPC,aAAa4F;gBACb3F,cAAcA;gBACdpB,UACE,8DAA8D;gBAC9DwE,wBAAwBmC,iCACtB,KAAClI;oBAAgB+E,MAAK;oBAAWxG,UAAU2J;8BACxCD;qBAGHA;gBAGJ7F,gBAAgBA;gBAChBC,iBAAiBA;gBACjBX,UAAU8F;gBACV7F,WAAW8F;gBACX7F,cAAc8F;gBACb,GAAI3B,wBAAwB;oBAAEyC;gBAAsB,CAAC;;YAExDb;SACD;IACH;IAIJ,uFAAuF;IACvF,IAAIe,qBAAyD,CAAC;IAC9D,IAAIC,iCAEA,CAAC;IACL,KAAK,MAAMpB,iBAAiBR,iBAAkB;QAC5C,MAAM,CAACM,kBAAkBuB,mBAAmBC,WAAW,GAAGtB;QAC1DmB,kBAAkB,CAACrB,iBAAiB,GAAGuB;QACvCD,8BAA8B,CAACtB,iBAAiB,GAAGwB;IACrD;IAEA,IAAIC,iBAAiBlG,wBAAU,KAACA,aAAY,OAAS;IACrD,MAAMwF,kBAAkBrK,wBAAwBc,MAAMoH,KAAK;IAC3D,IAAIF,wBAAwB+C,gBAAgB;QAC1C,IAAIV,iBAAiB;YACnBU,+BACE,KAAC9I;gBAEC+E,MAAK;gBACLxG,UAAU6J;0BAETU;eAJInK,eAAe;QAO1B;IACF;IAEA,MAAMoK,cAAiCD,iBACnC;QAACA;QAAgBjG;QAAeC;KAAe,GAC/C;IAEJ,wIAAwI;IACxI,IAAI,CAACqC,gBAAgB;QACnB,OAAO;YACLU;0BACA,MAAClJ,MAAM6F,QAAQ;;oBACZP;oBACAyG,mBAAmBM,QAAQ;;eAFTrK;YAIrBgK;YACAI;YACA/D;SACD;IACH;IAEA,MAAM2B,YAAYxB;IAClB,0EAA0E;IAC1E,8EAA8E;IAC9E,4EAA4E;IAC5E,gBAAgB;IAChB,6FAA6F;IAC7F,mGAAmG;IACnG,gGAAgG;IAChG,2GAA2G;IAC3G,yGAAyG;IACzG,uGAAuG;IACvG,qBAAqB;IACrB,IACErF,UAAUmE,kBAAkB,IAC5BnE,UAAUkE,YAAY,IACtBnE,aAAaqE,iBAAiB,EAC9B;QACA,OAAO;YACL2B;0BACA,MAAClJ,MAAM6F,QAAQ;;kCACb,KAAC3B;wBACCoI,QAAO;wBACPtE,OAAO7E,UAAU6E,KAAK;;oBAEvB1C;;eALkBtD;YAOrBgK;YACAI;YACA;SACD;IACH;IAEA,MAAMG,oBAAoBtM,kBAAkBsG;IAE5C,IACEkC,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB,YAAYoD,oBACZ;QACA,6EAA6E;QAC7ES,QAAQ3H,KAAK,CACX,CAAC,uGAAuG,EAAEL,SAAS;IAEvH;IAEA,IAAI6B,QAAQ;QACV,MAAMoG,gBAAgBzC;QAEtB,iDAAiD;QACjD,IAAI0C;QACJ,IAAIH,mBAAmB;YACrB,IAAIjF,oBAAoB;gBACtB,MAAMqF,kBACJ5I,sCAAsCgF;gBACxC,MAAM6D,wBACJ/I,yCAAyCV;gBAC3CuJ,4BACE,KAAChJ;oBACCsG,WAAWyC;oBACXI,cAAcxI;oBACdyI,QAAQ/D;oBACRgE,UAAU;wBAACH;wBAAuBD;qBAAgB;;YAGxD,OAAO;gBACLD,4BACE,KAAChJ;oBACCsG,WAAWyC;oBACXI,cAAcxI;oBACdyI,QAAQ/D;;YAGd;QACF,OAAO;YACL,uEAAuE;YACvE,wEAAwE;YACxE,MAAM+D,SAAShJ,mCACbiF,eACA5F;YAGF,uEAAuE;YACvE,uEAAuE;YACvE,SAAS;YACT,IAAI0J,eAAejJ,sCAAsCS,OAAOlB;YAEhE,IAAIjD,mBAAmBuM,gBAAgB;gBACrC,MAAMO,wBACJP;gBAEFC,4BACE,KAACM;oBACCF,QAAQA;oBACRD,cAAcA;oBACdI,iBAAiB;;YAGvB,OAAO;gBACLP,4BACE,KAACD;oBAAcK,QAAQA;oBAAQD,cAAcA;;YAEjD;QACF;QAEA,MAAMK,mBAAmB1I,YAAYxD;QACrC,MAAMmM,eACJ/L,wBAAwBc,MAAMoH,KAAK,WACnClI,wBAAwBc,MAAMoH,KAAK;QACrC,MAAM8D,cAAcF,mBAAmB,YAAY;QACnD,MAAMG,qBACJjE,wBAAwB+D,6BACtB,KAAC9J;YAEC+E,MAAMgF;YACNxL,UAAUuL;sBAETT;WAJI1K,eAAe,MAAMoL,eAO5BV;QAGJ,OAAO;YACLxD;0BACA,MAAClJ,MAAM6F,QAAQ;;oBACZwH;oBACA/H;kCACD,MAAC7B;;0CACC,KAACiG;gCAAeC,OAAOnH;;4BACtBiH;;;;eALgBzH;YAQrBgK;YACAI;YACA/D;SACD;IACH,OAAO;QACL,MAAMiF,mBAAmBtD;QACzB,MAAMuD,oDACJ5G,yBACA,cAAcjC,kBACd6F,OAAOC,IAAI,CAAC9F,gBAAgB8I,MAAM,GAAG;QAEvC,IAAIC;QAEJ,IAAIlB,mBAAmB;YACrB,IAAImB;YAEJ,IAAIpG,oBAAoB;gBACtB,MAAMqF,kBACJ5I,sCAAsCgF;gBAExC2E,8BACE,KAAC/J;oBACCqG,WAAWsD;oBACXK,OAAO5B;oBACPe,QAAQ/D;oBACR6E,SAASjB;;YAGf,OAAO;gBACLe,8BACE,KAAC/J;oBACCqG,WAAWsD;oBACXK,OAAO5B;oBACPe,QAAQ/D;;YAGd;YAEA,IAAIwE,mDAAmD;gBACrD,IAAIM;gBACJ,IAAIC;gBACJ,IAAIC;gBACJ,gGAAgG;gBAChG,uFAAuF;gBACvF,4GAA4G;gBAC5G,gHAAgH;gBAChH,mCAAmC;gBACnCF,wBAAwBG,qCAAqC;oBAC3DC,wBAAwBpH;oBACxBqH,cAActE;oBACdjG;oBACA2B;oBACAgI;oBACAvE;gBACF;gBACA+E,yBAAyBE,qCAAqC;oBAC5DC,wBAAwBlH;oBACxBmH,cAAchE;oBACdvG;oBACA2B;oBACAgI;oBACAvE;gBACF;gBACAgF,4BAA4BC,qCAAqC;oBAC/DC,wBAAwBhH;oBACxBiH,cAAc/D;oBACdxG;oBACA2B;oBACAgI;oBACAvE;gBACF;gBACA,IACE8E,yBACAC,0BACAC,2BACA;oBACAN,4BACE,MAACnK;wBAECyB,UAAU8I;wBACV7I,WAAW8I;wBACX7I,cAAc8I;;4BAEbzI;4BACAoI;;uBANI1L;gBASX,OAAO;oBACLyL,4BACE,MAACzN,MAAM6F,QAAQ;;4BACZP;4BACAoI;;uBAFkB1L;gBAKzB;YACF,OAAO;gBACLyL,4BACE,MAACzN,MAAM6F,QAAQ;;wBACZP;wBACAoI;;mBAFkB1L;YAKzB;QACF,OAAO;YACL,MAAM8K,SAAShJ,mCACbiF,eACA5F;YAGF,IAAIgL;YAEJ,IAAIjO,mBAAmBoN,mBAAmB;gBACxC,MAAMc,0BACJd;gBAEFa,8BACE,KAACC;oBACE,GAAGrC,kBAAkB;oBACtBe,QAAQA;oBACRuB,mBAAmB;;YAGzB,OAAO;gBACLF,8BACE,KAACb;oBAAkB,GAAGvB,kBAAkB;oBAAEe,QAAQA;;YAEtD;YAEA,IAAIS,mDAAmD;gBACrD,gGAAgG;gBAChG,uFAAuF;gBACvF,4GAA4G;gBAC5G,gHAAgH;gBAChH,mCAAmC;gBACnCE,4BACE,MAACnK;oBAECyB,UACE6E,gCACE;;4BACGtE;0CACD,MAACgI;gCAAiBR,QAAQA;;oCACvBhG;oCACA8C;;;;yBAGHrB;;wBAGLjD;wBACA6I;;mBAdInM;YAiBX,OAAO;gBACLyL,4BACE,MAACzN,MAAM6F,QAAQ;;wBACZP;wBACA6I;;mBAFkBnM;YAKzB;QACF;QAEA,MAAMsM,iBAAiBlN,wBAAwBc,MAAMoH,KAAK;QAC1D,MAAMiF,qBACJnF,wBAAwBkF,+BACtB,KAACjL;YAA6B+E,MAAK;YAASxG,UAAU0M;sBACnDb;WADkB,YAIrBA;QAGJ,2CAA2C;QAC3C,OAAO;YACLvE;YACAqF;YACAvC;YACAI;YACA/D;SACD;IACH;AACF;AAEA,eAAeqB,eAAe,EAC5BC,KAAK,EAGN;IACC,MAAM6E,IAAI7E;IACV,+FAA+F;IAC/F,IAAI6E,EAAEC,MAAM,KAAK,YAAY;QAC3B,MAAMD,EAAExF,KAAK;IACf,OAAO,IAAIwF,EAAEC,MAAM,KAAK,aAAa;QACnC,MAAMD;IACR;IACA,OAAO;AACT;AACA9E,eAAegF,WAAW,GAAG3N;AAE7B,SAASiN,qCAAqC,EAC5CC,sBAAsB,EACtBC,YAAY,EACZvK,iBAAiB,EACjB2B,WAAW,EACXgI,gBAAgB,EAChBvE,aAAa,EAQd;IACC,IAAIkF,wBAAwB;QAC1B,MAAMU,6BAA6B;YACjCtC,UAAU6B;QACZ;QACA,qBACE;;gBACG5I;8BACD,KAAC3B;oBACCqG,WAAWsD;oBACXK,OAAOgB;oBACP7B,QAAQ/D;;;;IAIhB;IACA,OAAO;AACT;AAEA,OAAO,SAAS6F,cACd3M,UAAsB,EACtBkC,0BAAsD;IAEtD,OAAO0K,kBAAkB,CAAC,GAAG5M,YAAYkC;AAC3C;AAEA,SAAS0K,kBACP1M,YAAoB,EACpBF,UAAsB,EACtBkC,0BAAsD;IAEtD,MAAM,EACJK,OAAO,EACPC,SAAS,EAAEE,MAAM,EAAE,EACnBD,cAAc,EACf,GAAGrE,gBAAgB4B;IAEpB,MAAM6G,eAAe3E,2BAA2BK;IAEhD,IAAIuE,gBAAwB5G;IAC5B,IAAI2G,gBAAgBA,aAAaE,KAAK,KAAK,MAAM;QAC/CD,gBAAgB;YACd,GAAG5G,YAAY;YACf,CAAC2G,aAAaG,KAAK,CAAC,EAAEH,aAAaE,KAAK;QAC1C;IACF;IAEA,MAAM8F,eAAe,OAAOnK,WAAW;IAEvC,IAAImK,cAAc;QAChB,OAAO/F;IACT,OAAO,IAAI,CAACrE,eAAe2H,QAAQ,EAAE;QACnC,4EAA4E;QAC5E,yEAAyE;QACzE,yEAAyE;QACzE,6BAA6B;QAC7B,wFAAwF;QACxF,OAAOtD;IACT,OAAO;QACL,OAAO8F,kBACL9F,eACA,qEAAqE;QACrE,8EAA8E;QAC9E,+EAA+E;QAC/E,+CAA+C;QAC/CrE,eAAe2H,QAAQ,EACvBlI;IAEJ;AACF;AAEA,eAAe2F,gCAAgC,EAC7CpH,GAAG,EACHqH,cAAc,EACdC,SAAS,EACTC,MAAM,EACN/H,IAAI,EAYL;IACC,MAAMkH,uBACJX,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzBjG,IAAIM,UAAU,CAACqG,sBAAsB;IACvC,MAAMC,MACJ,AAACb,CAAAA,QAAQC,GAAG,CAACa,YAAY,KAAK,SAC1Bd,QAAQC,GAAG,CAACc,uBAAuB,GACnC9G,IAAIM,UAAU,CAACsG,GAAG,AAAD,KAAM;IAC7B,MAAM,EAAEjG,eAAe,EAAE,GAAGX,IAAIU,YAAY;IAC5C,MAAM2L,UAAU/E,0BACd;;0BACE,KAACA;YACAC;;SAED1B;IAEJ,MAAM3G,WAAWR,wBAAwBc,MAAMoH,KAAKS;IAEpD,MAAMiF,iBACJ5F,wBAAwB2F,wBACtB,KAAC1L;QAEC+E,MAAM2B;QACNnI,UAAUA;kBAETmN;OAJI/M,eAAe,MAAM+H,kBAO5BgF;IAGJ,OAAO;QAACC;QAAgBpN;KAAS;AACnC", "ignoreList": [0]}