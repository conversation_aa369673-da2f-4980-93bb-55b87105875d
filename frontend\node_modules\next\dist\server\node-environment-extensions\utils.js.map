{"version": 3, "sources": ["../../../src/server/node-environment-extensions/utils.tsx"], "sourcesContent": ["import { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\nimport {\n  abortOnSynchronousPlatformIOAccess,\n  trackSynchronousPlatformIOAccessInDev,\n} from '../app-render/dynamic-rendering'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\ntype ApiType = 'time' | 'random' | 'crypto'\n\nexport function io(expression: string, type: ApiType) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  const workStore = workAsyncStorage.getStore()\n\n  if (!workUnitStore || !workStore) {\n    return\n  }\n\n  switch (workUnitStore.type) {\n    case 'prerender':\n    case 'prerender-runtime': {\n      const prerenderSignal = workUnitStore.controller.signal\n\n      if (prerenderSignal.aborted === false) {\n        // If the prerender signal is already aborted we don't need to construct\n        // any stacks because something else actually terminated the prerender.\n        let message: string\n        switch (type) {\n          case 'time':\n            message = `Route \"${workStore.route}\" used ${expression} instead of using \\`performance\\` or without explicitly calling \\`await connection()\\` beforehand. See more info here: https://nextjs.org/docs/messages/next-prerender-current-time`\n            break\n          case 'random':\n            message = `Route \"${workStore.route}\" used ${expression} outside of \\`\"use cache\"\\` and without explicitly calling \\`await connection()\\` beforehand. See more info here: https://nextjs.org/docs/messages/next-prerender-random`\n            break\n          case 'crypto':\n            message = `Route \"${workStore.route}\" used ${expression} outside of \\`\"use cache\"\\` and without explicitly calling \\`await connection()\\` beforehand. See more info here: https://nextjs.org/docs/messages/next-prerender-crypto`\n            break\n          default:\n            throw new InvariantError(\n              'Unknown expression type in abortOnSynchronousPlatformIOAccess.'\n            )\n        }\n\n        abortOnSynchronousPlatformIOAccess(\n          workStore.route,\n          expression,\n          applyOwnerStack(new Error(message), workUnitStore),\n          workUnitStore\n        )\n      }\n      break\n    }\n    case 'prerender-client': {\n      const prerenderSignal = workUnitStore.controller.signal\n\n      if (prerenderSignal.aborted === false) {\n        // If the prerender signal is already aborted we don't need to construct\n        // any stacks because something else actually terminated the prerender.\n        let message: string\n        switch (type) {\n          case 'time':\n            message = `Route \"${workStore.route}\" used ${expression} inside a Client Component without a Suspense boundary above it. See more info here: https://nextjs.org/docs/messages/next-prerender-current-time-client`\n            break\n          case 'random':\n            message = `Route \"${workStore.route}\" used ${expression} inside a Client Component without a Suspense boundary above it. See more info here: https://nextjs.org/docs/messages/next-prerender-random-client`\n            break\n          case 'crypto':\n            message = `Route \"${workStore.route}\" used ${expression} inside a Client Component without a Suspense boundary above it. See more info here: https://nextjs.org/docs/messages/next-prerender-crypto-client`\n            break\n          default:\n            throw new InvariantError(\n              'Unknown expression type in abortOnSynchronousPlatformIOAccess.'\n            )\n        }\n\n        abortOnSynchronousPlatformIOAccess(\n          workStore.route,\n          expression,\n          applyOwnerStack(new Error(message), workUnitStore),\n          workUnitStore\n        )\n      }\n      break\n    }\n    case 'request':\n      if (workUnitStore.prerenderPhase === true) {\n        trackSynchronousPlatformIOAccessInDev(workUnitStore)\n      }\n      break\n    case 'prerender-ppr':\n    case 'prerender-legacy':\n    case 'cache':\n    case 'private-cache':\n    case 'unstable-cache':\n      break\n    default:\n      workUnitStore satisfies never\n  }\n}\n\nfunction applyOwnerStack(error: Error, workUnitStore: PrerenderStoreModern) {\n  // TODO: Instead of stitching the stacks here, we should log the original\n  // error as-is when it occurs, and let `patchErrorInspect` handle adding the\n  // owner stack, instead of logging it deferred in the `LogSafely` component\n  // via `throwIfDisallowedDynamic`.\n  if (\n    process.env.NODE_ENV !== 'production' &&\n    workUnitStore.captureOwnerStack\n  ) {\n    const ownerStack = workUnitStore.captureOwnerStack()\n\n    if (ownerStack) {\n      let stack = ownerStack\n\n      if (error.stack) {\n        const frames: string[] = []\n\n        for (const frame of error.stack.split('\\n').slice(1)) {\n          if (frame.includes('react_stack_bottom_frame')) {\n            break\n          }\n\n          frames.push(frame)\n        }\n\n        stack = '\\n' + frames.join('\\n') + stack\n      }\n\n      error.stack = error.name + ': ' + error.message + stack\n    }\n  }\n\n  return error\n}\n"], "names": ["io", "expression", "type", "workUnitStore", "workUnitAsyncStorage", "getStore", "workStore", "workAsyncStorage", "prerenderSignal", "controller", "signal", "aborted", "message", "route", "InvariantError", "abortOnSynchronousPlatformIOAccess", "applyOwnerStack", "Error", "prerenderPhase", "trackSynchronousPlatformIOAccessInDev", "error", "process", "env", "NODE_ENV", "captureOwnerStack", "ownerStack", "stack", "frames", "frame", "split", "slice", "includes", "push", "join", "name"], "mappings": ";;;;+BAagBA;;;eAAAA;;;0CAbiB;8CAI1B;kCAIA;gCACwB;AAIxB,SAASA,GAAGC,UAAkB,EAAEC,IAAa;IAClD,MAAMC,gBAAgBC,kDAAoB,CAACC,QAAQ;IACnD,MAAMC,YAAYC,0CAAgB,CAACF,QAAQ;IAE3C,IAAI,CAACF,iBAAiB,CAACG,WAAW;QAChC;IACF;IAEA,OAAQH,cAAcD,IAAI;QACxB,KAAK;QACL,KAAK;YAAqB;gBACxB,MAAMM,kBAAkBL,cAAcM,UAAU,CAACC,MAAM;gBAEvD,IAAIF,gBAAgBG,OAAO,KAAK,OAAO;oBACrC,wEAAwE;oBACxE,uEAAuE;oBACvE,IAAIC;oBACJ,OAAQV;wBACN,KAAK;4BACHU,UAAU,CAAC,OAAO,EAAEN,UAAUO,KAAK,CAAC,OAAO,EAAEZ,WAAW,mLAAmL,CAAC;4BAC5O;wBACF,KAAK;4BACHW,UAAU,CAAC,OAAO,EAAEN,UAAUO,KAAK,CAAC,OAAO,EAAEZ,WAAW,wKAAwK,CAAC;4BACjO;wBACF,KAAK;4BACHW,UAAU,CAAC,OAAO,EAAEN,UAAUO,KAAK,CAAC,OAAO,EAAEZ,WAAW,wKAAwK,CAAC;4BACjO;wBACF;4BACE,MAAM,qBAEL,CAFK,IAAIa,8BAAc,CACtB,mEADI,qBAAA;uCAAA;4CAAA;8CAAA;4BAEN;oBACJ;oBAEAC,IAAAA,oDAAkC,EAChCT,UAAUO,KAAK,EACfZ,YACAe,gBAAgB,qBAAkB,CAAlB,IAAIC,MAAML,UAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAiB,IAAGT,gBACpCA;gBAEJ;gBACA;YACF;QACA,KAAK;YAAoB;gBACvB,MAAMK,kBAAkBL,cAAcM,UAAU,CAACC,MAAM;gBAEvD,IAAIF,gBAAgBG,OAAO,KAAK,OAAO;oBACrC,wEAAwE;oBACxE,uEAAuE;oBACvE,IAAIC;oBACJ,OAAQV;wBACN,KAAK;4BACHU,UAAU,CAAC,OAAO,EAAEN,UAAUO,KAAK,CAAC,OAAO,EAAEZ,WAAW,wJAAwJ,CAAC;4BACjN;wBACF,KAAK;4BACHW,UAAU,CAAC,OAAO,EAAEN,UAAUO,KAAK,CAAC,OAAO,EAAEZ,WAAW,kJAAkJ,CAAC;4BAC3M;wBACF,KAAK;4BACHW,UAAU,CAAC,OAAO,EAAEN,UAAUO,KAAK,CAAC,OAAO,EAAEZ,WAAW,kJAAkJ,CAAC;4BAC3M;wBACF;4BACE,MAAM,qBAEL,CAFK,IAAIa,8BAAc,CACtB,mEADI,qBAAA;uCAAA;4CAAA;8CAAA;4BAEN;oBACJ;oBAEAC,IAAAA,oDAAkC,EAChCT,UAAUO,KAAK,EACfZ,YACAe,gBAAgB,qBAAkB,CAAlB,IAAIC,MAAML,UAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAiB,IAAGT,gBACpCA;gBAEJ;gBACA;YACF;QACA,KAAK;YACH,IAAIA,cAAce,cAAc,KAAK,MAAM;gBACzCC,IAAAA,uDAAqC,EAAChB;YACxC;YACA;QACF,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH;QACF;YACEA;IACJ;AACF;AAEA,SAASa,gBAAgBI,KAAY,EAAEjB,aAAmC;IACxE,yEAAyE;IACzE,4EAA4E;IAC5E,2EAA2E;IAC3E,kCAAkC;IAClC,IACEkB,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACzBpB,cAAcqB,iBAAiB,EAC/B;QACA,MAAMC,aAAatB,cAAcqB,iBAAiB;QAElD,IAAIC,YAAY;YACd,IAAIC,QAAQD;YAEZ,IAAIL,MAAMM,KAAK,EAAE;gBACf,MAAMC,SAAmB,EAAE;gBAE3B,KAAK,MAAMC,SAASR,MAAMM,KAAK,CAACG,KAAK,CAAC,MAAMC,KAAK,CAAC,GAAI;oBACpD,IAAIF,MAAMG,QAAQ,CAAC,6BAA6B;wBAC9C;oBACF;oBAEAJ,OAAOK,IAAI,CAACJ;gBACd;gBAEAF,QAAQ,OAAOC,OAAOM,IAAI,CAAC,QAAQP;YACrC;YAEAN,MAAMM,KAAK,GAAGN,MAAMc,IAAI,GAAG,OAAOd,MAAMR,OAAO,GAAGc;QACpD;IACF;IAEA,OAAON;AACT", "ignoreList": [0]}