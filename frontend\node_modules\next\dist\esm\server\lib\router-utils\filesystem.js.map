{"version": 3, "sources": ["../../../../src/server/lib/router-utils/filesystem.ts"], "sourcesContent": ["import type {\n  FunctionsConfigManifest,\n  ManifestRoute,\n  PrerenderManifest,\n  RoutesManifest,\n} from '../../../build'\nimport type { NextConfigComplete } from '../../config-shared'\nimport type { MiddlewareManifest } from '../../../build/webpack/plugins/middleware-plugin'\nimport type { UnwrapPromise } from '../../../lib/coalesced-function'\nimport type { PatchMatcher } from '../../../shared/lib/router/utils/path-match'\nimport type { MiddlewareRouteMatch } from '../../../shared/lib/router/utils/middleware-route-matcher'\n\nimport path from 'path'\nimport fs from 'fs/promises'\nimport * as Log from '../../../build/output/log'\nimport setupDebug from 'next/dist/compiled/debug'\nimport { LRUCache } from '../lru-cache'\nimport loadCustomRoutes, { type Rewrite } from '../../../lib/load-custom-routes'\nimport { modifyRouteRegex } from '../../../lib/redirect-status'\nimport { FileType, fileExists } from '../../../lib/file-exists'\nimport { recursiveReadDir } from '../../../lib/recursive-readdir'\nimport { isDynamicRoute } from '../../../shared/lib/router/utils'\nimport { escapeStringRegexp } from '../../../shared/lib/escape-regexp'\nimport { getPathMatch } from '../../../shared/lib/router/utils/path-match'\nimport { getRouteRegex } from '../../../shared/lib/router/utils/route-regex'\nimport { getRouteMatcher } from '../../../shared/lib/router/utils/route-matcher'\nimport { pathHasPrefix } from '../../../shared/lib/router/utils/path-has-prefix'\nimport { normalizeLocalePath } from '../../../shared/lib/i18n/normalize-locale-path'\nimport { removePathPrefix } from '../../../shared/lib/router/utils/remove-path-prefix'\nimport { getMiddlewareRouteMatcher } from '../../../shared/lib/router/utils/middleware-route-matcher'\nimport {\n  APP_PATH_ROUTES_MANIFEST,\n  BUILD_ID_FILE,\n  FUNCTIONS_CONFIG_MANIFEST,\n  MIDDLEWARE_MANIFEST,\n  PAGES_MANIFEST,\n  PRERENDER_MANIFEST,\n  ROUTES_MANIFEST,\n} from '../../../shared/lib/constants'\nimport { normalizePathSep } from '../../../shared/lib/page-path/normalize-path-sep'\nimport { normalizeMetadataRoute } from '../../../lib/metadata/get-metadata-route'\nimport { RSCPathnameNormalizer } from '../../normalizers/request/rsc'\nimport { PrefetchRSCPathnameNormalizer } from '../../normalizers/request/prefetch-rsc'\nimport { encodeURIPath } from '../../../shared/lib/encode-uri-path'\n\nexport type FsOutput = {\n  type:\n    | 'appFile'\n    | 'pageFile'\n    | 'nextImage'\n    | 'publicFolder'\n    | 'nextStaticFolder'\n    | 'legacyStaticFolder'\n    | 'devVirtualFsItem'\n\n  itemPath: string\n  fsPath?: string\n  itemsRoot?: string\n  locale?: string\n}\n\nconst debug = setupDebug('next:router-server:filesystem')\n\nexport type FilesystemDynamicRoute = ManifestRoute & {\n  /**\n   * The path matcher that can be used to match paths against this route.\n   */\n  match: PatchMatcher\n}\n\nexport const buildCustomRoute = <T>(\n  type: 'redirect' | 'header' | 'rewrite' | 'before_files_rewrite',\n  item: T & { source: string },\n  basePath?: string,\n  caseSensitive?: boolean\n): T & { match: PatchMatcher; check?: boolean; regex: string } => {\n  const restrictedRedirectPaths = ['/_next'].map((p) =>\n    basePath ? `${basePath}${p}` : p\n  )\n  let builtRegex = ''\n  const match = getPathMatch(item.source, {\n    strict: true,\n    removeUnnamedParams: true,\n    regexModifier: (regex: string) => {\n      if (!(item as any).internal) {\n        regex = modifyRouteRegex(\n          regex,\n          type === 'redirect' ? restrictedRedirectPaths : undefined\n        )\n      }\n      builtRegex = regex\n      return builtRegex\n    },\n    sensitive: caseSensitive,\n  })\n\n  return {\n    ...item,\n    regex: builtRegex,\n    ...(type === 'rewrite' ? { check: true } : {}),\n    match,\n  }\n}\n\nexport async function setupFsCheck(opts: {\n  dir: string\n  dev: boolean\n  minimalMode?: boolean\n  config: NextConfigComplete\n}) {\n  const getItemsLru = !opts.dev\n    ? new LRUCache<FsOutput | null>(1024 * 1024, function length(value) {\n        if (!value) return 0\n        return (\n          (value.fsPath || '').length +\n          value.itemPath.length +\n          value.type.length\n        )\n      })\n    : undefined\n\n  // routes that have _next/data endpoints (SSG/SSP)\n  const nextDataRoutes = new Set<string>()\n  const publicFolderItems = new Set<string>()\n  const nextStaticFolderItems = new Set<string>()\n  const legacyStaticFolderItems = new Set<string>()\n\n  const appFiles = new Set<string>()\n  const pageFiles = new Set<string>()\n  let dynamicRoutes: FilesystemDynamicRoute[] = []\n\n  let middlewareMatcher:\n    | ReturnType<typeof getMiddlewareRouteMatcher>\n    | undefined = () => false\n\n  const distDir = path.join(opts.dir, opts.config.distDir)\n  const publicFolderPath = path.join(opts.dir, 'public')\n  const nextStaticFolderPath = path.join(distDir, 'static')\n  const legacyStaticFolderPath = path.join(opts.dir, 'static')\n  let customRoutes: UnwrapPromise<ReturnType<typeof loadCustomRoutes>> = {\n    redirects: [],\n    rewrites: {\n      beforeFiles: [],\n      afterFiles: [],\n      fallback: [],\n    },\n    headers: [],\n  }\n  let buildId = 'development'\n  let prerenderManifest: PrerenderManifest\n\n  if (!opts.dev) {\n    const buildIdPath = path.join(opts.dir, opts.config.distDir, BUILD_ID_FILE)\n    try {\n      buildId = await fs.readFile(buildIdPath, 'utf8')\n    } catch (err: any) {\n      if (err.code !== 'ENOENT') throw err\n      throw new Error(\n        `Could not find a production build in the '${opts.config.distDir}' directory. Try building your app with 'next build' before starting the production server. https://nextjs.org/docs/messages/production-start-no-build-id`\n      )\n    }\n\n    try {\n      for (const file of await recursiveReadDir(publicFolderPath)) {\n        // Ensure filename is encoded and normalized.\n        publicFolderItems.add(encodeURIPath(normalizePathSep(file)))\n      }\n    } catch (err: any) {\n      if (err.code !== 'ENOENT') {\n        throw err\n      }\n    }\n\n    try {\n      for (const file of await recursiveReadDir(legacyStaticFolderPath)) {\n        // Ensure filename is encoded and normalized.\n        legacyStaticFolderItems.add(encodeURIPath(normalizePathSep(file)))\n      }\n      Log.warn(\n        `The static directory has been deprecated in favor of the public directory. https://nextjs.org/docs/messages/static-dir-deprecated`\n      )\n    } catch (err: any) {\n      if (err.code !== 'ENOENT') {\n        throw err\n      }\n    }\n\n    try {\n      for (const file of await recursiveReadDir(nextStaticFolderPath)) {\n        // Ensure filename is encoded and normalized.\n        nextStaticFolderItems.add(\n          path.posix.join(\n            '/_next/static',\n            encodeURIPath(normalizePathSep(file))\n          )\n        )\n      }\n    } catch (err) {\n      if (opts.config.output !== 'standalone') throw err\n    }\n\n    const routesManifestPath = path.join(distDir, ROUTES_MANIFEST)\n    const prerenderManifestPath = path.join(distDir, PRERENDER_MANIFEST)\n    const middlewareManifestPath = path.join(\n      distDir,\n      'server',\n      MIDDLEWARE_MANIFEST\n    )\n    const functionsConfigManifestPath = path.join(\n      distDir,\n      'server',\n      FUNCTIONS_CONFIG_MANIFEST\n    )\n    const pagesManifestPath = path.join(distDir, 'server', PAGES_MANIFEST)\n    const appRoutesManifestPath = path.join(distDir, APP_PATH_ROUTES_MANIFEST)\n\n    const routesManifest = JSON.parse(\n      await fs.readFile(routesManifestPath, 'utf8')\n    ) as RoutesManifest\n\n    prerenderManifest = JSON.parse(\n      await fs.readFile(prerenderManifestPath, 'utf8')\n    ) as PrerenderManifest\n\n    const middlewareManifest = JSON.parse(\n      await fs.readFile(middlewareManifestPath, 'utf8').catch(() => '{}')\n    ) as MiddlewareManifest\n\n    const functionsConfigManifest = JSON.parse(\n      await fs.readFile(functionsConfigManifestPath, 'utf8').catch(() => '{}')\n    ) as FunctionsConfigManifest\n\n    const pagesManifest = JSON.parse(\n      await fs.readFile(pagesManifestPath, 'utf8')\n    )\n    const appRoutesManifest = JSON.parse(\n      await fs.readFile(appRoutesManifestPath, 'utf8').catch(() => '{}')\n    )\n\n    for (const key of Object.keys(pagesManifest)) {\n      // ensure the non-locale version is in the set\n      if (opts.config.i18n) {\n        pageFiles.add(\n          normalizeLocalePath(key, opts.config.i18n.locales).pathname\n        )\n      } else {\n        pageFiles.add(key)\n      }\n    }\n    for (const key of Object.keys(appRoutesManifest)) {\n      appFiles.add(appRoutesManifest[key])\n    }\n\n    const escapedBuildId = escapeStringRegexp(buildId)\n\n    for (const route of routesManifest.dataRoutes) {\n      if (isDynamicRoute(route.page)) {\n        const routeRegex = getRouteRegex(route.page)\n        dynamicRoutes.push({\n          ...route,\n          regex: routeRegex.re.toString(),\n          match: getRouteMatcher({\n            // TODO: fix this in the manifest itself, must also be fixed in\n            // upstream builder that relies on this\n            re: opts.config.i18n\n              ? new RegExp(\n                  route.dataRouteRegex.replace(\n                    `/${escapedBuildId}/`,\n                    `/${escapedBuildId}/(?<nextLocale>[^/]+?)/`\n                  )\n                )\n              : new RegExp(route.dataRouteRegex),\n            groups: routeRegex.groups,\n          }),\n        })\n      }\n      nextDataRoutes.add(route.page)\n    }\n\n    for (const route of routesManifest.dynamicRoutes) {\n      // If a route is marked as skipInternalRouting, it's not for the internal\n      // router, and instead has been added to support external routers.\n      if (route.skipInternalRouting) {\n        continue\n      }\n\n      dynamicRoutes.push({\n        ...route,\n        match: getRouteMatcher(getRouteRegex(route.page)),\n      })\n    }\n\n    if (middlewareManifest.middleware?.['/']?.matchers) {\n      middlewareMatcher = getMiddlewareRouteMatcher(\n        middlewareManifest.middleware?.['/']?.matchers\n      )\n    } else if (functionsConfigManifest?.functions['/_middleware']) {\n      middlewareMatcher = getMiddlewareRouteMatcher(\n        functionsConfigManifest.functions['/_middleware'].matchers ?? [\n          { regexp: '.*', originalSource: '/:path*' },\n        ]\n      )\n    }\n\n    customRoutes = {\n      redirects: routesManifest.redirects,\n      rewrites: routesManifest.rewrites\n        ? Array.isArray(routesManifest.rewrites)\n          ? {\n              beforeFiles: [],\n              afterFiles: routesManifest.rewrites,\n              fallback: [],\n            }\n          : routesManifest.rewrites\n        : {\n            beforeFiles: [],\n            afterFiles: [],\n            fallback: [],\n          },\n      headers: routesManifest.headers,\n    }\n  } else {\n    // dev handling\n    customRoutes = await loadCustomRoutes(opts.config)\n\n    prerenderManifest = {\n      version: 4,\n      routes: {},\n      dynamicRoutes: {},\n      notFoundRoutes: [],\n      preview: {\n        previewModeId: (require('crypto') as typeof import('crypto'))\n          .randomBytes(16)\n          .toString('hex'),\n        previewModeSigningKey: (require('crypto') as typeof import('crypto'))\n          .randomBytes(32)\n          .toString('hex'),\n        previewModeEncryptionKey: (require('crypto') as typeof import('crypto'))\n          .randomBytes(32)\n          .toString('hex'),\n      },\n    }\n  }\n\n  const headers = customRoutes.headers.map((item) =>\n    buildCustomRoute(\n      'header',\n      item,\n      opts.config.basePath,\n      opts.config.experimental.caseSensitiveRoutes\n    )\n  )\n  const redirects = customRoutes.redirects.map((item) =>\n    buildCustomRoute(\n      'redirect',\n      item,\n      opts.config.basePath,\n      opts.config.experimental.caseSensitiveRoutes\n    )\n  )\n  const rewrites = {\n    beforeFiles: customRoutes.rewrites.beforeFiles.map((item) =>\n      buildCustomRoute('before_files_rewrite', item)\n    ),\n    afterFiles: customRoutes.rewrites.afterFiles.map((item) =>\n      buildCustomRoute(\n        'rewrite',\n        item,\n        opts.config.basePath,\n        opts.config.experimental.caseSensitiveRoutes\n      )\n    ),\n    fallback: customRoutes.rewrites.fallback.map((item) =>\n      buildCustomRoute(\n        'rewrite',\n        item,\n        opts.config.basePath,\n        opts.config.experimental.caseSensitiveRoutes\n      )\n    ),\n  }\n\n  const { i18n } = opts.config\n\n  const handleLocale = (pathname: string, locales?: string[]) => {\n    let locale: string | undefined\n\n    if (i18n) {\n      const i18nResult = normalizeLocalePath(pathname, locales || i18n.locales)\n\n      pathname = i18nResult.pathname\n      locale = i18nResult.detectedLocale\n    }\n    return { locale, pathname }\n  }\n\n  debug('nextDataRoutes', nextDataRoutes)\n  debug('dynamicRoutes', dynamicRoutes)\n  debug('customRoutes', customRoutes)\n  debug('publicFolderItems', publicFolderItems)\n  debug('nextStaticFolderItems', nextStaticFolderItems)\n  debug('pageFiles', pageFiles)\n  debug('appFiles', appFiles)\n\n  let ensureFn: (item: FsOutput) => Promise<void> | undefined\n\n  const normalizers = {\n    // Because we can't know if the app directory is enabled or not at this\n    // stage, we assume that it is.\n    rsc: new RSCPathnameNormalizer(),\n    prefetchRSC: opts.config.experimental.ppr\n      ? new PrefetchRSCPathnameNormalizer()\n      : undefined,\n  }\n\n  return {\n    headers,\n    rewrites,\n    redirects,\n\n    buildId,\n    handleLocale,\n\n    appFiles,\n    pageFiles,\n    dynamicRoutes,\n    nextDataRoutes,\n\n    exportPathMapRoutes: undefined as\n      | undefined\n      | ReturnType<typeof buildCustomRoute<Rewrite>>[],\n\n    devVirtualFsItems: new Set<string>(),\n\n    prerenderManifest,\n    middlewareMatcher: middlewareMatcher as MiddlewareRouteMatch | undefined,\n\n    ensureCallback(fn: typeof ensureFn) {\n      ensureFn = fn\n    },\n\n    async getItem(itemPath: string): Promise<FsOutput | null> {\n      const originalItemPath = itemPath\n      const itemKey = originalItemPath\n      const lruResult = getItemsLru?.get(itemKey)\n\n      if (lruResult) {\n        return lruResult\n      }\n\n      const { basePath } = opts.config\n\n      const hasBasePath = pathHasPrefix(itemPath, basePath)\n\n      // Return null if path doesn't start with basePath\n      if (basePath && !hasBasePath) {\n        return null\n      }\n\n      // Remove basePath if it exists.\n      if (basePath && hasBasePath) {\n        itemPath = removePathPrefix(itemPath, basePath) || '/'\n      }\n\n      // Simulate minimal mode requests by normalizing RSC and postponed\n      // requests.\n      if (opts.minimalMode) {\n        if (normalizers.prefetchRSC?.match(itemPath)) {\n          itemPath = normalizers.prefetchRSC.normalize(itemPath, true)\n        } else if (normalizers.rsc.match(itemPath)) {\n          itemPath = normalizers.rsc.normalize(itemPath, true)\n        }\n      }\n\n      if (itemPath !== '/' && itemPath.endsWith('/')) {\n        itemPath = itemPath.substring(0, itemPath.length - 1)\n      }\n\n      let decodedItemPath = itemPath\n\n      try {\n        decodedItemPath = decodeURIComponent(itemPath)\n      } catch {}\n\n      if (itemPath === '/_next/image') {\n        return {\n          itemPath,\n          type: 'nextImage',\n        }\n      }\n\n      const itemsToCheck: Array<[Set<string>, FsOutput['type']]> = [\n        [this.devVirtualFsItems, 'devVirtualFsItem'],\n        [nextStaticFolderItems, 'nextStaticFolder'],\n        [legacyStaticFolderItems, 'legacyStaticFolder'],\n        [publicFolderItems, 'publicFolder'],\n        [appFiles, 'appFile'],\n        [pageFiles, 'pageFile'],\n      ]\n\n      for (let [items, type] of itemsToCheck) {\n        let locale: string | undefined\n        let curItemPath = itemPath\n        let curDecodedItemPath = decodedItemPath\n\n        const isDynamicOutput = type === 'pageFile' || type === 'appFile'\n\n        if (i18n) {\n          const localeResult = handleLocale(\n            itemPath,\n            // legacy behavior allows visiting static assets under\n            // default locale but no other locale\n            isDynamicOutput\n              ? undefined\n              : [\n                  i18n?.defaultLocale,\n                  // default locales from domains need to be matched too\n                  ...(i18n.domains?.map((item) => item.defaultLocale) || []),\n                ]\n          )\n\n          if (localeResult.pathname !== curItemPath) {\n            curItemPath = localeResult.pathname\n            locale = localeResult.locale\n\n            try {\n              curDecodedItemPath = decodeURIComponent(curItemPath)\n            } catch {}\n          }\n        }\n\n        if (type === 'legacyStaticFolder') {\n          if (!pathHasPrefix(curItemPath, '/static')) {\n            continue\n          }\n          curItemPath = curItemPath.substring('/static'.length)\n\n          try {\n            curDecodedItemPath = decodeURIComponent(curItemPath)\n          } catch {}\n        }\n\n        if (\n          type === 'nextStaticFolder' &&\n          !pathHasPrefix(curItemPath, '/_next/static')\n        ) {\n          continue\n        }\n\n        const nextDataPrefix = `/_next/data/${buildId}/`\n\n        if (\n          type === 'pageFile' &&\n          curItemPath.startsWith(nextDataPrefix) &&\n          curItemPath.endsWith('.json')\n        ) {\n          items = nextDataRoutes\n          // remove _next/data/<build-id> prefix\n          curItemPath = curItemPath.substring(nextDataPrefix.length - 1)\n\n          // remove .json postfix\n          curItemPath = curItemPath.substring(\n            0,\n            curItemPath.length - '.json'.length\n          )\n          const curLocaleResult = handleLocale(curItemPath)\n          curItemPath =\n            curLocaleResult.pathname === '/index'\n              ? '/'\n              : curLocaleResult.pathname\n\n          locale = curLocaleResult.locale\n\n          try {\n            curDecodedItemPath = decodeURIComponent(curItemPath)\n          } catch {}\n        }\n\n        let matchedItem = items.has(curItemPath)\n\n        // check decoded variant as well\n        if (!matchedItem && !opts.dev) {\n          matchedItem = items.has(curDecodedItemPath)\n          if (matchedItem) curItemPath = curDecodedItemPath\n          else {\n            // x-ref: https://github.com/vercel/next.js/issues/54008\n            // There're cases that urls get decoded before requests, we should support both encoded and decoded ones.\n            // e.g. nginx could decode the proxy urls, the below ones should be treated as the same:\n            // decoded version: `/_next/static/chunks/pages/blog/[slug]-d4858831b91b69f6.js`\n            // encoded version: `/_next/static/chunks/pages/blog/%5Bslug%5D-d4858831b91b69f6.js`\n            try {\n              // encode the special characters in the path and retrieve again to determine if path exists.\n              const encodedCurItemPath = encodeURIPath(curItemPath)\n              matchedItem = items.has(encodedCurItemPath)\n            } catch {}\n          }\n        }\n\n        if (matchedItem || opts.dev) {\n          let fsPath: string | undefined\n          let itemsRoot: string | undefined\n\n          switch (type) {\n            case 'nextStaticFolder': {\n              itemsRoot = nextStaticFolderPath\n              curItemPath = curItemPath.substring('/_next/static'.length)\n              break\n            }\n            case 'legacyStaticFolder': {\n              itemsRoot = legacyStaticFolderPath\n              break\n            }\n            case 'publicFolder': {\n              itemsRoot = publicFolderPath\n              break\n            }\n            case 'appFile':\n            case 'pageFile':\n            case 'nextImage':\n            case 'devVirtualFsItem': {\n              break\n            }\n            default: {\n              ;(type) satisfies never\n            }\n          }\n\n          if (itemsRoot && curItemPath) {\n            fsPath = path.posix.join(itemsRoot, curItemPath)\n          }\n\n          // dynamically check fs in development so we don't\n          // have to wait on the watcher\n          if (!matchedItem && opts.dev) {\n            const isStaticAsset = (\n              [\n                'nextStaticFolder',\n                'publicFolder',\n                'legacyStaticFolder',\n              ] as (typeof type)[]\n            ).includes(type)\n\n            if (isStaticAsset && itemsRoot) {\n              let found = fsPath && (await fileExists(fsPath, FileType.File))\n\n              if (!found) {\n                try {\n                  // In dev, we ensure encoded paths match\n                  // decoded paths on the filesystem so check\n                  // that variation as well\n                  const tempItemPath = decodeURIComponent(curItemPath)\n                  fsPath = path.posix.join(itemsRoot, tempItemPath)\n                  found = await fileExists(fsPath, FileType.File)\n                } catch {}\n\n                if (!found) {\n                  continue\n                }\n              }\n            } else if (type === 'pageFile' || type === 'appFile') {\n              const isAppFile = type === 'appFile'\n\n              // Attempt to ensure the page/app file is compiled and ready\n              if (ensureFn) {\n                const ensureItemPath = isAppFile\n                  ? normalizeMetadataRoute(curItemPath)\n                  : curItemPath\n\n                try {\n                  await ensureFn({ type, itemPath: ensureItemPath })\n                } catch (error) {\n                  // If ensure failed, skip this item and continue to the next one\n                  continue\n                }\n              }\n            } else {\n              continue\n            }\n          }\n\n          // i18n locales aren't matched for app dir\n          if (type === 'appFile' && locale && locale !== i18n?.defaultLocale) {\n            continue\n          }\n\n          const itemResult = {\n            type,\n            fsPath,\n            locale,\n            itemsRoot,\n            itemPath: curItemPath,\n          }\n\n          getItemsLru?.set(itemKey, itemResult)\n          return itemResult\n        }\n      }\n\n      getItemsLru?.set(itemKey, null)\n      return null\n    },\n    getDynamicRoutes() {\n      // this should include data routes\n      return this.dynamicRoutes\n    },\n    getMiddlewareMatchers() {\n      return this.middlewareMatcher\n    },\n  }\n}\n"], "names": ["path", "fs", "Log", "setupDebug", "L<PERSON><PERSON><PERSON>", "loadCustomRoutes", "modifyRouteRegex", "FileType", "fileExists", "recursiveReadDir", "isDynamicRoute", "escapeStringRegexp", "getPathMatch", "getRouteRegex", "getRouteMatcher", "pathHasPrefix", "normalizeLocalePath", "removePathPrefix", "getMiddlewareRouteMatcher", "APP_PATH_ROUTES_MANIFEST", "BUILD_ID_FILE", "FUNCTIONS_CONFIG_MANIFEST", "MIDDLEWARE_MANIFEST", "PAGES_MANIFEST", "PRERENDER_MANIFEST", "ROUTES_MANIFEST", "normalizePathSep", "normalizeMetadataRoute", "RSCPathnameNormalizer", "PrefetchRSCPathnameNormalizer", "encodeURIPath", "debug", "buildCustomRoute", "type", "item", "basePath", "caseSensitive", "restrictedRedirectPaths", "map", "p", "builtRegex", "match", "source", "strict", "removeUnnamedP<PERSON>ms", "regexModifier", "regex", "internal", "undefined", "sensitive", "check", "setupFsCheck", "opts", "getItemsLru", "dev", "length", "value", "fsPath", "itemPath", "nextDataRoutes", "Set", "publicFolderItems", "nextStaticFolderItems", "legacyStaticFolderItems", "appFiles", "pageFiles", "dynamicRoutes", "middlewareMatcher", "distDir", "join", "dir", "config", "publicFolderPath", "nextStaticFolderPath", "legacyStaticFolderPath", "customRoutes", "redirects", "rewrites", "beforeFiles", "afterFiles", "fallback", "headers", "buildId", "prerenderManifest", "middlewareManifest", "buildIdPath", "readFile", "err", "code", "Error", "file", "add", "warn", "posix", "output", "routesManifestPath", "prerenderManifestPath", "middlewareManifestPath", "functionsConfigManifestPath", "pagesManifestPath", "appRoutesManifestPath", "routesManifest", "JSON", "parse", "catch", "functionsConfigManifest", "pagesManifest", "appRoutesManifest", "key", "Object", "keys", "i18n", "locales", "pathname", "escapedBuildId", "route", "dataRoutes", "page", "routeRegex", "push", "re", "toString", "RegExp", "dataRouteRegex", "replace", "groups", "skipInternalRouting", "middleware", "matchers", "functions", "regexp", "originalSource", "Array", "isArray", "version", "routes", "notFoundRoutes", "preview", "previewModeId", "require", "randomBytes", "previewModeSigningKey", "previewModeEncryptionKey", "experimental", "caseSensitiveRoutes", "handleLocale", "locale", "i18nResult", "detectedLocale", "ensureFn", "normalizers", "rsc", "prefetchRSC", "ppr", "exportPathMapRoutes", "devVirtualFsItems", "ensure<PERSON><PERSON>back", "fn", "getItem", "originalItemPath", "itemKey", "lruResult", "get", "has<PERSON>ase<PERSON><PERSON>", "minimalMode", "normalize", "endsWith", "substring", "decodedItemPath", "decodeURIComponent", "itemsToCheck", "items", "curI<PERSON><PERSON><PERSON>", "curDecodedItemPath", "isDynamicOutput", "localeResult", "defaultLocale", "domains", "nextDataPrefix", "startsWith", "curLocaleResult", "matchedItem", "has", "encodedCurItemPath", "itemsRoot", "isStaticAsset", "includes", "found", "File", "tempItemPath", "isAppFile", "ensureItemPath", "error", "itemResult", "set", "getDynamicRoutes", "getMiddlewareMatchers"], "mappings": "AAYA,OAAOA,UAAU,OAAM;AACvB,OAAOC,QAAQ,cAAa;AAC5B,YAAYC,SAAS,4BAA2B;AAChD,OAAOC,gBAAgB,2BAA0B;AACjD,SAASC,QAAQ,QAAQ,eAAc;AACvC,OAAOC,sBAAwC,kCAAiC;AAChF,SAASC,gBAAgB,QAAQ,+BAA8B;AAC/D,SAASC,QAAQ,EAAEC,UAAU,QAAQ,2BAA0B;AAC/D,SAASC,gBAAgB,QAAQ,iCAAgC;AACjE,SAASC,cAAc,QAAQ,mCAAkC;AACjE,SAASC,kBAAkB,QAAQ,oCAAmC;AACtE,SAASC,YAAY,QAAQ,8CAA6C;AAC1E,SAASC,aAAa,QAAQ,+CAA8C;AAC5E,SAASC,eAAe,QAAQ,iDAAgD;AAChF,SAASC,aAAa,QAAQ,mDAAkD;AAChF,SAASC,mBAAmB,QAAQ,iDAAgD;AACpF,SAASC,gBAAgB,QAAQ,sDAAqD;AACtF,SAASC,yBAAyB,QAAQ,4DAA2D;AACrG,SACEC,wBAAwB,EACxBC,aAAa,EACbC,yBAAyB,EACzBC,mBAAmB,EACnBC,cAAc,EACdC,kBAAkB,EAClBC,eAAe,QACV,gCAA+B;AACtC,SAASC,gBAAgB,QAAQ,mDAAkD;AACnF,SAASC,sBAAsB,QAAQ,2CAA0C;AACjF,SAASC,qBAAqB,QAAQ,gCAA+B;AACrE,SAASC,6BAA6B,QAAQ,yCAAwC;AACtF,SAASC,aAAa,QAAQ,sCAAqC;AAkBnE,MAAMC,QAAQ5B,WAAW;AASzB,OAAO,MAAM6B,mBAAmB,CAC9BC,MACAC,MACAC,UACAC;IAEA,MAAMC,0BAA0B;QAAC;KAAS,CAACC,GAAG,CAAC,CAACC,IAC9CJ,WAAW,GAAGA,WAAWI,GAAG,GAAGA;IAEjC,IAAIC,aAAa;IACjB,MAAMC,QAAQ7B,aAAasB,KAAKQ,MAAM,EAAE;QACtCC,QAAQ;QACRC,qBAAqB;QACrBC,eAAe,CAACC;YACd,IAAI,CAAC,AAACZ,KAAaa,QAAQ,EAAE;gBAC3BD,QAAQxC,iBACNwC,OACAb,SAAS,aAAaI,0BAA0BW;YAEpD;YACAR,aAAaM;YACb,OAAON;QACT;QACAS,WAAWb;IACb;IAEA,OAAO;QACL,GAAGF,IAAI;QACPY,OAAON;QACP,GAAIP,SAAS,YAAY;YAAEiB,OAAO;QAAK,IAAI,CAAC,CAAC;QAC7CT;IACF;AACF,EAAC;AAED,OAAO,eAAeU,aAAaC,IAKlC;IACC,MAAMC,cAAc,CAACD,KAAKE,GAAG,GACzB,IAAIlD,SAA0B,OAAO,MAAM,SAASmD,OAAOC,KAAK;QAC9D,IAAI,CAACA,OAAO,OAAO;QACnB,OACE,AAACA,CAAAA,MAAMC,MAAM,IAAI,EAAC,EAAGF,MAAM,GAC3BC,MAAME,QAAQ,CAACH,MAAM,GACrBC,MAAMvB,IAAI,CAACsB,MAAM;IAErB,KACAP;IAEJ,kDAAkD;IAClD,MAAMW,iBAAiB,IAAIC;IAC3B,MAAMC,oBAAoB,IAAID;IAC9B,MAAME,wBAAwB,IAAIF;IAClC,MAAMG,0BAA0B,IAAIH;IAEpC,MAAMI,WAAW,IAAIJ;IACrB,MAAMK,YAAY,IAAIL;IACtB,IAAIM,gBAA0C,EAAE;IAEhD,IAAIC,oBAEY,IAAM;IAEtB,MAAMC,UAAUpE,KAAKqE,IAAI,CAACjB,KAAKkB,GAAG,EAAElB,KAAKmB,MAAM,CAACH,OAAO;IACvD,MAAMI,mBAAmBxE,KAAKqE,IAAI,CAACjB,KAAKkB,GAAG,EAAE;IAC7C,MAAMG,uBAAuBzE,KAAKqE,IAAI,CAACD,SAAS;IAChD,MAAMM,yBAAyB1E,KAAKqE,IAAI,CAACjB,KAAKkB,GAAG,EAAE;IACnD,IAAIK,eAAmE;QACrEC,WAAW,EAAE;QACbC,UAAU;YACRC,aAAa,EAAE;YACfC,YAAY,EAAE;YACdC,UAAU,EAAE;QACd;QACAC,SAAS,EAAE;IACb;IACA,IAAIC,UAAU;IACd,IAAIC;IAEJ,IAAI,CAAC/B,KAAKE,GAAG,EAAE;YA6IT8B,iCAAAA;QA5IJ,MAAMC,cAAcrF,KAAKqE,IAAI,CAACjB,KAAKkB,GAAG,EAAElB,KAAKmB,MAAM,CAACH,OAAO,EAAEhD;QAC7D,IAAI;YACF8D,UAAU,MAAMjF,GAAGqF,QAAQ,CAACD,aAAa;QAC3C,EAAE,OAAOE,KAAU;YACjB,IAAIA,IAAIC,IAAI,KAAK,UAAU,MAAMD;YACjC,MAAM,qBAEL,CAFK,IAAIE,MACR,CAAC,0CAA0C,EAAErC,KAAKmB,MAAM,CAACH,OAAO,CAAC,yJAAyJ,CAAC,GADvN,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI;YACF,KAAK,MAAMsB,QAAQ,CAAA,MAAMjF,iBAAiB+D,iBAAgB,EAAG;gBAC3D,6CAA6C;gBAC7CX,kBAAkB8B,GAAG,CAAC7D,cAAcJ,iBAAiBgE;YACvD;QACF,EAAE,OAAOH,KAAU;YACjB,IAAIA,IAAIC,IAAI,KAAK,UAAU;gBACzB,MAAMD;YACR;QACF;QAEA,IAAI;YACF,KAAK,MAAMG,QAAQ,CAAA,MAAMjF,iBAAiBiE,uBAAsB,EAAG;gBACjE,6CAA6C;gBAC7CX,wBAAwB4B,GAAG,CAAC7D,cAAcJ,iBAAiBgE;YAC7D;YACAxF,IAAI0F,IAAI,CACN,CAAC,iIAAiI,CAAC;QAEvI,EAAE,OAAOL,KAAU;YACjB,IAAIA,IAAIC,IAAI,KAAK,UAAU;gBACzB,MAAMD;YACR;QACF;QAEA,IAAI;YACF,KAAK,MAAMG,QAAQ,CAAA,MAAMjF,iBAAiBgE,qBAAoB,EAAG;gBAC/D,6CAA6C;gBAC7CX,sBAAsB6B,GAAG,CACvB3F,KAAK6F,KAAK,CAACxB,IAAI,CACb,iBACAvC,cAAcJ,iBAAiBgE;YAGrC;QACF,EAAE,OAAOH,KAAK;YACZ,IAAInC,KAAKmB,MAAM,CAACuB,MAAM,KAAK,cAAc,MAAMP;QACjD;QAEA,MAAMQ,qBAAqB/F,KAAKqE,IAAI,CAACD,SAAS3C;QAC9C,MAAMuE,wBAAwBhG,KAAKqE,IAAI,CAACD,SAAS5C;QACjD,MAAMyE,yBAAyBjG,KAAKqE,IAAI,CACtCD,SACA,UACA9C;QAEF,MAAM4E,8BAA8BlG,KAAKqE,IAAI,CAC3CD,SACA,UACA/C;QAEF,MAAM8E,oBAAoBnG,KAAKqE,IAAI,CAACD,SAAS,UAAU7C;QACvD,MAAM6E,wBAAwBpG,KAAKqE,IAAI,CAACD,SAASjD;QAEjD,MAAMkF,iBAAiBC,KAAKC,KAAK,CAC/B,MAAMtG,GAAGqF,QAAQ,CAACS,oBAAoB;QAGxCZ,oBAAoBmB,KAAKC,KAAK,CAC5B,MAAMtG,GAAGqF,QAAQ,CAACU,uBAAuB;QAG3C,MAAMZ,qBAAqBkB,KAAKC,KAAK,CACnC,MAAMtG,GAAGqF,QAAQ,CAACW,wBAAwB,QAAQO,KAAK,CAAC,IAAM;QAGhE,MAAMC,0BAA0BH,KAAKC,KAAK,CACxC,MAAMtG,GAAGqF,QAAQ,CAACY,6BAA6B,QAAQM,KAAK,CAAC,IAAM;QAGrE,MAAME,gBAAgBJ,KAAKC,KAAK,CAC9B,MAAMtG,GAAGqF,QAAQ,CAACa,mBAAmB;QAEvC,MAAMQ,oBAAoBL,KAAKC,KAAK,CAClC,MAAMtG,GAAGqF,QAAQ,CAACc,uBAAuB,QAAQI,KAAK,CAAC,IAAM;QAG/D,KAAK,MAAMI,OAAOC,OAAOC,IAAI,CAACJ,eAAgB;YAC5C,8CAA8C;YAC9C,IAAItD,KAAKmB,MAAM,CAACwC,IAAI,EAAE;gBACpB9C,UAAU0B,GAAG,CACX3E,oBAAoB4F,KAAKxD,KAAKmB,MAAM,CAACwC,IAAI,CAACC,OAAO,EAAEC,QAAQ;YAE/D,OAAO;gBACLhD,UAAU0B,GAAG,CAACiB;YAChB;QACF;QACA,KAAK,MAAMA,OAAOC,OAAOC,IAAI,CAACH,mBAAoB;YAChD3C,SAAS2B,GAAG,CAACgB,iBAAiB,CAACC,IAAI;QACrC;QAEA,MAAMM,iBAAiBvG,mBAAmBuE;QAE1C,KAAK,MAAMiC,SAASd,eAAee,UAAU,CAAE;YAC7C,IAAI1G,eAAeyG,MAAME,IAAI,GAAG;gBAC9B,MAAMC,aAAazG,cAAcsG,MAAME,IAAI;gBAC3CnD,cAAcqD,IAAI,CAAC;oBACjB,GAAGJ,KAAK;oBACRrE,OAAOwE,WAAWE,EAAE,CAACC,QAAQ;oBAC7BhF,OAAO3B,gBAAgB;wBACrB,+DAA+D;wBAC/D,uCAAuC;wBACvC0G,IAAIpE,KAAKmB,MAAM,CAACwC,IAAI,GAChB,IAAIW,OACFP,MAAMQ,cAAc,CAACC,OAAO,CAC1B,CAAC,CAAC,EAAEV,eAAe,CAAC,CAAC,EACrB,CAAC,CAAC,EAAEA,eAAe,uBAAuB,CAAC,KAG/C,IAAIQ,OAAOP,MAAMQ,cAAc;wBACnCE,QAAQP,WAAWO,MAAM;oBAC3B;gBACF;YACF;YACAlE,eAAegC,GAAG,CAACwB,MAAME,IAAI;QAC/B;QAEA,KAAK,MAAMF,SAASd,eAAenC,aAAa,CAAE;YAChD,yEAAyE;YACzE,kEAAkE;YAClE,IAAIiD,MAAMW,mBAAmB,EAAE;gBAC7B;YACF;YAEA5D,cAAcqD,IAAI,CAAC;gBACjB,GAAGJ,KAAK;gBACR1E,OAAO3B,gBAAgBD,cAAcsG,MAAME,IAAI;YACjD;QACF;QAEA,KAAIjC,iCAAAA,mBAAmB2C,UAAU,sBAA7B3C,kCAAAA,8BAA+B,CAAC,IAAI,qBAApCA,gCAAsC4C,QAAQ,EAAE;gBAEhD5C,kCAAAA;YADFjB,oBAAoBjD,2BAClBkE,kCAAAA,mBAAmB2C,UAAU,sBAA7B3C,mCAAAA,+BAA+B,CAAC,IAAI,qBAApCA,iCAAsC4C,QAAQ;QAElD,OAAO,IAAIvB,2CAAAA,wBAAyBwB,SAAS,CAAC,eAAe,EAAE;YAC7D9D,oBAAoBjD,0BAClBuF,wBAAwBwB,SAAS,CAAC,eAAe,CAACD,QAAQ,IAAI;gBAC5D;oBAAEE,QAAQ;oBAAMC,gBAAgB;gBAAU;aAC3C;QAEL;QAEAxD,eAAe;YACbC,WAAWyB,eAAezB,SAAS;YACnCC,UAAUwB,eAAexB,QAAQ,GAC7BuD,MAAMC,OAAO,CAAChC,eAAexB,QAAQ,IACnC;gBACEC,aAAa,EAAE;gBACfC,YAAYsB,eAAexB,QAAQ;gBACnCG,UAAU,EAAE;YACd,IACAqB,eAAexB,QAAQ,GACzB;gBACEC,aAAa,EAAE;gBACfC,YAAY,EAAE;gBACdC,UAAU,EAAE;YACd;YACJC,SAASoB,eAAepB,OAAO;QACjC;IACF,OAAO;QACL,eAAe;QACfN,eAAe,MAAMtE,iBAAiB+C,KAAKmB,MAAM;QAEjDY,oBAAoB;YAClBmD,SAAS;YACTC,QAAQ,CAAC;YACTrE,eAAe,CAAC;YAChBsE,gBAAgB,EAAE;YAClBC,SAAS;gBACPC,eAAe,AAACC,QAAQ,UACrBC,WAAW,CAAC,IACZnB,QAAQ,CAAC;gBACZoB,uBAAuB,AAACF,QAAQ,UAC7BC,WAAW,CAAC,IACZnB,QAAQ,CAAC;gBACZqB,0BAA0B,AAACH,QAAQ,UAChCC,WAAW,CAAC,IACZnB,QAAQ,CAAC;YACd;QACF;IACF;IAEA,MAAMxC,UAAUN,aAAaM,OAAO,CAAC3C,GAAG,CAAC,CAACJ,OACxCF,iBACE,UACAE,MACAkB,KAAKmB,MAAM,CAACpC,QAAQ,EACpBiB,KAAKmB,MAAM,CAACwE,YAAY,CAACC,mBAAmB;IAGhD,MAAMpE,YAAYD,aAAaC,SAAS,CAACtC,GAAG,CAAC,CAACJ,OAC5CF,iBACE,YACAE,MACAkB,KAAKmB,MAAM,CAACpC,QAAQ,EACpBiB,KAAKmB,MAAM,CAACwE,YAAY,CAACC,mBAAmB;IAGhD,MAAMnE,WAAW;QACfC,aAAaH,aAAaE,QAAQ,CAACC,WAAW,CAACxC,GAAG,CAAC,CAACJ,OAClDF,iBAAiB,wBAAwBE;QAE3C6C,YAAYJ,aAAaE,QAAQ,CAACE,UAAU,CAACzC,GAAG,CAAC,CAACJ,OAChDF,iBACE,WACAE,MACAkB,KAAKmB,MAAM,CAACpC,QAAQ,EACpBiB,KAAKmB,MAAM,CAACwE,YAAY,CAACC,mBAAmB;QAGhDhE,UAAUL,aAAaE,QAAQ,CAACG,QAAQ,CAAC1C,GAAG,CAAC,CAACJ,OAC5CF,iBACE,WACAE,MACAkB,KAAKmB,MAAM,CAACpC,QAAQ,EACpBiB,KAAKmB,MAAM,CAACwE,YAAY,CAACC,mBAAmB;IAGlD;IAEA,MAAM,EAAEjC,IAAI,EAAE,GAAG3D,KAAKmB,MAAM;IAE5B,MAAM0E,eAAe,CAAChC,UAAkBD;QACtC,IAAIkC;QAEJ,IAAInC,MAAM;YACR,MAAMoC,aAAanI,oBAAoBiG,UAAUD,WAAWD,KAAKC,OAAO;YAExEC,WAAWkC,WAAWlC,QAAQ;YAC9BiC,SAASC,WAAWC,cAAc;QACpC;QACA,OAAO;YAAEF;YAAQjC;QAAS;IAC5B;IAEAlF,MAAM,kBAAkB4B;IACxB5B,MAAM,iBAAiBmC;IACvBnC,MAAM,gBAAgB4C;IACtB5C,MAAM,qBAAqB8B;IAC3B9B,MAAM,yBAAyB+B;IAC/B/B,MAAM,aAAakC;IACnBlC,MAAM,YAAYiC;IAElB,IAAIqF;IAEJ,MAAMC,cAAc;QAClB,uEAAuE;QACvE,+BAA+B;QAC/BC,KAAK,IAAI3H;QACT4H,aAAapG,KAAKmB,MAAM,CAACwE,YAAY,CAACU,GAAG,GACrC,IAAI5H,kCACJmB;IACN;IAEA,OAAO;QACLiC;QACAJ;QACAD;QAEAM;QACA+D;QAEAjF;QACAC;QACAC;QACAP;QAEA+F,qBAAqB1G;QAIrB2G,mBAAmB,IAAI/F;QAEvBuB;QACAhB,mBAAmBA;QAEnByF,gBAAeC,EAAmB;YAChCR,WAAWQ;QACb;QAEA,MAAMC,SAAQpG,QAAgB;YAC5B,MAAMqG,mBAAmBrG;YACzB,MAAMsG,UAAUD;YAChB,MAAME,YAAY5G,+BAAAA,YAAa6G,GAAG,CAACF;YAEnC,IAAIC,WAAW;gBACb,OAAOA;YACT;YAEA,MAAM,EAAE9H,QAAQ,EAAE,GAAGiB,KAAKmB,MAAM;YAEhC,MAAM4F,cAAcpJ,cAAc2C,UAAUvB;YAE5C,kDAAkD;YAClD,IAAIA,YAAY,CAACgI,aAAa;gBAC5B,OAAO;YACT;YAEA,gCAAgC;YAChC,IAAIhI,YAAYgI,aAAa;gBAC3BzG,WAAWzC,iBAAiByC,UAAUvB,aAAa;YACrD;YAEA,kEAAkE;YAClE,YAAY;YACZ,IAAIiB,KAAKgH,WAAW,EAAE;oBAChBd;gBAAJ,KAAIA,2BAAAA,YAAYE,WAAW,qBAAvBF,yBAAyB7G,KAAK,CAACiB,WAAW;oBAC5CA,WAAW4F,YAAYE,WAAW,CAACa,SAAS,CAAC3G,UAAU;gBACzD,OAAO,IAAI4F,YAAYC,GAAG,CAAC9G,KAAK,CAACiB,WAAW;oBAC1CA,WAAW4F,YAAYC,GAAG,CAACc,SAAS,CAAC3G,UAAU;gBACjD;YACF;YAEA,IAAIA,aAAa,OAAOA,SAAS4G,QAAQ,CAAC,MAAM;gBAC9C5G,WAAWA,SAAS6G,SAAS,CAAC,GAAG7G,SAASH,MAAM,GAAG;YACrD;YAEA,IAAIiH,kBAAkB9G;YAEtB,IAAI;gBACF8G,kBAAkBC,mBAAmB/G;YACvC,EAAE,OAAM,CAAC;YAET,IAAIA,aAAa,gBAAgB;gBAC/B,OAAO;oBACLA;oBACAzB,MAAM;gBACR;YACF;YAEA,MAAMyI,eAAuD;gBAC3D;oBAAC,IAAI,CAACf,iBAAiB;oBAAE;iBAAmB;gBAC5C;oBAAC7F;oBAAuB;iBAAmB;gBAC3C;oBAACC;oBAAyB;iBAAqB;gBAC/C;oBAACF;oBAAmB;iBAAe;gBACnC;oBAACG;oBAAU;iBAAU;gBACrB;oBAACC;oBAAW;iBAAW;aACxB;YAED,KAAK,IAAI,CAAC0G,OAAO1I,KAAK,IAAIyI,aAAc;gBACtC,IAAIxB;gBACJ,IAAI0B,cAAclH;gBAClB,IAAImH,qBAAqBL;gBAEzB,MAAMM,kBAAkB7I,SAAS,cAAcA,SAAS;gBAExD,IAAI8E,MAAM;wBAUIA;oBATZ,MAAMgE,eAAe9B,aACnBvF,UACA,sDAAsD;oBACtD,qCAAqC;oBACrCoH,kBACI9H,YACA;wBACE+D,wBAAAA,KAAMiE,aAAa;wBACnB,sDAAsD;2BAClDjE,EAAAA,gBAAAA,KAAKkE,OAAO,qBAAZlE,cAAczE,GAAG,CAAC,CAACJ,OAASA,KAAK8I,aAAa,MAAK,EAAE;qBAC1D;oBAGP,IAAID,aAAa9D,QAAQ,KAAK2D,aAAa;wBACzCA,cAAcG,aAAa9D,QAAQ;wBACnCiC,SAAS6B,aAAa7B,MAAM;wBAE5B,IAAI;4BACF2B,qBAAqBJ,mBAAmBG;wBAC1C,EAAE,OAAM,CAAC;oBACX;gBACF;gBAEA,IAAI3I,SAAS,sBAAsB;oBACjC,IAAI,CAAClB,cAAc6J,aAAa,YAAY;wBAC1C;oBACF;oBACAA,cAAcA,YAAYL,SAAS,CAAC,UAAUhH,MAAM;oBAEpD,IAAI;wBACFsH,qBAAqBJ,mBAAmBG;oBAC1C,EAAE,OAAM,CAAC;gBACX;gBAEA,IACE3I,SAAS,sBACT,CAAClB,cAAc6J,aAAa,kBAC5B;oBACA;gBACF;gBAEA,MAAMM,iBAAiB,CAAC,YAAY,EAAEhG,QAAQ,CAAC,CAAC;gBAEhD,IACEjD,SAAS,cACT2I,YAAYO,UAAU,CAACD,mBACvBN,YAAYN,QAAQ,CAAC,UACrB;oBACAK,QAAQhH;oBACR,sCAAsC;oBACtCiH,cAAcA,YAAYL,SAAS,CAACW,eAAe3H,MAAM,GAAG;oBAE5D,uBAAuB;oBACvBqH,cAAcA,YAAYL,SAAS,CACjC,GACAK,YAAYrH,MAAM,GAAG,QAAQA,MAAM;oBAErC,MAAM6H,kBAAkBnC,aAAa2B;oBACrCA,cACEQ,gBAAgBnE,QAAQ,KAAK,WACzB,MACAmE,gBAAgBnE,QAAQ;oBAE9BiC,SAASkC,gBAAgBlC,MAAM;oBAE/B,IAAI;wBACF2B,qBAAqBJ,mBAAmBG;oBAC1C,EAAE,OAAM,CAAC;gBACX;gBAEA,IAAIS,cAAcV,MAAMW,GAAG,CAACV;gBAE5B,gCAAgC;gBAChC,IAAI,CAACS,eAAe,CAACjI,KAAKE,GAAG,EAAE;oBAC7B+H,cAAcV,MAAMW,GAAG,CAACT;oBACxB,IAAIQ,aAAaT,cAAcC;yBAC1B;wBACH,wDAAwD;wBACxD,yGAAyG;wBACzG,wFAAwF;wBACxF,gFAAgF;wBAChF,oFAAoF;wBACpF,IAAI;4BACF,4FAA4F;4BAC5F,MAAMU,qBAAqBzJ,cAAc8I;4BACzCS,cAAcV,MAAMW,GAAG,CAACC;wBAC1B,EAAE,OAAM,CAAC;oBACX;gBACF;gBAEA,IAAIF,eAAejI,KAAKE,GAAG,EAAE;oBAC3B,IAAIG;oBACJ,IAAI+H;oBAEJ,OAAQvJ;wBACN,KAAK;4BAAoB;gCACvBuJ,YAAY/G;gCACZmG,cAAcA,YAAYL,SAAS,CAAC,gBAAgBhH,MAAM;gCAC1D;4BACF;wBACA,KAAK;4BAAsB;gCACzBiI,YAAY9G;gCACZ;4BACF;wBACA,KAAK;4BAAgB;gCACnB8G,YAAYhH;gCACZ;4BACF;wBACA,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;4BAAoB;gCACvB;4BACF;wBACA;4BAAS;;gCACLvC;4BACJ;oBACF;oBAEA,IAAIuJ,aAAaZ,aAAa;wBAC5BnH,SAASzD,KAAK6F,KAAK,CAACxB,IAAI,CAACmH,WAAWZ;oBACtC;oBAEA,kDAAkD;oBAClD,8BAA8B;oBAC9B,IAAI,CAACS,eAAejI,KAAKE,GAAG,EAAE;wBAC5B,MAAMmI,gBAAgB,AACpB;4BACE;4BACA;4BACA;yBACD,CACDC,QAAQ,CAACzJ;wBAEX,IAAIwJ,iBAAiBD,WAAW;4BAC9B,IAAIG,QAAQlI,UAAW,MAAMjD,WAAWiD,QAAQlD,SAASqL,IAAI;4BAE7D,IAAI,CAACD,OAAO;gCACV,IAAI;oCACF,wCAAwC;oCACxC,2CAA2C;oCAC3C,yBAAyB;oCACzB,MAAME,eAAepB,mBAAmBG;oCACxCnH,SAASzD,KAAK6F,KAAK,CAACxB,IAAI,CAACmH,WAAWK;oCACpCF,QAAQ,MAAMnL,WAAWiD,QAAQlD,SAASqL,IAAI;gCAChD,EAAE,OAAM,CAAC;gCAET,IAAI,CAACD,OAAO;oCACV;gCACF;4BACF;wBACF,OAAO,IAAI1J,SAAS,cAAcA,SAAS,WAAW;4BACpD,MAAM6J,YAAY7J,SAAS;4BAE3B,4DAA4D;4BAC5D,IAAIoH,UAAU;gCACZ,MAAM0C,iBAAiBD,YACnBnK,uBAAuBiJ,eACvBA;gCAEJ,IAAI;oCACF,MAAMvB,SAAS;wCAAEpH;wCAAMyB,UAAUqI;oCAAe;gCAClD,EAAE,OAAOC,OAAO;oCAEd;gCACF;4BACF;wBACF,OAAO;4BACL;wBACF;oBACF;oBAEA,0CAA0C;oBAC1C,IAAI/J,SAAS,aAAaiH,UAAUA,YAAWnC,wBAAAA,KAAMiE,aAAa,GAAE;wBAClE;oBACF;oBAEA,MAAMiB,aAAa;wBACjBhK;wBACAwB;wBACAyF;wBACAsC;wBACA9H,UAAUkH;oBACZ;oBAEAvH,+BAAAA,YAAa6I,GAAG,CAAClC,SAASiC;oBAC1B,OAAOA;gBACT;YACF;YAEA5I,+BAAAA,YAAa6I,GAAG,CAAClC,SAAS;YAC1B,OAAO;QACT;QACAmC;YACE,kCAAkC;YAClC,OAAO,IAAI,CAACjI,aAAa;QAC3B;QACAkI;YACE,OAAO,IAAI,CAACjI,iBAAiB;QAC/B;IACF;AACF", "ignoreList": [0]}