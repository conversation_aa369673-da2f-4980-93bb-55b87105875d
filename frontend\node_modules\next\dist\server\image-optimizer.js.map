{"version": 3, "sources": ["../../src/server/image-optimizer.ts"], "sourcesContent": ["import { createHash } from 'crypto'\nimport { promises } from 'fs'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport { mediaType } from 'next/dist/compiled/@hapi/accept'\nimport contentDisposition from 'next/dist/compiled/content-disposition'\nimport imageSizeOf from 'next/dist/compiled/image-size'\nimport { detector } from 'next/dist/compiled/image-detector/detector.js'\nimport isAnimated from 'next/dist/compiled/is-animated'\nimport { join } from 'path'\nimport nodeUrl, { type UrlWithParsedQuery } from 'url'\n\nimport { getImageBlurSvg } from '../shared/lib/image-blur-svg'\nimport type { ImageConfigComplete } from '../shared/lib/image-config'\nimport { hasLocalMatch } from '../shared/lib/match-local-pattern'\nimport { hasRemoteMatch } from '../shared/lib/match-remote-pattern'\nimport type { NextConfigComplete } from './config-shared'\nimport { createRequestResponseMocks } from './lib/mock-request'\nimport type { NextUrlWithParsedQuery } from './request-meta'\nimport {\n  CachedRouteKind,\n  type CachedImageValue,\n  type IncrementalCacheEntry,\n  type IncrementalCacheValue,\n  type IncrementalResponseCacheEntry,\n} from './response-cache'\nimport { sendEtagResponse } from './send-payload'\nimport { getContentType, getExtension } from './serve-static'\nimport * as Log from '../build/output/log'\nimport isError from '../lib/is-error'\nimport { parseUrl } from '../lib/url'\nimport type { CacheControl } from './lib/cache-control'\nimport { InvariantError } from '../shared/lib/invariant-error'\n\ntype XCacheHeader = 'MISS' | 'HIT' | 'STALE'\n\nconst AVIF = 'image/avif'\nconst WEBP = 'image/webp'\nconst PNG = 'image/png'\nconst JPEG = 'image/jpeg'\nconst JXL = 'image/jxl'\nconst JP2 = 'image/jp2'\nconst HEIC = 'image/heic'\nconst GIF = 'image/gif'\nconst SVG = 'image/svg+xml'\nconst ICO = 'image/x-icon'\nconst ICNS = 'image/x-icns'\nconst TIFF = 'image/tiff'\nconst BMP = 'image/bmp'\nconst PDF = 'application/pdf'\nconst CACHE_VERSION = 4\nconst ANIMATABLE_TYPES = [WEBP, PNG, GIF]\nconst BYPASS_TYPES = [SVG, ICO, ICNS, BMP, JXL, HEIC]\nconst BLUR_IMG_SIZE = 8 // should match `next-image-loader`\nconst BLUR_QUALITY = 70 // should match `next-image-loader`\n\nlet _sharp: typeof import('sharp')\n\nexport function getSharp(concurrency: number | null | undefined) {\n  if (_sharp) {\n    return _sharp\n  }\n  try {\n    _sharp = require('sharp') as typeof import('sharp')\n    if (_sharp && _sharp.concurrency() > 1) {\n      // Reducing concurrency should reduce the memory usage too.\n      // We more aggressively reduce in dev but also reduce in prod.\n      // https://sharp.pixelplumbing.com/api-utility#concurrency\n      const divisor = process.env.NODE_ENV === 'development' ? 4 : 2\n      _sharp.concurrency(\n        concurrency ?? Math.floor(Math.max(_sharp.concurrency() / divisor, 1))\n      )\n    }\n  } catch (e: unknown) {\n    if (isError(e) && e.code === 'MODULE_NOT_FOUND') {\n      throw new Error(\n        'Module `sharp` not found. Please run `npm install --cpu=wasm32 sharp` to install it.'\n      )\n    }\n    throw e\n  }\n  return _sharp\n}\n\nexport interface ImageParamsResult {\n  href: string\n  isAbsolute: boolean\n  isStatic: boolean\n  width: number\n  quality: number\n  mimeType: string\n  sizes: number[]\n  minimumCacheTTL: number\n}\n\ninterface ImageUpstream {\n  buffer: Buffer\n  contentType: string | null | undefined\n  cacheControl: string | null | undefined\n  etag: string\n}\n\nfunction getSupportedMimeType(options: string[], accept = ''): string {\n  const mimeType = mediaType(accept, options)\n  return accept.includes(mimeType) ? mimeType : ''\n}\n\nexport function getHash(items: (string | number | Buffer)[]) {\n  const hash = createHash('sha256')\n  for (let item of items) {\n    if (typeof item === 'number') hash.update(String(item))\n    else {\n      hash.update(item)\n    }\n  }\n  // See https://en.wikipedia.org/wiki/Base64#URL_applications\n  return hash.digest('base64url')\n}\n\nexport function extractEtag(\n  etag: string | null | undefined,\n  imageBuffer: Buffer\n) {\n  if (etag) {\n    // upstream etag needs to be base64url encoded due to weak etag signature\n    // as we store this in the cache-entry file name.\n    return Buffer.from(etag).toString('base64url')\n  }\n  return getImageEtag(imageBuffer)\n}\n\nexport function getImageEtag(image: Buffer) {\n  return getHash([image])\n}\n\nasync function writeToCacheDir(\n  dir: string,\n  extension: string,\n  maxAge: number,\n  expireAt: number,\n  buffer: Buffer,\n  etag: string,\n  upstreamEtag: string\n) {\n  const filename = join(\n    dir,\n    `${maxAge}.${expireAt}.${etag}.${upstreamEtag}.${extension}`\n  )\n\n  await promises.rm(dir, { recursive: true, force: true }).catch(() => {})\n\n  await promises.mkdir(dir, { recursive: true })\n  await promises.writeFile(filename, buffer)\n}\n\n/**\n * Inspects the first few bytes of a buffer to determine if\n * it matches the \"magic number\" of known file signatures.\n * https://en.wikipedia.org/wiki/List_of_file_signatures\n */\nexport async function detectContentType(\n  buffer: Buffer,\n  skipMetadata: boolean | null | undefined,\n  concurrency?: number | null | undefined\n): Promise<string | null> {\n  if (buffer.byteLength === 0) {\n    return null\n  }\n  if ([0xff, 0xd8, 0xff].every((b, i) => buffer[i] === b)) {\n    return JPEG\n  }\n  if (\n    [0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a].every(\n      (b, i) => buffer[i] === b\n    )\n  ) {\n    return PNG\n  }\n  if ([0x47, 0x49, 0x46, 0x38].every((b, i) => buffer[i] === b)) {\n    return GIF\n  }\n  if (\n    [0x52, 0x49, 0x46, 0x46, 0, 0, 0, 0, 0x57, 0x45, 0x42, 0x50].every(\n      (b, i) => !b || buffer[i] === b\n    )\n  ) {\n    return WEBP\n  }\n  if ([0x3c, 0x3f, 0x78, 0x6d, 0x6c].every((b, i) => buffer[i] === b)) {\n    return SVG\n  }\n  if ([0x3c, 0x73, 0x76, 0x67].every((b, i) => buffer[i] === b)) {\n    return SVG\n  }\n  if (\n    [0, 0, 0, 0, 0x66, 0x74, 0x79, 0x70, 0x61, 0x76, 0x69, 0x66].every(\n      (b, i) => !b || buffer[i] === b\n    )\n  ) {\n    return AVIF\n  }\n  if ([0x00, 0x00, 0x01, 0x00].every((b, i) => buffer[i] === b)) {\n    return ICO\n  }\n  if ([0x69, 0x63, 0x6e, 0x73].every((b, i) => buffer[i] === b)) {\n    return ICNS\n  }\n  if ([0x49, 0x49, 0x2a, 0x00].every((b, i) => buffer[i] === b)) {\n    return TIFF\n  }\n  if ([0x42, 0x4d].every((b, i) => buffer[i] === b)) {\n    return BMP\n  }\n  if ([0xff, 0x0a].every((b, i) => buffer[i] === b)) {\n    return JXL\n  }\n  if (\n    [\n      0x00, 0x00, 0x00, 0x0c, 0x4a, 0x58, 0x4c, 0x20, 0x0d, 0x0a, 0x87, 0x0a,\n    ].every((b, i) => buffer[i] === b)\n  ) {\n    return JXL\n  }\n  if (\n    [0, 0, 0, 0, 0x66, 0x74, 0x79, 0x70, 0x68, 0x65, 0x69, 0x63].every(\n      (b, i) => !b || buffer[i] === b\n    )\n  ) {\n    return HEIC\n  }\n  if ([0x25, 0x50, 0x44, 0x46, 0x2d].every((b, i) => buffer[i] === b)) {\n    return PDF\n  }\n  if (\n    [\n      0x00, 0x00, 0x00, 0x0c, 0x6a, 0x50, 0x20, 0x20, 0x0d, 0x0a, 0x87, 0x0a,\n    ].every((b, i) => buffer[i] === b)\n  ) {\n    return JP2\n  }\n\n  let format:\n    | import('sharp').Metadata['format']\n    | ReturnType<typeof detector>\n    | undefined\n  format = detector(buffer)\n\n  if (!format && !skipMetadata) {\n    const sharp = getSharp(concurrency)\n    const meta = await sharp(buffer)\n      .metadata()\n      .catch((_) => null)\n    format = meta?.format\n  }\n\n  switch (format) {\n    case 'avif':\n      return AVIF\n    case 'webp':\n      return WEBP\n    case 'png':\n      return PNG\n    case 'jpeg':\n    case 'jpg':\n      return JPEG\n    case 'gif':\n      return GIF\n    case 'svg':\n      return SVG\n    case 'jxl':\n    case 'jxl-stream':\n      return JXL\n    case 'jp2':\n      return JP2\n    case 'tiff':\n    case 'tif':\n      return TIFF\n    case 'pdf':\n      return PDF\n    case 'bmp':\n      return BMP\n    case 'ico':\n      return ICO\n    case 'icns':\n      return ICNS\n    case 'dcraw':\n    case 'dz':\n    case 'exr':\n    case 'fits':\n    case 'heif':\n    case 'input':\n    case 'magick':\n    case 'openslide':\n    case 'ppm':\n    case 'rad':\n    case 'raw':\n    case 'v':\n    case 'cur':\n    case 'dds':\n    case 'j2c':\n    case 'ktx':\n    case 'pnm':\n    case 'psd':\n    case 'tga':\n    case undefined:\n    default:\n      return null\n  }\n}\n\nexport class ImageOptimizerCache {\n  private cacheDir: string\n  private nextConfig: NextConfigComplete\n\n  static validateParams(\n    req: IncomingMessage,\n    query: UrlWithParsedQuery['query'],\n    nextConfig: NextConfigComplete,\n    isDev: boolean\n  ): ImageParamsResult | { errorMessage: string } {\n    const imageData = nextConfig.images\n    const {\n      deviceSizes = [],\n      imageSizes = [],\n      domains = [],\n      minimumCacheTTL = 60,\n      formats = ['image/webp'],\n    } = imageData\n    const remotePatterns = nextConfig.images?.remotePatterns || []\n    const localPatterns = nextConfig.images?.localPatterns\n    const qualities = nextConfig.images?.qualities\n    const { url, w, q } = query\n    let href: string\n\n    if (domains.length > 0) {\n      Log.warnOnce(\n        'The \"images.domains\" configuration is deprecated. Please use \"images.remotePatterns\" configuration instead.'\n      )\n    }\n\n    if (!url) {\n      return { errorMessage: '\"url\" parameter is required' }\n    } else if (Array.isArray(url)) {\n      return { errorMessage: '\"url\" parameter cannot be an array' }\n    }\n\n    if (url.length > 3072) {\n      return { errorMessage: '\"url\" parameter is too long' }\n    }\n\n    if (url.startsWith('//')) {\n      return {\n        errorMessage: '\"url\" parameter cannot be a protocol-relative URL (//)',\n      }\n    }\n\n    let isAbsolute: boolean\n\n    if (url.startsWith('/')) {\n      href = url\n      isAbsolute = false\n      if (\n        /\\/_next\\/image($|\\/)/.test(\n          decodeURIComponent(parseUrl(url)?.pathname ?? '')\n        )\n      ) {\n        return {\n          errorMessage: '\"url\" parameter cannot be recursive',\n        }\n      }\n      if (!hasLocalMatch(localPatterns, url)) {\n        return { errorMessage: '\"url\" parameter is not allowed' }\n      }\n    } else {\n      let hrefParsed: URL\n\n      try {\n        hrefParsed = new URL(url)\n        href = hrefParsed.toString()\n        isAbsolute = true\n      } catch (_error) {\n        return { errorMessage: '\"url\" parameter is invalid' }\n      }\n\n      if (!['http:', 'https:'].includes(hrefParsed.protocol)) {\n        return { errorMessage: '\"url\" parameter is invalid' }\n      }\n\n      if (!hasRemoteMatch(domains, remotePatterns, hrefParsed)) {\n        return { errorMessage: '\"url\" parameter is not allowed' }\n      }\n    }\n\n    if (!w) {\n      return { errorMessage: '\"w\" parameter (width) is required' }\n    } else if (Array.isArray(w)) {\n      return { errorMessage: '\"w\" parameter (width) cannot be an array' }\n    } else if (!/^[0-9]+$/.test(w)) {\n      return {\n        errorMessage: '\"w\" parameter (width) must be an integer greater than 0',\n      }\n    }\n\n    if (!q) {\n      return { errorMessage: '\"q\" parameter (quality) is required' }\n    } else if (Array.isArray(q)) {\n      return { errorMessage: '\"q\" parameter (quality) cannot be an array' }\n    } else if (!/^[0-9]+$/.test(q)) {\n      return {\n        errorMessage:\n          '\"q\" parameter (quality) must be an integer between 1 and 100',\n      }\n    }\n\n    const width = parseInt(w, 10)\n\n    if (width <= 0 || isNaN(width)) {\n      return {\n        errorMessage: '\"w\" parameter (width) must be an integer greater than 0',\n      }\n    }\n\n    const sizes = [...(deviceSizes || []), ...(imageSizes || [])]\n\n    if (isDev) {\n      sizes.push(BLUR_IMG_SIZE)\n    }\n\n    const isValidSize =\n      sizes.includes(width) || (isDev && width <= BLUR_IMG_SIZE)\n\n    if (!isValidSize) {\n      return {\n        errorMessage: `\"w\" parameter (width) of ${width} is not allowed`,\n      }\n    }\n\n    const quality = parseInt(q, 10)\n\n    if (isNaN(quality) || quality < 1 || quality > 100) {\n      return {\n        errorMessage:\n          '\"q\" parameter (quality) must be an integer between 1 and 100',\n      }\n    }\n\n    if (qualities) {\n      if (isDev) {\n        qualities.push(BLUR_QUALITY)\n      }\n\n      if (!qualities.includes(quality)) {\n        return {\n          errorMessage: `\"q\" parameter (quality) of ${q} is not allowed`,\n        }\n      }\n    }\n\n    const mimeType = getSupportedMimeType(formats || [], req.headers['accept'])\n\n    const isStatic = url.startsWith(\n      `${nextConfig.basePath || ''}/_next/static/media`\n    )\n\n    return {\n      href,\n      sizes,\n      isAbsolute,\n      isStatic,\n      width,\n      quality,\n      mimeType,\n      minimumCacheTTL,\n    }\n  }\n\n  static getCacheKey({\n    href,\n    width,\n    quality,\n    mimeType,\n  }: {\n    href: string\n    width: number\n    quality: number\n    mimeType: string\n  }): string {\n    return getHash([CACHE_VERSION, href, width, quality, mimeType])\n  }\n\n  constructor({\n    distDir,\n    nextConfig,\n  }: {\n    distDir: string\n    nextConfig: NextConfigComplete\n  }) {\n    this.cacheDir = join(distDir, 'cache', 'images')\n    this.nextConfig = nextConfig\n  }\n\n  async get(cacheKey: string): Promise<IncrementalResponseCacheEntry | null> {\n    try {\n      const cacheDir = join(this.cacheDir, cacheKey)\n      const files = await promises.readdir(cacheDir)\n      const now = Date.now()\n\n      for (const file of files) {\n        const [maxAgeSt, expireAtSt, etag, upstreamEtag, extension] =\n          file.split('.', 5)\n        const buffer = await promises.readFile(join(cacheDir, file))\n        const expireAt = Number(expireAtSt)\n        const maxAge = Number(maxAgeSt)\n\n        return {\n          value: {\n            kind: CachedRouteKind.IMAGE,\n            etag,\n            buffer,\n            extension,\n            upstreamEtag,\n          },\n          revalidateAfter:\n            Math.max(maxAge, this.nextConfig.images.minimumCacheTTL) * 1000 +\n            Date.now(),\n          cacheControl: { revalidate: maxAge, expire: undefined },\n          isStale: now > expireAt,\n        }\n      }\n    } catch (_) {\n      // failed to read from cache dir, treat as cache miss\n    }\n    return null\n  }\n  async set(\n    cacheKey: string,\n    value: IncrementalCacheValue | null,\n    {\n      cacheControl,\n    }: {\n      cacheControl?: CacheControl\n    }\n  ) {\n    if (!this.nextConfig.experimental.isrFlushToDisk) {\n      return\n    }\n\n    if (value?.kind !== CachedRouteKind.IMAGE) {\n      throw new Error('invariant attempted to set non-image to image-cache')\n    }\n\n    const revalidate = cacheControl?.revalidate\n\n    if (typeof revalidate !== 'number') {\n      throw new InvariantError('revalidate must be a number for image-cache')\n    }\n\n    const expireAt =\n      Math.max(revalidate, this.nextConfig.images.minimumCacheTTL) * 1000 +\n      Date.now()\n\n    try {\n      await writeToCacheDir(\n        join(this.cacheDir, cacheKey),\n        value.extension,\n        revalidate,\n        expireAt,\n        value.buffer,\n        value.etag,\n        value.upstreamEtag\n      )\n    } catch (err) {\n      Log.error(`Failed to write image to cache ${cacheKey}`, err)\n    }\n  }\n}\nexport class ImageError extends Error {\n  statusCode: number\n\n  constructor(statusCode: number, message: string) {\n    super(message)\n\n    // ensure an error status is used > 400\n    if (statusCode >= 400) {\n      this.statusCode = statusCode\n    } else {\n      this.statusCode = 500\n    }\n  }\n}\n\nfunction parseCacheControl(\n  str: string | null | undefined\n): Map<string, string> {\n  const map = new Map<string, string>()\n  if (!str) {\n    return map\n  }\n  for (let directive of str.split(',')) {\n    let [key, value] = directive.trim().split('=', 2)\n    key = key.toLowerCase()\n    if (value) {\n      value = value.toLowerCase()\n    }\n    map.set(key, value)\n  }\n  return map\n}\n\nexport function getMaxAge(str: string | null | undefined): number {\n  const map = parseCacheControl(str)\n  if (map) {\n    let age = map.get('s-maxage') || map.get('max-age') || ''\n    if (age.startsWith('\"') && age.endsWith('\"')) {\n      age = age.slice(1, -1)\n    }\n    const n = parseInt(age, 10)\n    if (!isNaN(n)) {\n      return n\n    }\n  }\n  return 0\n}\nexport function getPreviouslyCachedImageOrNull(\n  upstreamImage: ImageUpstream,\n  previousCacheEntry: IncrementalCacheEntry | null | undefined\n): CachedImageValue | null {\n  if (\n    previousCacheEntry?.value?.kind === 'IMAGE' &&\n    // Images that are SVGs, animated or failed the optimization previously end up using upstreamEtag as their etag as well,\n    // in these cases we want to trigger a new \"optimization\" attempt.\n    previousCacheEntry.value.upstreamEtag !== previousCacheEntry.value.etag &&\n    // and the upstream etag is the same as the previous cache entry's\n    upstreamImage.etag === previousCacheEntry.value.upstreamEtag\n  ) {\n    return previousCacheEntry.value\n  }\n  return null\n}\n\nexport async function optimizeImage({\n  buffer,\n  contentType,\n  quality,\n  width,\n  height,\n  concurrency,\n  limitInputPixels,\n  sequentialRead,\n  timeoutInSeconds,\n}: {\n  buffer: Buffer\n  contentType: string\n  quality: number\n  width: number\n  height?: number\n  concurrency?: number | null\n  limitInputPixels?: number\n  sequentialRead?: boolean | null\n  timeoutInSeconds?: number\n}): Promise<Buffer> {\n  const sharp = getSharp(concurrency)\n  const transformer = sharp(buffer, {\n    limitInputPixels,\n    sequentialRead: sequentialRead ?? undefined,\n  })\n    .timeout({\n      seconds: timeoutInSeconds ?? 7,\n    })\n    .rotate()\n\n  if (height) {\n    transformer.resize(width, height)\n  } else {\n    transformer.resize(width, undefined, {\n      withoutEnlargement: true,\n    })\n  }\n\n  if (contentType === AVIF) {\n    transformer.avif({\n      quality: Math.max(quality - 20, 1),\n      effort: 3,\n    })\n  } else if (contentType === WEBP) {\n    transformer.webp({ quality })\n  } else if (contentType === PNG) {\n    transformer.png({ quality })\n  } else if (contentType === JPEG) {\n    transformer.jpeg({ quality, mozjpeg: true })\n  }\n\n  const optimizedBuffer = await transformer.toBuffer()\n\n  return optimizedBuffer\n}\n\nexport async function fetchExternalImage(href: string): Promise<ImageUpstream> {\n  const res = await fetch(href, {\n    signal: AbortSignal.timeout(7_000),\n  }).catch((err) => err as Error)\n\n  if (res instanceof Error) {\n    const err = res as Error\n    if (err.name === 'TimeoutError') {\n      Log.error('upstream image response timed out for', href)\n      throw new ImageError(\n        504,\n        '\"url\" parameter is valid but upstream response timed out'\n      )\n    }\n    throw err\n  }\n\n  if (!res.ok) {\n    Log.error('upstream image response failed for', href, res.status)\n    throw new ImageError(\n      res.status,\n      '\"url\" parameter is valid but upstream response is invalid'\n    )\n  }\n\n  const buffer = Buffer.from(await res.arrayBuffer())\n  const contentType = res.headers.get('Content-Type')\n  const cacheControl = res.headers.get('Cache-Control')\n  const etag = extractEtag(res.headers.get('ETag'), buffer)\n  return { buffer, contentType, cacheControl, etag }\n}\n\nexport async function fetchInternalImage(\n  href: string,\n  _req: IncomingMessage,\n  _res: ServerResponse,\n  handleRequest: (\n    newReq: IncomingMessage,\n    newRes: ServerResponse,\n    newParsedUrl?: NextUrlWithParsedQuery\n  ) => Promise<void>\n): Promise<ImageUpstream> {\n  try {\n    const mocked = createRequestResponseMocks({\n      url: href,\n      method: _req.method || 'GET',\n      socket: _req.socket,\n    })\n\n    await handleRequest(mocked.req, mocked.res, nodeUrl.parse(href, true))\n    await mocked.res.hasStreamed\n\n    if (!mocked.res.statusCode) {\n      Log.error('image response failed for', href, mocked.res.statusCode)\n      throw new ImageError(\n        mocked.res.statusCode,\n        '\"url\" parameter is valid but internal response is invalid'\n      )\n    }\n\n    const buffer = Buffer.concat(mocked.res.buffers)\n    const contentType = mocked.res.getHeader('Content-Type')\n    const cacheControl = mocked.res.getHeader('Cache-Control')\n    const etag = extractEtag(mocked.res.getHeader('ETag'), buffer)\n\n    return { buffer, contentType, cacheControl, etag }\n  } catch (err) {\n    Log.error('upstream image response failed for', href, err)\n    throw new ImageError(\n      500,\n      '\"url\" parameter is valid but upstream response is invalid'\n    )\n  }\n}\n\nexport async function imageOptimizer(\n  imageUpstream: ImageUpstream,\n  paramsResult: Pick<\n    ImageParamsResult,\n    'href' | 'width' | 'quality' | 'mimeType'\n  >,\n  nextConfig: {\n    experimental: Pick<\n      NextConfigComplete['experimental'],\n      | 'imgOptConcurrency'\n      | 'imgOptMaxInputPixels'\n      | 'imgOptSequentialRead'\n      | 'imgOptSkipMetadata'\n      | 'imgOptTimeoutInSeconds'\n    >\n    images: Pick<\n      NextConfigComplete['images'],\n      'dangerouslyAllowSVG' | 'minimumCacheTTL'\n    >\n  },\n  opts: {\n    isDev?: boolean\n    silent?: boolean\n    previousCacheEntry?: IncrementalResponseCacheEntry | null\n  }\n): Promise<{\n  buffer: Buffer\n  contentType: string\n  maxAge: number\n  etag: string\n  upstreamEtag: string\n  error?: unknown\n}> {\n  const { href, quality, width, mimeType } = paramsResult\n  const { buffer: upstreamBuffer, etag: upstreamEtag } = imageUpstream\n  const maxAge = Math.max(\n    nextConfig.images.minimumCacheTTL,\n    getMaxAge(imageUpstream.cacheControl)\n  )\n\n  const upstreamType = await detectContentType(\n    upstreamBuffer,\n    nextConfig.experimental.imgOptSkipMetadata,\n    nextConfig.experimental.imgOptConcurrency\n  )\n\n  if (\n    !upstreamType ||\n    !upstreamType.startsWith('image/') ||\n    upstreamType.includes(',')\n  ) {\n    if (!opts.silent) {\n      Log.error(\n        \"The requested resource isn't a valid image for\",\n        href,\n        'received',\n        upstreamType\n      )\n    }\n    throw new ImageError(400, \"The requested resource isn't a valid image.\")\n  }\n  if (\n    upstreamType.startsWith('image/svg') &&\n    !nextConfig.images.dangerouslyAllowSVG\n  ) {\n    if (!opts.silent) {\n      Log.error(\n        `The requested resource \"${href}\" has type \"${upstreamType}\" but dangerouslyAllowSVG is disabled. Consider adding the \"unoptimized\" property to the <Image>.`\n      )\n    }\n    throw new ImageError(\n      400,\n      '\"url\" parameter is valid but image type is not allowed'\n    )\n  }\n  if (ANIMATABLE_TYPES.includes(upstreamType) && isAnimated(upstreamBuffer)) {\n    if (!opts.silent) {\n      Log.warnOnce(\n        `The requested resource \"${href}\" is an animated image so it will not be optimized. Consider adding the \"unoptimized\" property to the <Image>.`\n      )\n    }\n    return {\n      buffer: upstreamBuffer,\n      contentType: upstreamType,\n      maxAge,\n      etag: upstreamEtag,\n      upstreamEtag,\n    }\n  }\n  if (BYPASS_TYPES.includes(upstreamType)) {\n    return {\n      buffer: upstreamBuffer,\n      contentType: upstreamType,\n      maxAge,\n      etag: upstreamEtag,\n      upstreamEtag,\n    }\n  }\n\n  let contentType: string\n\n  if (mimeType) {\n    contentType = mimeType\n  } else if (\n    getExtension(upstreamType) &&\n    upstreamType !== WEBP &&\n    upstreamType !== AVIF\n  ) {\n    contentType = upstreamType\n  } else {\n    contentType = JPEG\n  }\n  const previouslyCachedImage = getPreviouslyCachedImageOrNull(\n    imageUpstream,\n    opts.previousCacheEntry\n  )\n  if (previouslyCachedImage) {\n    return {\n      buffer: previouslyCachedImage.buffer,\n      contentType,\n      maxAge: opts?.previousCacheEntry?.cacheControl?.revalidate || maxAge,\n      etag: previouslyCachedImage.etag,\n      upstreamEtag: previouslyCachedImage.upstreamEtag,\n    }\n  }\n\n  try {\n    let optimizedBuffer = await optimizeImage({\n      buffer: upstreamBuffer,\n      contentType,\n      quality,\n      width,\n      concurrency: nextConfig.experimental.imgOptConcurrency,\n      limitInputPixels: nextConfig.experimental.imgOptMaxInputPixels,\n      sequentialRead: nextConfig.experimental.imgOptSequentialRead,\n      timeoutInSeconds: nextConfig.experimental.imgOptTimeoutInSeconds,\n    })\n    if (opts.isDev && width <= BLUR_IMG_SIZE && quality === BLUR_QUALITY) {\n      // During `next dev`, we don't want to generate blur placeholders with webpack\n      // because it can delay starting the dev server. Instead, `next-image-loader.js`\n      // will inline a special url to lazily generate the blur placeholder at request time.\n      const meta = await getImageSize(optimizedBuffer)\n      const blurOpts = {\n        blurWidth: meta.width,\n        blurHeight: meta.height,\n        blurDataURL: `data:${contentType};base64,${optimizedBuffer.toString(\n          'base64'\n        )}`,\n      }\n      optimizedBuffer = Buffer.from(unescape(getImageBlurSvg(blurOpts)))\n      contentType = 'image/svg+xml'\n    }\n    return {\n      buffer: optimizedBuffer,\n      contentType,\n      maxAge,\n      etag: getImageEtag(optimizedBuffer),\n      upstreamEtag,\n    }\n  } catch (error) {\n    if (upstreamType) {\n      // If we fail to optimize, fallback to the original image\n      return {\n        buffer: upstreamBuffer,\n        contentType: upstreamType,\n        maxAge: nextConfig.images.minimumCacheTTL,\n        etag: upstreamEtag,\n        upstreamEtag,\n        error,\n      }\n    } else {\n      throw new ImageError(\n        400,\n        'Unable to optimize image and unable to fallback to upstream image'\n      )\n    }\n  }\n}\n\nfunction getFileNameWithExtension(\n  url: string,\n  contentType: string | null\n): string {\n  const [urlWithoutQueryParams] = url.split('?', 1)\n  const fileNameWithExtension = urlWithoutQueryParams.split('/').pop()\n  if (!contentType || !fileNameWithExtension) {\n    return 'image.bin'\n  }\n\n  const [fileName] = fileNameWithExtension.split('.', 1)\n  const extension = getExtension(contentType)\n  return `${fileName}.${extension}`\n}\n\nfunction setResponseHeaders(\n  req: IncomingMessage,\n  res: ServerResponse,\n  url: string,\n  etag: string,\n  contentType: string | null,\n  isStatic: boolean,\n  xCache: XCacheHeader,\n  imagesConfig: ImageConfigComplete,\n  maxAge: number,\n  isDev: boolean\n) {\n  res.setHeader('Vary', 'Accept')\n  res.setHeader(\n    'Cache-Control',\n    isStatic\n      ? 'public, max-age=315360000, immutable'\n      : `public, max-age=${isDev ? 0 : maxAge}, must-revalidate`\n  )\n  if (sendEtagResponse(req, res, etag)) {\n    // already called res.end() so we're finished\n    return { finished: true }\n  }\n  if (contentType) {\n    res.setHeader('Content-Type', contentType)\n  }\n\n  const fileName = getFileNameWithExtension(url, contentType)\n  res.setHeader(\n    'Content-Disposition',\n    contentDisposition(fileName, { type: imagesConfig.contentDispositionType })\n  )\n\n  res.setHeader('Content-Security-Policy', imagesConfig.contentSecurityPolicy)\n  res.setHeader('X-Nextjs-Cache', xCache)\n\n  return { finished: false }\n}\n\nexport function sendResponse(\n  req: IncomingMessage,\n  res: ServerResponse,\n  url: string,\n  extension: string,\n  buffer: Buffer,\n  etag: string,\n  isStatic: boolean,\n  xCache: XCacheHeader,\n  imagesConfig: ImageConfigComplete,\n  maxAge: number,\n  isDev: boolean\n) {\n  const contentType = getContentType(extension)\n  const result = setResponseHeaders(\n    req,\n    res,\n    url,\n    etag,\n    contentType,\n    isStatic,\n    xCache,\n    imagesConfig,\n    maxAge,\n    isDev\n  )\n  if (!result.finished) {\n    res.setHeader('Content-Length', Buffer.byteLength(buffer))\n    res.end(buffer)\n  }\n}\n\nexport async function getImageSize(buffer: Buffer): Promise<{\n  width?: number\n  height?: number\n}> {\n  const { width, height } = imageSizeOf(buffer)\n  return { width, height }\n}\n"], "names": ["ImageError", "ImageOptimizerCache", "detectContentType", "extractEtag", "fetchExternalImage", "fetchInternalImage", "getHash", "getImageEtag", "getImageSize", "getMaxAge", "getPreviouslyCachedImageOrNull", "getSharp", "imageOptimizer", "optimizeImage", "sendResponse", "AVIF", "WEBP", "PNG", "JPEG", "JXL", "JP2", "HEIC", "GIF", "SVG", "ICO", "ICNS", "TIFF", "BMP", "PDF", "CACHE_VERSION", "ANIMATABLE_TYPES", "BYPASS_TYPES", "BLUR_IMG_SIZE", "BLUR_QUALITY", "_sharp", "concurrency", "require", "divisor", "process", "env", "NODE_ENV", "Math", "floor", "max", "e", "isError", "code", "Error", "getSupportedMimeType", "options", "accept", "mimeType", "mediaType", "includes", "items", "hash", "createHash", "item", "update", "String", "digest", "etag", "imageBuffer", "<PERSON><PERSON><PERSON>", "from", "toString", "image", "writeToCacheDir", "dir", "extension", "maxAge", "expireAt", "buffer", "upstreamEtag", "filename", "join", "promises", "rm", "recursive", "force", "catch", "mkdir", "writeFile", "skipMetadata", "byteLength", "every", "b", "i", "format", "detector", "sharp", "meta", "metadata", "_", "undefined", "validateParams", "req", "query", "nextConfig", "isDev", "imageData", "images", "deviceSizes", "imageSizes", "domains", "minimumCacheTTL", "formats", "remotePatterns", "localPatterns", "qualities", "url", "w", "q", "href", "length", "Log", "warnOnce", "errorMessage", "Array", "isArray", "startsWith", "isAbsolute", "parseUrl", "test", "decodeURIComponent", "pathname", "hasLocalMatch", "hrefParsed", "URL", "_error", "protocol", "hasRemoteMatch", "width", "parseInt", "isNaN", "sizes", "push", "isValidSize", "quality", "headers", "isStatic", "basePath", "get<PERSON><PERSON><PERSON><PERSON>", "constructor", "distDir", "cacheDir", "get", "cache<PERSON>ey", "files", "readdir", "now", "Date", "file", "maxAgeSt", "expireAtSt", "split", "readFile", "Number", "value", "kind", "CachedRouteKind", "IMAGE", "revalidateAfter", "cacheControl", "revalidate", "expire", "isStale", "set", "experimental", "isrFlushToDisk", "InvariantError", "err", "error", "statusCode", "message", "parseCacheControl", "str", "map", "Map", "directive", "key", "trim", "toLowerCase", "age", "endsWith", "slice", "n", "upstreamImage", "previousCacheEntry", "contentType", "height", "limitInputPixels", "sequentialRead", "timeoutInSeconds", "transformer", "timeout", "seconds", "rotate", "resize", "withoutEnlargement", "avif", "effort", "webp", "png", "jpeg", "mozjpeg", "optimizedBuffer", "<PERSON><PERSON><PERSON><PERSON>", "res", "fetch", "signal", "AbortSignal", "name", "ok", "status", "arrayBuffer", "_req", "_res", "handleRequest", "mocked", "createRequestResponseMocks", "method", "socket", "nodeUrl", "parse", "hasStreamed", "concat", "buffers", "<PERSON><PERSON><PERSON><PERSON>", "imageUpstream", "paramsResult", "opts", "upstreamBuffer", "upstreamType", "imgOptSkipMetadata", "imgOptConcurrency", "silent", "dangerouslyAllowSVG", "isAnimated", "getExtension", "previouslyCachedImage", "imgOptMaxInputPixels", "imgOptSequentialRead", "imgOptTimeoutInSeconds", "blurOpts", "blur<PERSON>idth", "blurHeight", "blurDataURL", "unescape", "getImageBlurSvg", "getFileNameWithExtension", "urlWithoutQueryParams", "fileNameWithExtension", "pop", "fileName", "setResponseHeaders", "xCache", "imagesConfig", "<PERSON><PERSON><PERSON><PERSON>", "sendEtagResponse", "finished", "contentDisposition", "type", "contentDispositionType", "contentSecurityPolicy", "getContentType", "result", "end", "imageSizeOf"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;IA+jBaA,UAAU;eAAVA;;IA1QAC,mBAAmB;eAAnBA;;IAtJSC,iBAAiB;eAAjBA;;IAzCNC,WAAW;eAAXA;;IAkkBMC,kBAAkB;eAAlBA;;IAgCAC,kBAAkB;eAAlBA;;IA9mBNC,OAAO;eAAPA;;IAwBAC,YAAY;eAAZA;;IA04BMC,YAAY;eAAZA;;IA5aNC,SAAS;eAATA;;IAcAC,8BAA8B;eAA9BA;;IArjBAC,QAAQ;eAARA;;IA0sBMC,cAAc;eAAdA;;IApIAC,aAAa;eAAbA;;IA6WNC,YAAY;eAAZA;;;wBA5+BW;oBACF;wBAEC;2EACK;kEACP;0BACC;mEACF;sBACF;4DAC4B;8BAEjB;mCAEF;oCACC;6BAEY;+BAQpC;6BAC0B;6BACY;6DACxB;gEACD;sBACK;gCAEM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI/B,MAAMC,OAAO;AACb,MAAMC,OAAO;AACb,MAAMC,MAAM;AACZ,MAAMC,OAAO;AACb,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,OAAO;AACb,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,OAAO;AACb,MAAMC,OAAO;AACb,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,gBAAgB;AACtB,MAAMC,mBAAmB;IAACd;IAAMC;IAAKK;CAAI;AACzC,MAAMS,eAAe;IAACR;IAAKC;IAAKC;IAAME;IAAKR;IAAKE;CAAK;AACrD,MAAMW,gBAAgB,EAAE,mCAAmC;;AAC3D,MAAMC,eAAe,GAAG,mCAAmC;;AAE3D,IAAIC;AAEG,SAASvB,SAASwB,WAAsC;IAC7D,IAAID,QAAQ;QACV,OAAOA;IACT;IACA,IAAI;QACFA,SAASE,QAAQ;QACjB,IAAIF,UAAUA,OAAOC,WAAW,KAAK,GAAG;YACtC,2DAA2D;YAC3D,8DAA8D;YAC9D,0DAA0D;YAC1D,MAAME,UAAUC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,IAAI;YAC7DN,OAAOC,WAAW,CAChBA,eAAeM,KAAKC,KAAK,CAACD,KAAKE,GAAG,CAACT,OAAOC,WAAW,KAAKE,SAAS;QAEvE;IACF,EAAE,OAAOO,GAAY;QACnB,IAAIC,IAAAA,gBAAO,EAACD,MAAMA,EAAEE,IAAI,KAAK,oBAAoB;YAC/C,MAAM,qBAEL,CAFK,IAAIC,MACR,yFADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,MAAMH;IACR;IACA,OAAOV;AACT;AAoBA,SAASc,qBAAqBC,OAAiB,EAAEC,SAAS,EAAE;IAC1D,MAAMC,WAAWC,IAAAA,iBAAS,EAACF,QAAQD;IACnC,OAAOC,OAAOG,QAAQ,CAACF,YAAYA,WAAW;AAChD;AAEO,SAAS7C,QAAQgD,KAAmC;IACzD,MAAMC,OAAOC,IAAAA,kBAAU,EAAC;IACxB,KAAK,IAAIC,QAAQH,MAAO;QACtB,IAAI,OAAOG,SAAS,UAAUF,KAAKG,MAAM,CAACC,OAAOF;aAC5C;YACHF,KAAKG,MAAM,CAACD;QACd;IACF;IACA,4DAA4D;IAC5D,OAAOF,KAAKK,MAAM,CAAC;AACrB;AAEO,SAASzD,YACd0D,IAA+B,EAC/BC,WAAmB;IAEnB,IAAID,MAAM;QACR,yEAAyE;QACzE,iDAAiD;QACjD,OAAOE,OAAOC,IAAI,CAACH,MAAMI,QAAQ,CAAC;IACpC;IACA,OAAO1D,aAAauD;AACtB;AAEO,SAASvD,aAAa2D,KAAa;IACxC,OAAO5D,QAAQ;QAAC4D;KAAM;AACxB;AAEA,eAAeC,gBACbC,GAAW,EACXC,SAAiB,EACjBC,MAAc,EACdC,QAAgB,EAChBC,MAAc,EACdX,IAAY,EACZY,YAAoB;IAEpB,MAAMC,WAAWC,IAAAA,UAAI,EACnBP,KACA,GAAGE,OAAO,CAAC,EAAEC,SAAS,CAAC,EAAEV,KAAK,CAAC,EAAEY,aAAa,CAAC,EAAEJ,WAAW;IAG9D,MAAMO,YAAQ,CAACC,EAAE,CAACT,KAAK;QAAEU,WAAW;QAAMC,OAAO;IAAK,GAAGC,KAAK,CAAC,KAAO;IAEtE,MAAMJ,YAAQ,CAACK,KAAK,CAACb,KAAK;QAAEU,WAAW;IAAK;IAC5C,MAAMF,YAAQ,CAACM,SAAS,CAACR,UAAUF;AACrC;AAOO,eAAetE,kBACpBsE,MAAc,EACdW,YAAwC,EACxChD,WAAuC;IAEvC,IAAIqC,OAAOY,UAAU,KAAK,GAAG;QAC3B,OAAO;IACT;IACA,IAAI;QAAC;QAAM;QAAM;KAAK,CAACC,KAAK,CAAC,CAACC,GAAGC,IAAMf,MAAM,CAACe,EAAE,KAAKD,IAAI;QACvD,OAAOpE;IACT;IACA,IACE;QAAC;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK,CAACmE,KAAK,CACpD,CAACC,GAAGC,IAAMf,MAAM,CAACe,EAAE,KAAKD,IAE1B;QACA,OAAOrE;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAACoE,KAAK,CAAC,CAACC,GAAGC,IAAMf,MAAM,CAACe,EAAE,KAAKD,IAAI;QAC7D,OAAOhE;IACT;IACA,IACE;QAAC;QAAM;QAAM;QAAM;QAAM;QAAG;QAAG;QAAG;QAAG;QAAM;QAAM;QAAM;KAAK,CAAC+D,KAAK,CAChE,CAACC,GAAGC,IAAM,CAACD,KAAKd,MAAM,CAACe,EAAE,KAAKD,IAEhC;QACA,OAAOtE;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;QAAM;KAAK,CAACqE,KAAK,CAAC,CAACC,GAAGC,IAAMf,MAAM,CAACe,EAAE,KAAKD,IAAI;QACnE,OAAO/D;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAAC8D,KAAK,CAAC,CAACC,GAAGC,IAAMf,MAAM,CAACe,EAAE,KAAKD,IAAI;QAC7D,OAAO/D;IACT;IACA,IACE;QAAC;QAAG;QAAG;QAAG;QAAG;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK,CAAC8D,KAAK,CAChE,CAACC,GAAGC,IAAM,CAACD,KAAKd,MAAM,CAACe,EAAE,KAAKD,IAEhC;QACA,OAAOvE;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAACsE,KAAK,CAAC,CAACC,GAAGC,IAAMf,MAAM,CAACe,EAAE,KAAKD,IAAI;QAC7D,OAAO9D;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAAC6D,KAAK,CAAC,CAACC,GAAGC,IAAMf,MAAM,CAACe,EAAE,KAAKD,IAAI;QAC7D,OAAO7D;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAAC4D,KAAK,CAAC,CAACC,GAAGC,IAAMf,MAAM,CAACe,EAAE,KAAKD,IAAI;QAC7D,OAAO5D;IACT;IACA,IAAI;QAAC;QAAM;KAAK,CAAC2D,KAAK,CAAC,CAACC,GAAGC,IAAMf,MAAM,CAACe,EAAE,KAAKD,IAAI;QACjD,OAAO3D;IACT;IACA,IAAI;QAAC;QAAM;KAAK,CAAC0D,KAAK,CAAC,CAACC,GAAGC,IAAMf,MAAM,CAACe,EAAE,KAAKD,IAAI;QACjD,OAAOnE;IACT;IACA,IACE;QACE;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KACnE,CAACkE,KAAK,CAAC,CAACC,GAAGC,IAAMf,MAAM,CAACe,EAAE,KAAKD,IAChC;QACA,OAAOnE;IACT;IACA,IACE;QAAC;QAAG;QAAG;QAAG;QAAG;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK,CAACkE,KAAK,CAChE,CAACC,GAAGC,IAAM,CAACD,KAAKd,MAAM,CAACe,EAAE,KAAKD,IAEhC;QACA,OAAOjE;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;QAAM;KAAK,CAACgE,KAAK,CAAC,CAACC,GAAGC,IAAMf,MAAM,CAACe,EAAE,KAAKD,IAAI;QACnE,OAAO1D;IACT;IACA,IACE;QACE;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KACnE,CAACyD,KAAK,CAAC,CAACC,GAAGC,IAAMf,MAAM,CAACe,EAAE,KAAKD,IAChC;QACA,OAAOlE;IACT;IAEA,IAAIoE;IAIJA,SAASC,IAAAA,kBAAQ,EAACjB;IAElB,IAAI,CAACgB,UAAU,CAACL,cAAc;QAC5B,MAAMO,QAAQ/E,SAASwB;QACvB,MAAMwD,OAAO,MAAMD,MAAMlB,QACtBoB,QAAQ,GACRZ,KAAK,CAAC,CAACa,IAAM;QAChBL,SAASG,wBAAAA,KAAMH,MAAM;IACvB;IAEA,OAAQA;QACN,KAAK;YACH,OAAOzE;QACT,KAAK;YACH,OAAOC;QACT,KAAK;YACH,OAAOC;QACT,KAAK;QACL,KAAK;YACH,OAAOC;QACT,KAAK;YACH,OAAOI;QACT,KAAK;YACH,OAAOC;QACT,KAAK;QACL,KAAK;YACH,OAAOJ;QACT,KAAK;YACH,OAAOC;QACT,KAAK;QACL,KAAK;YACH,OAAOM;QACT,KAAK;YACH,OAAOE;QACT,KAAK;YACH,OAAOD;QACT,KAAK;YACH,OAAOH;QACT,KAAK;YACH,OAAOC;QACT,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAKqE;QACL;YACE,OAAO;IACX;AACF;AAEO,MAAM7F;IAIX,OAAO8F,eACLC,GAAoB,EACpBC,KAAkC,EAClCC,UAA8B,EAC9BC,KAAc,EACgC;YASvBD,oBACDA,qBACJA;QAVlB,MAAME,YAAYF,WAAWG,MAAM;QACnC,MAAM,EACJC,cAAc,EAAE,EAChBC,aAAa,EAAE,EACfC,UAAU,EAAE,EACZC,kBAAkB,EAAE,EACpBC,UAAU;YAAC;SAAa,EACzB,GAAGN;QACJ,MAAMO,iBAAiBT,EAAAA,qBAAAA,WAAWG,MAAM,qBAAjBH,mBAAmBS,cAAc,KAAI,EAAE;QAC9D,MAAMC,iBAAgBV,sBAAAA,WAAWG,MAAM,qBAAjBH,oBAAmBU,aAAa;QACtD,MAAMC,aAAYX,sBAAAA,WAAWG,MAAM,qBAAjBH,oBAAmBW,SAAS;QAC9C,MAAM,EAAEC,GAAG,EAAEC,CAAC,EAAEC,CAAC,EAAE,GAAGf;QACtB,IAAIgB;QAEJ,IAAIT,QAAQU,MAAM,GAAG,GAAG;YACtBC,KAAIC,QAAQ,CACV;QAEJ;QAEA,IAAI,CAACN,KAAK;YACR,OAAO;gBAAEO,cAAc;YAA8B;QACvD,OAAO,IAAIC,MAAMC,OAAO,CAACT,MAAM;YAC7B,OAAO;gBAAEO,cAAc;YAAqC;QAC9D;QAEA,IAAIP,IAAII,MAAM,GAAG,MAAM;YACrB,OAAO;gBAAEG,cAAc;YAA8B;QACvD;QAEA,IAAIP,IAAIU,UAAU,CAAC,OAAO;YACxB,OAAO;gBACLH,cAAc;YAChB;QACF;QAEA,IAAII;QAEJ,IAAIX,IAAIU,UAAU,CAAC,MAAM;gBAKAE;YAJvBT,OAAOH;YACPW,aAAa;YACb,IACE,uBAAuBE,IAAI,CACzBC,mBAAmBF,EAAAA,YAAAA,IAAAA,cAAQ,EAACZ,yBAATY,UAAeG,QAAQ,KAAI,MAEhD;gBACA,OAAO;oBACLR,cAAc;gBAChB;YACF;YACA,IAAI,CAACS,IAAAA,gCAAa,EAAClB,eAAeE,MAAM;gBACtC,OAAO;oBAAEO,cAAc;gBAAiC;YAC1D;QACF,OAAO;YACL,IAAIU;YAEJ,IAAI;gBACFA,aAAa,IAAIC,IAAIlB;gBACrBG,OAAOc,WAAW9D,QAAQ;gBAC1BwD,aAAa;YACf,EAAE,OAAOQ,QAAQ;gBACf,OAAO;oBAAEZ,cAAc;gBAA6B;YACtD;YAEA,IAAI,CAAC;gBAAC;gBAAS;aAAS,CAAChE,QAAQ,CAAC0E,WAAWG,QAAQ,GAAG;gBACtD,OAAO;oBAAEb,cAAc;gBAA6B;YACtD;YAEA,IAAI,CAACc,IAAAA,kCAAc,EAAC3B,SAASG,gBAAgBoB,aAAa;gBACxD,OAAO;oBAAEV,cAAc;gBAAiC;YAC1D;QACF;QAEA,IAAI,CAACN,GAAG;YACN,OAAO;gBAAEM,cAAc;YAAoC;QAC7D,OAAO,IAAIC,MAAMC,OAAO,CAACR,IAAI;YAC3B,OAAO;gBAAEM,cAAc;YAA2C;QACpE,OAAO,IAAI,CAAC,WAAWM,IAAI,CAACZ,IAAI;YAC9B,OAAO;gBACLM,cAAc;YAChB;QACF;QAEA,IAAI,CAACL,GAAG;YACN,OAAO;gBAAEK,cAAc;YAAsC;QAC/D,OAAO,IAAIC,MAAMC,OAAO,CAACP,IAAI;YAC3B,OAAO;gBAAEK,cAAc;YAA6C;QACtE,OAAO,IAAI,CAAC,WAAWM,IAAI,CAACX,IAAI;YAC9B,OAAO;gBACLK,cACE;YACJ;QACF;QAEA,MAAMe,QAAQC,SAAStB,GAAG;QAE1B,IAAIqB,SAAS,KAAKE,MAAMF,QAAQ;YAC9B,OAAO;gBACLf,cAAc;YAChB;QACF;QAEA,MAAMkB,QAAQ;eAAKjC,eAAe,EAAE;eAAOC,cAAc,EAAE;SAAE;QAE7D,IAAIJ,OAAO;YACToC,MAAMC,IAAI,CAACxG;QACb;QAEA,MAAMyG,cACJF,MAAMlF,QAAQ,CAAC+E,UAAWjC,SAASiC,SAASpG;QAE9C,IAAI,CAACyG,aAAa;YAChB,OAAO;gBACLpB,cAAc,CAAC,yBAAyB,EAAEe,MAAM,eAAe,CAAC;YAClE;QACF;QAEA,MAAMM,UAAUL,SAASrB,GAAG;QAE5B,IAAIsB,MAAMI,YAAYA,UAAU,KAAKA,UAAU,KAAK;YAClD,OAAO;gBACLrB,cACE;YACJ;QACF;QAEA,IAAIR,WAAW;YACb,IAAIV,OAAO;gBACTU,UAAU2B,IAAI,CAACvG;YACjB;YAEA,IAAI,CAAC4E,UAAUxD,QAAQ,CAACqF,UAAU;gBAChC,OAAO;oBACLrB,cAAc,CAAC,2BAA2B,EAAEL,EAAE,eAAe,CAAC;gBAChE;YACF;QACF;QAEA,MAAM7D,WAAWH,qBAAqB0D,WAAW,EAAE,EAAEV,IAAI2C,OAAO,CAAC,SAAS;QAE1E,MAAMC,WAAW9B,IAAIU,UAAU,CAC7B,GAAGtB,WAAW2C,QAAQ,IAAI,GAAG,mBAAmB,CAAC;QAGnD,OAAO;YACL5B;YACAsB;YACAd;YACAmB;YACAR;YACAM;YACAvF;YACAsD;QACF;IACF;IAEA,OAAOqC,YAAY,EACjB7B,IAAI,EACJmB,KAAK,EACLM,OAAO,EACPvF,QAAQ,EAMT,EAAU;QACT,OAAO7C,QAAQ;YAACuB;YAAeoF;YAAMmB;YAAOM;YAASvF;SAAS;IAChE;IAEA4F,YAAY,EACVC,OAAO,EACP9C,UAAU,EAIX,CAAE;QACD,IAAI,CAAC+C,QAAQ,GAAGtE,IAAAA,UAAI,EAACqE,SAAS,SAAS;QACvC,IAAI,CAAC9C,UAAU,GAAGA;IACpB;IAEA,MAAMgD,IAAIC,QAAgB,EAAiD;QACzE,IAAI;YACF,MAAMF,WAAWtE,IAAAA,UAAI,EAAC,IAAI,CAACsE,QAAQ,EAAEE;YACrC,MAAMC,QAAQ,MAAMxE,YAAQ,CAACyE,OAAO,CAACJ;YACrC,MAAMK,MAAMC,KAAKD,GAAG;YAEpB,KAAK,MAAME,QAAQJ,MAAO;gBACxB,MAAM,CAACK,UAAUC,YAAY7F,MAAMY,cAAcJ,UAAU,GACzDmF,KAAKG,KAAK,CAAC,KAAK;gBAClB,MAAMnF,SAAS,MAAMI,YAAQ,CAACgF,QAAQ,CAACjF,IAAAA,UAAI,EAACsE,UAAUO;gBACtD,MAAMjF,WAAWsF,OAAOH;gBACxB,MAAMpF,SAASuF,OAAOJ;gBAEtB,OAAO;oBACLK,OAAO;wBACLC,MAAMC,8BAAe,CAACC,KAAK;wBAC3BpG;wBACAW;wBACAH;wBACAI;oBACF;oBACAyF,iBACEzH,KAAKE,GAAG,CAAC2B,QAAQ,IAAI,CAAC4B,UAAU,CAACG,MAAM,CAACI,eAAe,IAAI,OAC3D8C,KAAKD,GAAG;oBACVa,cAAc;wBAAEC,YAAY9F;wBAAQ+F,QAAQvE;oBAAU;oBACtDwE,SAAShB,MAAM/E;gBACjB;YACF;QACF,EAAE,OAAOsB,GAAG;QACV,qDAAqD;QACvD;QACA,OAAO;IACT;IACA,MAAM0E,IACJpB,QAAgB,EAChBW,KAAmC,EACnC,EACEK,YAAY,EAGb,EACD;QACA,IAAI,CAAC,IAAI,CAACjE,UAAU,CAACsE,YAAY,CAACC,cAAc,EAAE;YAChD;QACF;QAEA,IAAIX,CAAAA,yBAAAA,MAAOC,IAAI,MAAKC,8BAAe,CAACC,KAAK,EAAE;YACzC,MAAM,qBAAgE,CAAhE,IAAIlH,MAAM,wDAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAA+D;QACvE;QAEA,MAAMqH,aAAaD,gCAAAA,aAAcC,UAAU;QAE3C,IAAI,OAAOA,eAAe,UAAU;YAClC,MAAM,qBAAiE,CAAjE,IAAIM,8BAAc,CAAC,gDAAnB,qBAAA;uBAAA;4BAAA;8BAAA;YAAgE;QACxE;QAEA,MAAMnG,WACJ9B,KAAKE,GAAG,CAACyH,YAAY,IAAI,CAAClE,UAAU,CAACG,MAAM,CAACI,eAAe,IAAI,OAC/D8C,KAAKD,GAAG;QAEV,IAAI;YACF,MAAMnF,gBACJQ,IAAAA,UAAI,EAAC,IAAI,CAACsE,QAAQ,EAAEE,WACpBW,MAAMzF,SAAS,EACf+F,YACA7F,UACAuF,MAAMtF,MAAM,EACZsF,MAAMjG,IAAI,EACViG,MAAMrF,YAAY;QAEtB,EAAE,OAAOkG,KAAK;YACZxD,KAAIyD,KAAK,CAAC,CAAC,+BAA+B,EAAEzB,UAAU,EAAEwB;QAC1D;IACF;AACF;AACO,MAAM3K,mBAAmB+C;IAG9BgG,YAAY8B,UAAkB,EAAEC,OAAe,CAAE;QAC/C,KAAK,CAACA;QAEN,uCAAuC;QACvC,IAAID,cAAc,KAAK;YACrB,IAAI,CAACA,UAAU,GAAGA;QACpB,OAAO;YACL,IAAI,CAACA,UAAU,GAAG;QACpB;IACF;AACF;AAEA,SAASE,kBACPC,GAA8B;IAE9B,MAAMC,MAAM,IAAIC;IAChB,IAAI,CAACF,KAAK;QACR,OAAOC;IACT;IACA,KAAK,IAAIE,aAAaH,IAAIrB,KAAK,CAAC,KAAM;QACpC,IAAI,CAACyB,KAAKtB,MAAM,GAAGqB,UAAUE,IAAI,GAAG1B,KAAK,CAAC,KAAK;QAC/CyB,MAAMA,IAAIE,WAAW;QACrB,IAAIxB,OAAO;YACTA,QAAQA,MAAMwB,WAAW;QAC3B;QACAL,IAAIV,GAAG,CAACa,KAAKtB;IACf;IACA,OAAOmB;AACT;AAEO,SAASxK,UAAUuK,GAA8B;IACtD,MAAMC,MAAMF,kBAAkBC;IAC9B,IAAIC,KAAK;QACP,IAAIM,MAAMN,IAAI/B,GAAG,CAAC,eAAe+B,IAAI/B,GAAG,CAAC,cAAc;QACvD,IAAIqC,IAAI/D,UAAU,CAAC,QAAQ+D,IAAIC,QAAQ,CAAC,MAAM;YAC5CD,MAAMA,IAAIE,KAAK,CAAC,GAAG,CAAC;QACtB;QACA,MAAMC,IAAIrD,SAASkD,KAAK;QACxB,IAAI,CAACjD,MAAMoD,IAAI;YACb,OAAOA;QACT;IACF;IACA,OAAO;AACT;AACO,SAAShL,+BACdiL,aAA4B,EAC5BC,kBAA4D;QAG1DA;IADF,IACEA,CAAAA,uCAAAA,4BAAAA,mBAAoB9B,KAAK,qBAAzB8B,0BAA2B7B,IAAI,MAAK,WACpC,wHAAwH;IACxH,kEAAkE;IAClE6B,mBAAmB9B,KAAK,CAACrF,YAAY,KAAKmH,mBAAmB9B,KAAK,CAACjG,IAAI,IACvE,kEAAkE;IAClE8H,cAAc9H,IAAI,KAAK+H,mBAAmB9B,KAAK,CAACrF,YAAY,EAC5D;QACA,OAAOmH,mBAAmB9B,KAAK;IACjC;IACA,OAAO;AACT;AAEO,eAAejJ,cAAc,EAClC2D,MAAM,EACNqH,WAAW,EACXnD,OAAO,EACPN,KAAK,EACL0D,MAAM,EACN3J,WAAW,EACX4J,gBAAgB,EAChBC,cAAc,EACdC,gBAAgB,EAWjB;IACC,MAAMvG,QAAQ/E,SAASwB;IACvB,MAAM+J,cAAcxG,MAAMlB,QAAQ;QAChCuH;QACAC,gBAAgBA,kBAAkBlG;IACpC,GACGqG,OAAO,CAAC;QACPC,SAASH,oBAAoB;IAC/B,GACCI,MAAM;IAET,IAAIP,QAAQ;QACVI,YAAYI,MAAM,CAAClE,OAAO0D;IAC5B,OAAO;QACLI,YAAYI,MAAM,CAAClE,OAAOtC,WAAW;YACnCyG,oBAAoB;QACtB;IACF;IAEA,IAAIV,gBAAgB9K,MAAM;QACxBmL,YAAYM,IAAI,CAAC;YACf9D,SAASjG,KAAKE,GAAG,CAAC+F,UAAU,IAAI;YAChC+D,QAAQ;QACV;IACF,OAAO,IAAIZ,gBAAgB7K,MAAM;QAC/BkL,YAAYQ,IAAI,CAAC;YAAEhE;QAAQ;IAC7B,OAAO,IAAImD,gBAAgB5K,KAAK;QAC9BiL,YAAYS,GAAG,CAAC;YAAEjE;QAAQ;IAC5B,OAAO,IAAImD,gBAAgB3K,MAAM;QAC/BgL,YAAYU,IAAI,CAAC;YAAElE;YAASmE,SAAS;QAAK;IAC5C;IAEA,MAAMC,kBAAkB,MAAMZ,YAAYa,QAAQ;IAElD,OAAOD;AACT;AAEO,eAAe1M,mBAAmB6G,IAAY;IACnD,MAAM+F,MAAM,MAAMC,MAAMhG,MAAM;QAC5BiG,QAAQC,YAAYhB,OAAO,CAAC;IAC9B,GAAGnH,KAAK,CAAC,CAAC2F,MAAQA;IAElB,IAAIqC,eAAejK,OAAO;QACxB,MAAM4H,MAAMqC;QACZ,IAAIrC,IAAIyC,IAAI,KAAK,gBAAgB;YAC/BjG,KAAIyD,KAAK,CAAC,yCAAyC3D;YACnD,MAAM,qBAGL,CAHK,IAAIjH,WACR,KACA,6DAFI,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QACA,MAAM2K;IACR;IAEA,IAAI,CAACqC,IAAIK,EAAE,EAAE;QACXlG,KAAIyD,KAAK,CAAC,sCAAsC3D,MAAM+F,IAAIM,MAAM;QAChE,MAAM,qBAGL,CAHK,IAAItN,WACRgN,IAAIM,MAAM,EACV,8DAFI,qBAAA;mBAAA;wBAAA;0BAAA;QAGN;IACF;IAEA,MAAM9I,SAAST,OAAOC,IAAI,CAAC,MAAMgJ,IAAIO,WAAW;IAChD,MAAM1B,cAAcmB,IAAIrE,OAAO,CAACO,GAAG,CAAC;IACpC,MAAMiB,eAAe6C,IAAIrE,OAAO,CAACO,GAAG,CAAC;IACrC,MAAMrF,OAAO1D,YAAY6M,IAAIrE,OAAO,CAACO,GAAG,CAAC,SAAS1E;IAClD,OAAO;QAAEA;QAAQqH;QAAa1B;QAActG;IAAK;AACnD;AAEO,eAAexD,mBACpB4G,IAAY,EACZuG,IAAqB,EACrBC,IAAoB,EACpBC,aAIkB;IAElB,IAAI;QACF,MAAMC,SAASC,IAAAA,uCAA0B,EAAC;YACxC9G,KAAKG;YACL4G,QAAQL,KAAKK,MAAM,IAAI;YACvBC,QAAQN,KAAKM,MAAM;QACrB;QAEA,MAAMJ,cAAcC,OAAO3H,GAAG,EAAE2H,OAAOX,GAAG,EAAEe,YAAO,CAACC,KAAK,CAAC/G,MAAM;QAChE,MAAM0G,OAAOX,GAAG,CAACiB,WAAW;QAE5B,IAAI,CAACN,OAAOX,GAAG,CAACnC,UAAU,EAAE;YAC1B1D,KAAIyD,KAAK,CAAC,6BAA6B3D,MAAM0G,OAAOX,GAAG,CAACnC,UAAU;YAClE,MAAM,qBAGL,CAHK,IAAI7K,WACR2N,OAAOX,GAAG,CAACnC,UAAU,EACrB,8DAFI,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QAEA,MAAMrG,SAAST,OAAOmK,MAAM,CAACP,OAAOX,GAAG,CAACmB,OAAO;QAC/C,MAAMtC,cAAc8B,OAAOX,GAAG,CAACoB,SAAS,CAAC;QACzC,MAAMjE,eAAewD,OAAOX,GAAG,CAACoB,SAAS,CAAC;QAC1C,MAAMvK,OAAO1D,YAAYwN,OAAOX,GAAG,CAACoB,SAAS,CAAC,SAAS5J;QAEvD,OAAO;YAAEA;YAAQqH;YAAa1B;YAActG;QAAK;IACnD,EAAE,OAAO8G,KAAK;QACZxD,KAAIyD,KAAK,CAAC,sCAAsC3D,MAAM0D;QACtD,MAAM,qBAGL,CAHK,IAAI3K,WACR,KACA,8DAFI,qBAAA;mBAAA;wBAAA;0BAAA;QAGN;IACF;AACF;AAEO,eAAeY,eACpByN,aAA4B,EAC5BC,YAGC,EACDpI,UAaC,EACDqI,IAIC;IASD,MAAM,EAAEtH,IAAI,EAAEyB,OAAO,EAAEN,KAAK,EAAEjF,QAAQ,EAAE,GAAGmL;IAC3C,MAAM,EAAE9J,QAAQgK,cAAc,EAAE3K,MAAMY,YAAY,EAAE,GAAG4J;IACvD,MAAM/J,SAAS7B,KAAKE,GAAG,CACrBuD,WAAWG,MAAM,CAACI,eAAe,EACjChG,UAAU4N,cAAclE,YAAY;IAGtC,MAAMsE,eAAe,MAAMvO,kBACzBsO,gBACAtI,WAAWsE,YAAY,CAACkE,kBAAkB,EAC1CxI,WAAWsE,YAAY,CAACmE,iBAAiB;IAG3C,IACE,CAACF,gBACD,CAACA,aAAajH,UAAU,CAAC,aACzBiH,aAAapL,QAAQ,CAAC,MACtB;QACA,IAAI,CAACkL,KAAKK,MAAM,EAAE;YAChBzH,KAAIyD,KAAK,CACP,kDACA3D,MACA,YACAwH;QAEJ;QACA,MAAM,qBAAkE,CAAlE,IAAIzO,WAAW,KAAK,gDAApB,qBAAA;mBAAA;wBAAA;0BAAA;QAAiE;IACzE;IACA,IACEyO,aAAajH,UAAU,CAAC,gBACxB,CAACtB,WAAWG,MAAM,CAACwI,mBAAmB,EACtC;QACA,IAAI,CAACN,KAAKK,MAAM,EAAE;YAChBzH,KAAIyD,KAAK,CACP,CAAC,wBAAwB,EAAE3D,KAAK,YAAY,EAAEwH,aAAa,iGAAiG,CAAC;QAEjK;QACA,MAAM,qBAGL,CAHK,IAAIzO,WACR,KACA,2DAFI,qBAAA;mBAAA;wBAAA;0BAAA;QAGN;IACF;IACA,IAAI8B,iBAAiBuB,QAAQ,CAACoL,iBAAiBK,IAAAA,mBAAU,EAACN,iBAAiB;QACzE,IAAI,CAACD,KAAKK,MAAM,EAAE;YAChBzH,KAAIC,QAAQ,CACV,CAAC,wBAAwB,EAAEH,KAAK,8GAA8G,CAAC;QAEnJ;QACA,OAAO;YACLzC,QAAQgK;YACR3C,aAAa4C;YACbnK;YACAT,MAAMY;YACNA;QACF;IACF;IACA,IAAI1C,aAAasB,QAAQ,CAACoL,eAAe;QACvC,OAAO;YACLjK,QAAQgK;YACR3C,aAAa4C;YACbnK;YACAT,MAAMY;YACNA;QACF;IACF;IAEA,IAAIoH;IAEJ,IAAI1I,UAAU;QACZ0I,cAAc1I;IAChB,OAAO,IACL4L,IAAAA,yBAAY,EAACN,iBACbA,iBAAiBzN,QACjByN,iBAAiB1N,MACjB;QACA8K,cAAc4C;IAChB,OAAO;QACL5C,cAAc3K;IAChB;IACA,MAAM8N,wBAAwBtO,+BAC5B2N,eACAE,KAAK3C,kBAAkB;IAEzB,IAAIoD,uBAAuB;YAIfT,uCAAAA;QAHV,OAAO;YACL/J,QAAQwK,sBAAsBxK,MAAM;YACpCqH;YACAvH,QAAQiK,CAAAA,yBAAAA,2BAAAA,KAAM3C,kBAAkB,sBAAxB2C,wCAAAA,yBAA0BpE,YAAY,qBAAtCoE,sCAAwCnE,UAAU,KAAI9F;YAC9DT,MAAMmL,sBAAsBnL,IAAI;YAChCY,cAAcuK,sBAAsBvK,YAAY;QAClD;IACF;IAEA,IAAI;QACF,IAAIqI,kBAAkB,MAAMjM,cAAc;YACxC2D,QAAQgK;YACR3C;YACAnD;YACAN;YACAjG,aAAa+D,WAAWsE,YAAY,CAACmE,iBAAiB;YACtD5C,kBAAkB7F,WAAWsE,YAAY,CAACyE,oBAAoB;YAC9DjD,gBAAgB9F,WAAWsE,YAAY,CAAC0E,oBAAoB;YAC5DjD,kBAAkB/F,WAAWsE,YAAY,CAAC2E,sBAAsB;QAClE;QACA,IAAIZ,KAAKpI,KAAK,IAAIiC,SAASpG,iBAAiB0G,YAAYzG,cAAc;YACpE,8EAA8E;YAC9E,gFAAgF;YAChF,qFAAqF;YACrF,MAAM0D,OAAO,MAAMnF,aAAasM;YAChC,MAAMsC,WAAW;gBACfC,WAAW1J,KAAKyC,KAAK;gBACrBkH,YAAY3J,KAAKmG,MAAM;gBACvByD,aAAa,CAAC,KAAK,EAAE1D,YAAY,QAAQ,EAAEiB,gBAAgB7I,QAAQ,CACjE,WACC;YACL;YACA6I,kBAAkB/I,OAAOC,IAAI,CAACwL,SAASC,IAAAA,6BAAe,EAACL;YACvDvD,cAAc;QAChB;QACA,OAAO;YACLrH,QAAQsI;YACRjB;YACAvH;YACAT,MAAMtD,aAAauM;YACnBrI;QACF;IACF,EAAE,OAAOmG,OAAO;QACd,IAAI6D,cAAc;YAChB,yDAAyD;YACzD,OAAO;gBACLjK,QAAQgK;gBACR3C,aAAa4C;gBACbnK,QAAQ4B,WAAWG,MAAM,CAACI,eAAe;gBACzC5C,MAAMY;gBACNA;gBACAmG;YACF;QACF,OAAO;YACL,MAAM,qBAGL,CAHK,IAAI5K,WACR,KACA,sEAFI,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;IACF;AACF;AAEA,SAAS0P,yBACP5I,GAAW,EACX+E,WAA0B;IAE1B,MAAM,CAAC8D,sBAAsB,GAAG7I,IAAI6C,KAAK,CAAC,KAAK;IAC/C,MAAMiG,wBAAwBD,sBAAsBhG,KAAK,CAAC,KAAKkG,GAAG;IAClE,IAAI,CAAChE,eAAe,CAAC+D,uBAAuB;QAC1C,OAAO;IACT;IAEA,MAAM,CAACE,SAAS,GAAGF,sBAAsBjG,KAAK,CAAC,KAAK;IACpD,MAAMtF,YAAY0K,IAAAA,yBAAY,EAAClD;IAC/B,OAAO,GAAGiE,SAAS,CAAC,EAAEzL,WAAW;AACnC;AAEA,SAAS0L,mBACP/J,GAAoB,EACpBgH,GAAmB,EACnBlG,GAAW,EACXjD,IAAY,EACZgI,WAA0B,EAC1BjD,QAAiB,EACjBoH,MAAoB,EACpBC,YAAiC,EACjC3L,MAAc,EACd6B,KAAc;IAEd6G,IAAIkD,SAAS,CAAC,QAAQ;IACtBlD,IAAIkD,SAAS,CACX,iBACAtH,WACI,yCACA,CAAC,gBAAgB,EAAEzC,QAAQ,IAAI7B,OAAO,iBAAiB,CAAC;IAE9D,IAAI6L,IAAAA,6BAAgB,EAACnK,KAAKgH,KAAKnJ,OAAO;QACpC,6CAA6C;QAC7C,OAAO;YAAEuM,UAAU;QAAK;IAC1B;IACA,IAAIvE,aAAa;QACfmB,IAAIkD,SAAS,CAAC,gBAAgBrE;IAChC;IAEA,MAAMiE,WAAWJ,yBAAyB5I,KAAK+E;IAC/CmB,IAAIkD,SAAS,CACX,uBACAG,IAAAA,2BAAkB,EAACP,UAAU;QAAEQ,MAAML,aAAaM,sBAAsB;IAAC;IAG3EvD,IAAIkD,SAAS,CAAC,2BAA2BD,aAAaO,qBAAqB;IAC3ExD,IAAIkD,SAAS,CAAC,kBAAkBF;IAEhC,OAAO;QAAEI,UAAU;IAAM;AAC3B;AAEO,SAAStP,aACdkF,GAAoB,EACpBgH,GAAmB,EACnBlG,GAAW,EACXzC,SAAiB,EACjBG,MAAc,EACdX,IAAY,EACZ+E,QAAiB,EACjBoH,MAAoB,EACpBC,YAAiC,EACjC3L,MAAc,EACd6B,KAAc;IAEd,MAAM0F,cAAc4E,IAAAA,2BAAc,EAACpM;IACnC,MAAMqM,SAASX,mBACb/J,KACAgH,KACAlG,KACAjD,MACAgI,aACAjD,UACAoH,QACAC,cACA3L,QACA6B;IAEF,IAAI,CAACuK,OAAON,QAAQ,EAAE;QACpBpD,IAAIkD,SAAS,CAAC,kBAAkBnM,OAAOqB,UAAU,CAACZ;QAClDwI,IAAI2D,GAAG,CAACnM;IACV;AACF;AAEO,eAAehE,aAAagE,MAAc;IAI/C,MAAM,EAAE4D,KAAK,EAAE0D,MAAM,EAAE,GAAG8E,IAAAA,kBAAW,EAACpM;IACtC,OAAO;QAAE4D;QAAO0D;IAAO;AACzB", "ignoreList": [0]}