{"version": 3, "sources": ["../../../src/server/node-environment-extensions/console-dev.tsx"], "sourcesContent": ["import { dim } from '../../lib/picocolors'\nimport { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external'\n\ntype InterceptableConsoleMethod =\n  | 'error'\n  | 'assert'\n  | 'debug'\n  | 'dir'\n  | 'dirxml'\n  | 'group'\n  | 'groupCollapsed'\n  | 'groupEnd'\n  | 'info'\n  | 'log'\n  | 'table'\n  | 'trace'\n  | 'warn'\n\nconst isColorSupported = dim('test') !== 'test'\n\n// 50% opacity for dimmed text\nconst dimStyle = 'color: color(from currentColor xyz x y z / 0.5);'\nconst reactBadgeFormat = '\\x1b[0m\\x1b[7m%c%s\\x1b[0m%c '\n\nfunction dimmedConsoleArgs(...inputArgs: any[]): any[] {\n  if (!isColorSupported) {\n    return inputArgs\n  }\n\n  const newArgs = inputArgs.slice(0)\n  let template = ''\n  let argumentsPointer = 0\n  if (typeof inputArgs[0] === 'string') {\n    const originalTemplateString = inputArgs[0]\n    // Remove the original template string from the args.\n    newArgs.splice(argumentsPointer, 1)\n    argumentsPointer += 1\n\n    let i = 0\n    if (originalTemplateString.startsWith(reactBadgeFormat)) {\n      i = reactBadgeFormat.length\n      // for `format` we already moved the pointer earlier\n      // style, badge, reset style\n      argumentsPointer += 3\n      template += reactBadgeFormat\n      // React's badge reset styles, reapply dimming\n      template += '\\x1b[2m%c'\n      // argumentsPointer includes template\n      newArgs.splice(argumentsPointer - 1, 0, dimStyle)\n      // dim the badge\n      newArgs[0] += `;${dimStyle}`\n    }\n\n    for (i; i < originalTemplateString.length; i++) {\n      const currentChar = originalTemplateString[i]\n      if (currentChar !== '%') {\n        template += currentChar\n        continue\n      }\n\n      const nextChar = originalTemplateString[i + 1]\n      ++i\n\n      switch (nextChar) {\n        case 'f':\n        case 'O':\n        case 'o':\n        case 'd':\n        case 's':\n        case 'i':\n        case 'c':\n          ++argumentsPointer\n          template += `%${nextChar}`\n          break\n        default:\n          template += `%${nextChar}`\n      }\n    }\n  }\n\n  for (\n    argumentsPointer;\n    argumentsPointer < inputArgs.length;\n    ++argumentsPointer\n  ) {\n    const arg = inputArgs[argumentsPointer]\n    const argType = typeof arg\n    if (argumentsPointer > 0) {\n      template += ' '\n    }\n    switch (argType) {\n      case 'boolean':\n      case 'string':\n        template += '%s'\n        break\n      case 'bigint':\n        template += '%s'\n        break\n      case 'number':\n        if (arg % 0) {\n          template += '%f'\n        } else {\n          template += '%d'\n        }\n        break\n      case 'object':\n        template += '%O'\n        break\n      case 'symbol':\n      case 'undefined':\n      case 'function':\n        template += '%s'\n        break\n      default:\n        // deopt to string for new, unknown types\n        template += '%s'\n    }\n  }\n\n  template += '\\x1b[22m'\n\n  return [dim(`%c${template}`), dimStyle, ...newArgs]\n}\n\nfunction dimConsoleCall(\n  methodName: InterceptableConsoleMethod,\n  args: any[]\n): any[] {\n  switch (methodName) {\n    case 'dir':\n    case 'dirxml':\n    case 'group':\n    case 'groupCollapsed':\n    case 'groupEnd':\n    case 'table': {\n      // These methods cannot be colorized because they don't take a formatting string.\n      return args\n    }\n    case 'assert': {\n      // assert takes formatting options as the second argument.\n      return [args[0]].concat(...dimmedConsoleArgs(args[1], ...args.slice(2)))\n    }\n    case 'error':\n    case 'debug':\n    case 'info':\n    case 'log':\n    case 'trace':\n    case 'warn':\n      return dimmedConsoleArgs(args[0], ...args.slice(1))\n    default:\n      return methodName satisfies never\n  }\n}\n\n// Based on https://github.com/facebook/react/blob/28dc0776be2e1370fe217549d32aee2519f0cf05/packages/react-server/src/ReactFlightServer.js#L248\nfunction patchConsoleMethodDEV(methodName: InterceptableConsoleMethod): void {\n  const descriptor = Object.getOwnPropertyDescriptor(console, methodName)\n  if (\n    descriptor &&\n    (descriptor.configurable || descriptor.writable) &&\n    typeof descriptor.value === 'function'\n  ) {\n    const originalMethod = descriptor.value\n    const originalName = Object.getOwnPropertyDescriptor(originalMethod, 'name')\n    const wrapperMethod = function (this: typeof console, ...args: any[]) {\n      const workUnitStore = workUnitAsyncStorage.getStore()\n\n      switch (workUnitStore?.type) {\n        case 'prerender':\n        case 'prerender-client':\n        case 'prerender-runtime':\n          originalMethod.apply(this, dimConsoleCall(methodName, args))\n          break\n        case 'prerender-ppr':\n        case 'prerender-legacy':\n        case 'request':\n        case 'cache':\n        case 'private-cache':\n        case 'unstable-cache':\n        case undefined:\n          originalMethod.apply(this, args)\n          break\n        default:\n          workUnitStore satisfies never\n      }\n    }\n    if (originalName) {\n      Object.defineProperty(wrapperMethod, 'name', originalName)\n    }\n    Object.defineProperty(console, methodName, {\n      value: wrapperMethod,\n    })\n  }\n}\n\npatchConsoleMethodDEV('error')\npatchConsoleMethodDEV('assert')\npatchConsoleMethodDEV('debug')\npatchConsoleMethodDEV('dir')\npatchConsoleMethodDEV('dirxml')\npatchConsoleMethodDEV('group')\npatchConsoleMethodDEV('groupCollapsed')\npatchConsoleMethodDEV('groupEnd')\npatchConsoleMethodDEV('info')\npatchConsoleMethodDEV('log')\npatchConsoleMethodDEV('table')\npatchConsoleMethodDEV('trace')\npatchConsoleMethodDEV('warn')\n"], "names": ["isColorSupported", "dim", "dimStyle", "reactBadgeFormat", "dimmedConsoleArgs", "inputArgs", "newArgs", "slice", "template", "argumentsPointer", "originalTemplateString", "splice", "i", "startsWith", "length", "currentChar", "nextChar", "arg", "argType", "dimConsoleCall", "methodName", "args", "concat", "patchConsoleMethodDEV", "descriptor", "Object", "getOwnPropertyDescriptor", "console", "configurable", "writable", "value", "originalMethod", "originalName", "wrapperMethod", "workUnitStore", "workUnitAsyncStorage", "getStore", "type", "apply", "undefined", "defineProperty"], "mappings": ";;;;4BAAoB;8CACiB;AAiBrC,MAAMA,mBAAmBC,IAAAA,eAAG,EAAC,YAAY;AAEzC,8BAA8B;AAC9B,MAAMC,WAAW;AACjB,MAAMC,mBAAmB;AAEzB,SAASC,kBAAkB,GAAGC,SAAgB;IAC5C,IAAI,CAACL,kBAAkB;QACrB,OAAOK;IACT;IAEA,MAAMC,UAAUD,UAAUE,KAAK,CAAC;IAChC,IAAIC,WAAW;IACf,IAAIC,mBAAmB;IACvB,IAAI,OAAOJ,SAAS,CAAC,EAAE,KAAK,UAAU;QACpC,MAAMK,yBAAyBL,SAAS,CAAC,EAAE;QAC3C,qDAAqD;QACrDC,QAAQK,MAAM,CAACF,kBAAkB;QACjCA,oBAAoB;QAEpB,IAAIG,IAAI;QACR,IAAIF,uBAAuBG,UAAU,CAACV,mBAAmB;YACvDS,IAAIT,iBAAiBW,MAAM;YAC3B,oDAAoD;YACpD,4BAA4B;YAC5BL,oBAAoB;YACpBD,YAAYL;YACZ,8CAA8C;YAC9CK,YAAY;YACZ,qCAAqC;YACrCF,QAAQK,MAAM,CAACF,mBAAmB,GAAG,GAAGP;YACxC,gBAAgB;YAChBI,OAAO,CAAC,EAAE,IAAI,CAAC,CAAC,EAAEJ,UAAU;QAC9B;QAEA,IAAKU,GAAGA,IAAIF,uBAAuBI,MAAM,EAAEF,IAAK;YAC9C,MAAMG,cAAcL,sBAAsB,CAACE,EAAE;YAC7C,IAAIG,gBAAgB,KAAK;gBACvBP,YAAYO;gBACZ;YACF;YAEA,MAAMC,WAAWN,sBAAsB,CAACE,IAAI,EAAE;YAC9C,EAAEA;YAEF,OAAQI;gBACN,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBACH,EAAEP;oBACFD,YAAY,CAAC,CAAC,EAAEQ,UAAU;oBAC1B;gBACF;oBACER,YAAY,CAAC,CAAC,EAAEQ,UAAU;YAC9B;QACF;IACF;IAEA,IACEP,kBACAA,mBAAmBJ,UAAUS,MAAM,EACnC,EAAEL,iBACF;QACA,MAAMQ,MAAMZ,SAAS,CAACI,iBAAiB;QACvC,MAAMS,UAAU,OAAOD;QACvB,IAAIR,mBAAmB,GAAG;YACxBD,YAAY;QACd;QACA,OAAQU;YACN,KAAK;YACL,KAAK;gBACHV,YAAY;gBACZ;YACF,KAAK;gBACHA,YAAY;gBACZ;YACF,KAAK;gBACH,IAAIS,MAAM,GAAG;oBACXT,YAAY;gBACd,OAAO;oBACLA,YAAY;gBACd;gBACA;YACF,KAAK;gBACHA,YAAY;gBACZ;YACF,KAAK;YACL,KAAK;YACL,KAAK;gBACHA,YAAY;gBACZ;YACF;gBACE,yCAAyC;gBACzCA,YAAY;QAChB;IACF;IAEAA,YAAY;IAEZ,OAAO;QAACP,IAAAA,eAAG,EAAC,CAAC,EAAE,EAAEO,UAAU;QAAGN;WAAaI;KAAQ;AACrD;AAEA,SAASa,eACPC,UAAsC,EACtCC,IAAW;IAEX,OAAQD;QACN,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAS;gBACZ,iFAAiF;gBACjF,OAAOC;YACT;QACA,KAAK;YAAU;gBACb,0DAA0D;gBAC1D,OAAO;oBAACA,IAAI,CAAC,EAAE;iBAAC,CAACC,MAAM,IAAIlB,kBAAkBiB,IAAI,CAAC,EAAE,KAAKA,KAAKd,KAAK,CAAC;YACtE;QACA,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAOH,kBAAkBiB,IAAI,CAAC,EAAE,KAAKA,KAAKd,KAAK,CAAC;QAClD;YACE,OAAOa;IACX;AACF;AAEA,+IAA+I;AAC/I,SAASG,sBAAsBH,UAAsC;IACnE,MAAMI,aAAaC,OAAOC,wBAAwB,CAACC,SAASP;IAC5D,IACEI,cACCA,CAAAA,WAAWI,YAAY,IAAIJ,WAAWK,QAAQ,AAAD,KAC9C,OAAOL,WAAWM,KAAK,KAAK,YAC5B;QACA,MAAMC,iBAAiBP,WAAWM,KAAK;QACvC,MAAME,eAAeP,OAAOC,wBAAwB,CAACK,gBAAgB;QACrE,MAAME,gBAAgB,SAAgC,GAAGZ,IAAW;YAClE,MAAMa,gBAAgBC,kDAAoB,CAACC,QAAQ;YAEnD,OAAQF,iCAAAA,cAAeG,IAAI;gBACzB,KAAK;gBACL,KAAK;gBACL,KAAK;oBACHN,eAAeO,KAAK,CAAC,IAAI,EAAEnB,eAAeC,YAAYC;oBACtD;gBACF,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAKkB;oBACHR,eAAeO,KAAK,CAAC,IAAI,EAAEjB;oBAC3B;gBACF;oBACEa;YACJ;QACF;QACA,IAAIF,cAAc;YAChBP,OAAOe,cAAc,CAACP,eAAe,QAAQD;QAC/C;QACAP,OAAOe,cAAc,CAACb,SAASP,YAAY;YACzCU,OAAOG;QACT;IACF;AACF;AAEAV,sBAAsB;AACtBA,sBAAsB;AACtBA,sBAAsB;AACtBA,sBAAsB;AACtBA,sBAAsB;AACtBA,sBAAsB;AACtBA,sBAAsB;AACtBA,sBAAsB;AACtBA,sBAAsB;AACtBA,sBAAsB;AACtBA,sBAAsB;AACtBA,sBAAsB;AACtBA,sBAAsB", "ignoreList": [0]}