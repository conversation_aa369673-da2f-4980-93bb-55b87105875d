{"version": 3, "sources": ["../../src/build/load-entrypoint.ts"], "sourcesContent": ["import fs from 'fs/promises'\nimport path from 'path'\nimport { loadBindings } from './swc'\n\n// NOTE: this should be updated if this loader file is moved.\nconst PACKAGE_ROOT = path.normalize(path.join(__dirname, '../..'))\nconst TEMPLATE_SRC_FOLDER = path.normalize(path.join(__dirname, './templates'))\nconst TEMPLATES_ESM_FOLDER = path.normalize(\n  path.join(__dirname, '../../dist/esm/build/templates')\n)\n\n/**\n * Load the entrypoint file from the ESM directory and performs string\n * replacements of the template variables specified in the `replacements`\n * argument.\n *\n * For non-string replacements, the template should use the\n * `declare const ${key}: ${type}` syntax. to ensure that the type is correct\n * and the typescript can compile. You may have to use `@ts-expect-error` to\n * handle replacement values that are related to imports.\n *\n * @param entrypoint the entrypoint to load\n * @param replacements string replacements to perform\n * @param injections code injections to perform\n * @param imports optional imports to insert or set to null\n * @returns the loaded file with the replacements\n */\nexport async function loadEntrypoint(\n  entrypoint:\n    | 'app-page'\n    | 'app-route'\n    | 'edge-app-route'\n    | 'edge-ssr'\n    | 'edge-ssr-app'\n    | 'middleware'\n    | 'pages'\n    | 'pages-api',\n  replacements: Record<`VAR_${string}`, string>,\n  injections?: Record<string, string>,\n  imports?: Record<string, string | null>\n): Promise<string> {\n  let bindings = await loadBindings()\n\n  const templatePath = path.resolve(\n    path.join(TEMPLATES_ESM_FOLDER, `${entrypoint}.js`)\n  )\n  let content = await fs.readFile(templatePath)\n\n  return bindings.expandNextJsTemplate(\n    content,\n    // Ensure that we use unix-style path separators for the import paths\n    path.join(TEMPLATE_SRC_FOLDER, `${entrypoint}.js`).replace(/\\\\/g, '/'),\n    PACKAGE_ROOT.replace(/\\\\/g, '/'),\n    replacements,\n    injections ?? {},\n    imports ?? {}\n  )\n}\n"], "names": ["fs", "path", "loadBindings", "PACKAGE_ROOT", "normalize", "join", "__dirname", "TEMPLATE_SRC_FOLDER", "TEMPLATES_ESM_FOLDER", "loadEntrypoint", "entrypoint", "replacements", "injections", "imports", "bindings", "templatePath", "resolve", "content", "readFile", "expandNextJsTemplate", "replace"], "mappings": "AAAA,OAAOA,QAAQ,cAAa;AAC5B,OAAOC,UAAU,OAAM;AACvB,SAASC,YAAY,QAAQ,QAAO;AAEpC,6DAA6D;AAC7D,MAAMC,eAAeF,KAAKG,SAAS,CAACH,KAAKI,IAAI,CAACC,WAAW;AACzD,MAAMC,sBAAsBN,KAAKG,SAAS,CAACH,KAAKI,IAAI,CAACC,WAAW;AAChE,MAAME,uBAAuBP,KAAKG,SAAS,CACzCH,KAAKI,IAAI,CAACC,WAAW;AAGvB;;;;;;;;;;;;;;;CAeC,GACD,OAAO,eAAeG,eACpBC,UAQe,EACfC,YAA6C,EAC7CC,UAAmC,EACnCC,OAAuC;IAEvC,IAAIC,WAAW,MAAMZ;IAErB,MAAMa,eAAed,KAAKe,OAAO,CAC/Bf,KAAKI,IAAI,CAACG,sBAAsB,GAAGE,WAAW,GAAG,CAAC;IAEpD,IAAIO,UAAU,MAAMjB,GAAGkB,QAAQ,CAACH;IAEhC,OAAOD,SAASK,oBAAoB,CAClCF,SACA,qEAAqE;IACrEhB,KAAKI,IAAI,CAACE,qBAAqB,GAAGG,WAAW,GAAG,CAAC,EAAEU,OAAO,CAAC,OAAO,MAClEjB,aAAaiB,OAAO,CAAC,OAAO,MAC5BT,cACAC,cAAc,CAAC,GACfC,WAAW,CAAC;AAEhB", "ignoreList": [0]}