{"version": 3, "sources": ["../../../src/server/dev/hot-reloader-turbopack.ts"], "sourcesContent": ["import type { Socket } from 'net'\nimport { mkdir, writeFile } from 'fs/promises'\nimport { join, extname, relative } from 'path'\nimport { pathToFileURL } from 'url'\n\nimport ws from 'next/dist/compiled/ws'\n\nimport type { OutputState } from '../../build/output/store'\nimport { store as consoleStore } from '../../build/output/store'\nimport type {\n  CompilationError,\n  HMR_ACTION_TYPES,\n  NextJsHotReloaderInterface,\n  ReloadPageAction,\n  SyncAction,\n  TurbopackConnectedAction,\n} from './hot-reloader-types'\nimport { HMR_ACTIONS_SENT_TO_BROWSER } from './hot-reloader-types'\nimport type {\n  Update as TurbopackUpdate,\n  Endpoint,\n  WrittenEndpoint,\n  TurbopackResult,\n  Project,\n  Entrypoints,\n} from '../../build/swc/types'\nimport { createDefineEnv } from '../../build/swc'\nimport * as Log from '../../build/output/log'\nimport {\n  getVersionInfo,\n  matchNextPageBundleRequest,\n} from './hot-reloader-webpack'\nimport { BLOCKED_PAGES } from '../../shared/lib/constants'\nimport {\n  getOverlayMiddleware,\n  getSourceMapMiddleware,\n} from './middleware-turbopack'\nimport { PageNotFoundError } from '../../shared/lib/utils'\nimport { debounce } from '../utils'\nimport { deleteCache } from './require-cache'\nimport {\n  clearAllModuleContexts,\n  clearModuleContext,\n} from '../lib/render-server'\nimport { denormalizePagePath } from '../../shared/lib/page-path/denormalize-page-path'\nimport { trace } from '../../trace'\nimport {\n  AssetMapper,\n  type ChangeSubscriptions,\n  type ClientState,\n  handleEntrypoints,\n  handlePagesErrorRoute,\n  handleRouteType,\n  hasEntrypointForKey,\n  msToNs,\n  type ReadyIds,\n  type SendHmr,\n  type StartBuilding,\n  processTopLevelIssues,\n  printNonFatalIssue,\n  normalizedPageToTurbopackStructureRoute,\n} from './turbopack-utils'\nimport {\n  propagateServerField,\n  type ServerFields,\n  type SetupOpts,\n} from '../lib/router-utils/setup-dev-bundler'\nimport { TurbopackManifestLoader } from '../../shared/lib/turbopack/manifest-loader'\nimport { findPagePathData } from './on-demand-entry-handler'\nimport type { RouteDefinition } from '../route-definitions/route-definition'\nimport {\n  type EntryKey,\n  getEntryKey,\n  splitEntryKey,\n} from '../../shared/lib/turbopack/entry-key'\nimport { FAST_REFRESH_RUNTIME_RELOAD } from './messages'\nimport { generateEncryptionKeyBase64 } from '../app-render/encryption-utils-server'\nimport { isAppPageRouteDefinition } from '../route-definitions/app-page-route-definition'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport type { ModernSourceMapPayload } from '../lib/source-maps'\nimport { getNodeDebugType } from '../lib/utils'\nimport { isMetadataRouteFile } from '../../lib/metadata/is-metadata-route'\nimport { setBundlerFindSourceMapImplementation } from '../patch-error-inspect'\nimport { getNextErrorFeedbackMiddleware } from '../../next-devtools/server/get-next-error-feedback-middleware'\nimport {\n  formatIssue,\n  getTurbopackJsConfig,\n  isPersistentCachingEnabled,\n  isWellKnownError,\n  processIssues,\n  renderStyledStringToErrorAnsi,\n  type EntryIssuesMap,\n  type TopLevelIssuesMap,\n} from '../../shared/lib/turbopack/utils'\nimport { getDevOverlayFontMiddleware } from '../../next-devtools/server/font/get-dev-overlay-font-middleware'\nimport { devIndicatorServerState } from './dev-indicator-server-state'\nimport { getDisableDevIndicatorMiddleware } from '../../next-devtools/server/dev-indicator-middleware'\nimport { getRestartDevServerMiddleware } from '../../next-devtools/server/restart-dev-server-middleware'\nimport { backgroundLogCompilationEvents } from '../../shared/lib/turbopack/compilation-events'\nimport { getSupportedBrowsers } from '../../build/utils'\nimport { receiveBrowserLogsTurbopack } from './browser-logs/receive-logs'\nimport { normalizePath } from '../../lib/normalize-path'\nimport {\n  devToolsConfigMiddleware,\n  getDevToolsConfig,\n} from '../../next-devtools/server/devtools-config-middleware'\n\nconst wsServer = new ws.Server({ noServer: true })\nconst isTestMode = !!(\n  process.env.NEXT_TEST_MODE ||\n  process.env.__NEXT_TEST_MODE ||\n  process.env.DEBUG\n)\n\nconst sessionId = Math.floor(Number.MAX_SAFE_INTEGER * Math.random())\n\ndeclare const __next__clear_chunk_cache__: (() => void) | null | undefined\n\n/**\n * Replaces turbopack:///[project] with the specified project in the `source` field.\n */\nfunction rewriteTurbopackSources(\n  projectRoot: string,\n  sourceMap: ModernSourceMapPayload\n): void {\n  if ('sections' in sourceMap) {\n    for (const section of sourceMap.sections) {\n      rewriteTurbopackSources(projectRoot, section.map)\n    }\n  } else {\n    for (let i = 0; i < sourceMap.sources.length; i++) {\n      sourceMap.sources[i] = pathToFileURL(\n        join(\n          projectRoot,\n          sourceMap.sources[i].replace(/turbopack:\\/\\/\\/\\[project\\]/, '')\n        )\n      ).toString()\n    }\n  }\n}\n\nfunction getSourceMapFromTurbopack(\n  project: Project,\n  projectRoot: string,\n  sourceURL: string\n): ModernSourceMapPayload | undefined {\n  let sourceMapJson: string | null = null\n\n  try {\n    sourceMapJson = project.getSourceMapSync(sourceURL)\n  } catch (err) {}\n\n  if (sourceMapJson === null) {\n    return undefined\n  } else {\n    const payload: ModernSourceMapPayload = JSON.parse(sourceMapJson)\n    // The sourcemap from Turbopack is not yet written to disk so its `sources`\n    // are not absolute paths yet. We need to rewrite them to be absolute paths.\n    rewriteTurbopackSources(projectRoot, payload)\n    return payload\n  }\n}\n\nexport async function createHotReloaderTurbopack(\n  opts: SetupOpts & { isSrcDir: boolean },\n  serverFields: ServerFields,\n  distDir: string,\n  resetFetch: () => void\n): Promise<NextJsHotReloaderInterface> {\n  const dev = true\n  const buildId = 'development'\n  const { nextConfig, dir: projectPath } = opts\n\n  const { loadBindings } =\n    require('../../build/swc') as typeof import('../../build/swc')\n\n  let bindings = await loadBindings()\n\n  // For the debugging purpose, check if createNext or equivalent next instance setup in test cases\n  // works correctly. Normally `run-test` hides output so only will be visible when `--debug` flag is used.\n  if (isTestMode) {\n    ;(require('console') as typeof import('console')).log(\n      'Creating turbopack project',\n      {\n        dir: projectPath,\n        testMode: isTestMode,\n      }\n    )\n  }\n\n  const hasRewrites =\n    opts.fsChecker.rewrites.afterFiles.length > 0 ||\n    opts.fsChecker.rewrites.beforeFiles.length > 0 ||\n    opts.fsChecker.rewrites.fallback.length > 0\n\n  const hotReloaderSpan = trace('hot-reloader', undefined, {\n    version: process.env.__NEXT_VERSION as string,\n  })\n  // Ensure the hotReloaderSpan is flushed immediately as it's the parentSpan for all processing\n  // of the current `next dev` invocation.\n  hotReloaderSpan.stop()\n\n  const encryptionKey = await generateEncryptionKeyBase64({\n    isBuild: false,\n    distDir,\n  })\n\n  // TODO: Implement\n  let clientRouterFilters: any\n  if (nextConfig.experimental.clientRouterFilter) {\n    // TODO this need to be set correctly for persistent caching to work\n  }\n\n  const supportedBrowsers = getSupportedBrowsers(projectPath, dev)\n  const currentNodeJsVersion = process.versions.node\n\n  const rootPath =\n    opts.nextConfig.turbopack?.root ||\n    opts.nextConfig.outputFileTracingRoot ||\n    projectPath\n  const project = await bindings.turbo.createProject(\n    {\n      rootPath,\n      projectPath: normalizePath(relative(rootPath, projectPath) || '.'),\n      distDir,\n      nextConfig: opts.nextConfig,\n      jsConfig: await getTurbopackJsConfig(projectPath, nextConfig),\n      watch: {\n        enable: dev,\n        pollIntervalMs: nextConfig.watchOptions?.pollIntervalMs,\n      },\n      dev,\n      env: process.env as Record<string, string>,\n      defineEnv: createDefineEnv({\n        isTurbopack: true,\n        clientRouterFilters,\n        config: nextConfig,\n        dev,\n        distDir,\n        projectPath,\n        fetchCacheKeyPrefix: opts.nextConfig.experimental.fetchCacheKeyPrefix,\n        hasRewrites,\n        // TODO: Implement\n        middlewareMatchers: undefined,\n        rewrites: opts.fsChecker.rewrites,\n      }),\n      buildId,\n      encryptionKey,\n      previewProps: opts.fsChecker.prerenderManifest.preview,\n      browserslistQuery: supportedBrowsers.join(', '),\n      noMangling: false,\n      currentNodeJsVersion,\n    },\n    {\n      persistentCaching: isPersistentCachingEnabled(opts.nextConfig),\n      memoryLimit: opts.nextConfig.experimental?.turbopackMemoryLimit,\n      isShortSession: false,\n    }\n  )\n  backgroundLogCompilationEvents(project, {\n    eventTypes: ['StartupCacheInvalidationEvent', 'TimingEvent'],\n  })\n  setBundlerFindSourceMapImplementation(\n    getSourceMapFromTurbopack.bind(null, project, projectPath)\n  )\n  opts.onDevServerCleanup?.(async () => {\n    setBundlerFindSourceMapImplementation(() => undefined)\n    await project.onExit()\n  })\n  const entrypointsSubscription = project.entrypointsSubscribe()\n\n  const currentWrittenEntrypoints: Map<EntryKey, WrittenEndpoint> = new Map()\n  const currentEntrypoints: Entrypoints = {\n    global: {\n      app: undefined,\n      document: undefined,\n      error: undefined,\n\n      middleware: undefined,\n      instrumentation: undefined,\n    },\n\n    page: new Map(),\n    app: new Map(),\n  }\n\n  const currentTopLevelIssues: TopLevelIssuesMap = new Map()\n  const currentEntryIssues: EntryIssuesMap = new Map()\n\n  const manifestLoader = new TurbopackManifestLoader({\n    buildId,\n    distDir,\n    encryptionKey,\n  })\n\n  // Dev specific\n  const changeSubscriptions: ChangeSubscriptions = new Map()\n  const serverPathState = new Map<string, string>()\n  const readyIds: ReadyIds = new Set()\n  let currentEntriesHandlingResolve: ((value?: unknown) => void) | undefined\n  let currentEntriesHandling = new Promise(\n    (resolve) => (currentEntriesHandlingResolve = resolve)\n  )\n\n  const assetMapper = new AssetMapper()\n\n  function clearRequireCache(\n    key: EntryKey,\n    writtenEndpoint: WrittenEndpoint,\n    {\n      force,\n    }: {\n      // Always clear the cache, don't check if files have changed\n      force?: boolean\n    } = {}\n  ): boolean {\n    if (force) {\n      for (const { path, contentHash } of writtenEndpoint.serverPaths) {\n        serverPathState.set(path, contentHash)\n      }\n    } else {\n      // Figure out if the server files have changed\n      let hasChange = false\n      for (const { path, contentHash } of writtenEndpoint.serverPaths) {\n        // We ignore source maps\n        if (path.endsWith('.map')) continue\n        const localKey = `${key}:${path}`\n        const localHash = serverPathState.get(localKey)\n        const globalHash = serverPathState.get(path)\n        if (\n          (localHash && localHash !== contentHash) ||\n          (globalHash && globalHash !== contentHash)\n        ) {\n          hasChange = true\n          serverPathState.set(key, contentHash)\n          serverPathState.set(path, contentHash)\n        } else {\n          if (!localHash) {\n            serverPathState.set(key, contentHash)\n          }\n          if (!globalHash) {\n            serverPathState.set(path, contentHash)\n          }\n        }\n      }\n\n      if (!hasChange) {\n        return false\n      }\n    }\n\n    resetFetch()\n\n    // Not available in:\n    // - Pages Router (no server-side HMR)\n    // - Edge Runtime (uses browser runtime which already disposes chunks individually)\n    if (typeof __next__clear_chunk_cache__ === 'function') {\n      __next__clear_chunk_cache__()\n    }\n\n    const serverPaths = writtenEndpoint.serverPaths.map(({ path: p }) =>\n      join(distDir, p)\n    )\n\n    for (const file of serverPaths) {\n      clearModuleContext(file)\n      deleteCache(file)\n    }\n\n    return true\n  }\n\n  const buildingIds = new Set()\n\n  const startBuilding: StartBuilding = (id, requestUrl, forceRebuild) => {\n    if (!forceRebuild && readyIds.has(id)) {\n      return () => {}\n    }\n    if (buildingIds.size === 0) {\n      consoleStore.setState(\n        {\n          loading: true,\n          trigger: id,\n          url: requestUrl,\n        } as OutputState,\n        true\n      )\n    }\n    buildingIds.add(id)\n    return function finishBuilding() {\n      if (buildingIds.size === 0) {\n        return\n      }\n      readyIds.add(id)\n      buildingIds.delete(id)\n      if (buildingIds.size === 0) {\n        hmrEventHappened = false\n        consoleStore.setState(\n          {\n            loading: false,\n          } as OutputState,\n          true\n        )\n      }\n    }\n  }\n\n  let hmrEventHappened = false\n  let hmrHash = 0\n\n  const clients = new Set<ws>()\n  const clientStates = new WeakMap<ws, ClientState>()\n\n  function sendToClient(client: ws, payload: HMR_ACTION_TYPES) {\n    client.send(JSON.stringify(payload))\n  }\n\n  function sendEnqueuedMessages() {\n    for (const [, issueMap] of currentEntryIssues) {\n      if (\n        [...issueMap.values()].filter((i) => i.severity !== 'warning').length >\n        0\n      ) {\n        // During compilation errors we want to delay the HMR events until errors are fixed\n        return\n      }\n    }\n\n    for (const client of clients) {\n      const state = clientStates.get(client)\n      if (!state) {\n        continue\n      }\n\n      for (const [, issueMap] of state.clientIssues) {\n        if (\n          [...issueMap.values()].filter((i) => i.severity !== 'warning')\n            .length > 0\n        ) {\n          // During compilation errors we want to delay the HMR events until errors are fixed\n          return\n        }\n      }\n\n      for (const payload of state.hmrPayloads.values()) {\n        sendToClient(client, payload)\n      }\n      state.hmrPayloads.clear()\n\n      if (state.turbopackUpdates.length > 0) {\n        sendToClient(client, {\n          action: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE,\n          data: state.turbopackUpdates,\n        })\n        state.turbopackUpdates.length = 0\n      }\n    }\n  }\n  const sendEnqueuedMessagesDebounce = debounce(sendEnqueuedMessages, 2)\n\n  const sendHmr: SendHmr = (id: string, payload: HMR_ACTION_TYPES) => {\n    for (const client of clients) {\n      clientStates.get(client)?.hmrPayloads.set(id, payload)\n    }\n\n    hmrEventHappened = true\n    sendEnqueuedMessagesDebounce()\n  }\n\n  function sendTurbopackMessage(payload: TurbopackUpdate) {\n    // TODO(PACK-2049): For some reason we end up emitting hundreds of issues messages on bigger apps,\n    //   a lot of which are duplicates.\n    //   They are currently not handled on the client at all, so might as well not send them for now.\n    payload.diagnostics = []\n    payload.issues = []\n\n    for (const client of clients) {\n      clientStates.get(client)?.turbopackUpdates.push(payload)\n    }\n\n    hmrEventHappened = true\n    sendEnqueuedMessagesDebounce()\n  }\n\n  async function subscribeToChanges(\n    key: EntryKey,\n    includeIssues: boolean,\n    endpoint: Endpoint,\n    makePayload: (\n      change: TurbopackResult,\n      hash: string\n    ) => Promise<HMR_ACTION_TYPES> | HMR_ACTION_TYPES | void,\n    onError?: (\n      error: Error\n    ) => Promise<HMR_ACTION_TYPES> | HMR_ACTION_TYPES | void\n  ) {\n    if (changeSubscriptions.has(key)) {\n      return\n    }\n\n    const { side } = splitEntryKey(key)\n\n    const changedPromise = endpoint[`${side}Changed`](includeIssues)\n    changeSubscriptions.set(key, changedPromise)\n    try {\n      const changed = await changedPromise\n\n      for await (const change of changed) {\n        processIssues(currentEntryIssues, key, change, false, true)\n        // TODO: Get an actual content hash from Turbopack.\n        const payload = await makePayload(change, String(++hmrHash))\n        if (payload) {\n          sendHmr(key, payload)\n        }\n      }\n    } catch (e) {\n      changeSubscriptions.delete(key)\n      const payload = await onError?.(e as Error)\n      if (payload) {\n        sendHmr(key, payload)\n      }\n      return\n    }\n    changeSubscriptions.delete(key)\n  }\n\n  async function unsubscribeFromChanges(key: EntryKey) {\n    const subscription = await changeSubscriptions.get(key)\n    if (subscription) {\n      await subscription.return?.()\n      changeSubscriptions.delete(key)\n    }\n    currentEntryIssues.delete(key)\n  }\n\n  async function subscribeToHmrEvents(client: ws, id: string) {\n    const key = getEntryKey('assets', 'client', id)\n    if (!hasEntrypointForKey(currentEntrypoints, key, assetMapper)) {\n      // maybe throw an error / force the client to reload?\n      return\n    }\n\n    const state = clientStates.get(client)\n    if (!state || state.subscriptions.has(id)) {\n      return\n    }\n\n    const subscription = project!.hmrEvents(id)\n    state.subscriptions.set(id, subscription)\n\n    // The subscription will always emit once, which is the initial\n    // computation. This is not a change, so swallow it.\n    try {\n      await subscription.next()\n\n      for await (const data of subscription) {\n        processIssues(state.clientIssues, key, data, false, true)\n        if (data.type !== 'issues') {\n          sendTurbopackMessage(data)\n        }\n      }\n    } catch (e) {\n      // The client might be using an HMR session from a previous server, tell them\n      // to fully reload the page to resolve the issue. We can't use\n      // `hotReloader.send` since that would force every connected client to\n      // reload, only this client is out of date.\n      const reloadAction: ReloadPageAction = {\n        action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n        data: `error in HMR event subscription for ${id}: ${e}`,\n      }\n      sendToClient(client, reloadAction)\n      client.close()\n      return\n    }\n  }\n\n  function unsubscribeFromHmrEvents(client: ws, id: string) {\n    const state = clientStates.get(client)\n    if (!state) {\n      return\n    }\n\n    const subscription = state.subscriptions.get(id)\n    subscription?.return!()\n\n    const key = getEntryKey('assets', 'client', id)\n    state.clientIssues.delete(key)\n  }\n\n  async function handleEntrypointsSubscription() {\n    for await (const entrypoints of entrypointsSubscription) {\n      if (!currentEntriesHandlingResolve) {\n        currentEntriesHandling = new Promise(\n          // eslint-disable-next-line no-loop-func\n          (resolve) => (currentEntriesHandlingResolve = resolve)\n        )\n      }\n\n      processTopLevelIssues(currentTopLevelIssues, entrypoints)\n\n      await handleEntrypoints({\n        entrypoints,\n\n        currentEntrypoints,\n\n        currentEntryIssues,\n        manifestLoader,\n        devRewrites: opts.fsChecker.rewrites,\n        productionRewrites: undefined,\n        logErrors: true,\n\n        dev: {\n          assetMapper,\n          changeSubscriptions,\n          clients,\n          clientStates,\n          serverFields,\n\n          hooks: {\n            handleWrittenEndpoint: (id, result, forceDeleteCache) => {\n              currentWrittenEntrypoints.set(id, result)\n              return clearRequireCache(id, result, { force: forceDeleteCache })\n            },\n            propagateServerField: propagateServerField.bind(null, opts),\n            sendHmr,\n            startBuilding,\n            subscribeToChanges,\n            unsubscribeFromChanges,\n            unsubscribeFromHmrEvents,\n          },\n        },\n      })\n\n      currentEntriesHandlingResolve!()\n      currentEntriesHandlingResolve = undefined\n    }\n  }\n\n  await mkdir(join(distDir, 'server'), { recursive: true })\n  await mkdir(join(distDir, 'static', buildId), { recursive: true })\n  await writeFile(\n    join(distDir, 'package.json'),\n    JSON.stringify(\n      {\n        type: 'commonjs',\n      },\n      null,\n      2\n    )\n  )\n\n  const middlewares = [\n    getOverlayMiddleware({\n      project,\n      projectPath,\n      isSrcDir: opts.isSrcDir,\n    }),\n    getSourceMapMiddleware(project),\n    getNextErrorFeedbackMiddleware(opts.telemetry),\n    getDevOverlayFontMiddleware(),\n    getDisableDevIndicatorMiddleware(),\n    getRestartDevServerMiddleware({\n      telemetry: opts.telemetry,\n      turbopackProject: project,\n    }),\n    devToolsConfigMiddleware({\n      distDir,\n      sendUpdateSignal: (data) => {\n        hotReloader.send({\n          action: HMR_ACTIONS_SENT_TO_BROWSER.DEVTOOLS_CONFIG,\n          data,\n        })\n      },\n    }),\n  ]\n\n  const versionInfoPromise = getVersionInfo()\n\n  let devtoolsFrontendUrl: string | undefined\n  const nodeDebugType = getNodeDebugType()\n  if (nodeDebugType) {\n    const debugPort = process.debugPort\n    let debugInfo\n    try {\n      // It requires to use 127.0.0.1 instead of localhost for server-side fetching.\n      const debugInfoList = await fetch(\n        `http://127.0.0.1:${debugPort}/json/list`\n      ).then((res) => res.json())\n      debugInfo = debugInfoList[0]\n    } catch {}\n    if (debugInfo) {\n      devtoolsFrontendUrl = debugInfo.devtoolsFrontendUrl\n    }\n  }\n\n  const hotReloader: NextJsHotReloaderInterface = {\n    turbopackProject: project,\n    activeWebpackConfigs: undefined,\n    serverStats: null,\n    edgeServerStats: null,\n    async run(req, res, _parsedUrl) {\n      // intercept page chunks request and ensure them with turbopack\n      if (req.url?.startsWith('/_next/static/chunks/pages/')) {\n        const params = matchNextPageBundleRequest(req.url)\n\n        if (params) {\n          const decodedPagePath = `/${params.path\n            .map((param: string) => decodeURIComponent(param))\n            .join('/')}`\n\n          const denormalizedPagePath = denormalizePagePath(decodedPagePath)\n\n          await hotReloader\n            .ensurePage({\n              page: denormalizedPagePath,\n              clientOnly: false,\n              definition: undefined,\n              url: req.url,\n            })\n            .catch(console.error)\n        }\n      }\n\n      for (const middleware of middlewares) {\n        let calledNext = false\n\n        await middleware(req, res, () => {\n          calledNext = true\n        })\n\n        if (!calledNext) {\n          return { finished: true }\n        }\n      }\n\n      // Request was not finished.\n      return { finished: undefined }\n    },\n\n    // TODO: Figure out if socket type can match the NextJsHotReloaderInterface\n    onHMR(req, socket: Socket, head, onUpgrade) {\n      wsServer.handleUpgrade(req, socket, head, (client) => {\n        onUpgrade(client)\n        const clientIssues: EntryIssuesMap = new Map()\n        const subscriptions: Map<string, AsyncIterator<any>> = new Map()\n\n        clients.add(client)\n        clientStates.set(client, {\n          clientIssues,\n          hmrPayloads: new Map(),\n          turbopackUpdates: [],\n          subscriptions,\n        })\n\n        client.on('close', () => {\n          // Remove active subscriptions\n          for (const subscription of subscriptions.values()) {\n            subscription.return?.()\n          }\n          clientStates.delete(client)\n          clients.delete(client)\n        })\n\n        client.addEventListener('message', async ({ data }) => {\n          const parsedData = JSON.parse(\n            typeof data !== 'string' ? data.toString() : data\n          )\n\n          // Next.js messages\n          switch (parsedData.event) {\n            case 'span-end': {\n              hotReloaderSpan.manualTraceChild(\n                parsedData.spanName,\n                msToNs(parsedData.startTime),\n                msToNs(parsedData.endTime),\n                parsedData.attributes\n              )\n              break\n            }\n            case 'client-hmr-latency': // { id, startTime, endTime, page, updatedModules, isPageHidden }\n              hotReloaderSpan.manualTraceChild(\n                parsedData.event,\n                msToNs(parsedData.startTime),\n                msToNs(parsedData.endTime),\n                {\n                  updatedModules: parsedData.updatedModules,\n                  page: parsedData.page,\n                  isPageHidden: parsedData.isPageHidden,\n                }\n              )\n              break\n\n            case 'client-error': // { errorCount, clientId }\n            case 'client-warning': // { warningCount, clientId }\n            case 'client-success': // { clientId }\n            case 'server-component-reload-page': // { clientId }\n            case 'client-reload-page': // { clientId }\n            case 'client-removed-page': // { page }\n            case 'client-full-reload': // { stackTrace, hadRuntimeError }\n              const { hadRuntimeError, dependencyChain } = parsedData\n              if (hadRuntimeError) {\n                Log.warn(FAST_REFRESH_RUNTIME_RELOAD)\n              }\n              if (\n                Array.isArray(dependencyChain) &&\n                typeof dependencyChain[0] === 'string'\n              ) {\n                const cleanedModulePath = dependencyChain[0]\n                  .replace(/^\\[project\\]/, '.')\n                  .replace(/ \\[.*\\] \\(.*\\)$/, '')\n                Log.warn(\n                  `Fast Refresh had to perform a full reload when ${cleanedModulePath} changed. Read more: https://nextjs.org/docs/messages/fast-refresh-reload`\n                )\n              }\n              break\n            case 'client-added-page':\n              // TODO\n              break\n            case 'browser-logs': {\n              if (nextConfig.experimental.browserDebugInfoInTerminal) {\n                await receiveBrowserLogsTurbopack({\n                  entries: parsedData.entries,\n                  router: parsedData.router,\n                  sourceType: parsedData.sourceType,\n                  project,\n                  projectPath,\n                  distDir,\n                  config: nextConfig.experimental.browserDebugInfoInTerminal,\n                })\n              }\n              break\n            }\n\n            default:\n              // Might be a Turbopack message...\n              if (!parsedData.type) {\n                throw new Error(`unrecognized HMR message \"${data}\"`)\n              }\n          }\n\n          // Turbopack messages\n          switch (parsedData.type) {\n            case 'turbopack-subscribe':\n              subscribeToHmrEvents(client, parsedData.path)\n              break\n\n            case 'turbopack-unsubscribe':\n              unsubscribeFromHmrEvents(client, parsedData.path)\n              break\n\n            default:\n              if (!parsedData.event) {\n                throw new Error(`unrecognized Turbopack HMR message \"${data}\"`)\n              }\n          }\n        })\n\n        const turbopackConnected: TurbopackConnectedAction = {\n          action: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED,\n          data: { sessionId },\n        }\n        sendToClient(client, turbopackConnected)\n\n        const errors: CompilationError[] = []\n\n        for (const entryIssues of currentEntryIssues.values()) {\n          for (const issue of entryIssues.values()) {\n            if (issue.severity !== 'warning') {\n              errors.push({\n                message: formatIssue(issue),\n              })\n            } else {\n              printNonFatalIssue(issue)\n            }\n          }\n        }\n\n        if (devIndicatorServerState.disabledUntil < Date.now()) {\n          devIndicatorServerState.disabledUntil = 0\n        }\n\n        ;(async function () {\n          const versionInfo = await versionInfoPromise\n          const devToolsConfig = await getDevToolsConfig(distDir)\n\n          const sync: SyncAction = {\n            action: HMR_ACTIONS_SENT_TO_BROWSER.SYNC,\n            errors,\n            warnings: [],\n            hash: '',\n            versionInfo,\n            debug: {\n              devtoolsFrontendUrl,\n            },\n            devIndicator: devIndicatorServerState,\n            devToolsConfig,\n          }\n\n          sendToClient(client, sync)\n        })()\n      })\n    },\n\n    send(action) {\n      const payload = JSON.stringify(action)\n      for (const client of clients) {\n        client.send(payload)\n      }\n    },\n\n    setHmrServerError(_error) {\n      // Not implemented yet.\n    },\n    clearHmrServerError() {\n      // Not implemented yet.\n    },\n    async start() {},\n    async getCompilationErrors(page) {\n      const appEntryKey = getEntryKey('app', 'server', page)\n      const pagesEntryKey = getEntryKey('pages', 'server', page)\n\n      const topLevelIssues = currentTopLevelIssues.values()\n\n      const thisEntryIssues =\n        currentEntryIssues.get(appEntryKey) ??\n        currentEntryIssues.get(pagesEntryKey)\n\n      if (thisEntryIssues !== undefined && thisEntryIssues.size > 0) {\n        // If there is an error related to the requesting page we display it instead of the first error\n        return [...topLevelIssues, ...thisEntryIssues.values()]\n          .map((issue) => {\n            const formattedIssue = formatIssue(issue)\n            if (issue.severity === 'warning') {\n              printNonFatalIssue(issue)\n              return null\n            } else if (isWellKnownError(issue)) {\n              Log.error(formattedIssue)\n            }\n\n            return new Error(formattedIssue)\n          })\n          .filter((error) => error !== null)\n      }\n\n      // Otherwise, return all errors across pages\n      const errors = []\n      for (const issue of topLevelIssues) {\n        if (issue.severity !== 'warning') {\n          errors.push(new Error(formatIssue(issue)))\n        }\n      }\n      for (const entryIssues of currentEntryIssues.values()) {\n        for (const issue of entryIssues.values()) {\n          if (issue.severity !== 'warning') {\n            const message = formatIssue(issue)\n            errors.push(new Error(message))\n          } else {\n            printNonFatalIssue(issue)\n          }\n        }\n      }\n      return errors\n    },\n    async invalidate({\n      // .env files or tsconfig/jsconfig change\n      reloadAfterInvalidation,\n    }) {\n      if (reloadAfterInvalidation) {\n        for (const [key, entrypoint] of currentWrittenEntrypoints) {\n          clearRequireCache(key, entrypoint, { force: true })\n        }\n\n        await clearAllModuleContexts()\n        this.send({\n          action: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES,\n          hash: String(++hmrHash),\n        })\n      }\n    },\n    async buildFallbackError() {\n      // Not implemented yet.\n    },\n    async ensurePage({\n      page: inputPage,\n      // Unused parameters\n      // clientOnly,\n      appPaths,\n      definition,\n      isApp,\n      url: requestUrl,\n    }) {\n      // When there is no route definition this is an internal file not a route the user added.\n      // Middleware and instrumentation are handled in turbpack-utils.ts handleEntrypoints instead.\n      if (!definition) {\n        if (inputPage === '/middleware') return\n        if (inputPage === '/src/middleware') return\n        if (inputPage === '/instrumentation') return\n        if (inputPage === '/src/instrumentation') return\n      }\n\n      return hotReloaderSpan\n        .traceChild('ensure-page', {\n          inputPage,\n        })\n        .traceAsyncFn(async () => {\n          if (BLOCKED_PAGES.includes(inputPage) && inputPage !== '/_error') {\n            return\n          }\n\n          await currentEntriesHandling\n\n          // TODO We shouldn't look into the filesystem again. This should use the information from entrypoints\n          let routeDef: Pick<\n            RouteDefinition,\n            'filename' | 'bundlePath' | 'page'\n          > =\n            definition ??\n            (await findPagePathData(\n              projectPath,\n              inputPage,\n              nextConfig.pageExtensions,\n              opts.pagesDir,\n              opts.appDir,\n              !!nextConfig.experimental.globalNotFound\n            ))\n\n          // If the route is actually an app page route, then we should have access\n          // to the app route definition, and therefore, the appPaths from it.\n          if (!appPaths && definition && isAppPageRouteDefinition(definition)) {\n            appPaths = definition.appPaths\n          }\n\n          let page = routeDef.page\n          if (appPaths) {\n            const normalizedPage = normalizeAppPath(page)\n\n            // filter out paths that are not exact matches (e.g. catchall)\n            const matchingAppPaths = appPaths.filter(\n              (path) => normalizeAppPath(path) === normalizedPage\n            )\n\n            // the last item in the array is the root page, if there are parallel routes\n            page = matchingAppPaths[matchingAppPaths.length - 1]\n          }\n\n          const pathname = definition?.pathname ?? inputPage\n\n          if (page === '/_error') {\n            let finishBuilding = startBuilding(pathname, requestUrl, false)\n            try {\n              await handlePagesErrorRoute({\n                currentEntryIssues,\n                entrypoints: currentEntrypoints,\n                manifestLoader,\n                devRewrites: opts.fsChecker.rewrites,\n                productionRewrites: undefined,\n                logErrors: true,\n                hooks: {\n                  subscribeToChanges,\n                  handleWrittenEndpoint: (id, result, forceDeleteCache) => {\n                    currentWrittenEntrypoints.set(id, result)\n                    assetMapper.setPathsForKey(id, result.clientPaths)\n                    return clearRequireCache(id, result, {\n                      force: forceDeleteCache,\n                    })\n                  },\n                },\n              })\n            } finally {\n              finishBuilding()\n            }\n            return\n          }\n\n          const isInsideAppDir = routeDef.bundlePath.startsWith('app/')\n          const isEntryMetadataRouteFile = isMetadataRouteFile(\n            routeDef.filename.replace(opts.appDir || '', ''),\n            nextConfig.pageExtensions,\n            true\n          )\n          const normalizedAppPage = isEntryMetadataRouteFile\n            ? normalizedPageToTurbopackStructureRoute(\n                page,\n                extname(routeDef.filename)\n              )\n            : page\n\n          const route = isInsideAppDir\n            ? currentEntrypoints.app.get(normalizedAppPage)\n            : currentEntrypoints.page.get(page)\n\n          if (!route) {\n            // TODO: why is this entry missing in turbopack?\n            if (page === '/middleware') return\n            if (page === '/src/middleware') return\n            if (page === '/instrumentation') return\n            if (page === '/src/instrumentation') return\n\n            throw new PageNotFoundError(`route not found ${page}`)\n          }\n\n          // We don't throw on ensureOpts.isApp === true for page-api\n          // since this can happen when app pages make\n          // api requests to page API routes.\n          if (isApp && route.type === 'page') {\n            throw new Error(`mis-matched route type: isApp && page for ${page}`)\n          }\n\n          const finishBuilding = startBuilding(pathname, requestUrl, false)\n          try {\n            await handleRouteType({\n              dev,\n              page,\n              pathname,\n              route,\n              currentEntryIssues,\n              entrypoints: currentEntrypoints,\n              manifestLoader,\n              readyIds,\n              devRewrites: opts.fsChecker.rewrites,\n              productionRewrites: undefined,\n              logErrors: true,\n\n              hooks: {\n                subscribeToChanges,\n                handleWrittenEndpoint: (id, result, forceDeleteCache) => {\n                  currentWrittenEntrypoints.set(id, result)\n                  assetMapper.setPathsForKey(id, result.clientPaths)\n                  return clearRequireCache(id, result, {\n                    force: forceDeleteCache,\n                  })\n                },\n              },\n            })\n          } finally {\n            finishBuilding()\n          }\n        })\n    },\n    close() {\n      for (const wsClient of clients) {\n        // it's okay to not cleanly close these websocket connections, this is dev\n        wsClient.terminate()\n      }\n      clients.clear()\n    },\n  }\n\n  handleEntrypointsSubscription().catch((err) => {\n    console.error(err)\n    process.exit(1)\n  })\n\n  // Write empty manifests\n  await currentEntriesHandling\n  await manifestLoader.writeManifests({\n    devRewrites: opts.fsChecker.rewrites,\n    productionRewrites: undefined,\n    entrypoints: currentEntrypoints,\n  })\n\n  async function handleProjectUpdates() {\n    for await (const updateMessage of project.updateInfoSubscribe(30)) {\n      switch (updateMessage.updateType) {\n        case 'start': {\n          hotReloader.send({ action: HMR_ACTIONS_SENT_TO_BROWSER.BUILDING })\n          break\n        }\n        case 'end': {\n          sendEnqueuedMessages()\n\n          function addErrors(\n            errorsMap: Map<string, CompilationError>,\n            issues: EntryIssuesMap\n          ) {\n            for (const issueMap of issues.values()) {\n              for (const [key, issue] of issueMap) {\n                if (issue.severity === 'warning') continue\n                if (errorsMap.has(key)) continue\n\n                const message = formatIssue(issue)\n\n                errorsMap.set(key, {\n                  message,\n                  details: issue.detail\n                    ? renderStyledStringToErrorAnsi(issue.detail)\n                    : undefined,\n                })\n              }\n            }\n          }\n\n          const errors = new Map<string, CompilationError>()\n          addErrors(errors, currentEntryIssues)\n\n          for (const client of clients) {\n            const state = clientStates.get(client)\n            if (!state) {\n              continue\n            }\n\n            const clientErrors = new Map(errors)\n            addErrors(clientErrors, state.clientIssues)\n\n            sendToClient(client, {\n              action: HMR_ACTIONS_SENT_TO_BROWSER.BUILT,\n              hash: String(++hmrHash),\n              errors: [...clientErrors.values()],\n              warnings: [],\n            })\n          }\n\n          if (hmrEventHappened) {\n            const time = updateMessage.value.duration\n            const timeMessage =\n              time > 2000 ? `${Math.round(time / 100) / 10}s` : `${time}ms`\n            Log.event(`Compiled in ${timeMessage}`)\n            hmrEventHappened = false\n          }\n          break\n        }\n        default:\n      }\n    }\n  }\n\n  handleProjectUpdates().catch((err) => {\n    console.error(err)\n    process.exit(1)\n  })\n\n  return hotReloader\n}\n"], "names": ["createHotReloaderTurbopack", "wsServer", "ws", "Server", "noServer", "isTestMode", "process", "env", "NEXT_TEST_MODE", "__NEXT_TEST_MODE", "DEBUG", "sessionId", "Math", "floor", "Number", "MAX_SAFE_INTEGER", "random", "rewriteTurbopackSources", "projectRoot", "sourceMap", "section", "sections", "map", "i", "sources", "length", "pathToFileURL", "join", "replace", "toString", "getSourceMapFromTurbopack", "project", "sourceURL", "sourceMapJson", "getSourceMapSync", "err", "undefined", "payload", "JSON", "parse", "opts", "serverFields", "distDir", "resetFetch", "nextConfig", "dev", "buildId", "dir", "projectPath", "loadBindings", "require", "bindings", "log", "testMode", "hasRewrites", "fs<PERSON><PERSON><PERSON>", "rewrites", "afterFiles", "beforeFiles", "fallback", "hotReloaderSpan", "trace", "version", "__NEXT_VERSION", "stop", "<PERSON><PERSON><PERSON>", "generateEncryptionKeyBase64", "isBuild", "clientRouterFilters", "experimental", "clientRouterFilter", "supportedBrowsers", "getSupportedBrowsers", "currentNodeJsVersion", "versions", "node", "rootPath", "turbopack", "root", "outputFileTracingRoot", "turbo", "createProject", "normalizePath", "relative", "jsConfig", "getTurbopackJsConfig", "watch", "enable", "pollIntervalMs", "watchOptions", "defineEnv", "createDefineEnv", "isTurbopack", "config", "fetchCacheKeyPrefix", "middlewareMatchers", "previewProps", "prerenderManifest", "preview", "browserslistQuery", "noMangling", "persistentCaching", "isPersistentCachingEnabled", "memoryLimit", "turbopackMemoryLimit", "isShortSession", "backgroundLogCompilationEvents", "eventTypes", "setBundlerFindSourceMapImplementation", "bind", "onDevServerCleanup", "onExit", "entrypointsSubscription", "entrypointsSubscribe", "currentWrittenEntrypoints", "Map", "currentEntrypoints", "global", "app", "document", "error", "middleware", "instrumentation", "page", "currentTopLevelIssues", "currentEntryIssues", "manifest<PERSON><PERSON>der", "TurbopackManifestLoader", "changeSubscriptions", "serverPathState", "readyIds", "Set", "currentEntriesHandlingResolve", "currentEntriesHandling", "Promise", "resolve", "assetMapper", "AssetMapper", "clearRequireCache", "key", "writtenEndpoint", "force", "path", "contentHash", "serverPaths", "set", "hasChange", "endsWith", "localKey", "localHash", "get", "globalHash", "__next__clear_chunk_cache__", "p", "file", "clearModuleContext", "deleteCache", "buildingIds", "startBuilding", "id", "requestUrl", "forceRebuild", "has", "size", "consoleStore", "setState", "loading", "trigger", "url", "add", "finishBuilding", "delete", "hmrEventHappened", "hmrHash", "clients", "clientStates", "WeakMap", "sendToClient", "client", "send", "stringify", "sendEnqueuedMessages", "issueMap", "values", "filter", "severity", "state", "clientIssues", "hmrPayloads", "clear", "turbopackUpdates", "action", "HMR_ACTIONS_SENT_TO_BROWSER", "TURBOPACK_MESSAGE", "data", "sendEnqueuedMessagesDebounce", "debounce", "sendHmr", "sendTurbopackMessage", "diagnostics", "issues", "push", "subscribeToChanges", "includeIssues", "endpoint", "makePayload", "onError", "side", "splitEntryKey", "changedPromise", "changed", "change", "processIssues", "String", "e", "unsubscribeFromChanges", "subscription", "return", "subscribeToHmrEvents", "getEntry<PERSON>ey", "hasEntrypointForKey", "subscriptions", "hmrEvents", "next", "type", "reloadAction", "RELOAD_PAGE", "close", "unsubscribeFromHmrEvents", "handleEntrypointsSubscription", "entrypoints", "processTopLevelIssues", "handleEntrypoints", "devRewrites", "productionRewrites", "logErrors", "hooks", "handleWrittenEndpoint", "result", "forceDeleteCache", "propagateServerField", "mkdir", "recursive", "writeFile", "middlewares", "getOverlayMiddleware", "isSrcDir", "getSourceMapMiddleware", "getNextErrorFeedbackMiddleware", "telemetry", "getDevOverlayFontMiddleware", "getDisableDevIndicatorMiddleware", "getRestartDevServerMiddleware", "turbopackProject", "devToolsConfigMiddleware", "sendUpdateSignal", "hotReloader", "DEVTOOLS_CONFIG", "versionInfoPromise", "getVersionInfo", "devtoolsFrontendUrl", "nodeDebugType", "getNodeDebugType", "debugPort", "debugInfo", "debugInfoList", "fetch", "then", "res", "json", "activeWebpackConfigs", "serverStats", "edgeServerStats", "run", "req", "_parsedUrl", "startsWith", "params", "matchNextPageBundleRequest", "decodedPagePath", "param", "decodeURIComponent", "denormalizedPagePath", "denormalizePagePath", "ensurePage", "clientOnly", "definition", "catch", "console", "calledNext", "finished", "onHMR", "socket", "head", "onUpgrade", "handleUpgrade", "on", "addEventListener", "parsedData", "event", "manualTraceChild", "spanName", "msToNs", "startTime", "endTime", "attributes", "updatedModules", "isPageHidden", "hadRuntimeError", "dependency<PERSON><PERSON>n", "Log", "warn", "FAST_REFRESH_RUNTIME_RELOAD", "Array", "isArray", "cleanedModulePath", "browserDebugInfoInTerminal", "receiveBrowserLogsTurbopack", "entries", "router", "sourceType", "Error", "turbopackConnected", "TURBOPACK_CONNECTED", "errors", "entryIssues", "issue", "message", "formatIssue", "printNonFatalIssue", "devIndicatorServerState", "disabledUntil", "Date", "now", "versionInfo", "devToolsConfig", "getDevToolsConfig", "sync", "SYNC", "warnings", "hash", "debug", "devIndicator", "setHmrServerError", "_error", "clearHmrServerError", "start", "getCompilationErrors", "appEntry<PERSON>ey", "pagesEntry<PERSON>ey", "topLevelIssues", "thisEntryIssues", "formattedIssue", "isWellKnownError", "invalidate", "reloadAfterInvalidation", "entrypoint", "clearAllModuleContexts", "SERVER_COMPONENT_CHANGES", "buildFallbackError", "inputPage", "appPaths", "isApp", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "BLOCKED_PAGES", "includes", "routeDef", "findPagePathData", "pageExtensions", "pagesDir", "appDir", "globalNotFound", "isAppPageRouteDefinition", "normalizedPage", "normalizeAppPath", "matchingAppPaths", "pathname", "handlePagesErrorRoute", "setPathsFor<PERSON>ey", "clientPaths", "isInsideAppDir", "bundlePath", "isEntryMetadataRouteFile", "isMetadataRouteFile", "filename", "normalizedAppPage", "normalizedPageToTurbopackStructureRoute", "extname", "route", "PageNotFoundError", "handleRouteType", "wsClient", "terminate", "exit", "writeManifests", "handleProjectUpdates", "updateMessage", "updateInfoSubscribe", "updateType", "BUILDING", "addErrors", "errorsMap", "details", "detail", "renderStyledStringToErrorAnsi", "clientErrors", "BUILT", "time", "value", "duration", "timeMessage", "round"], "mappings": ";;;;+BAmKsBA;;;eAAAA;;;0BAlKW;sBACO;qBACV;2DAEf;uBAGuB;kCASM;qBASZ;6DACX;oCAId;2BACuB;qCAIvB;uBAC2B;wBACT;8BACG;8BAIrB;qCAC6B;uBACd;gCAgBf;iCAKA;gCACiC;sCACP;0BAM1B;0BACqC;uCACA;wCACH;0BACR;wBAEA;iCACG;mCACkB;gDACP;wBAUxC;6CACqC;yCACJ;wCACS;4CACH;mCACC;wBACV;6BACO;+BACd;0CAIvB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEP,MAAMC,WAAW,IAAIC,WAAE,CAACC,MAAM,CAAC;IAAEC,UAAU;AAAK;AAChD,MAAMC,aAAa,CAAC,CAClBC,CAAAA,QAAQC,GAAG,CAACC,cAAc,IAC1BF,QAAQC,GAAG,CAACE,gBAAgB,IAC5BH,QAAQC,GAAG,CAACG,KAAK,AAAD;AAGlB,MAAMC,YAAYC,KAAKC,KAAK,CAACC,OAAOC,gBAAgB,GAAGH,KAAKI,MAAM;AAIlE;;CAEC,GACD,SAASC,wBACPC,WAAmB,EACnBC,SAAiC;IAEjC,IAAI,cAAcA,WAAW;QAC3B,KAAK,MAAMC,WAAWD,UAAUE,QAAQ,CAAE;YACxCJ,wBAAwBC,aAAaE,QAAQE,GAAG;QAClD;IACF,OAAO;QACL,IAAK,IAAIC,IAAI,GAAGA,IAAIJ,UAAUK,OAAO,CAACC,MAAM,EAAEF,IAAK;YACjDJ,UAAUK,OAAO,CAACD,EAAE,GAAGG,IAAAA,kBAAa,EAClCC,IAAAA,UAAI,EACFT,aACAC,UAAUK,OAAO,CAACD,EAAE,CAACK,OAAO,CAAC,+BAA+B,MAE9DC,QAAQ;QACZ;IACF;AACF;AAEA,SAASC,0BACPC,OAAgB,EAChBb,WAAmB,EACnBc,SAAiB;IAEjB,IAAIC,gBAA+B;IAEnC,IAAI;QACFA,gBAAgBF,QAAQG,gBAAgB,CAACF;IAC3C,EAAE,OAAOG,KAAK,CAAC;IAEf,IAAIF,kBAAkB,MAAM;QAC1B,OAAOG;IACT,OAAO;QACL,MAAMC,UAAkCC,KAAKC,KAAK,CAACN;QACnD,2EAA2E;QAC3E,4EAA4E;QAC5EhB,wBAAwBC,aAAamB;QACrC,OAAOA;IACT;AACF;AAEO,eAAerC,2BACpBwC,IAAuC,EACvCC,YAA0B,EAC1BC,OAAe,EACfC,UAAsB;QAkDpBH,4BAYoBI,0BA0BLJ;IAtFjB,MAAMK,MAAM;IACZ,MAAMC,UAAU;IAChB,MAAM,EAAEF,UAAU,EAAEG,KAAKC,WAAW,EAAE,GAAGR;IAEzC,MAAM,EAAES,YAAY,EAAE,GACpBC,QAAQ;IAEV,IAAIC,WAAW,MAAMF;IAErB,iGAAiG;IACjG,yGAAyG;IACzG,IAAI5C,YAAY;;QACZ6C,QAAQ,WAAwCE,GAAG,CACnD,8BACA;YACEL,KAAKC;YACLK,UAAUhD;QACZ;IAEJ;IAEA,MAAMiD,cACJd,KAAKe,SAAS,CAACC,QAAQ,CAACC,UAAU,CAAChC,MAAM,GAAG,KAC5Ce,KAAKe,SAAS,CAACC,QAAQ,CAACE,WAAW,CAACjC,MAAM,GAAG,KAC7Ce,KAAKe,SAAS,CAACC,QAAQ,CAACG,QAAQ,CAAClC,MAAM,GAAG;IAE5C,MAAMmC,kBAAkBC,IAAAA,YAAK,EAAC,gBAAgBzB,WAAW;QACvD0B,SAASxD,QAAQC,GAAG,CAACwD,cAAc;IACrC;IACA,8FAA8F;IAC9F,wCAAwC;IACxCH,gBAAgBI,IAAI;IAEpB,MAAMC,gBAAgB,MAAMC,IAAAA,kDAA2B,EAAC;QACtDC,SAAS;QACTzB;IACF;IAEA,kBAAkB;IAClB,IAAI0B;IACJ,IAAIxB,WAAWyB,YAAY,CAACC,kBAAkB,EAAE;IAC9C,oEAAoE;IACtE;IAEA,MAAMC,oBAAoBC,IAAAA,4BAAoB,EAACxB,aAAaH;IAC5D,MAAM4B,uBAAuBnE,QAAQoE,QAAQ,CAACC,IAAI;IAElD,MAAMC,WACJpC,EAAAA,6BAAAA,KAAKI,UAAU,CAACiC,SAAS,qBAAzBrC,2BAA2BsC,IAAI,KAC/BtC,KAAKI,UAAU,CAACmC,qBAAqB,IACrC/B;IACF,MAAMjB,UAAU,MAAMoB,SAAS6B,KAAK,CAACC,aAAa,CAChD;QACEL;QACA5B,aAAakC,IAAAA,4BAAa,EAACC,IAAAA,cAAQ,EAACP,UAAU5B,gBAAgB;QAC9DN;QACAE,YAAYJ,KAAKI,UAAU;QAC3BwC,UAAU,MAAMC,IAAAA,4BAAoB,EAACrC,aAAaJ;QAClD0C,OAAO;YACLC,QAAQ1C;YACR2C,cAAc,GAAE5C,2BAAAA,WAAW6C,YAAY,qBAAvB7C,yBAAyB4C,cAAc;QACzD;QACA3C;QACAtC,KAAKD,QAAQC,GAAG;QAChBmF,WAAWC,IAAAA,oBAAe,EAAC;YACzBC,aAAa;YACbxB;YACAyB,QAAQjD;YACRC;YACAH;YACAM;YACA8C,qBAAqBtD,KAAKI,UAAU,CAACyB,YAAY,CAACyB,mBAAmB;YACrExC;YACA,kBAAkB;YAClByC,oBAAoB3D;YACpBoB,UAAUhB,KAAKe,SAAS,CAACC,QAAQ;QACnC;QACAV;QACAmB;QACA+B,cAAcxD,KAAKe,SAAS,CAAC0C,iBAAiB,CAACC,OAAO;QACtDC,mBAAmB5B,kBAAkB5C,IAAI,CAAC;QAC1CyE,YAAY;QACZ3B;IACF,GACA;QACE4B,mBAAmBC,IAAAA,kCAA0B,EAAC9D,KAAKI,UAAU;QAC7D2D,WAAW,GAAE/D,gCAAAA,KAAKI,UAAU,CAACyB,YAAY,qBAA5B7B,8BAA8BgE,oBAAoB;QAC/DC,gBAAgB;IAClB;IAEFC,IAAAA,iDAA8B,EAAC3E,SAAS;QACtC4E,YAAY;YAAC;YAAiC;SAAc;IAC9D;IACAC,IAAAA,wDAAqC,EACnC9E,0BAA0B+E,IAAI,CAAC,MAAM9E,SAASiB;IAEhDR,KAAKsE,kBAAkB,oBAAvBtE,KAAKsE,kBAAkB,MAAvBtE,MAA0B;QACxBoE,IAAAA,wDAAqC,EAAC,IAAMxE;QAC5C,MAAML,QAAQgF,MAAM;IACtB;IACA,MAAMC,0BAA0BjF,QAAQkF,oBAAoB;IAE5D,MAAMC,4BAA4D,IAAIC;IACtE,MAAMC,qBAAkC;QACtCC,QAAQ;YACNC,KAAKlF;YACLmF,UAAUnF;YACVoF,OAAOpF;YAEPqF,YAAYrF;YACZsF,iBAAiBtF;QACnB;QAEAuF,MAAM,IAAIR;QACVG,KAAK,IAAIH;IACX;IAEA,MAAMS,wBAA2C,IAAIT;IACrD,MAAMU,qBAAqC,IAAIV;IAE/C,MAAMW,iBAAiB,IAAIC,uCAAuB,CAAC;QACjDjF;QACAJ;QACAuB;IACF;IAEA,eAAe;IACf,MAAM+D,sBAA2C,IAAIb;IACrD,MAAMc,kBAAkB,IAAId;IAC5B,MAAMe,WAAqB,IAAIC;IAC/B,IAAIC;IACJ,IAAIC,yBAAyB,IAAIC,QAC/B,CAACC,UAAaH,gCAAgCG;IAGhD,MAAMC,cAAc,IAAIC,2BAAW;IAEnC,SAASC,kBACPC,GAAa,EACbC,eAAgC,EAChC,EACEC,KAAK,EAIN,GAAG,CAAC,CAAC;QAEN,IAAIA,OAAO;YACT,KAAK,MAAM,EAAEC,IAAI,EAAEC,WAAW,EAAE,IAAIH,gBAAgBI,WAAW,CAAE;gBAC/Df,gBAAgBgB,GAAG,CAACH,MAAMC;YAC5B;QACF,OAAO;YACL,8CAA8C;YAC9C,IAAIG,YAAY;YAChB,KAAK,MAAM,EAAEJ,IAAI,EAAEC,WAAW,EAAE,IAAIH,gBAAgBI,WAAW,CAAE;gBAC/D,wBAAwB;gBACxB,IAAIF,KAAKK,QAAQ,CAAC,SAAS;gBAC3B,MAAMC,WAAW,GAAGT,IAAI,CAAC,EAAEG,MAAM;gBACjC,MAAMO,YAAYpB,gBAAgBqB,GAAG,CAACF;gBACtC,MAAMG,aAAatB,gBAAgBqB,GAAG,CAACR;gBACvC,IACE,AAACO,aAAaA,cAAcN,eAC3BQ,cAAcA,eAAeR,aAC9B;oBACAG,YAAY;oBACZjB,gBAAgBgB,GAAG,CAACN,KAAKI;oBACzBd,gBAAgBgB,GAAG,CAACH,MAAMC;gBAC5B,OAAO;oBACL,IAAI,CAACM,WAAW;wBACdpB,gBAAgBgB,GAAG,CAACN,KAAKI;oBAC3B;oBACA,IAAI,CAACQ,YAAY;wBACftB,gBAAgBgB,GAAG,CAACH,MAAMC;oBAC5B;gBACF;YACF;YAEA,IAAI,CAACG,WAAW;gBACd,OAAO;YACT;QACF;QAEAvG;QAEA,oBAAoB;QACpB,sCAAsC;QACtC,mFAAmF;QACnF,IAAI,OAAO6G,gCAAgC,YAAY;YACrDA;QACF;QAEA,MAAMR,cAAcJ,gBAAgBI,WAAW,CAAC1H,GAAG,CAAC,CAAC,EAAEwH,MAAMW,CAAC,EAAE,GAC9D9H,IAAAA,UAAI,EAACe,SAAS+G;QAGhB,KAAK,MAAMC,QAAQV,YAAa;YAC9BW,IAAAA,gCAAkB,EAACD;YACnBE,IAAAA,yBAAW,EAACF;QACd;QAEA,OAAO;IACT;IAEA,MAAMG,cAAc,IAAI1B;IAExB,MAAM2B,gBAA+B,CAACC,IAAIC,YAAYC;QACpD,IAAI,CAACA,gBAAgB/B,SAASgC,GAAG,CAACH,KAAK;YACrC,OAAO,KAAO;QAChB;QACA,IAAIF,YAAYM,IAAI,KAAK,GAAG;YAC1BC,YAAY,CAACC,QAAQ,CACnB;gBACEC,SAAS;gBACTC,SAASR;gBACTS,KAAKR;YACP,GACA;QAEJ;QACAH,YAAYY,GAAG,CAACV;QAChB,OAAO,SAASW;YACd,IAAIb,YAAYM,IAAI,KAAK,GAAG;gBAC1B;YACF;YACAjC,SAASuC,GAAG,CAACV;YACbF,YAAYc,MAAM,CAACZ;YACnB,IAAIF,YAAYM,IAAI,KAAK,GAAG;gBAC1BS,mBAAmB;gBACnBR,YAAY,CAACC,QAAQ,CACnB;oBACEC,SAAS;gBACX,GACA;YAEJ;QACF;IACF;IAEA,IAAIM,mBAAmB;IACvB,IAAIC,UAAU;IAEd,MAAMC,UAAU,IAAI3C;IACpB,MAAM4C,eAAe,IAAIC;IAEzB,SAASC,aAAaC,MAAU,EAAE7I,OAAyB;QACzD6I,OAAOC,IAAI,CAAC7I,KAAK8I,SAAS,CAAC/I;IAC7B;IAEA,SAASgJ;QACP,KAAK,MAAM,GAAGC,SAAS,IAAIzD,mBAAoB;YAC7C,IACE;mBAAIyD,SAASC,MAAM;aAAG,CAACC,MAAM,CAAC,CAACjK,IAAMA,EAAEkK,QAAQ,KAAK,WAAWhK,MAAM,GACrE,GACA;gBACA,mFAAmF;gBACnF;YACF;QACF;QAEA,KAAK,MAAMyJ,UAAUJ,QAAS;YAC5B,MAAMY,QAAQX,aAAazB,GAAG,CAAC4B;YAC/B,IAAI,CAACQ,OAAO;gBACV;YACF;YAEA,KAAK,MAAM,GAAGJ,SAAS,IAAII,MAAMC,YAAY,CAAE;gBAC7C,IACE;uBAAIL,SAASC,MAAM;iBAAG,CAACC,MAAM,CAAC,CAACjK,IAAMA,EAAEkK,QAAQ,KAAK,WACjDhK,MAAM,GAAG,GACZ;oBACA,mFAAmF;oBACnF;gBACF;YACF;YAEA,KAAK,MAAMY,WAAWqJ,MAAME,WAAW,CAACL,MAAM,GAAI;gBAChDN,aAAaC,QAAQ7I;YACvB;YACAqJ,MAAME,WAAW,CAACC,KAAK;YAEvB,IAAIH,MAAMI,gBAAgB,CAACrK,MAAM,GAAG,GAAG;gBACrCwJ,aAAaC,QAAQ;oBACnBa,QAAQC,6CAA2B,CAACC,iBAAiB;oBACrDC,MAAMR,MAAMI,gBAAgB;gBAC9B;gBACAJ,MAAMI,gBAAgB,CAACrK,MAAM,GAAG;YAClC;QACF;IACF;IACA,MAAM0K,+BAA+BC,IAAAA,gBAAQ,EAACf,sBAAsB;IAEpE,MAAMgB,UAAmB,CAACtC,IAAY1H;QACpC,KAAK,MAAM6I,UAAUJ,QAAS;gBAC5BC;aAAAA,oBAAAA,aAAazB,GAAG,CAAC4B,4BAAjBH,kBAA0Ba,WAAW,CAAC3C,GAAG,CAACc,IAAI1H;QAChD;QAEAuI,mBAAmB;QACnBuB;IACF;IAEA,SAASG,qBAAqBjK,OAAwB;QACpD,kGAAkG;QAClG,mCAAmC;QACnC,iGAAiG;QACjGA,QAAQkK,WAAW,GAAG,EAAE;QACxBlK,QAAQmK,MAAM,GAAG,EAAE;QAEnB,KAAK,MAAMtB,UAAUJ,QAAS;gBAC5BC;aAAAA,oBAAAA,aAAazB,GAAG,CAAC4B,4BAAjBH,kBAA0Be,gBAAgB,CAACW,IAAI,CAACpK;QAClD;QAEAuI,mBAAmB;QACnBuB;IACF;IAEA,eAAeO,mBACb/D,GAAa,EACbgE,aAAsB,EACtBC,QAAkB,EAClBC,WAGwD,EACxDC,OAEwD;QAExD,IAAI9E,oBAAoBkC,GAAG,CAACvB,MAAM;YAChC;QACF;QAEA,MAAM,EAAEoE,IAAI,EAAE,GAAGC,IAAAA,uBAAa,EAACrE;QAE/B,MAAMsE,iBAAiBL,QAAQ,CAAC,GAAGG,KAAK,OAAO,CAAC,CAAC,CAACJ;QAClD3E,oBAAoBiB,GAAG,CAACN,KAAKsE;QAC7B,IAAI;YACF,MAAMC,UAAU,MAAMD;YAEtB,WAAW,MAAME,UAAUD,QAAS;gBAClCE,IAAAA,qBAAa,EAACvF,oBAAoBc,KAAKwE,QAAQ,OAAO;gBACtD,mDAAmD;gBACnD,MAAM9K,UAAU,MAAMwK,YAAYM,QAAQE,OAAO,EAAExC;gBACnD,IAAIxI,SAAS;oBACXgK,QAAQ1D,KAAKtG;gBACf;YACF;QACF,EAAE,OAAOiL,GAAG;YACVtF,oBAAoB2C,MAAM,CAAChC;YAC3B,MAAMtG,UAAU,OAAMyK,2BAAAA,QAAUQ;YAChC,IAAIjL,SAAS;gBACXgK,QAAQ1D,KAAKtG;YACf;YACA;QACF;QACA2F,oBAAoB2C,MAAM,CAAChC;IAC7B;IAEA,eAAe4E,uBAAuB5E,GAAa;QACjD,MAAM6E,eAAe,MAAMxF,oBAAoBsB,GAAG,CAACX;QACnD,IAAI6E,cAAc;YAChB,OAAMA,aAAaC,MAAM,oBAAnBD,aAAaC,MAAM,MAAnBD;YACNxF,oBAAoB2C,MAAM,CAAChC;QAC7B;QACAd,mBAAmB8C,MAAM,CAAChC;IAC5B;IAEA,eAAe+E,qBAAqBxC,MAAU,EAAEnB,EAAU;QACxD,MAAMpB,MAAMgF,IAAAA,qBAAW,EAAC,UAAU,UAAU5D;QAC5C,IAAI,CAAC6D,IAAAA,mCAAmB,EAACxG,oBAAoBuB,KAAKH,cAAc;YAC9D,qDAAqD;YACrD;QACF;QAEA,MAAMkD,QAAQX,aAAazB,GAAG,CAAC4B;QAC/B,IAAI,CAACQ,SAASA,MAAMmC,aAAa,CAAC3D,GAAG,CAACH,KAAK;YACzC;QACF;QAEA,MAAMyD,eAAezL,QAAS+L,SAAS,CAAC/D;QACxC2B,MAAMmC,aAAa,CAAC5E,GAAG,CAACc,IAAIyD;QAE5B,+DAA+D;QAC/D,oDAAoD;QACpD,IAAI;YACF,MAAMA,aAAaO,IAAI;YAEvB,WAAW,MAAM7B,QAAQsB,aAAc;gBACrCJ,IAAAA,qBAAa,EAAC1B,MAAMC,YAAY,EAAEhD,KAAKuD,MAAM,OAAO;gBACpD,IAAIA,KAAK8B,IAAI,KAAK,UAAU;oBAC1B1B,qBAAqBJ;gBACvB;YACF;QACF,EAAE,OAAOoB,GAAG;YACV,6EAA6E;YAC7E,8DAA8D;YAC9D,sEAAsE;YACtE,2CAA2C;YAC3C,MAAMW,eAAiC;gBACrClC,QAAQC,6CAA2B,CAACkC,WAAW;gBAC/ChC,MAAM,CAAC,oCAAoC,EAAEnC,GAAG,EAAE,EAAEuD,GAAG;YACzD;YACArC,aAAaC,QAAQ+C;YACrB/C,OAAOiD,KAAK;YACZ;QACF;IACF;IAEA,SAASC,yBAAyBlD,MAAU,EAAEnB,EAAU;QACtD,MAAM2B,QAAQX,aAAazB,GAAG,CAAC4B;QAC/B,IAAI,CAACQ,OAAO;YACV;QACF;QAEA,MAAM8B,eAAe9B,MAAMmC,aAAa,CAACvE,GAAG,CAACS;QAC7CyD,gCAAAA,aAAcC,MAAM;QAEpB,MAAM9E,MAAMgF,IAAAA,qBAAW,EAAC,UAAU,UAAU5D;QAC5C2B,MAAMC,YAAY,CAAChB,MAAM,CAAChC;IAC5B;IAEA,eAAe0F;QACb,WAAW,MAAMC,eAAetH,wBAAyB;YACvD,IAAI,CAACoB,+BAA+B;gBAClCC,yBAAyB,IAAIC,QAC3B,wCAAwC;gBACxC,CAACC,UAAaH,gCAAgCG;YAElD;YAEAgG,IAAAA,qCAAqB,EAAC3G,uBAAuB0G;YAE7C,MAAME,IAAAA,iCAAiB,EAAC;gBACtBF;gBAEAlH;gBAEAS;gBACAC;gBACA2G,aAAajM,KAAKe,SAAS,CAACC,QAAQ;gBACpCkL,oBAAoBtM;gBACpBuM,WAAW;gBAEX9L,KAAK;oBACH2F;oBACAR;oBACA8C;oBACAC;oBACAtI;oBAEAmM,OAAO;wBACLC,uBAAuB,CAAC9E,IAAI+E,QAAQC;4BAClC7H,0BAA0B+B,GAAG,CAACc,IAAI+E;4BAClC,OAAOpG,kBAAkBqB,IAAI+E,QAAQ;gCAAEjG,OAAOkG;4BAAiB;wBACjE;wBACAC,sBAAsBA,qCAAoB,CAACnI,IAAI,CAAC,MAAMrE;wBACtD6J;wBACAvC;wBACA4C;wBACAa;wBACAa;oBACF;gBACF;YACF;YAEAhG;YACAA,gCAAgChG;QAClC;IACF;IAEA,MAAM6M,IAAAA,eAAK,EAACtN,IAAAA,UAAI,EAACe,SAAS,WAAW;QAAEwM,WAAW;IAAK;IACvD,MAAMD,IAAAA,eAAK,EAACtN,IAAAA,UAAI,EAACe,SAAS,UAAUI,UAAU;QAAEoM,WAAW;IAAK;IAChE,MAAMC,IAAAA,mBAAS,EACbxN,IAAAA,UAAI,EAACe,SAAS,iBACdJ,KAAK8I,SAAS,CACZ;QACE4C,MAAM;IACR,GACA,MACA;IAIJ,MAAMoB,cAAc;QAClBC,IAAAA,yCAAoB,EAAC;YACnBtN;YACAiB;YACAsM,UAAU9M,KAAK8M,QAAQ;QACzB;QACAC,IAAAA,2CAAsB,EAACxN;QACvByN,IAAAA,8DAA8B,EAAChN,KAAKiN,SAAS;QAC7CC,IAAAA,wDAA2B;QAC3BC,IAAAA,wDAAgC;QAChCC,IAAAA,yDAA6B,EAAC;YAC5BH,WAAWjN,KAAKiN,SAAS;YACzBI,kBAAkB9N;QACpB;QACA+N,IAAAA,kDAAwB,EAAC;YACvBpN;YACAqN,kBAAkB,CAAC7D;gBACjB8D,YAAY7E,IAAI,CAAC;oBACfY,QAAQC,6CAA2B,CAACiE,eAAe;oBACnD/D;gBACF;YACF;QACF;KACD;IAED,MAAMgE,qBAAqBC,IAAAA,kCAAc;IAEzC,IAAIC;IACJ,MAAMC,gBAAgBC,IAAAA,wBAAgB;IACtC,IAAID,eAAe;QACjB,MAAME,YAAYjQ,QAAQiQ,SAAS;QACnC,IAAIC;QACJ,IAAI;YACF,8EAA8E;YAC9E,MAAMC,gBAAgB,MAAMC,MAC1B,CAAC,iBAAiB,EAAEH,UAAU,UAAU,CAAC,EACzCI,IAAI,CAAC,CAACC,MAAQA,IAAIC,IAAI;YACxBL,YAAYC,aAAa,CAAC,EAAE;QAC9B,EAAE,OAAM,CAAC;QACT,IAAID,WAAW;YACbJ,sBAAsBI,UAAUJ,mBAAmB;QACrD;IACF;IAEA,MAAMJ,cAA0C;QAC9CH,kBAAkB9N;QAClB+O,sBAAsB1O;QACtB2O,aAAa;QACbC,iBAAiB;QACjB,MAAMC,KAAIC,GAAG,EAAEN,GAAG,EAAEO,UAAU;gBAExBD;YADJ,+DAA+D;YAC/D,KAAIA,WAAAA,IAAI1G,GAAG,qBAAP0G,SAASE,UAAU,CAAC,gCAAgC;gBACtD,MAAMC,SAASC,IAAAA,8CAA0B,EAACJ,IAAI1G,GAAG;gBAEjD,IAAI6G,QAAQ;oBACV,MAAME,kBAAkB,CAAC,CAAC,EAAEF,OAAOvI,IAAI,CACpCxH,GAAG,CAAC,CAACkQ,QAAkBC,mBAAmBD,QAC1C7P,IAAI,CAAC,MAAM;oBAEd,MAAM+P,uBAAuBC,IAAAA,wCAAmB,EAACJ;oBAEjD,MAAMvB,YACH4B,UAAU,CAAC;wBACVjK,MAAM+J;wBACNG,YAAY;wBACZC,YAAY1P;wBACZoI,KAAK0G,IAAI1G,GAAG;oBACd,GACCuH,KAAK,CAACC,QAAQxK,KAAK;gBACxB;YACF;YAEA,KAAK,MAAMC,cAAc2H,YAAa;gBACpC,IAAI6C,aAAa;gBAEjB,MAAMxK,WAAWyJ,KAAKN,KAAK;oBACzBqB,aAAa;gBACf;gBAEA,IAAI,CAACA,YAAY;oBACf,OAAO;wBAAEC,UAAU;oBAAK;gBAC1B;YACF;YAEA,4BAA4B;YAC5B,OAAO;gBAAEA,UAAU9P;YAAU;QAC/B;QAEA,2EAA2E;QAC3E+P,OAAMjB,GAAG,EAAEkB,MAAc,EAAEC,IAAI,EAAEC,SAAS;YACxCrS,SAASsS,aAAa,CAACrB,KAAKkB,QAAQC,MAAM,CAACnH;gBACzCoH,UAAUpH;gBACV,MAAMS,eAA+B,IAAIxE;gBACzC,MAAM0G,gBAAiD,IAAI1G;gBAE3D2D,QAAQL,GAAG,CAACS;gBACZH,aAAa9B,GAAG,CAACiC,QAAQ;oBACvBS;oBACAC,aAAa,IAAIzE;oBACjB2E,kBAAkB,EAAE;oBACpB+B;gBACF;gBAEA3C,OAAOsH,EAAE,CAAC,SAAS;oBACjB,8BAA8B;oBAC9B,KAAK,MAAMhF,gBAAgBK,cAActC,MAAM,GAAI;wBACjDiC,aAAaC,MAAM,oBAAnBD,aAAaC,MAAM,MAAnBD;oBACF;oBACAzC,aAAaJ,MAAM,CAACO;oBACpBJ,QAAQH,MAAM,CAACO;gBACjB;gBAEAA,OAAOuH,gBAAgB,CAAC,WAAW,OAAO,EAAEvG,IAAI,EAAE;oBAChD,MAAMwG,aAAapQ,KAAKC,KAAK,CAC3B,OAAO2J,SAAS,WAAWA,KAAKrK,QAAQ,KAAKqK;oBAG/C,mBAAmB;oBACnB,OAAQwG,WAAWC,KAAK;wBACtB,KAAK;4BAAY;gCACf/O,gBAAgBgP,gBAAgB,CAC9BF,WAAWG,QAAQ,EACnBC,IAAAA,sBAAM,EAACJ,WAAWK,SAAS,GAC3BD,IAAAA,sBAAM,EAACJ,WAAWM,OAAO,GACzBN,WAAWO,UAAU;gCAEvB;4BACF;wBACA,KAAK;4BACHrP,gBAAgBgP,gBAAgB,CAC9BF,WAAWC,KAAK,EAChBG,IAAAA,sBAAM,EAACJ,WAAWK,SAAS,GAC3BD,IAAAA,sBAAM,EAACJ,WAAWM,OAAO,GACzB;gCACEE,gBAAgBR,WAAWQ,cAAc;gCACzCvL,MAAM+K,WAAW/K,IAAI;gCACrBwL,cAAcT,WAAWS,YAAY;4BACvC;4BAEF;wBAEF,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;4BACH,MAAM,EAAEC,eAAe,EAAEC,eAAe,EAAE,GAAGX;4BAC7C,IAAIU,iBAAiB;gCACnBE,KAAIC,IAAI,CAACC,qCAA2B;4BACtC;4BACA,IACEC,MAAMC,OAAO,CAACL,oBACd,OAAOA,eAAe,CAAC,EAAE,KAAK,UAC9B;gCACA,MAAMM,oBAAoBN,eAAe,CAAC,EAAE,CACzCzR,OAAO,CAAC,gBAAgB,KACxBA,OAAO,CAAC,mBAAmB;gCAC9B0R,KAAIC,IAAI,CACN,CAAC,+CAA+C,EAAEI,kBAAkB,yEAAyE,CAAC;4BAElJ;4BACA;wBACF,KAAK;4BAEH;wBACF,KAAK;4BAAgB;gCACnB,IAAI/Q,WAAWyB,YAAY,CAACuP,0BAA0B,EAAE;oCACtD,MAAMC,IAAAA,wCAA2B,EAAC;wCAChCC,SAASpB,WAAWoB,OAAO;wCAC3BC,QAAQrB,WAAWqB,MAAM;wCACzBC,YAAYtB,WAAWsB,UAAU;wCACjCjS;wCACAiB;wCACAN;wCACAmD,QAAQjD,WAAWyB,YAAY,CAACuP,0BAA0B;oCAC5D;gCACF;gCACA;4BACF;wBAEA;4BACE,kCAAkC;4BAClC,IAAI,CAAClB,WAAW1E,IAAI,EAAE;gCACpB,MAAM,qBAA+C,CAA/C,IAAIiG,MAAM,CAAC,0BAA0B,EAAE/H,KAAK,CAAC,CAAC,GAA9C,qBAAA;2CAAA;gDAAA;kDAAA;gCAA8C;4BACtD;oBACJ;oBAEA,qBAAqB;oBACrB,OAAQwG,WAAW1E,IAAI;wBACrB,KAAK;4BACHN,qBAAqBxC,QAAQwH,WAAW5J,IAAI;4BAC5C;wBAEF,KAAK;4BACHsF,yBAAyBlD,QAAQwH,WAAW5J,IAAI;4BAChD;wBAEF;4BACE,IAAI,CAAC4J,WAAWC,KAAK,EAAE;gCACrB,MAAM,qBAAyD,CAAzD,IAAIsB,MAAM,CAAC,oCAAoC,EAAE/H,KAAK,CAAC,CAAC,GAAxD,qBAAA;2CAAA;gDAAA;kDAAA;gCAAwD;4BAChE;oBACJ;gBACF;gBAEA,MAAMgI,qBAA+C;oBACnDnI,QAAQC,6CAA2B,CAACmI,mBAAmB;oBACvDjI,MAAM;wBAAEvL;oBAAU;gBACpB;gBACAsK,aAAaC,QAAQgJ;gBAErB,MAAME,SAA6B,EAAE;gBAErC,KAAK,MAAMC,eAAexM,mBAAmB0D,MAAM,GAAI;oBACrD,KAAK,MAAM+I,SAASD,YAAY9I,MAAM,GAAI;wBACxC,IAAI+I,MAAM7I,QAAQ,KAAK,WAAW;4BAChC2I,OAAO3H,IAAI,CAAC;gCACV8H,SAASC,IAAAA,mBAAW,EAACF;4BACvB;wBACF,OAAO;4BACLG,IAAAA,kCAAkB,EAACH;wBACrB;oBACF;gBACF;gBAEA,IAAII,gDAAuB,CAACC,aAAa,GAAGC,KAAKC,GAAG,IAAI;oBACtDH,gDAAuB,CAACC,aAAa,GAAG;gBAC1C;;gBAEE,CAAA;oBACA,MAAMG,cAAc,MAAM5E;oBAC1B,MAAM6E,iBAAiB,MAAMC,IAAAA,2CAAiB,EAACtS;oBAE/C,MAAMuS,OAAmB;wBACvBlJ,QAAQC,6CAA2B,CAACkJ,IAAI;wBACxCd;wBACAe,UAAU,EAAE;wBACZC,MAAM;wBACNN;wBACAO,OAAO;4BACLjF;wBACF;wBACAkF,cAAcZ,gDAAuB;wBACrCK;oBACF;oBAEA9J,aAAaC,QAAQ+J;gBACvB,CAAA;YACF;QACF;QAEA9J,MAAKY,MAAM;YACT,MAAM1J,UAAUC,KAAK8I,SAAS,CAACW;YAC/B,KAAK,MAAMb,UAAUJ,QAAS;gBAC5BI,OAAOC,IAAI,CAAC9I;YACd;QACF;QAEAkT,mBAAkBC,MAAM;QACtB,uBAAuB;QACzB;QACAC;QACE,uBAAuB;QACzB;QACA,MAAMC,UAAS;QACf,MAAMC,sBAAqBhO,IAAI;YAC7B,MAAMiO,cAAcjI,IAAAA,qBAAW,EAAC,OAAO,UAAUhG;YACjD,MAAMkO,gBAAgBlI,IAAAA,qBAAW,EAAC,SAAS,UAAUhG;YAErD,MAAMmO,iBAAiBlO,sBAAsB2D,MAAM;YAEnD,MAAMwK,kBACJlO,mBAAmByB,GAAG,CAACsM,gBACvB/N,mBAAmByB,GAAG,CAACuM;YAEzB,IAAIE,oBAAoB3T,aAAa2T,gBAAgB5L,IAAI,GAAG,GAAG;gBAC7D,+FAA+F;gBAC/F,OAAO;uBAAI2L;uBAAmBC,gBAAgBxK,MAAM;iBAAG,CACpDjK,GAAG,CAAC,CAACgT;oBACJ,MAAM0B,iBAAiBxB,IAAAA,mBAAW,EAACF;oBACnC,IAAIA,MAAM7I,QAAQ,KAAK,WAAW;wBAChCgJ,IAAAA,kCAAkB,EAACH;wBACnB,OAAO;oBACT,OAAO,IAAI2B,IAAAA,wBAAgB,EAAC3B,QAAQ;wBAClChB,KAAI9L,KAAK,CAACwO;oBACZ;oBAEA,OAAO,qBAAyB,CAAzB,IAAI/B,MAAM+B,iBAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAwB;gBACjC,GACCxK,MAAM,CAAC,CAAChE,QAAUA,UAAU;YACjC;YAEA,4CAA4C;YAC5C,MAAM4M,SAAS,EAAE;YACjB,KAAK,MAAME,SAASwB,eAAgB;gBAClC,IAAIxB,MAAM7I,QAAQ,KAAK,WAAW;oBAChC2I,OAAO3H,IAAI,CAAC,qBAA6B,CAA7B,IAAIwH,MAAMO,IAAAA,mBAAW,EAACF,SAAtB,qBAAA;+BAAA;oCAAA;sCAAA;oBAA4B;gBAC1C;YACF;YACA,KAAK,MAAMD,eAAexM,mBAAmB0D,MAAM,GAAI;gBACrD,KAAK,MAAM+I,SAASD,YAAY9I,MAAM,GAAI;oBACxC,IAAI+I,MAAM7I,QAAQ,KAAK,WAAW;wBAChC,MAAM8I,UAAUC,IAAAA,mBAAW,EAACF;wBAC5BF,OAAO3H,IAAI,CAAC,qBAAkB,CAAlB,IAAIwH,MAAMM,UAAV,qBAAA;mCAAA;wCAAA;0CAAA;wBAAiB;oBAC/B,OAAO;wBACLE,IAAAA,kCAAkB,EAACH;oBACrB;gBACF;YACF;YACA,OAAOF;QACT;QACA,MAAM8B,YAAW,EACf,yCAAyC;QACzCC,uBAAuB,EACxB;YACC,IAAIA,yBAAyB;gBAC3B,KAAK,MAAM,CAACxN,KAAKyN,WAAW,IAAIlP,0BAA2B;oBACzDwB,kBAAkBC,KAAKyN,YAAY;wBAAEvN,OAAO;oBAAK;gBACnD;gBAEA,MAAMwN,IAAAA,oCAAsB;gBAC5B,IAAI,CAAClL,IAAI,CAAC;oBACRY,QAAQC,6CAA2B,CAACsK,wBAAwB;oBAC5DlB,MAAM/H,OAAO,EAAExC;gBACjB;YACF;QACF;QACA,MAAM0L;QACJ,uBAAuB;QACzB;QACA,MAAM3E,YAAW,EACfjK,MAAM6O,SAAS,EACf,oBAAoB;QACpB,cAAc;QACdC,QAAQ,EACR3E,UAAU,EACV4E,KAAK,EACLlM,KAAKR,UAAU,EAChB;YACC,yFAAyF;YACzF,6FAA6F;YAC7F,IAAI,CAAC8H,YAAY;gBACf,IAAI0E,cAAc,eAAe;gBACjC,IAAIA,cAAc,mBAAmB;gBACrC,IAAIA,cAAc,oBAAoB;gBACtC,IAAIA,cAAc,wBAAwB;YAC5C;YAEA,OAAO5S,gBACJ+S,UAAU,CAAC,eAAe;gBACzBH;YACF,GACCI,YAAY,CAAC;gBACZ,IAAIC,wBAAa,CAACC,QAAQ,CAACN,cAAcA,cAAc,WAAW;oBAChE;gBACF;gBAEA,MAAMnO;gBAEN,qGAAqG;gBACrG,IAAI0O,WAIFjF,cACC,MAAMkF,IAAAA,sCAAgB,EACrBhU,aACAwT,WACA5T,WAAWqU,cAAc,EACzBzU,KAAK0U,QAAQ,EACb1U,KAAK2U,MAAM,EACX,CAAC,CAACvU,WAAWyB,YAAY,CAAC+S,cAAc;gBAG5C,yEAAyE;gBACzE,oEAAoE;gBACpE,IAAI,CAACX,YAAY3E,cAAcuF,IAAAA,gDAAwB,EAACvF,aAAa;oBACnE2E,WAAW3E,WAAW2E,QAAQ;gBAChC;gBAEA,IAAI9O,OAAOoP,SAASpP,IAAI;gBACxB,IAAI8O,UAAU;oBACZ,MAAMa,iBAAiBC,IAAAA,0BAAgB,EAAC5P;oBAExC,8DAA8D;oBAC9D,MAAM6P,mBAAmBf,SAASjL,MAAM,CACtC,CAAC1C,OAASyO,IAAAA,0BAAgB,EAACzO,UAAUwO;oBAGvC,4EAA4E;oBAC5E3P,OAAO6P,gBAAgB,CAACA,iBAAiB/V,MAAM,GAAG,EAAE;gBACtD;gBAEA,MAAMgW,WAAW3F,CAAAA,8BAAAA,WAAY2F,QAAQ,KAAIjB;gBAEzC,IAAI7O,SAAS,WAAW;oBACtB,IAAI+C,iBAAiBZ,cAAc2N,UAAUzN,YAAY;oBACzD,IAAI;wBACF,MAAM0N,IAAAA,qCAAqB,EAAC;4BAC1B7P;4BACAyG,aAAalH;4BACbU;4BACA2G,aAAajM,KAAKe,SAAS,CAACC,QAAQ;4BACpCkL,oBAAoBtM;4BACpBuM,WAAW;4BACXC,OAAO;gCACLlC;gCACAmC,uBAAuB,CAAC9E,IAAI+E,QAAQC;oCAClC7H,0BAA0B+B,GAAG,CAACc,IAAI+E;oCAClCtG,YAAYmP,cAAc,CAAC5N,IAAI+E,OAAO8I,WAAW;oCACjD,OAAOlP,kBAAkBqB,IAAI+E,QAAQ;wCACnCjG,OAAOkG;oCACT;gCACF;4BACF;wBACF;oBACF,SAAU;wBACRrE;oBACF;oBACA;gBACF;gBAEA,MAAMmN,iBAAiBd,SAASe,UAAU,CAAC1G,UAAU,CAAC;gBACtD,MAAM2G,2BAA2BC,IAAAA,oCAAmB,EAClDjB,SAASkB,QAAQ,CAACrW,OAAO,CAACY,KAAK2U,MAAM,IAAI,IAAI,KAC7CvU,WAAWqU,cAAc,EACzB;gBAEF,MAAMiB,oBAAoBH,2BACtBI,IAAAA,uDAAuC,EACrCxQ,MACAyQ,IAAAA,aAAO,EAACrB,SAASkB,QAAQ,KAE3BtQ;gBAEJ,MAAM0Q,QAAQR,iBACVzQ,mBAAmBE,GAAG,CAACgC,GAAG,CAAC4O,qBAC3B9Q,mBAAmBO,IAAI,CAAC2B,GAAG,CAAC3B;gBAEhC,IAAI,CAAC0Q,OAAO;oBACV,gDAAgD;oBAChD,IAAI1Q,SAAS,eAAe;oBAC5B,IAAIA,SAAS,mBAAmB;oBAChC,IAAIA,SAAS,oBAAoB;oBACjC,IAAIA,SAAS,wBAAwB;oBAErC,MAAM,IAAI2Q,wBAAiB,CAAC,CAAC,gBAAgB,EAAE3Q,MAAM;gBACvD;gBAEA,2DAA2D;gBAC3D,4CAA4C;gBAC5C,mCAAmC;gBACnC,IAAI+O,SAAS2B,MAAMrK,IAAI,KAAK,QAAQ;oBAClC,MAAM,qBAA8D,CAA9D,IAAIiG,MAAM,CAAC,0CAA0C,EAAEtM,MAAM,GAA7D,qBAAA;+BAAA;oCAAA;sCAAA;oBAA6D;gBACrE;gBAEA,MAAM+C,iBAAiBZ,cAAc2N,UAAUzN,YAAY;gBAC3D,IAAI;oBACF,MAAMuO,IAAAA,+BAAe,EAAC;wBACpB1V;wBACA8E;wBACA8P;wBACAY;wBACAxQ;wBACAyG,aAAalH;wBACbU;wBACAI;wBACAuG,aAAajM,KAAKe,SAAS,CAACC,QAAQ;wBACpCkL,oBAAoBtM;wBACpBuM,WAAW;wBAEXC,OAAO;4BACLlC;4BACAmC,uBAAuB,CAAC9E,IAAI+E,QAAQC;gCAClC7H,0BAA0B+B,GAAG,CAACc,IAAI+E;gCAClCtG,YAAYmP,cAAc,CAAC5N,IAAI+E,OAAO8I,WAAW;gCACjD,OAAOlP,kBAAkBqB,IAAI+E,QAAQ;oCACnCjG,OAAOkG;gCACT;4BACF;wBACF;oBACF;gBACF,SAAU;oBACRrE;gBACF;YACF;QACJ;QACAyD;YACE,KAAK,MAAMqK,YAAY1N,QAAS;gBAC9B,0EAA0E;gBAC1E0N,SAASC,SAAS;YACpB;YACA3N,QAAQe,KAAK;QACf;IACF;IAEAwC,gCAAgC0D,KAAK,CAAC,CAAC5P;QACrC6P,QAAQxK,KAAK,CAACrF;QACd7B,QAAQoY,IAAI,CAAC;IACf;IAEA,wBAAwB;IACxB,MAAMrQ;IACN,MAAMP,eAAe6Q,cAAc,CAAC;QAClClK,aAAajM,KAAKe,SAAS,CAACC,QAAQ;QACpCkL,oBAAoBtM;QACpBkM,aAAalH;IACf;IAEA,eAAewR;QACb,WAAW,MAAMC,iBAAiB9W,QAAQ+W,mBAAmB,CAAC,IAAK;YACjE,OAAQD,cAAcE,UAAU;gBAC9B,KAAK;oBAAS;wBACZ/I,YAAY7E,IAAI,CAAC;4BAAEY,QAAQC,6CAA2B,CAACgN,QAAQ;wBAAC;wBAChE;oBACF;gBACA,KAAK;oBAAO;wBACV3N;wBAEA,SAAS4N,UACPC,SAAwC,EACxC1M,MAAsB;4BAEtB,KAAK,MAAMlB,YAAYkB,OAAOjB,MAAM,GAAI;gCACtC,KAAK,MAAM,CAAC5C,KAAK2L,MAAM,IAAIhJ,SAAU;oCACnC,IAAIgJ,MAAM7I,QAAQ,KAAK,WAAW;oCAClC,IAAIyN,UAAUhP,GAAG,CAACvB,MAAM;oCAExB,MAAM4L,UAAUC,IAAAA,mBAAW,EAACF;oCAE5B4E,UAAUjQ,GAAG,CAACN,KAAK;wCACjB4L;wCACA4E,SAAS7E,MAAM8E,MAAM,GACjBC,IAAAA,qCAA6B,EAAC/E,MAAM8E,MAAM,IAC1ChX;oCACN;gCACF;4BACF;wBACF;wBAEA,MAAMgS,SAAS,IAAIjN;wBACnB8R,UAAU7E,QAAQvM;wBAElB,KAAK,MAAMqD,UAAUJ,QAAS;4BAC5B,MAAMY,QAAQX,aAAazB,GAAG,CAAC4B;4BAC/B,IAAI,CAACQ,OAAO;gCACV;4BACF;4BAEA,MAAM4N,eAAe,IAAInS,IAAIiN;4BAC7B6E,UAAUK,cAAc5N,MAAMC,YAAY;4BAE1CV,aAAaC,QAAQ;gCACnBa,QAAQC,6CAA2B,CAACuN,KAAK;gCACzCnE,MAAM/H,OAAO,EAAExC;gCACfuJ,QAAQ;uCAAIkF,aAAa/N,MAAM;iCAAG;gCAClC4J,UAAU,EAAE;4BACd;wBACF;wBAEA,IAAIvK,kBAAkB;4BACpB,MAAM4O,OAAOX,cAAcY,KAAK,CAACC,QAAQ;4BACzC,MAAMC,cACJH,OAAO,OAAO,GAAG5Y,KAAKgZ,KAAK,CAACJ,OAAO,OAAO,GAAG,CAAC,CAAC,GAAG,GAAGA,KAAK,EAAE,CAAC;4BAC/DlG,KAAIX,KAAK,CAAC,CAAC,YAAY,EAAEgH,aAAa;4BACtC/O,mBAAmB;wBACrB;wBACA;oBACF;gBACA;YACF;QACF;IACF;IAEAgO,uBAAuB7G,KAAK,CAAC,CAAC5P;QAC5B6P,QAAQxK,KAAK,CAACrF;QACd7B,QAAQoY,IAAI,CAAC;IACf;IAEA,OAAO1I;AACT", "ignoreList": [0]}