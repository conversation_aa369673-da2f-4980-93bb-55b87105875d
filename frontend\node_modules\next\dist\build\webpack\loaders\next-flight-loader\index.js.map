{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-flight-loader/index.ts"], "sourcesContent": ["import {\n  ModuleFilenameHelpers,\n  type javascript,\n  type LoaderContext,\n  type NormalModule,\n  type webpack,\n} from 'next/dist/compiled/webpack/webpack'\nimport { RSC_MOD_REF_PROXY_ALIAS } from '../../../../lib/constants'\nimport {\n  BARREL_OPTIMIZATION_PREFIX,\n  RSC_MODULE_TYPES,\n} from '../../../../shared/lib/constants'\nimport { warnOnce } from '../../../../shared/lib/utils/warn-once'\nimport { getRSCModuleInformation } from '../../../analysis/get-page-static-info'\nimport { formatBarrelOptimizedResource } from '../../utils'\nimport { getModuleBuildInfo } from '../get-module-build-info'\n\ntype SourceType = javascript.JavascriptParser['sourceType'] | 'commonjs'\n\nconst noopHeadPath = require.resolve('next/dist/client/components/noop-head')\n// For edge runtime it will be aliased to esm version by webpack\nconst MODULE_PROXY_PATH =\n  'next/dist/build/webpack/loaders/next-flight-loader/module-proxy'\n\nexport function getAssumedSourceType(\n  mod: webpack.Module,\n  sourceType: SourceType\n): SourceType {\n  const buildInfo = getModuleBuildInfo(mod)\n  const detectedClientEntryType = buildInfo?.rsc?.clientEntryType\n  const clientRefs = buildInfo?.rsc?.clientRefs || []\n\n  // It's tricky to detect the type of a client boundary, but we should always\n  // use the `module` type when we can, to support `export *` and `export from`\n  // syntax in other modules that import this client boundary.\n\n  if (sourceType === 'auto') {\n    if (detectedClientEntryType === 'auto') {\n      if (clientRefs.length === 0) {\n        // If there's zero export detected in the client boundary, and it's the\n        // `auto` type, we can safely assume it's a CJS module because it doesn't\n        // have ESM exports.\n        return 'commonjs'\n      } else if (!clientRefs.includes('*')) {\n        // Otherwise, we assume it's an ESM module.\n        return 'module'\n      }\n    } else if (detectedClientEntryType === 'cjs') {\n      return 'commonjs'\n    }\n  }\n\n  return sourceType\n}\n\nexport default function transformSource(\n  this: LoaderContext<undefined>,\n  source: string,\n  sourceMap: any\n) {\n  // Avoid buffer to be consumed\n  if (typeof source !== 'string') {\n    throw new Error('Expected source to have been transformed to a string.')\n  }\n  const module = this._module!\n\n  // Assign the RSC meta information to buildInfo.\n  // Exclude next internal files which are not marked as client files\n  const buildInfo = getModuleBuildInfo(module)\n  buildInfo.rsc = getRSCModuleInformation(source, true)\n  let prefix = ''\n  if (process.env.BUILTIN_FLIGHT_CLIENT_ENTRY_PLUGIN) {\n    const rscModuleInformationJson = JSON.stringify(buildInfo.rsc)\n    prefix = `/* __rspack_internal_rsc_module_information_do_not_use__ ${rscModuleInformationJson} */\\n`\n    source = prefix + source\n  }\n  prefix += `// This file is generated by the Webpack next-flight-loader.\\n`\n\n  // Resource key is the unique identifier for the resource. When RSC renders\n  // a client module, that key is used to identify that module across all compiler\n  // layers.\n  //\n  // Usually it's the module's file path + the export name (e.g. `foo.js#bar`).\n  // But with Barrel Optimizations, one file can be splitted into multiple modules,\n  // so when you import `foo.js#bar` and `foo.js#baz`, they are actually different\n  // \"foo.js\" being created by the Barrel Loader (one only exports `bar`, the other\n  // only exports `baz`).\n  //\n  // Because of that, we must add another query param to the resource key to\n  // differentiate them.\n  let resourceKey: string = this.resourcePath\n  if (module.matchResource?.startsWith(BARREL_OPTIMIZATION_PREFIX)) {\n    resourceKey = formatBarrelOptimizedResource(\n      resourceKey,\n      module.matchResource\n    )\n  }\n\n  // A client boundary.\n  if (buildInfo.rsc?.type === RSC_MODULE_TYPES.client) {\n    const assumedSourceType = getAssumedSourceType(\n      module,\n      sourceTypeFromModule(module)\n    )\n\n    const clientRefs = buildInfo.rsc.clientRefs!\n    const stringifiedResourceKey = JSON.stringify(resourceKey)\n\n    if (assumedSourceType === 'module') {\n      if (clientRefs.length === 0) {\n        return this.callback(null, 'export {}')\n      }\n\n      if (clientRefs.includes('*')) {\n        this.callback(\n          new Error(\n            `It's currently unsupported to use \"export *\" in a client boundary. Please use named exports instead.`\n          )\n        )\n        return\n      }\n\n      let esmSource =\n        prefix +\n        `\\\nimport { registerClientReference } from \"react-server-dom-webpack/server\";\n`\n      for (const ref of clientRefs) {\n        if (ref === 'default') {\n          esmSource += `export default registerClientReference(\nfunction() { throw new Error(${JSON.stringify(`Attempted to call the default \\\nexport of ${stringifiedResourceKey} from the server, but it's on the client. \\\nIt's not possible to invoke a client function from the server, it can only be \\\nrendered as a Component or passed to props of a Client Component.`)}); },\n${stringifiedResourceKey},\n\"default\",\n);\\n`\n        } else {\n          esmSource += `export const ${ref} = registerClientReference(\nfunction() { throw new Error(${JSON.stringify(`Attempted to call ${ref}() from \\\nthe server but ${ref} is on the client. It's not possible to invoke a client \\\nfunction from the server, it can only be rendered as a Component or passed to \\\nprops of a Client Component.`)}); },\n${stringifiedResourceKey},\n${JSON.stringify(ref)},\n);`\n        }\n      }\n\n      const compilation = this._compilation!\n      const originalSourceURL = process.env.NEXT_RSPACK\n        ? `webpack://_N_E/${this.utils.contextify(this.context || this.rootContext, this.resourcePath)}/__nextjs-internal-proxy.mjs`\n        : ModuleFilenameHelpers.createFilename(\n            module,\n            {\n              moduleFilenameTemplate:\n                'webpack://[namespace]/[resource-path]/__nextjs-internal-proxy.mjs',\n              namespace: '_N_E',\n            },\n            {\n              requestShortener: compilation.requestShortener,\n              chunkGraph: compilation.chunkGraph,\n              hashFunction: compilation.outputOptions.hashFunction,\n            }\n          )\n\n      return this.callback(null, esmSource, {\n        version: 3,\n        sources: [originalSourceURL],\n        // minimal, parseable mappings\n        mappings: 'AAAA',\n        sourcesContent: [esmSource],\n      })\n    } else if (assumedSourceType === 'commonjs') {\n      let cjsSource =\n        prefix +\n        `\\\nconst { createProxy } = require(\"${MODULE_PROXY_PATH}\")\n\nmodule.exports = createProxy(${stringifiedResourceKey})\n`\n\n      const compilation = this._compilation!\n      const originalSourceURL = process.env.NEXT_RSPACK\n        ? `webpack://_N_E/${this.utils.contextify(this.context || this.rootContext, this.resourcePath)}/__nextjs-internal-proxy.cjs`\n        : ModuleFilenameHelpers.createFilename(\n            module,\n            {\n              moduleFilenameTemplate:\n                'webpack://[namespace]/[resource-path]/__nextjs-internal-proxy.cjs',\n              namespace: '_N_E',\n            },\n            {\n              requestShortener: compilation.requestShortener,\n              chunkGraph: compilation.chunkGraph,\n              hashFunction: compilation.outputOptions.hashFunction,\n            }\n          )\n\n      return this.callback(null, cjsSource, {\n        version: 3,\n        sources: [originalSourceURL],\n        // minimal, parseable mappings\n        mappings: 'AAAA',\n        sourcesContent: [cjsSource],\n      })\n    }\n  }\n\n  if (buildInfo.rsc?.type !== RSC_MODULE_TYPES.client) {\n    if (noopHeadPath === this.resourcePath) {\n      warnOnce(\n        `Warning: You're using \\`next/head\\` inside the \\`app\\` directory, please migrate to the Metadata API. See https://nextjs.org/docs/app/building-your-application/upgrading/app-router-migration#step-3-migrating-nexthead for more details.`\n      )\n    }\n  }\n\n  const replacedSource = source.replace(\n    RSC_MOD_REF_PROXY_ALIAS,\n    MODULE_PROXY_PATH\n  )\n  this.callback(null, replacedSource, sourceMap)\n}\n\nfunction sourceTypeFromModule(module: NormalModule): SourceType {\n  const moduleType = module.type\n  switch (moduleType) {\n    case 'javascript/auto':\n      return 'auto'\n    case 'javascript/dynamic':\n      return 'script'\n    case 'javascript/esm':\n      return 'module'\n    default:\n      throw new Error('Unexpected module type ' + moduleType)\n  }\n}\n"], "names": ["transformSource", "getAssumedSourceType", "noopHeadPath", "require", "resolve", "MODULE_PROXY_PATH", "mod", "sourceType", "buildInfo", "getModuleBuildInfo", "detectedClientEntryType", "rsc", "clientEntryType", "clientRefs", "length", "includes", "source", "sourceMap", "module", "Error", "_module", "getRSCModuleInformation", "prefix", "process", "env", "BUILTIN_FLIGHT_CLIENT_ENTRY_PLUGIN", "rscModuleInformationJson", "JSON", "stringify", "resourceKey", "resourcePath", "matchResource", "startsWith", "BARREL_OPTIMIZATION_PREFIX", "formatBarrelOptimizedResource", "type", "RSC_MODULE_TYPES", "client", "assumedSourceType", "sourceTypeFromModule", "stringifiedResourceKey", "callback", "esmSource", "ref", "compilation", "_compilation", "originalSourceURL", "NEXT_RSPACK", "utils", "contextify", "context", "rootContext", "ModuleFilenameHelpers", "createFilename", "moduleFilenameTemplate", "namespace", "requestShortener", "chunkGraph", "hashFunction", "outputOptions", "version", "sources", "mappings", "sourcesContent", "cjsSource", "warnOnce", "replacedSource", "replace", "RSC_MOD_REF_PROXY_ALIAS", "moduleType"], "mappings": ";;;;;;;;;;;;;;;IAuDA,OAuKC;eAvKuBA;;IA/BRC,oBAAoB;eAApBA;;;yBAlBT;2BACiC;4BAIjC;0BACkB;mCACe;uBACM;oCACX;AAInC,MAAMC,eAAeC,QAAQC,OAAO,CAAC;AACrC,gEAAgE;AAChE,MAAMC,oBACJ;AAEK,SAASJ,qBACdK,GAAmB,EACnBC,UAAsB;QAGUC,gBACbA;IAFnB,MAAMA,YAAYC,IAAAA,sCAAkB,EAACH;IACrC,MAAMI,0BAA0BF,8BAAAA,iBAAAA,UAAWG,GAAG,qBAAdH,eAAgBI,eAAe;IAC/D,MAAMC,aAAaL,CAAAA,8BAAAA,kBAAAA,UAAWG,GAAG,qBAAdH,gBAAgBK,UAAU,KAAI,EAAE;IAEnD,4EAA4E;IAC5E,6EAA6E;IAC7E,4DAA4D;IAE5D,IAAIN,eAAe,QAAQ;QACzB,IAAIG,4BAA4B,QAAQ;YACtC,IAAIG,WAAWC,MAAM,KAAK,GAAG;gBAC3B,uEAAuE;gBACvE,yEAAyE;gBACzE,oBAAoB;gBACpB,OAAO;YACT,OAAO,IAAI,CAACD,WAAWE,QAAQ,CAAC,MAAM;gBACpC,2CAA2C;gBAC3C,OAAO;YACT;QACF,OAAO,IAAIL,4BAA4B,OAAO;YAC5C,OAAO;QACT;IACF;IAEA,OAAOH;AACT;AAEe,SAASP,gBAEtBgB,MAAc,EACdC,SAAc;QAiCVC,uBAQAV,gBA8GAA;IArJJ,8BAA8B;IAC9B,IAAI,OAAOQ,WAAW,UAAU;QAC9B,MAAM,qBAAkE,CAAlE,IAAIG,MAAM,0DAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAiE;IACzE;IACA,MAAMD,UAAS,IAAI,CAACE,OAAO;IAE3B,gDAAgD;IAChD,mEAAmE;IACnE,MAAMZ,YAAYC,IAAAA,sCAAkB,EAACS;IACrCV,UAAUG,GAAG,GAAGU,IAAAA,0CAAuB,EAACL,QAAQ;IAChD,IAAIM,SAAS;IACb,IAAIC,QAAQC,GAAG,CAACC,kCAAkC,EAAE;QAClD,MAAMC,2BAA2BC,KAAKC,SAAS,CAACpB,UAAUG,GAAG;QAC7DW,SAAS,CAAC,yDAAyD,EAAEI,yBAAyB,KAAK,CAAC;QACpGV,SAASM,SAASN;IACpB;IACAM,UAAU,CAAC,8DAA8D,CAAC;IAE1E,2EAA2E;IAC3E,gFAAgF;IAChF,UAAU;IACV,EAAE;IACF,6EAA6E;IAC7E,iFAAiF;IACjF,gFAAgF;IAChF,iFAAiF;IACjF,uBAAuB;IACvB,EAAE;IACF,0EAA0E;IAC1E,sBAAsB;IACtB,IAAIO,cAAsB,IAAI,CAACC,YAAY;IAC3C,KAAIZ,wBAAAA,QAAOa,aAAa,qBAApBb,sBAAsBc,UAAU,CAACC,sCAA0B,GAAG;QAChEJ,cAAcK,IAAAA,oCAA6B,EACzCL,aACAX,QAAOa,aAAa;IAExB;IAEA,qBAAqB;IACrB,IAAIvB,EAAAA,iBAAAA,UAAUG,GAAG,qBAAbH,eAAe2B,IAAI,MAAKC,4BAAgB,CAACC,MAAM,EAAE;QACnD,MAAMC,oBAAoBrC,qBACxBiB,SACAqB,qBAAqBrB;QAGvB,MAAML,aAAaL,UAAUG,GAAG,CAACE,UAAU;QAC3C,MAAM2B,yBAAyBb,KAAKC,SAAS,CAACC;QAE9C,IAAIS,sBAAsB,UAAU;YAClC,IAAIzB,WAAWC,MAAM,KAAK,GAAG;gBAC3B,OAAO,IAAI,CAAC2B,QAAQ,CAAC,MAAM;YAC7B;YAEA,IAAI5B,WAAWE,QAAQ,CAAC,MAAM;gBAC5B,IAAI,CAAC0B,QAAQ,CACX,qBAEC,CAFD,IAAItB,MACF,CAAC,oGAAoG,CAAC,GADxG,qBAAA;2BAAA;gCAAA;kCAAA;gBAEA;gBAEF;YACF;YAEA,IAAIuB,YACFpB,SACA,CAAC;;AAET,CAAC;YACK,KAAK,MAAMqB,OAAO9B,WAAY;gBAC5B,IAAI8B,QAAQ,WAAW;oBACrBD,aAAa,CAAC;6BACK,EAAEf,KAAKC,SAAS,CAAC,CAAC;UACrC,EAAEY,uBAAuB;;iEAE8B,CAAC,EAAE;AACpE,EAAEA,uBAAuB;;IAErB,CAAC;gBACG,OAAO;oBACLE,aAAa,CAAC,aAAa,EAAEC,IAAI;6BACd,EAAEhB,KAAKC,SAAS,CAAC,CAAC,kBAAkB,EAAEe,IAAI;eACxD,EAAEA,IAAI;;4BAEO,CAAC,EAAE;AAC/B,EAAEH,uBAAuB;AACzB,EAAEb,KAAKC,SAAS,CAACe,KAAK;EACpB,CAAC;gBACK;YACF;YAEA,MAAMC,cAAc,IAAI,CAACC,YAAY;YACrC,MAAMC,oBAAoBvB,QAAQC,GAAG,CAACuB,WAAW,GAC7C,CAAC,eAAe,EAAE,IAAI,CAACC,KAAK,CAACC,UAAU,CAAC,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,WAAW,EAAE,IAAI,CAACrB,YAAY,EAAE,4BAA4B,CAAC,GAC1HsB,8BAAqB,CAACC,cAAc,CAClCnC,SACA;gBACEoC,wBACE;gBACFC,WAAW;YACb,GACA;gBACEC,kBAAkBZ,YAAYY,gBAAgB;gBAC9CC,YAAYb,YAAYa,UAAU;gBAClCC,cAAcd,YAAYe,aAAa,CAACD,YAAY;YACtD;YAGN,OAAO,IAAI,CAACjB,QAAQ,CAAC,MAAMC,WAAW;gBACpCkB,SAAS;gBACTC,SAAS;oBAACf;iBAAkB;gBAC5B,8BAA8B;gBAC9BgB,UAAU;gBACVC,gBAAgB;oBAACrB;iBAAU;YAC7B;QACF,OAAO,IAAIJ,sBAAsB,YAAY;YAC3C,IAAI0B,YACF1C,SACA,CAAC;iCACwB,EAAEjB,kBAAkB;;6BAExB,EAAEmC,uBAAuB;AACtD,CAAC;YAEK,MAAMI,cAAc,IAAI,CAACC,YAAY;YACrC,MAAMC,oBAAoBvB,QAAQC,GAAG,CAACuB,WAAW,GAC7C,CAAC,eAAe,EAAE,IAAI,CAACC,KAAK,CAACC,UAAU,CAAC,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,WAAW,EAAE,IAAI,CAACrB,YAAY,EAAE,4BAA4B,CAAC,GAC1HsB,8BAAqB,CAACC,cAAc,CAClCnC,SACA;gBACEoC,wBACE;gBACFC,WAAW;YACb,GACA;gBACEC,kBAAkBZ,YAAYY,gBAAgB;gBAC9CC,YAAYb,YAAYa,UAAU;gBAClCC,cAAcd,YAAYe,aAAa,CAACD,YAAY;YACtD;YAGN,OAAO,IAAI,CAACjB,QAAQ,CAAC,MAAMuB,WAAW;gBACpCJ,SAAS;gBACTC,SAAS;oBAACf;iBAAkB;gBAC5B,8BAA8B;gBAC9BgB,UAAU;gBACVC,gBAAgB;oBAACC;iBAAU;YAC7B;QACF;IACF;IAEA,IAAIxD,EAAAA,kBAAAA,UAAUG,GAAG,qBAAbH,gBAAe2B,IAAI,MAAKC,4BAAgB,CAACC,MAAM,EAAE;QACnD,IAAInC,iBAAiB,IAAI,CAAC4B,YAAY,EAAE;YACtCmC,IAAAA,kBAAQ,EACN,CAAC,0OAA0O,CAAC;QAEhP;IACF;IAEA,MAAMC,iBAAiBlD,OAAOmD,OAAO,CACnCC,kCAAuB,EACvB/D;IAEF,IAAI,CAACoC,QAAQ,CAAC,MAAMyB,gBAAgBjD;AACtC;AAEA,SAASsB,qBAAqBrB,OAAoB;IAChD,MAAMmD,aAAanD,QAAOiB,IAAI;IAC9B,OAAQkC;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,MAAM,qBAAiD,CAAjD,IAAIlD,MAAM,4BAA4BkD,aAAtC,qBAAA;uBAAA;4BAAA;8BAAA;YAAgD;IAC1D;AACF", "ignoreList": [0]}