{"version": 3, "sources": ["../../../src/server/dev/middleware-webpack.ts"], "sourcesContent": ["import { findSourceMap, type SourceMap } from 'module'\nimport path from 'path'\nimport { fileURLToPath, pathToFileURL } from 'url'\nimport { SourceMapConsumer } from 'next/dist/compiled/source-map08'\nimport { getSourceMapFromFile } from './get-source-map-from-file'\nimport {\n  devirtualizeReactServerURL,\n  findApplicableSourceMapPayload,\n  sourceMapIgnoreListsEverything,\n  type BasicSourceMapPayload,\n  type ModernSourceMapPayload,\n} from '../lib/source-maps'\nimport { openFileInEditor } from '../../next-devtools/server/launch-editor'\nimport {\n  getOriginalCodeFrame,\n  ignoreListAnonymousStackFramesIfSandwiched,\n  type StackFrame,\n  type IgnorableStackFrame,\n  type OriginalStackFrameResponse,\n  type OriginalStackFramesRequest,\n  type OriginalStackFramesResponse,\n} from '../../next-devtools/server/shared'\nimport { middlewareResponse } from '../../next-devtools/server/middleware-response'\n\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type webpack from 'webpack'\nimport type {\n  NullableMappedPosition,\n  RawSourceMap,\n} from 'next/dist/compiled/source-map08'\nimport { formatFrameSourceFile } from '../../next-devtools/shared/webpack-module-path'\nimport type { MappedPosition } from 'source-map'\nimport { inspect } from 'util'\n\nfunction shouldIgnoreSource(sourceURL: string): boolean {\n  return (\n    sourceURL.includes('node_modules') ||\n    // Only relevant for when Next.js is symlinked e.g. in the Next.js monorepo\n    sourceURL.includes('next/dist') ||\n    sourceURL.startsWith('node:')\n  )\n}\n\ntype IgnoredSources = Array<{ url: string; ignored: boolean }>\n\ntype SourceAttributes = {\n  sourcePosition: NullableMappedPosition\n  sourceContent: string | null\n}\n\ntype Source =\n  | {\n      type: 'file'\n      sourceMap: BasicSourceMapPayload\n      ignoredSources: IgnoredSources\n      moduleURL: string\n    }\n  | {\n      type: 'bundle'\n      sourceMap: BasicSourceMapPayload\n      ignoredSources: IgnoredSources\n      compilation: webpack.Compilation\n      moduleId: string\n      moduleURL: string\n    }\n\nfunction getModuleById(\n  id: string | undefined,\n  compilation: webpack.Compilation\n) {\n  const { chunkGraph, modules } = compilation\n\n  return [...modules].find((module) => chunkGraph.getModuleId(module) === id)\n}\n\nfunction findModuleNotFoundFromError(errorMessage: string | undefined) {\n  return errorMessage?.match(/'([^']+)' module/)?.[1]\n}\n\nfunction getSourcePath(source: string) {\n  if (source.startsWith('file://')) {\n    return fileURLToPath(source)\n  }\n  return source.replace(/^(webpack:\\/\\/\\/|webpack:\\/\\/|webpack:\\/\\/_N_E\\/)/, '')\n}\n\n/**\n * @returns 1-based lines and 0-based columns\n */\nasync function findOriginalSourcePositionAndContent(\n  sourceMap: ModernSourceMapPayload,\n  position: { line1: number | null; column1: number | null }\n): Promise<SourceAttributes | null> {\n  let consumer: SourceMapConsumer\n  try {\n    consumer = await new SourceMapConsumer(sourceMap)\n  } catch (cause) {\n    console.error(\n      new Error(\n        `${sourceMap.file}: Invalid source map. Only conformant source maps can be used to find the original code.`,\n        { cause }\n      )\n    )\n    return null\n  }\n\n  try {\n    const sourcePosition = consumer.originalPositionFor({\n      line: position.line1 ?? 1,\n      // 0-based columns out requires 0-based columns in.\n      column: (position.column1 ?? 1) - 1,\n    })\n\n    if (!sourcePosition.source) {\n      return null\n    }\n\n    const sourceContent: string | null =\n      consumer.sourceContentFor(\n        sourcePosition.source,\n        /* returnNullOnMissing */ true\n      ) ?? null\n\n    return {\n      sourcePosition,\n      sourceContent,\n    }\n  } finally {\n    consumer.destroy()\n  }\n}\n\nexport function getIgnoredSources(\n  sourceMap: RawSourceMap & { ignoreList?: number[] }\n): IgnoredSources {\n  const ignoreList = new Set<number>(sourceMap.ignoreList ?? [])\n  const moduleFilenames = sourceMap?.sources ?? []\n\n  for (let index = 0; index < moduleFilenames.length; index++) {\n    // bundlerFilePath case: webpack://./app/page.tsx\n    const webpackSourceURL = moduleFilenames[index]\n    // Format the path to the normal file path\n    const formattedFilePath = formatFrameSourceFile(webpackSourceURL)\n    if (shouldIgnoreSource(formattedFilePath)) {\n      ignoreList.add(index)\n    }\n  }\n\n  const ignoredSources = sourceMap.sources.map((source, index) => {\n    return {\n      url: source,\n      ignored: ignoreList.has(sourceMap.sources.indexOf(source)),\n      content: sourceMap.sourcesContent?.[index] ?? null,\n    }\n  })\n  return ignoredSources\n}\n\nfunction isIgnoredSource(\n  source: Source,\n  sourcePosition: MappedPosition | NullableMappedPosition\n) {\n  if (sourcePosition.source == null) {\n    return true\n  }\n  for (const ignoredSource of source.ignoredSources) {\n    if (ignoredSource.ignored && ignoredSource.url === sourcePosition.source) {\n      return true\n    }\n  }\n\n  return false\n}\n\nfunction findOriginalSourcePositionAndContentFromCompilation(\n  moduleId: string | undefined,\n  importedModule: string,\n  compilation: webpack.Compilation\n): SourceAttributes | null {\n  const module = getModuleById(moduleId, compilation)\n  return module?.buildInfo?.importLocByPath?.get(importedModule) ?? null\n}\n\nexport async function createOriginalStackFrame({\n  ignoredByDefault,\n  source,\n  rootDirectory,\n  frame,\n  errorMessage,\n}: {\n  /** setting this to true will not consult ignoreList */\n  ignoredByDefault: boolean\n  source: Source\n  rootDirectory: string\n  frame: StackFrame\n  errorMessage?: string\n}): Promise<OriginalStackFrameResponse | null> {\n  const moduleNotFound = findModuleNotFoundFromError(errorMessage)\n  const result = await (() => {\n    if (moduleNotFound) {\n      if (source.type === 'file') {\n        return undefined\n      }\n\n      return findOriginalSourcePositionAndContentFromCompilation(\n        source.moduleId,\n        moduleNotFound,\n        source.compilation\n      )\n    }\n    return findOriginalSourcePositionAndContent(source.sourceMap, frame)\n  })()\n\n  if (!result) {\n    return null\n  }\n  const { sourcePosition, sourceContent } = result\n\n  if (!sourcePosition.source) {\n    return null\n  }\n\n  const ignored =\n    ignoredByDefault ||\n    isIgnoredSource(source, sourcePosition) ||\n    // If the source file is externals, should be excluded even it's not ignored source.\n    // e.g. webpack://next/dist/.. needs to be ignored\n    shouldIgnoreSource(source.moduleURL)\n\n  const sourcePath = getSourcePath(\n    // When sourcePosition.source is the loader path the modulePath is generally better.\n    (sourcePosition.source!.includes('|')\n      ? source.moduleURL\n      : sourcePosition.source) || source.moduleURL\n  )\n  const filePath = path.resolve(rootDirectory, sourcePath)\n  const resolvedFilePath = path.relative(rootDirectory, filePath)\n\n  const traced: IgnorableStackFrame = {\n    file: resolvedFilePath,\n    line1: sourcePosition.line,\n    column1: sourcePosition.column === null ? null : sourcePosition.column + 1,\n    methodName:\n      // We ignore the sourcemapped name since it won't be the correct name.\n      // The callsite will point to the column of the variable name instead of the\n      // name of the enclosing function.\n      // TODO(NDX-531): Spy on prepareStackTrace to get the enclosing line number for method name mapping.\n      // default is not a valid identifier in JS so webpack uses a custom variable when it's an unnamed default export\n      // Resolve it back to `default` for the method name if the source position didn't have the method.\n      frame.methodName\n        ?.replace('__WEBPACK_DEFAULT_EXPORT__', 'default')\n        ?.replace('__webpack_exports__.', ''),\n    arguments: [],\n    ignored,\n  }\n\n  return {\n    originalStackFrame: traced,\n    originalCodeFrame: getOriginalCodeFrame(traced, sourceContent),\n  }\n}\n\nasync function getSourceMapFromCompilation(\n  id: string,\n  compilation: webpack.Compilation\n): Promise<RawSourceMap | undefined> {\n  try {\n    const module = getModuleById(id, compilation)\n\n    if (!module) {\n      return undefined\n    }\n\n    // @ts-expect-error The types for `CodeGenerationResults.get` require a\n    // runtime to be passed as second argument, but apparently it also works\n    // without it.\n    const codeGenerationResult = compilation.codeGenerationResults.get(module)\n    const source = codeGenerationResult?.sources.get('javascript')\n\n    return source?.map() ?? undefined\n  } catch (err) {\n    console.error(`Failed to lookup module by ID (\"${id}\"):`, err)\n    return undefined\n  }\n}\n\nasync function getSource(\n  frame: {\n    file: string | null\n    line1: number | null\n    column1: number | null\n  },\n  options: {\n    getCompilations: () => webpack.Compilation[]\n  }\n): Promise<Source | undefined> {\n  let sourceURL = frame.file ?? ''\n  const { getCompilations } = options\n\n  sourceURL = devirtualizeReactServerURL(sourceURL)\n\n  let nativeSourceMap: SourceMap | undefined\n  try {\n    nativeSourceMap = findSourceMap(sourceURL)\n  } catch (cause) {\n    throw new Error(\n      `${sourceURL}: Invalid source map. Only conformant source maps can be used to find the original code.`,\n      { cause }\n    )\n  }\n\n  if (nativeSourceMap !== undefined) {\n    const sourceMapPayload = nativeSourceMap.payload\n    return {\n      type: 'file',\n      sourceMap: findApplicableSourceMapPayload(\n        (frame.line1 ?? 1) - 1,\n        (frame.column1 ?? 1) - 1,\n        sourceMapPayload\n      )!,\n\n      ignoredSources: getIgnoredSources(\n        // @ts-expect-error -- TODO: Support IndexSourceMap\n        sourceMapPayload\n      ),\n      moduleURL: sourceURL,\n    }\n  }\n\n  if (path.isAbsolute(sourceURL)) {\n    sourceURL = pathToFileURL(sourceURL).href\n  }\n\n  if (sourceURL.startsWith('file:')) {\n    const sourceMap = await getSourceMapFromFile(sourceURL)\n    return sourceMap\n      ? {\n          type: 'file',\n          sourceMap,\n          ignoredSources: getIgnoredSources(sourceMap),\n          moduleURL: sourceURL,\n        }\n      : undefined\n  }\n\n  // webpack-internal:///./src/hello.tsx => ./src/hello.tsx\n  // webpack://_N_E/./src/hello.tsx => ./src/hello.tsx\n  const moduleId = sourceURL\n    .replace(/^(webpack-internal:\\/\\/\\/|webpack:\\/\\/(_N_E\\/)?)/, '')\n    .replace(/\\?\\d+$/, '')\n\n  // (rsc)/./src/hello.tsx => ./src/hello.tsx\n  const moduleURL = moduleId.replace(/^(\\(.*\\)\\/?)/, '')\n\n  for (const compilation of getCompilations()) {\n    const sourceMap = await getSourceMapFromCompilation(moduleId, compilation)\n\n    if (sourceMap) {\n      const ignoredSources = getIgnoredSources(sourceMap)\n      return {\n        type: 'bundle',\n        sourceMap,\n        compilation,\n        moduleId,\n        moduleURL,\n        ignoredSources,\n      }\n    }\n  }\n\n  return undefined\n}\n\nexport async function getOriginalStackFrames({\n  isServer,\n  isEdgeServer,\n  isAppDirectory,\n  frames,\n  clientStats,\n  serverStats,\n  edgeServerStats,\n  rootDirectory,\n}: {\n  isServer: boolean\n  isEdgeServer: boolean\n  isAppDirectory: boolean\n  frames: readonly StackFrame[]\n  clientStats: () => webpack.Stats | null\n  serverStats: () => webpack.Stats | null\n  edgeServerStats: () => webpack.Stats | null\n  rootDirectory: string\n}): Promise<OriginalStackFramesResponse> {\n  const frameResponses = await Promise.all(\n    frames.map(\n      (frame): Promise<OriginalStackFramesResponse[number]> =>\n        getOriginalStackFrame({\n          isServer,\n          isEdgeServer,\n          isAppDirectory,\n          frame,\n          clientStats,\n          serverStats,\n          edgeServerStats,\n          rootDirectory,\n        }).then(\n          (value) => {\n            return {\n              status: 'fulfilled',\n              value,\n            }\n          },\n          (reason) => {\n            return {\n              status: 'rejected',\n              reason: inspect(reason, { colors: false }),\n            }\n          }\n        )\n    )\n  )\n\n  ignoreListAnonymousStackFramesIfSandwiched(frameResponses)\n\n  return frameResponses\n}\n\nasync function getOriginalStackFrame({\n  isServer,\n  isEdgeServer,\n  isAppDirectory,\n  frame,\n  clientStats,\n  serverStats,\n  edgeServerStats,\n  rootDirectory,\n}: {\n  isServer: boolean\n  isEdgeServer: boolean\n  isAppDirectory: boolean\n  frame: StackFrame\n  clientStats: () => webpack.Stats | null\n  serverStats: () => webpack.Stats | null\n  edgeServerStats: () => webpack.Stats | null\n  rootDirectory: string\n}): Promise<OriginalStackFrameResponse> {\n  const filename = frame.file ?? ''\n  const source = await getSource(frame, {\n    getCompilations: () => {\n      const compilations: webpack.Compilation[] = []\n\n      // Try Client Compilation first. In `pages` we leverage\n      // `isClientError` to check. In `app` it depends on if it's a server\n      // / client component and when the code throws. E.g. during HTML\n      // rendering it's the server/edge compilation.\n      if ((!isEdgeServer && !isServer) || isAppDirectory) {\n        const compilation = clientStats()?.compilation\n\n        if (compilation) {\n          compilations.push(compilation)\n        }\n      }\n\n      // Try Server Compilation. In `pages` this could be something\n      // imported in getServerSideProps/getStaticProps as the code for\n      // those is tree-shaken. In `app` this finds server components and\n      // code that was imported from a server component. It also covers\n      // when client component code throws during HTML rendering.\n      if (isServer || isAppDirectory) {\n        const compilation = serverStats()?.compilation\n\n        if (compilation) {\n          compilations.push(compilation)\n        }\n      }\n\n      // Try Edge Server Compilation. Both cases are the same as Server\n      // Compilation, main difference is that it covers `runtime: 'edge'`\n      // pages/app routes.\n      if (isEdgeServer || isAppDirectory) {\n        const compilation = edgeServerStats()?.compilation\n\n        if (compilation) {\n          compilations.push(compilation)\n        }\n      }\n\n      return compilations\n    },\n  })\n\n  let defaultNormalizedStackFrameLocation = frame.file\n  if (\n    defaultNormalizedStackFrameLocation !== null &&\n    defaultNormalizedStackFrameLocation.startsWith('file://')\n  ) {\n    defaultNormalizedStackFrameLocation = path.relative(\n      rootDirectory,\n      fileURLToPath(defaultNormalizedStackFrameLocation)\n    )\n  }\n  // This stack frame is used for the one that couldn't locate the source or source mapped frame\n  const defaultStackFrame: IgnorableStackFrame = {\n    file: defaultNormalizedStackFrameLocation,\n    line1: frame.line1,\n    column1: frame.column1,\n    methodName: frame.methodName,\n    ignored: shouldIgnoreSource(filename),\n    arguments: [],\n  }\n  if (!source) {\n    // return original stack frame with no source map\n    return {\n      originalStackFrame: defaultStackFrame,\n      originalCodeFrame: null,\n    }\n  }\n  defaultStackFrame.ignored ||= sourceMapIgnoreListsEverything(source.sourceMap)\n\n  const originalStackFrameResponse = await createOriginalStackFrame({\n    ignoredByDefault: defaultStackFrame.ignored,\n    frame,\n    source,\n    rootDirectory,\n  })\n\n  if (!originalStackFrameResponse) {\n    return {\n      originalStackFrame: defaultStackFrame,\n      originalCodeFrame: null,\n    }\n  }\n\n  return originalStackFrameResponse\n}\n\nexport function getOverlayMiddleware(options: {\n  rootDirectory: string\n  isSrcDir: boolean\n  clientStats: () => webpack.Stats | null\n  serverStats: () => webpack.Stats | null\n  edgeServerStats: () => webpack.Stats | null\n}) {\n  const { rootDirectory, isSrcDir, clientStats, serverStats, edgeServerStats } =\n    options\n\n  return async function (\n    req: IncomingMessage,\n    res: ServerResponse,\n    next: () => void\n  ): Promise<void> {\n    const { pathname, searchParams } = new URL(`http://n${req.url}`)\n\n    if (pathname === '/__nextjs_original-stack-frames') {\n      if (req.method !== 'POST') {\n        return middlewareResponse.badRequest(res)\n      }\n\n      const body = await new Promise<string>((resolve, reject) => {\n        let data = ''\n        req.on('data', (chunk) => {\n          data += chunk\n        })\n        req.on('end', () => resolve(data))\n        req.on('error', reject)\n      })\n\n      try {\n        const { frames, isServer, isEdgeServer, isAppDirectory } = JSON.parse(\n          body\n        ) as OriginalStackFramesRequest\n\n        return middlewareResponse.json(\n          res,\n          await getOriginalStackFrames({\n            isServer,\n            isEdgeServer,\n            isAppDirectory,\n            frames,\n            clientStats,\n            serverStats,\n            edgeServerStats,\n            rootDirectory,\n          })\n        )\n      } catch (err) {\n        return middlewareResponse.badRequest(res)\n      }\n    } else if (pathname === '/__nextjs_launch-editor') {\n      const frame = {\n        file: searchParams.get('file') as string,\n        methodName: searchParams.get('methodName') as string,\n        line1: parseInt(searchParams.get('line1') ?? '1', 10) || 1,\n        column1: parseInt(searchParams.get('column1') ?? '1', 10) || 1,\n        arguments: searchParams.getAll('arguments').filter(Boolean),\n      } satisfies StackFrame\n\n      if (!frame.file) return middlewareResponse.badRequest(res)\n\n      let openEditorResult\n      const isAppRelativePath = searchParams.get('isAppRelativePath') === '1'\n      if (isAppRelativePath) {\n        const relativeFilePath = searchParams.get('file') || ''\n        const appPath = path.join(\n          'app',\n          isSrcDir ? 'src' : '',\n          relativeFilePath\n        )\n        openEditorResult = await openFileInEditor(appPath, 1, 1, rootDirectory)\n      } else {\n        // TODO: How do we differentiate layers and actual file paths with round brackets?\n        // frame files may start with their webpack layer, like (middleware)/middleware.js\n        const filePath = frame.file.replace(/^\\([^)]+\\)\\//, '')\n        openEditorResult = await openFileInEditor(\n          filePath,\n          frame.line1,\n          frame.column1 ?? 1,\n          rootDirectory\n        )\n      }\n      if (openEditorResult.error) {\n        console.error('Failed to launch editor:', openEditorResult.error)\n        return middlewareResponse.internalServerError(\n          res,\n          openEditorResult.error\n        )\n      }\n      if (!openEditorResult.found) {\n        return middlewareResponse.notFound(res)\n      }\n      return middlewareResponse.noContent(res)\n    }\n\n    return next()\n  }\n}\n\nexport function getSourceMapMiddleware(options: {\n  clientStats: () => webpack.Stats | null\n  serverStats: () => webpack.Stats | null\n  edgeServerStats: () => webpack.Stats | null\n}) {\n  const { clientStats, serverStats, edgeServerStats } = options\n\n  return async function (\n    req: IncomingMessage,\n    res: ServerResponse,\n    next: () => void\n  ): Promise<void> {\n    const { pathname, searchParams } = new URL(`http://n${req.url}`)\n\n    if (pathname !== '/__nextjs_source-map') {\n      return next()\n    }\n\n    const filename = searchParams.get('filename')\n\n    if (!filename) {\n      return middlewareResponse.badRequest(res)\n    }\n\n    let source: Source | undefined\n\n    try {\n      source = await getSource(\n        {\n          file: filename,\n          // Webpack doesn't use Index Source Maps\n          line1: null,\n          column1: null,\n        },\n        {\n          getCompilations: () => {\n            const compilations: webpack.Compilation[] = []\n\n            for (const stats of [\n              clientStats(),\n              serverStats(),\n              edgeServerStats(),\n            ]) {\n              if (stats?.compilation) {\n                compilations.push(stats.compilation)\n              }\n            }\n\n            return compilations\n          },\n        }\n      )\n    } catch (error) {\n      return middlewareResponse.internalServerError(res, error)\n    }\n\n    if (!source) {\n      return middlewareResponse.noContent(res)\n    }\n\n    return middlewareResponse.json(res, source.sourceMap)\n  }\n}\n"], "names": ["createOriginalStackFrame", "getIgnoredSources", "getOriginalStackFrames", "getOverlayMiddleware", "getSourceMapMiddleware", "shouldIgnoreSource", "sourceURL", "includes", "startsWith", "getModuleById", "id", "compilation", "chunkGraph", "modules", "find", "module", "getModuleId", "findModuleNotFoundFromError", "errorMessage", "match", "getSourcePath", "source", "fileURLToPath", "replace", "findOriginalSourcePositionAndContent", "sourceMap", "position", "consumer", "SourceMapConsumer", "cause", "console", "error", "Error", "file", "sourcePosition", "originalPositionFor", "line", "line1", "column", "column1", "sourceContent", "sourceContentFor", "destroy", "ignoreList", "Set", "moduleFilenames", "sources", "index", "length", "webpackSourceURL", "formattedFilePath", "formatFrameSourceFile", "add", "ignoredSources", "map", "url", "ignored", "has", "indexOf", "content", "sourcesContent", "isIgnoredSource", "ignoredSource", "findOriginalSourcePositionAndContentFromCompilation", "moduleId", "importedModule", "buildInfo", "importLocByPath", "get", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rootDirectory", "frame", "moduleNotFound", "result", "type", "undefined", "moduleURL", "sourcePath", "filePath", "path", "resolve", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "relative", "traced", "methodName", "arguments", "originalStackFrame", "originalCodeFrame", "getOriginalCodeFrame", "getSourceMapFromCompilation", "codeGenerationResult", "codeGenerationResults", "err", "getSource", "options", "getCompilations", "devirtualizeReactServerURL", "nativeSourceMap", "findSourceMap", "sourceMapPayload", "payload", "findApplicableSourceMapPayload", "isAbsolute", "pathToFileURL", "href", "getSourceMapFromFile", "isServer", "isEdgeServer", "isAppDirectory", "frames", "clientStats", "serverStats", "edgeServerStats", "frameResponses", "Promise", "all", "getOriginalStackFrame", "then", "value", "status", "reason", "inspect", "colors", "ignoreListAnonymousStackFramesIfSandwiched", "filename", "compilations", "push", "defaultNormalizedStackFrameLocation", "defaultStackFrame", "sourceMapIgnoreListsEverything", "originalStackFrameResponse", "isSrcDir", "req", "res", "next", "pathname", "searchParams", "URL", "method", "middlewareResponse", "badRequest", "body", "reject", "data", "on", "chunk", "JSON", "parse", "json", "parseInt", "getAll", "filter", "Boolean", "openEditorResult", "isAppRelativePath", "relativeFilePath", "appPath", "join", "openFileInEditor", "internalServerError", "found", "notFound", "noContent", "stats"], "mappings": ";;;;;;;;;;;;;;;;;;IAuLsBA,wBAAwB;eAAxBA;;IAnDNC,iBAAiB;eAAjBA;;IAiPMC,sBAAsB;eAAtBA;;IAkKNC,oBAAoB;eAApBA;;IAqGAC,sBAAsB;eAAtBA;;;wBA5nB8B;6DAC7B;qBAC4B;6BACX;sCACG;4BAO9B;8BAC0B;wBAS1B;oCAC4B;mCAQG;sBAEd;;;;;;AAExB,SAASC,mBAAmBC,SAAiB;IAC3C,OACEA,UAAUC,QAAQ,CAAC,mBACnB,2EAA2E;IAC3ED,UAAUC,QAAQ,CAAC,gBACnBD,UAAUE,UAAU,CAAC;AAEzB;AAyBA,SAASC,cACPC,EAAsB,EACtBC,WAAgC;IAEhC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAE,GAAGF;IAEhC,OAAO;WAAIE;KAAQ,CAACC,IAAI,CAAC,CAACC,UAAWH,WAAWI,WAAW,CAACD,aAAYL;AAC1E;AAEA,SAASO,4BAA4BC,YAAgC;QAC5DA;IAAP,OAAOA,iCAAAA,sBAAAA,aAAcC,KAAK,CAAC,wCAApBD,mBAAyC,CAAC,EAAE;AACrD;AAEA,SAASE,cAAcC,MAAc;IACnC,IAAIA,OAAOb,UAAU,CAAC,YAAY;QAChC,OAAOc,IAAAA,kBAAa,EAACD;IACvB;IACA,OAAOA,OAAOE,OAAO,CAAC,qDAAqD;AAC7E;AAEA;;CAEC,GACD,eAAeC,qCACbC,SAAiC,EACjCC,QAA0D;IAE1D,IAAIC;IACJ,IAAI;QACFA,WAAW,MAAM,IAAIC,8BAAiB,CAACH;IACzC,EAAE,OAAOI,OAAO;QACdC,QAAQC,KAAK,CACX,qBAGC,CAHD,IAAIC,MACF,GAAGP,UAAUQ,IAAI,CAAC,wFAAwF,CAAC,EAC3G;YAAEJ;QAAM,IAFV,qBAAA;mBAAA;wBAAA;0BAAA;QAGA;QAEF,OAAO;IACT;IAEA,IAAI;QACF,MAAMK,iBAAiBP,SAASQ,mBAAmB,CAAC;YAClDC,MAAMV,SAASW,KAAK,IAAI;YACxB,mDAAmD;YACnDC,QAAQ,AAACZ,CAAAA,SAASa,OAAO,IAAI,CAAA,IAAK;QACpC;QAEA,IAAI,CAACL,eAAeb,MAAM,EAAE;YAC1B,OAAO;QACT;QAEA,MAAMmB,gBACJb,SAASc,gBAAgB,CACvBP,eAAeb,MAAM,EACrB,uBAAuB,GAAG,SACvB;QAEP,OAAO;YACLa;YACAM;QACF;IACF,SAAU;QACRb,SAASe,OAAO;IAClB;AACF;AAEO,SAASzC,kBACdwB,SAAmD;IAEnD,MAAMkB,aAAa,IAAIC,IAAYnB,UAAUkB,UAAU,IAAI,EAAE;IAC7D,MAAME,kBAAkBpB,CAAAA,6BAAAA,UAAWqB,OAAO,KAAI,EAAE;IAEhD,IAAK,IAAIC,QAAQ,GAAGA,QAAQF,gBAAgBG,MAAM,EAAED,QAAS;QAC3D,iDAAiD;QACjD,MAAME,mBAAmBJ,eAAe,CAACE,MAAM;QAC/C,0CAA0C;QAC1C,MAAMG,oBAAoBC,IAAAA,wCAAqB,EAACF;QAChD,IAAI5C,mBAAmB6C,oBAAoB;YACzCP,WAAWS,GAAG,CAACL;QACjB;IACF;IAEA,MAAMM,iBAAiB5B,UAAUqB,OAAO,CAACQ,GAAG,CAAC,CAACjC,QAAQ0B;YAIzCtB;QAHX,OAAO;YACL8B,KAAKlC;YACLmC,SAASb,WAAWc,GAAG,CAAChC,UAAUqB,OAAO,CAACY,OAAO,CAACrC;YAClDsC,SAASlC,EAAAA,4BAAAA,UAAUmC,cAAc,qBAAxBnC,yBAA0B,CAACsB,MAAM,KAAI;QAChD;IACF;IACA,OAAOM;AACT;AAEA,SAASQ,gBACPxC,MAAc,EACda,cAAuD;IAEvD,IAAIA,eAAeb,MAAM,IAAI,MAAM;QACjC,OAAO;IACT;IACA,KAAK,MAAMyC,iBAAiBzC,OAAOgC,cAAc,CAAE;QACjD,IAAIS,cAAcN,OAAO,IAAIM,cAAcP,GAAG,KAAKrB,eAAeb,MAAM,EAAE;YACxE,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA,SAAS0C,oDACPC,QAA4B,EAC5BC,cAAsB,EACtBtD,WAAgC;QAGzBI,mCAAAA;IADP,MAAMA,UAASN,cAAcuD,UAAUrD;IACvC,OAAOI,CAAAA,4BAAAA,oBAAAA,QAAQmD,SAAS,sBAAjBnD,oCAAAA,kBAAmBoD,eAAe,qBAAlCpD,kCAAoCqD,GAAG,CAACH,oBAAmB;AACpE;AAEO,eAAejE,yBAAyB,EAC7CqE,gBAAgB,EAChBhD,MAAM,EACNiD,aAAa,EACbC,KAAK,EACLrD,YAAY,EAQb;QA+CK,sEAAsE;IACtE,4EAA4E;IAC5E,kCAAkC;IAClC,oGAAoG;IACpG,gHAAgH;IAChH,kGAAkG;IAClGqD,2BAAAA;IApDJ,MAAMC,iBAAiBvD,4BAA4BC;IACnD,MAAMuD,SAAS,MAAM,AAAC,CAAA;QACpB,IAAID,gBAAgB;YAClB,IAAInD,OAAOqD,IAAI,KAAK,QAAQ;gBAC1B,OAAOC;YACT;YAEA,OAAOZ,oDACL1C,OAAO2C,QAAQ,EACfQ,gBACAnD,OAAOV,WAAW;QAEtB;QACA,OAAOa,qCAAqCH,OAAOI,SAAS,EAAE8C;IAChE,CAAA;IAEA,IAAI,CAACE,QAAQ;QACX,OAAO;IACT;IACA,MAAM,EAAEvC,cAAc,EAAEM,aAAa,EAAE,GAAGiC;IAE1C,IAAI,CAACvC,eAAeb,MAAM,EAAE;QAC1B,OAAO;IACT;IAEA,MAAMmC,UACJa,oBACAR,gBAAgBxC,QAAQa,mBACxB,oFAAoF;IACpF,kDAAkD;IAClD7B,mBAAmBgB,OAAOuD,SAAS;IAErC,MAAMC,aAAazD,cAEjB,AADA,oFAAoF;IACnFc,CAAAA,eAAeb,MAAM,CAAEd,QAAQ,CAAC,OAC7Bc,OAAOuD,SAAS,GAChB1C,eAAeb,MAAM,AAAD,KAAMA,OAAOuD,SAAS;IAEhD,MAAME,WAAWC,aAAI,CAACC,OAAO,CAACV,eAAeO;IAC7C,MAAMI,mBAAmBF,aAAI,CAACG,QAAQ,CAACZ,eAAeQ;IAEtD,MAAMK,SAA8B;QAClClD,MAAMgD;QACN5C,OAAOH,eAAeE,IAAI;QAC1BG,SAASL,eAAeI,MAAM,KAAK,OAAO,OAAOJ,eAAeI,MAAM,GAAG;QACzE8C,UAAU,GAORb,oBAAAA,MAAMa,UAAU,sBAAhBb,4BAAAA,kBACIhD,OAAO,CAAC,8BAA8B,+BAD1CgD,0BAEIhD,OAAO,CAAC,wBAAwB;QACtC8D,WAAW,EAAE;QACb7B;IACF;IAEA,OAAO;QACL8B,oBAAoBH;QACpBI,mBAAmBC,IAAAA,4BAAoB,EAACL,QAAQ3C;IAClD;AACF;AAEA,eAAeiD,4BACb/E,EAAU,EACVC,WAAgC;IAEhC,IAAI;QACF,MAAMI,UAASN,cAAcC,IAAIC;QAEjC,IAAI,CAACI,SAAQ;YACX,OAAO4D;QACT;QAEA,uEAAuE;QACvE,wEAAwE;QACxE,cAAc;QACd,MAAMe,uBAAuB/E,YAAYgF,qBAAqB,CAACvB,GAAG,CAACrD;QACnE,MAAMM,SAASqE,wCAAAA,qBAAsB5C,OAAO,CAACsB,GAAG,CAAC;QAEjD,OAAO/C,CAAAA,0BAAAA,OAAQiC,GAAG,OAAMqB;IAC1B,EAAE,OAAOiB,KAAK;QACZ9D,QAAQC,KAAK,CAAC,CAAC,gCAAgC,EAAErB,GAAG,GAAG,CAAC,EAAEkF;QAC1D,OAAOjB;IACT;AACF;AAEA,eAAekB,UACbtB,KAIC,EACDuB,OAEC;IAED,IAAIxF,YAAYiE,MAAMtC,IAAI,IAAI;IAC9B,MAAM,EAAE8D,eAAe,EAAE,GAAGD;IAE5BxF,YAAY0F,IAAAA,sCAA0B,EAAC1F;IAEvC,IAAI2F;IACJ,IAAI;QACFA,kBAAkBC,IAAAA,qBAAa,EAAC5F;IAClC,EAAE,OAAOuB,OAAO;QACd,MAAM,qBAGL,CAHK,IAAIG,MACR,GAAG1B,UAAU,wFAAwF,CAAC,EACtG;YAAEuB;QAAM,IAFJ,qBAAA;mBAAA;wBAAA;0BAAA;QAGN;IACF;IAEA,IAAIoE,oBAAoBtB,WAAW;QACjC,MAAMwB,mBAAmBF,gBAAgBG,OAAO;QAChD,OAAO;YACL1B,MAAM;YACNjD,WAAW4E,IAAAA,0CAA8B,EACvC,AAAC9B,CAAAA,MAAMlC,KAAK,IAAI,CAAA,IAAK,GACrB,AAACkC,CAAAA,MAAMhC,OAAO,IAAI,CAAA,IAAK,GACvB4D;YAGF9C,gBAAgBpD,kBACd,mDAAmD;YACnDkG;YAEFvB,WAAWtE;QACb;IACF;IAEA,IAAIyE,aAAI,CAACuB,UAAU,CAAChG,YAAY;QAC9BA,YAAYiG,IAAAA,kBAAa,EAACjG,WAAWkG,IAAI;IAC3C;IAEA,IAAIlG,UAAUE,UAAU,CAAC,UAAU;QACjC,MAAMiB,YAAY,MAAMgF,IAAAA,0CAAoB,EAACnG;QAC7C,OAAOmB,YACH;YACEiD,MAAM;YACNjD;YACA4B,gBAAgBpD,kBAAkBwB;YAClCmD,WAAWtE;QACb,IACAqE;IACN;IAEA,yDAAyD;IACzD,oDAAoD;IACpD,MAAMX,WAAW1D,UACdiB,OAAO,CAAC,oDAAoD,IAC5DA,OAAO,CAAC,UAAU;IAErB,2CAA2C;IAC3C,MAAMqD,YAAYZ,SAASzC,OAAO,CAAC,gBAAgB;IAEnD,KAAK,MAAMZ,eAAeoF,kBAAmB;QAC3C,MAAMtE,YAAY,MAAMgE,4BAA4BzB,UAAUrD;QAE9D,IAAIc,WAAW;YACb,MAAM4B,iBAAiBpD,kBAAkBwB;YACzC,OAAO;gBACLiD,MAAM;gBACNjD;gBACAd;gBACAqD;gBACAY;gBACAvB;YACF;QACF;IACF;IAEA,OAAOsB;AACT;AAEO,eAAezE,uBAAuB,EAC3CwG,QAAQ,EACRC,YAAY,EACZC,cAAc,EACdC,MAAM,EACNC,WAAW,EACXC,WAAW,EACXC,eAAe,EACf1C,aAAa,EAUd;IACC,MAAM2C,iBAAiB,MAAMC,QAAQC,GAAG,CACtCN,OAAOvD,GAAG,CACR,CAACiB,QACC6C,sBAAsB;YACpBV;YACAC;YACAC;YACArC;YACAuC;YACAC;YACAC;YACA1C;QACF,GAAG+C,IAAI,CACL,CAACC;YACC,OAAO;gBACLC,QAAQ;gBACRD;YACF;QACF,GACA,CAACE;YACC,OAAO;gBACLD,QAAQ;gBACRC,QAAQC,IAAAA,aAAO,EAACD,QAAQ;oBAAEE,QAAQ;gBAAM;YAC1C;QACF;IAKRC,IAAAA,kDAA0C,EAACV;IAE3C,OAAOA;AACT;AAEA,eAAeG,sBAAsB,EACnCV,QAAQ,EACRC,YAAY,EACZC,cAAc,EACdrC,KAAK,EACLuC,WAAW,EACXC,WAAW,EACXC,eAAe,EACf1C,aAAa,EAUd;IACC,MAAMsD,WAAWrD,MAAMtC,IAAI,IAAI;IAC/B,MAAMZ,SAAS,MAAMwE,UAAUtB,OAAO;QACpCwB,iBAAiB;YACf,MAAM8B,eAAsC,EAAE;YAE9C,uDAAuD;YACvD,oEAAoE;YACpE,gEAAgE;YAChE,8CAA8C;YAC9C,IAAI,AAAC,CAAClB,gBAAgB,CAACD,YAAaE,gBAAgB;oBAC9BE;gBAApB,MAAMnG,eAAcmG,eAAAA,kCAAAA,aAAenG,WAAW;gBAE9C,IAAIA,aAAa;oBACfkH,aAAaC,IAAI,CAACnH;gBACpB;YACF;YAEA,6DAA6D;YAC7D,gEAAgE;YAChE,kEAAkE;YAClE,iEAAiE;YACjE,2DAA2D;YAC3D,IAAI+F,YAAYE,gBAAgB;oBACVG;gBAApB,MAAMpG,eAAcoG,eAAAA,kCAAAA,aAAepG,WAAW;gBAE9C,IAAIA,aAAa;oBACfkH,aAAaC,IAAI,CAACnH;gBACpB;YACF;YAEA,iEAAiE;YACjE,mEAAmE;YACnE,oBAAoB;YACpB,IAAIgG,gBAAgBC,gBAAgB;oBACdI;gBAApB,MAAMrG,eAAcqG,mBAAAA,sCAAAA,iBAAmBrG,WAAW;gBAElD,IAAIA,aAAa;oBACfkH,aAAaC,IAAI,CAACnH;gBACpB;YACF;YAEA,OAAOkH;QACT;IACF;IAEA,IAAIE,sCAAsCxD,MAAMtC,IAAI;IACpD,IACE8F,wCAAwC,QACxCA,oCAAoCvH,UAAU,CAAC,YAC/C;QACAuH,sCAAsChD,aAAI,CAACG,QAAQ,CACjDZ,eACAhD,IAAAA,kBAAa,EAACyG;IAElB;IACA,8FAA8F;IAC9F,MAAMC,oBAAyC;QAC7C/F,MAAM8F;QACN1F,OAAOkC,MAAMlC,KAAK;QAClBE,SAASgC,MAAMhC,OAAO;QACtB6C,YAAYb,MAAMa,UAAU;QAC5B5B,SAASnD,mBAAmBuH;QAC5BvC,WAAW,EAAE;IACf;IACA,IAAI,CAAChE,QAAQ;QACX,iDAAiD;QACjD,OAAO;YACLiE,oBAAoB0C;YACpBzC,mBAAmB;QACrB;IACF;IACAyC,kBAAkBxE,OAAO,KAAKyE,IAAAA,0CAA8B,EAAC5G,OAAOI,SAAS;IAE7E,MAAMyG,6BAA6B,MAAMlI,yBAAyB;QAChEqE,kBAAkB2D,kBAAkBxE,OAAO;QAC3Ce;QACAlD;QACAiD;IACF;IAEA,IAAI,CAAC4D,4BAA4B;QAC/B,OAAO;YACL5C,oBAAoB0C;YACpBzC,mBAAmB;QACrB;IACF;IAEA,OAAO2C;AACT;AAEO,SAAS/H,qBAAqB2F,OAMpC;IACC,MAAM,EAAExB,aAAa,EAAE6D,QAAQ,EAAErB,WAAW,EAAEC,WAAW,EAAEC,eAAe,EAAE,GAC1ElB;IAEF,OAAO,eACLsC,GAAoB,EACpBC,GAAmB,EACnBC,IAAgB;QAEhB,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAE,GAAG,IAAIC,IAAI,CAAC,QAAQ,EAAEL,IAAI7E,GAAG,EAAE;QAE/D,IAAIgF,aAAa,mCAAmC;YAClD,IAAIH,IAAIM,MAAM,KAAK,QAAQ;gBACzB,OAAOC,sCAAkB,CAACC,UAAU,CAACP;YACvC;YAEA,MAAMQ,OAAO,MAAM,IAAI3B,QAAgB,CAAClC,SAAS8D;gBAC/C,IAAIC,OAAO;gBACXX,IAAIY,EAAE,CAAC,QAAQ,CAACC;oBACdF,QAAQE;gBACV;gBACAb,IAAIY,EAAE,CAAC,OAAO,IAAMhE,QAAQ+D;gBAC5BX,IAAIY,EAAE,CAAC,SAASF;YAClB;YAEA,IAAI;gBACF,MAAM,EAAEjC,MAAM,EAAEH,QAAQ,EAAEC,YAAY,EAAEC,cAAc,EAAE,GAAGsC,KAAKC,KAAK,CACnEN;gBAGF,OAAOF,sCAAkB,CAACS,IAAI,CAC5Bf,KACA,MAAMnI,uBAAuB;oBAC3BwG;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACA1C;gBACF;YAEJ,EAAE,OAAOsB,KAAK;gBACZ,OAAO+C,sCAAkB,CAACC,UAAU,CAACP;YACvC;QACF,OAAO,IAAIE,aAAa,2BAA2B;YACjD,MAAMhE,QAAQ;gBACZtC,MAAMuG,aAAapE,GAAG,CAAC;gBACvBgB,YAAYoD,aAAapE,GAAG,CAAC;gBAC7B/B,OAAOgH,SAASb,aAAapE,GAAG,CAAC,YAAY,KAAK,OAAO;gBACzD7B,SAAS8G,SAASb,aAAapE,GAAG,CAAC,cAAc,KAAK,OAAO;gBAC7DiB,WAAWmD,aAAac,MAAM,CAAC,aAAaC,MAAM,CAACC;YACrD;YAEA,IAAI,CAACjF,MAAMtC,IAAI,EAAE,OAAO0G,sCAAkB,CAACC,UAAU,CAACP;YAEtD,IAAIoB;YACJ,MAAMC,oBAAoBlB,aAAapE,GAAG,CAAC,yBAAyB;YACpE,IAAIsF,mBAAmB;gBACrB,MAAMC,mBAAmBnB,aAAapE,GAAG,CAAC,WAAW;gBACrD,MAAMwF,UAAU7E,aAAI,CAAC8E,IAAI,CACvB,OACA1B,WAAW,QAAQ,IACnBwB;gBAEFF,mBAAmB,MAAMK,IAAAA,8BAAgB,EAACF,SAAS,GAAG,GAAGtF;YAC3D,OAAO;gBACL,kFAAkF;gBAClF,kFAAkF;gBAClF,MAAMQ,WAAWP,MAAMtC,IAAI,CAACV,OAAO,CAAC,gBAAgB;gBACpDkI,mBAAmB,MAAMK,IAAAA,8BAAgB,EACvChF,UACAP,MAAMlC,KAAK,EACXkC,MAAMhC,OAAO,IAAI,GACjB+B;YAEJ;YACA,IAAImF,iBAAiB1H,KAAK,EAAE;gBAC1BD,QAAQC,KAAK,CAAC,4BAA4B0H,iBAAiB1H,KAAK;gBAChE,OAAO4G,sCAAkB,CAACoB,mBAAmB,CAC3C1B,KACAoB,iBAAiB1H,KAAK;YAE1B;YACA,IAAI,CAAC0H,iBAAiBO,KAAK,EAAE;gBAC3B,OAAOrB,sCAAkB,CAACsB,QAAQ,CAAC5B;YACrC;YACA,OAAOM,sCAAkB,CAACuB,SAAS,CAAC7B;QACtC;QAEA,OAAOC;IACT;AACF;AAEO,SAASlI,uBAAuB0F,OAItC;IACC,MAAM,EAAEgB,WAAW,EAAEC,WAAW,EAAEC,eAAe,EAAE,GAAGlB;IAEtD,OAAO,eACLsC,GAAoB,EACpBC,GAAmB,EACnBC,IAAgB;QAEhB,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAE,GAAG,IAAIC,IAAI,CAAC,QAAQ,EAAEL,IAAI7E,GAAG,EAAE;QAE/D,IAAIgF,aAAa,wBAAwB;YACvC,OAAOD;QACT;QAEA,MAAMV,WAAWY,aAAapE,GAAG,CAAC;QAElC,IAAI,CAACwD,UAAU;YACb,OAAOe,sCAAkB,CAACC,UAAU,CAACP;QACvC;QAEA,IAAIhH;QAEJ,IAAI;YACFA,SAAS,MAAMwE,UACb;gBACE5D,MAAM2F;gBACN,wCAAwC;gBACxCvF,OAAO;gBACPE,SAAS;YACX,GACA;gBACEwD,iBAAiB;oBACf,MAAM8B,eAAsC,EAAE;oBAE9C,KAAK,MAAMsC,SAAS;wBAClBrD;wBACAC;wBACAC;qBACD,CAAE;wBACD,IAAImD,yBAAAA,MAAOxJ,WAAW,EAAE;4BACtBkH,aAAaC,IAAI,CAACqC,MAAMxJ,WAAW;wBACrC;oBACF;oBAEA,OAAOkH;gBACT;YACF;QAEJ,EAAE,OAAO9F,OAAO;YACd,OAAO4G,sCAAkB,CAACoB,mBAAmB,CAAC1B,KAAKtG;QACrD;QAEA,IAAI,CAACV,QAAQ;YACX,OAAOsH,sCAAkB,CAACuB,SAAS,CAAC7B;QACtC;QAEA,OAAOM,sCAAkB,CAACS,IAAI,CAACf,KAAKhH,OAAOI,SAAS;IACtD;AACF", "ignoreList": [0]}