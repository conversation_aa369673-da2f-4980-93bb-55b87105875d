{"version": 3, "sources": ["../../../src/server/app-render/postponed-state.ts"], "sourcesContent": ["import type { FallbackRouteParams } from '../../server/request/fallback-params'\nimport type { Params } from '../request/params'\nimport {\n  createPrerenderResumeDataCache,\n  createRenderResumeDataCache,\n  type PrerenderResumeDataCache,\n  type RenderResumeDataCache,\n} from '../resume-data-cache/resume-data-cache'\nimport { stringifyResumeDataCache } from '../resume-data-cache/resume-data-cache'\n\nexport enum DynamicState {\n  /**\n   * The dynamic access occurred during the RSC render phase.\n   */\n  DATA = 1,\n\n  /**\n   * The dynamic access occurred during the HTML shell render phase.\n   */\n  HTML = 2,\n}\n\n/**\n * The postponed state for dynamic data.\n */\nexport type DynamicDataPostponedState = {\n  /**\n   * The type of dynamic state.\n   */\n  readonly type: DynamicState.DATA\n\n  /**\n   * The immutable resume data cache.\n   */\n  readonly renderResumeDataCache: RenderResumeDataCache\n}\n\n/**\n * The postponed state for dynamic HTML.\n */\nexport type DynamicHTMLPostponedState = {\n  /**\n   * The type of dynamic state.\n   */\n  readonly type: DynamicState.HTML\n\n  /**\n   * The postponed data used by React.\n   */\n  readonly data: [\n    preludeState: DynamicHTMLPreludeState,\n    postponed: ReactPostponed,\n  ]\n\n  /**\n   * The immutable resume data cache.\n   */\n  readonly renderResumeDataCache: RenderResumeDataCache\n}\n\nexport const enum DynamicHTMLPreludeState {\n  Empty = 0,\n  Full = 1,\n}\n\ntype ReactPostponed = NonNullable<\n  import('react-dom/static').PrerenderResult['postponed']\n>\n\nexport type PostponedState =\n  | DynamicDataPostponedState\n  | DynamicHTMLPostponedState\n\nexport async function getDynamicHTMLPostponedState(\n  postponed: ReactPostponed,\n  preludeState: DynamicHTMLPreludeState,\n  fallbackRouteParams: FallbackRouteParams | null,\n  resumeDataCache: PrerenderResumeDataCache | RenderResumeDataCache\n): Promise<string> {\n  const data: DynamicHTMLPostponedState['data'] = [preludeState, postponed]\n  const dataString = JSON.stringify(data)\n\n  if (!fallbackRouteParams || fallbackRouteParams.size === 0) {\n    // Serialized as `<postponedString.length>:<postponedString><renderResumeDataCache>`\n    return `${dataString.length}:${dataString}${await stringifyResumeDataCache(\n      createRenderResumeDataCache(resumeDataCache)\n    )}`\n  }\n\n  const replacements: Array<[string, string]> = Array.from(fallbackRouteParams)\n  const replacementsString = JSON.stringify(replacements)\n\n  // Serialized as `<replacements.length><replacements><data>`\n  const postponedString = `${replacementsString.length}${replacementsString}${dataString}`\n\n  // Serialized as `<postponedString.length>:<postponedString><renderResumeDataCache>`\n  return `${postponedString.length}:${postponedString}${await stringifyResumeDataCache(resumeDataCache)}`\n}\n\nexport async function getDynamicDataPostponedState(\n  resumeDataCache: PrerenderResumeDataCache | RenderResumeDataCache\n): Promise<string> {\n  return `4:null${await stringifyResumeDataCache(createRenderResumeDataCache(resumeDataCache))}`\n}\n\nexport function parsePostponedState(\n  state: string,\n  params: Params | undefined\n): PostponedState {\n  try {\n    const postponedStringLengthMatch = state.match(/^([0-9]*):/)?.[1]\n    if (!postponedStringLengthMatch) {\n      throw new Error(`Invariant: invalid postponed state ${state}`)\n    }\n\n    const postponedStringLength = parseInt(postponedStringLengthMatch)\n\n    // We add a `:` to the end of the length as the first character of the\n    // postponed string is the length of the replacement entries.\n    const postponedString = state.slice(\n      postponedStringLengthMatch.length + 1,\n      postponedStringLengthMatch.length + postponedStringLength + 1\n    )\n\n    const renderResumeDataCache = createRenderResumeDataCache(\n      state.slice(postponedStringLengthMatch.length + postponedStringLength + 1)\n    )\n\n    try {\n      if (postponedString === 'null') {\n        return { type: DynamicState.DATA, renderResumeDataCache }\n      }\n\n      if (/^[0-9]/.test(postponedString)) {\n        const match = postponedString.match(/^([0-9]*)/)?.[1]\n        if (!match) {\n          throw new Error(\n            `Invariant: invalid postponed state ${JSON.stringify(postponedString)}`\n          )\n        }\n\n        // This is the length of the replacements entries.\n        const length = parseInt(match)\n        const replacements = JSON.parse(\n          postponedString.slice(\n            match.length,\n            // We then go to the end of the string.\n            match.length + length\n          )\n        ) as ReadonlyArray<[string, string]>\n\n        let postponed = postponedString.slice(match.length + length)\n        for (const [key, searchValue] of replacements) {\n          const value = params?.[key] ?? ''\n          const replaceValue = Array.isArray(value) ? value.join('/') : value\n          postponed = postponed.replaceAll(searchValue, replaceValue)\n        }\n\n        return {\n          type: DynamicState.HTML,\n          data: JSON.parse(postponed),\n          renderResumeDataCache,\n        }\n      }\n\n      return {\n        type: DynamicState.HTML,\n        data: JSON.parse(postponedString),\n        renderResumeDataCache,\n      }\n    } catch (err) {\n      console.error('Failed to parse postponed state', err)\n      return { type: DynamicState.DATA, renderResumeDataCache }\n    }\n  } catch (err) {\n    console.error('Failed to parse postponed state', err)\n    return {\n      type: DynamicState.DATA,\n      renderResumeDataCache: createPrerenderResumeDataCache(),\n    }\n  }\n}\n\nexport function getPostponedFromState(state: DynamicHTMLPostponedState) {\n  const [preludeState, postponed] = state.data\n  return { preludeState, postponed }\n}\n"], "names": ["DynamicHTMLPreludeState", "DynamicState", "getDynamicDataPostponedState", "getDynamicHTMLPostponedState", "getPostponedFromState", "parsePostponedState", "postponed", "preludeState", "fallbackRouteParams", "resumeDataCache", "data", "dataString", "JSON", "stringify", "size", "length", "stringifyResumeDataCache", "createRenderResumeDataCache", "replacements", "Array", "from", "replacementsString", "postponedString", "state", "params", "postponedStringLengthMatch", "match", "Error", "postponedStringLength", "parseInt", "slice", "renderResumeDataCache", "type", "test", "parse", "key", "searchValue", "value", "replaceValue", "isArray", "join", "replaceAll", "err", "console", "error", "createPrerenderResumeDataCache"], "mappings": ";;;;;;;;;;;;;;;;;;;IA4DkBA,uBAAuB;eAAvBA;;IAlDNC,YAAY;eAAZA;;IAyFUC,4BAA4B;eAA5BA;;IA1BAC,4BAA4B;eAA5BA;;IA8GNC,qBAAqB;eAArBA;;IA9EAC,mBAAmB;eAAnBA;;;iCAlGT;AAGA,IAAA,AAAKJ,sCAAAA;IACV;;GAEC;IAGD;;GAEC;WARSA;;AAkDL,IAAA,AAAWD,iDAAAA;;;WAAAA;;AAaX,eAAeG,6BACpBG,SAAyB,EACzBC,YAAqC,EACrCC,mBAA+C,EAC/CC,eAAiE;IAEjE,MAAMC,OAA0C;QAACH;QAAcD;KAAU;IACzE,MAAMK,aAAaC,KAAKC,SAAS,CAACH;IAElC,IAAI,CAACF,uBAAuBA,oBAAoBM,IAAI,KAAK,GAAG;QAC1D,oFAAoF;QACpF,OAAO,GAAGH,WAAWI,MAAM,CAAC,CAAC,EAAEJ,aAAa,MAAMK,IAAAA,yCAAwB,EACxEC,IAAAA,4CAA2B,EAACR,mBAC3B;IACL;IAEA,MAAMS,eAAwCC,MAAMC,IAAI,CAACZ;IACzD,MAAMa,qBAAqBT,KAAKC,SAAS,CAACK;IAE1C,4DAA4D;IAC5D,MAAMI,kBAAkB,GAAGD,mBAAmBN,MAAM,GAAGM,qBAAqBV,YAAY;IAExF,oFAAoF;IACpF,OAAO,GAAGW,gBAAgBP,MAAM,CAAC,CAAC,EAAEO,kBAAkB,MAAMN,IAAAA,yCAAwB,EAACP,kBAAkB;AACzG;AAEO,eAAeP,6BACpBO,eAAiE;IAEjE,OAAO,CAAC,MAAM,EAAE,MAAMO,IAAAA,yCAAwB,EAACC,IAAAA,4CAA2B,EAACR,mBAAmB;AAChG;AAEO,SAASJ,oBACdkB,KAAa,EACbC,MAA0B;IAE1B,IAAI;YACiCD;QAAnC,MAAME,8BAA6BF,eAAAA,MAAMG,KAAK,CAAC,kCAAZH,YAA2B,CAAC,EAAE;QACjE,IAAI,CAACE,4BAA4B;YAC/B,MAAM,qBAAwD,CAAxD,IAAIE,MAAM,CAAC,mCAAmC,EAAEJ,OAAO,GAAvD,qBAAA;uBAAA;4BAAA;8BAAA;YAAuD;QAC/D;QAEA,MAAMK,wBAAwBC,SAASJ;QAEvC,sEAAsE;QACtE,6DAA6D;QAC7D,MAAMH,kBAAkBC,MAAMO,KAAK,CACjCL,2BAA2BV,MAAM,GAAG,GACpCU,2BAA2BV,MAAM,GAAGa,wBAAwB;QAG9D,MAAMG,wBAAwBd,IAAAA,4CAA2B,EACvDM,MAAMO,KAAK,CAACL,2BAA2BV,MAAM,GAAGa,wBAAwB;QAG1E,IAAI;YACF,IAAIN,oBAAoB,QAAQ;gBAC9B,OAAO;oBAAEU,IAAI;oBAAqBD;gBAAsB;YAC1D;YAEA,IAAI,SAASE,IAAI,CAACX,kBAAkB;oBACpBA;gBAAd,MAAMI,SAAQJ,yBAAAA,gBAAgBI,KAAK,CAAC,iCAAtBJ,sBAAoC,CAAC,EAAE;gBACrD,IAAI,CAACI,OAAO;oBACV,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,mCAAmC,EAAEf,KAAKC,SAAS,CAACS,kBAAkB,GADnE,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,kDAAkD;gBAClD,MAAMP,SAASc,SAASH;gBACxB,MAAMR,eAAeN,KAAKsB,KAAK,CAC7BZ,gBAAgBQ,KAAK,CACnBJ,MAAMX,MAAM,EACZ,uCAAuC;gBACvCW,MAAMX,MAAM,GAAGA;gBAInB,IAAIT,YAAYgB,gBAAgBQ,KAAK,CAACJ,MAAMX,MAAM,GAAGA;gBACrD,KAAK,MAAM,CAACoB,KAAKC,YAAY,IAAIlB,aAAc;oBAC7C,MAAMmB,QAAQb,CAAAA,0BAAAA,MAAQ,CAACW,IAAI,KAAI;oBAC/B,MAAMG,eAAenB,MAAMoB,OAAO,CAACF,SAASA,MAAMG,IAAI,CAAC,OAAOH;oBAC9D/B,YAAYA,UAAUmC,UAAU,CAACL,aAAaE;gBAChD;gBAEA,OAAO;oBACLN,IAAI;oBACJtB,MAAME,KAAKsB,KAAK,CAAC5B;oBACjByB;gBACF;YACF;YAEA,OAAO;gBACLC,IAAI;gBACJtB,MAAME,KAAKsB,KAAK,CAACZ;gBACjBS;YACF;QACF,EAAE,OAAOW,KAAK;YACZC,QAAQC,KAAK,CAAC,mCAAmCF;YACjD,OAAO;gBAAEV,IAAI;gBAAqBD;YAAsB;QAC1D;IACF,EAAE,OAAOW,KAAK;QACZC,QAAQC,KAAK,CAAC,mCAAmCF;QACjD,OAAO;YACLV,IAAI;YACJD,uBAAuBc,IAAAA,+CAA8B;QACvD;IACF;AACF;AAEO,SAASzC,sBAAsBmB,KAAgC;IACpE,MAAM,CAAChB,cAAcD,UAAU,GAAGiB,MAAMb,IAAI;IAC5C,OAAO;QAAEH;QAAcD;IAAU;AACnC", "ignoreList": [0]}