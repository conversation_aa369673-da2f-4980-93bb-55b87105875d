{"version": 3, "sources": ["../../../src/build/templates/edge-ssr.ts"], "sourcesContent": ["import '../../server/web/globals'\nimport { adapter, type NextRequestHint } from '../../server/web/adapter'\nimport { IncrementalCache } from '../../server/lib/incremental-cache'\nimport { initializeCacheHandlers } from '../../server/use-cache/handlers'\n\nimport Document from 'VAR_MODULE_DOCUMENT'\nimport * as appMod from 'VAR_MODULE_APP'\nimport * as userlandPage from 'VAR_USERLAND'\nimport * as userlandErrorPage from 'VAR_MODULE_GLOBAL_ERROR'\n\ndeclare const userland500Page: any\ndeclare const incrementalCacheHandler: any\n// OPTIONAL_IMPORT:* as userland500Page\n// OPTIONAL_IMPORT:incrementalCacheHandler\n\nimport RouteModule, {\n  type PagesRouteHandlerContext,\n} from '../../server/route-modules/pages/module'\nimport { WebNextRequest, WebNextResponse } from '../../server/base-http/web'\n\nimport type { RequestData } from '../../server/web/types'\nimport type { NextConfigComplete } from '../../server/config-shared'\nimport type { NextFetchEvent } from '../../server/web/spec-extension/fetch-event'\nimport type RenderResult from '../../server/render-result'\nimport type { RenderResultMetadata } from '../../server/render-result'\nimport { getTracer, SpanKind, type Span } from '../../server/lib/trace/tracer'\nimport { BaseServerSpan } from '../../server/lib/trace/constants'\nimport { HTML_CONTENT_TYPE_HEADER } from '../../lib/constants'\n\n// injected by the loader afterwards.\ndeclare const nextConfig: NextConfigComplete\ndeclare const pageRouteModuleOptions: any\ndeclare const errorRouteModuleOptions: any\ndeclare const user500RouteModuleOptions: any\n// INJECT:nextConfig\n// INJECT:pageRouteModuleOptions\n// INJECT:errorRouteModuleOptions\n// INJECT:user500RouteModuleOptions\n\n// Initialize the cache handlers interface.\ninitializeCacheHandlers()\n\n// expose this for the route-module\n;(globalThis as any).nextConfig = nextConfig\n\nconst pageMod = {\n  ...userlandPage,\n  routeModule: new RouteModule({\n    ...pageRouteModuleOptions,\n    components: {\n      App: appMod.default,\n      Document,\n    },\n    userland: userlandPage,\n  }),\n}\n\nconst errorMod = {\n  ...userlandErrorPage,\n  routeModule: new RouteModule({\n    ...errorRouteModuleOptions,\n    components: {\n      App: appMod.default,\n      Document,\n    },\n    userland: userlandErrorPage,\n  }),\n}\n\n// FIXME: this needs to be made compatible with the template\nconst error500Mod = userland500Page\n  ? {\n      ...userland500Page,\n      routeModule: new RouteModule({\n        ...user500RouteModuleOptions,\n        components: {\n          App: appMod.default,\n          Document,\n        },\n        userland: userland500Page,\n      }),\n    }\n  : null\n\nexport const ComponentMod = pageMod\n\nasync function requestHandler(\n  req: NextRequestHint,\n  _event: NextFetchEvent\n): Promise<Response> {\n  let srcPage = 'VAR_PAGE'\n\n  const relativeUrl = `${req.nextUrl.pathname}${req.nextUrl.search}`\n  const baseReq = new WebNextRequest(req)\n  const pageRouteModule = pageMod.routeModule as RouteModule\n  const prepareResult = await pageRouteModule.prepare(baseReq, null, {\n    srcPage,\n    multiZoneDraftMode: false,\n  })\n\n  if (!prepareResult) {\n    return new Response('Bad Request', {\n      status: 400,\n    })\n  }\n  const {\n    query,\n    params,\n    buildId,\n    isNextDataRequest,\n    buildManifest,\n    prerenderManifest,\n    reactLoadableManifest,\n    clientReferenceManifest,\n    subresourceIntegrityManifest,\n    dynamicCssManifest,\n  } = prepareResult\n\n  const renderContext: PagesRouteHandlerContext = {\n    page: srcPage,\n    query,\n    params,\n\n    sharedContext: {\n      buildId,\n      deploymentId: process.env.NEXT_DEPLOYMENT_ID,\n      customServer: undefined,\n    },\n\n    renderContext: {\n      isFallback: false,\n      isDraftMode: false,\n      developmentNotFoundSourcePage: undefined,\n    },\n\n    renderOpts: {\n      params,\n      page: srcPage,\n      supportsDynamicResponse: true,\n      Component: pageMod.Component,\n      ComponentMod: pageMod,\n      pageConfig: pageMod.pageConfig,\n      routeModule: pageMod.routeModule,\n      canonicalBase: nextConfig.amp.canonicalBase || '',\n      previewProps: prerenderManifest.preview,\n      ampOptimizerConfig: nextConfig.experimental.amp?.optimizer,\n      basePath: nextConfig.basePath,\n      assetPrefix: nextConfig.assetPrefix,\n      images: nextConfig.images,\n      optimizeCss: nextConfig.experimental.optimizeCss,\n      nextConfigOutput: nextConfig.output,\n      nextScriptWorkers: nextConfig.experimental.nextScriptWorkers,\n      disableOptimizedLoading: nextConfig.experimental.disableOptimizedLoading,\n      domainLocales: nextConfig.i18n?.domains,\n      distDir: '',\n      crossOrigin: nextConfig.crossOrigin ? nextConfig.crossOrigin : undefined,\n      largePageDataBytes: nextConfig.experimental.largePageDataBytes,\n      // Only the `publicRuntimeConfig` key is exposed to the client side\n      // It'll be rendered as part of __NEXT_DATA__ on the client side\n      runtimeConfig:\n        Object.keys(nextConfig.publicRuntimeConfig).length > 0\n          ? nextConfig.publicRuntimeConfig\n          : undefined,\n\n      isExperimentalCompile: nextConfig.experimental.isExperimentalCompile,\n      // `htmlLimitedBots` is passed to server as serialized config in string format\n      experimental: {\n        clientTraceMetadata: nextConfig.experimental.clientTraceMetadata,\n      },\n\n      buildManifest,\n      subresourceIntegrityManifest,\n      reactLoadableManifest,\n      clientReferenceManifest,\n      dynamicCssManifest,\n    },\n  }\n  let finalStatus = 200\n\n  const renderResultToResponse = (\n    result: RenderResult<RenderResultMetadata>\n  ): Response => {\n    // Handle null responses\n    if (result.isNull) {\n      finalStatus = 500\n      return new Response(null, { status: 500 })\n    }\n\n    // Extract metadata\n    const { metadata } = result\n    finalStatus = metadata.statusCode || 200\n    const headers = new Headers()\n\n    // Set content type\n    const contentType = result.contentType || HTML_CONTENT_TYPE_HEADER\n    headers.set('Content-Type', contentType)\n\n    // Add metadata headers\n    if (metadata.headers) {\n      for (const [key, value] of Object.entries(metadata.headers)) {\n        if (value !== undefined) {\n          if (Array.isArray(value)) {\n            // Handle multiple header values\n            for (const v of value) {\n              headers.append(key, String(v))\n            }\n          } else {\n            headers.set(key, String(value))\n          }\n        }\n      }\n    }\n\n    // Handle static response\n    if (!result.isDynamic) {\n      const body = result.toUnchunkedString()\n      headers.set(\n        'Content-Length',\n        String(new TextEncoder().encode(body).length)\n      )\n      return new Response(body, {\n        status: finalStatus,\n        headers,\n      })\n    }\n\n    // Handle dynamic/streaming response\n    // For edge runtime, we need to create a readable stream that pipes from the result\n    const { readable, writable } = new TransformStream()\n\n    // Start piping the result to the writable stream\n    // This is done asynchronously to avoid blocking the response creation\n    result.pipeTo(writable).catch((err) => {\n      console.error('Error piping RenderResult to response:', err)\n    })\n\n    return new Response(readable, {\n      status: finalStatus,\n      headers,\n    })\n  }\n\n  const invokeRender = async (span?: Span): Promise<Response> => {\n    try {\n      const result = await pageRouteModule\n        .render(\n          // @ts-expect-error we don't type this for edge\n          baseReq,\n          new WebNextResponse(undefined),\n          {\n            ...renderContext,\n            renderOpts: {\n              ...renderContext.renderOpts,\n              getServerSideProps: pageMod.getServerSideProps,\n              Component: pageMod.default || pageMod,\n              ComponentMod: pageMod,\n              pageConfig: pageMod.config,\n              isNextDataRequest,\n            },\n          }\n        )\n        .finally(() => {\n          if (!span) return\n\n          span.setAttributes({\n            'http.status_code': finalStatus,\n            'next.rsc': false,\n          })\n\n          const rootSpanAttributes = tracer.getRootSpanAttributes()\n          // We were unable to get attributes, probably OTEL is not enabled\n          if (!rootSpanAttributes) {\n            return\n          }\n\n          if (\n            rootSpanAttributes.get('next.span_type') !==\n            BaseServerSpan.handleRequest\n          ) {\n            console.warn(\n              `Unexpected root span type '${rootSpanAttributes.get(\n                'next.span_type'\n              )}'. Please report this Next.js issue https://github.com/vercel/next.js`\n            )\n            return\n          }\n\n          const route = rootSpanAttributes.get('next.route')\n          if (route) {\n            const name = `${req.method} ${route}`\n\n            span.setAttributes({\n              'next.route': route,\n              'http.route': route,\n              'next.span_name': name,\n            })\n            span.updateName(name)\n          } else {\n            span.updateName(`${req.method} ${relativeUrl}`)\n          }\n        })\n\n      return renderResultToResponse(result)\n    } catch (err) {\n      const errModule = error500Mod || errorMod\n      const errRouteModule = errModule.routeModule as RouteModule\n\n      if (errRouteModule.isDev) {\n        throw err\n      }\n\n      await errRouteModule.onRequestError(baseReq, err, {\n        routerKind: 'Pages Router',\n        routePath: srcPage,\n        routeType: 'render',\n        revalidateReason: undefined,\n      })\n\n      const errResult = await errRouteModule.render(\n        // @ts-expect-error we don't type this for edge\n        baseReq,\n        new WebNextResponse(undefined),\n        {\n          ...renderContext,\n          page: error500Mod ? '/500' : '/_error',\n          renderOpts: {\n            ...renderContext.renderOpts,\n            getServerSideProps: errModule.getServerSideProps,\n            Component: errModule.default || errModule,\n            ComponentMod: errModule,\n            pageConfig: errModule.config,\n          },\n        }\n      )\n\n      return renderResultToResponse(errResult)\n    }\n  }\n\n  const tracer = getTracer()\n\n  return tracer.withPropagatedContext(req.headers, () =>\n    tracer.trace(\n      BaseServerSpan.handleRequest,\n      {\n        spanName: `${req.method} ${relativeUrl}`,\n        kind: SpanKind.SERVER,\n        attributes: {\n          'http.method': req.method,\n          'http.target': relativeUrl,\n          'http.route': srcPage,\n        },\n      },\n      invokeRender\n    )\n  )\n}\n\nexport default function nHandler(opts: { page: string; request: RequestData }) {\n  return adapter({\n    ...opts,\n    IncrementalCache,\n    handler: requestHandler,\n    incrementalCacheHandler,\n    bypassNextUrl: true,\n  })\n}\n"], "names": ["adapter", "IncrementalCache", "initializeCacheHandlers", "Document", "appMod", "userlandPage", "userlandErrorPage", "RouteModule", "WebNextRequest", "WebNextResponse", "getTracer", "SpanKind", "BaseServerSpan", "HTML_CONTENT_TYPE_HEADER", "globalThis", "nextConfig", "pageMod", "routeModule", "pageRouteModuleOptions", "components", "App", "default", "userland", "errorMod", "errorRouteModuleOptions", "error500Mod", "userland500Page", "user500RouteModuleOptions", "ComponentMod", "requestHandler", "req", "_event", "srcPage", "relativeUrl", "nextUrl", "pathname", "search", "baseReq", "pageRouteModule", "prepareResult", "prepare", "multiZoneDraftMode", "Response", "status", "query", "params", "buildId", "isNextDataRequest", "buildManifest", "prerenderManifest", "reactLoadableManifest", "clientReferenceManifest", "subresourceIntegrityManifest", "dynamicCssManifest", "renderContext", "page", "sharedContext", "deploymentId", "process", "env", "NEXT_DEPLOYMENT_ID", "customServer", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "isDraftMode", "developmentNotFoundSourcePage", "renderOpts", "supportsDynamicResponse", "Component", "pageConfig", "canonicalBase", "amp", "previewProps", "preview", "ampOptimizerConfig", "experimental", "optimizer", "basePath", "assetPrefix", "images", "optimizeCss", "nextConfigOutput", "output", "nextScriptWorkers", "disableOptimizedLoading", "domainLocales", "i18n", "domains", "distDir", "crossOrigin", "largePageDataBytes", "runtimeConfig", "Object", "keys", "publicRuntimeConfig", "length", "isExperimentalCompile", "clientTraceMetadata", "finalStatus", "renderResultToResponse", "result", "isNull", "metadata", "statusCode", "headers", "Headers", "contentType", "set", "key", "value", "entries", "Array", "isArray", "v", "append", "String", "isDynamic", "body", "toUnchunkedString", "TextEncoder", "encode", "readable", "writable", "TransformStream", "pipeTo", "catch", "err", "console", "error", "invokeRender", "span", "render", "getServerSideProps", "config", "finally", "setAttributes", "rootSpanAttributes", "tracer", "getRootSpanAttributes", "get", "handleRequest", "warn", "route", "name", "method", "updateName", "errModule", "errRouteModule", "isDev", "onRequestError", "routerKind", "routePath", "routeType", "revalidateReason", "err<PERSON><PERSON><PERSON>", "withPropagatedContext", "trace", "spanName", "kind", "SERVER", "attributes", "nH<PERSON><PERSON>", "opts", "handler", "incremental<PERSON>ache<PERSON><PERSON><PERSON>", "bypassNextUrl"], "mappings": "AAAA,OAAO,2BAA0B;AACjC,SAASA,OAAO,QAA8B,2BAA0B;AACxE,SAASC,gBAAgB,QAAQ,qCAAoC;AACrE,SAASC,uBAAuB,QAAQ,kCAAiC;AAEzE,OAAOC,cAAc,sBAAqB;AAC1C,YAAYC,YAAY,iBAAgB;AACxC,YAAYC,kBAAkB,eAAc;AAC5C,YAAYC,uBAAuB,0BAAyB;AAI5D,uCAAuC;AACvC,0CAA0C;AAE1C,OAAOC,iBAEA,0CAAyC;AAChD,SAASC,cAAc,EAAEC,eAAe,QAAQ,6BAA4B;AAO5E,SAASC,SAAS,EAAEC,QAAQ,QAAmB,gCAA+B;AAC9E,SAASC,cAAc,QAAQ,mCAAkC;AACjE,SAASC,wBAAwB,QAAQ,sBAAqB;AAO9D,oBAAoB;AACpB,gCAAgC;AAChC,iCAAiC;AACjC,mCAAmC;AAEnC,2CAA2C;AAC3CX;AAGEY,WAAmBC,UAAU,GAAGA;AAElC,MAAMC,UAAU;IACd,GAAGX,YAAY;IACfY,aAAa,IAAIV,YAAY;QAC3B,GAAGW,sBAAsB;QACzBC,YAAY;YACVC,KAAKhB,OAAOiB,OAAO;YACnBlB;QACF;QACAmB,UAAUjB;IACZ;AACF;AAEA,MAAMkB,WAAW;IACf,GAAGjB,iBAAiB;IACpBW,aAAa,IAAIV,YAAY;QAC3B,GAAGiB,uBAAuB;QAC1BL,YAAY;YACVC,KAAKhB,OAAOiB,OAAO;YACnBlB;QACF;QACAmB,UAAUhB;IACZ;AACF;AAEA,4DAA4D;AAC5D,MAAMmB,cAAcC,kBAChB;IACE,GAAGA,eAAe;IAClBT,aAAa,IAAIV,YAAY;QAC3B,GAAGoB,yBAAyB;QAC5BR,YAAY;YACVC,KAAKhB,OAAOiB,OAAO;YACnBlB;QACF;QACAmB,UAAUI;IACZ;AACF,IACA;AAEJ,OAAO,MAAME,eAAeZ,QAAO;AAEnC,eAAea,eACbC,GAAoB,EACpBC,MAAsB;QAyDEhB,8BAQLA;IA/DnB,IAAIiB,UAAU;IAEd,MAAMC,cAAc,GAAGH,IAAII,OAAO,CAACC,QAAQ,GAAGL,IAAII,OAAO,CAACE,MAAM,EAAE;IAClE,MAAMC,UAAU,IAAI7B,eAAesB;IACnC,MAAMQ,kBAAkBtB,QAAQC,WAAW;IAC3C,MAAMsB,gBAAgB,MAAMD,gBAAgBE,OAAO,CAACH,SAAS,MAAM;QACjEL;QACAS,oBAAoB;IACtB;IAEA,IAAI,CAACF,eAAe;QAClB,OAAO,IAAIG,SAAS,eAAe;YACjCC,QAAQ;QACV;IACF;IACA,MAAM,EACJC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,iBAAiB,EACjBC,aAAa,EACbC,iBAAiB,EACjBC,qBAAqB,EACrBC,uBAAuB,EACvBC,4BAA4B,EAC5BC,kBAAkB,EACnB,GAAGd;IAEJ,MAAMe,gBAA0C;QAC9CC,MAAMvB;QACNY;QACAC;QAEAW,eAAe;YACbV;YACAW,cAAcC,QAAQC,GAAG,CAACC,kBAAkB;YAC5CC,cAAcC;QAChB;QAEAR,eAAe;YACbS,YAAY;YACZC,aAAa;YACbC,+BAA+BH;QACjC;QAEAI,YAAY;YACVrB;YACAU,MAAMvB;YACNmC,yBAAyB;YACzBC,WAAWpD,QAAQoD,SAAS;YAC5BxC,cAAcZ;YACdqD,YAAYrD,QAAQqD,UAAU;YAC9BpD,aAAaD,QAAQC,WAAW;YAChCqD,eAAevD,WAAWwD,GAAG,CAACD,aAAa,IAAI;YAC/CE,cAAcvB,kBAAkBwB,OAAO;YACvCC,kBAAkB,GAAE3D,+BAAAA,WAAW4D,YAAY,CAACJ,GAAG,qBAA3BxD,6BAA6B6D,SAAS;YAC1DC,UAAU9D,WAAW8D,QAAQ;YAC7BC,aAAa/D,WAAW+D,WAAW;YACnCC,QAAQhE,WAAWgE,MAAM;YACzBC,aAAajE,WAAW4D,YAAY,CAACK,WAAW;YAChDC,kBAAkBlE,WAAWmE,MAAM;YACnCC,mBAAmBpE,WAAW4D,YAAY,CAACQ,iBAAiB;YAC5DC,yBAAyBrE,WAAW4D,YAAY,CAACS,uBAAuB;YACxEC,aAAa,GAAEtE,mBAAAA,WAAWuE,IAAI,qBAAfvE,iBAAiBwE,OAAO;YACvCC,SAAS;YACTC,aAAa1E,WAAW0E,WAAW,GAAG1E,WAAW0E,WAAW,GAAG3B;YAC/D4B,oBAAoB3E,WAAW4D,YAAY,CAACe,kBAAkB;YAC9D,mEAAmE;YACnE,gEAAgE;YAChEC,eACEC,OAAOC,IAAI,CAAC9E,WAAW+E,mBAAmB,EAAEC,MAAM,GAAG,IACjDhF,WAAW+E,mBAAmB,GAC9BhC;YAENkC,uBAAuBjF,WAAW4D,YAAY,CAACqB,qBAAqB;YACpE,8EAA8E;YAC9ErB,cAAc;gBACZsB,qBAAqBlF,WAAW4D,YAAY,CAACsB,mBAAmB;YAClE;YAEAjD;YACAI;YACAF;YACAC;YACAE;QACF;IACF;IACA,IAAI6C,cAAc;IAElB,MAAMC,yBAAyB,CAC7BC;QAEA,wBAAwB;QACxB,IAAIA,OAAOC,MAAM,EAAE;YACjBH,cAAc;YACd,OAAO,IAAIxD,SAAS,MAAM;gBAAEC,QAAQ;YAAI;QAC1C;QAEA,mBAAmB;QACnB,MAAM,EAAE2D,QAAQ,EAAE,GAAGF;QACrBF,cAAcI,SAASC,UAAU,IAAI;QACrC,MAAMC,UAAU,IAAIC;QAEpB,mBAAmB;QACnB,MAAMC,cAAcN,OAAOM,WAAW,IAAI7F;QAC1C2F,QAAQG,GAAG,CAAC,gBAAgBD;QAE5B,uBAAuB;QACvB,IAAIJ,SAASE,OAAO,EAAE;YACpB,KAAK,MAAM,CAACI,KAAKC,MAAM,IAAIjB,OAAOkB,OAAO,CAACR,SAASE,OAAO,EAAG;gBAC3D,IAAIK,UAAU/C,WAAW;oBACvB,IAAIiD,MAAMC,OAAO,CAACH,QAAQ;wBACxB,gCAAgC;wBAChC,KAAK,MAAMI,KAAKJ,MAAO;4BACrBL,QAAQU,MAAM,CAACN,KAAKO,OAAOF;wBAC7B;oBACF,OAAO;wBACLT,QAAQG,GAAG,CAACC,KAAKO,OAAON;oBAC1B;gBACF;YACF;QACF;QAEA,yBAAyB;QACzB,IAAI,CAACT,OAAOgB,SAAS,EAAE;YACrB,MAAMC,OAAOjB,OAAOkB,iBAAiB;YACrCd,QAAQG,GAAG,CACT,kBACAQ,OAAO,IAAII,cAAcC,MAAM,CAACH,MAAMtB,MAAM;YAE9C,OAAO,IAAIrD,SAAS2E,MAAM;gBACxB1E,QAAQuD;gBACRM;YACF;QACF;QAEA,oCAAoC;QACpC,mFAAmF;QACnF,MAAM,EAAEiB,QAAQ,EAAEC,QAAQ,EAAE,GAAG,IAAIC;QAEnC,iDAAiD;QACjD,sEAAsE;QACtEvB,OAAOwB,MAAM,CAACF,UAAUG,KAAK,CAAC,CAACC;YAC7BC,QAAQC,KAAK,CAAC,0CAA0CF;QAC1D;QAEA,OAAO,IAAIpF,SAAS+E,UAAU;YAC5B9E,QAAQuD;YACRM;QACF;IACF;IAEA,MAAMyB,eAAe,OAAOC;QAC1B,IAAI;YACF,MAAM9B,SAAS,MAAM9D,gBAClB6F,MAAM,CACL,+CAA+C;YAC/C9F,SACA,IAAI5B,gBAAgBqD,YACpB;gBACE,GAAGR,aAAa;gBAChBY,YAAY;oBACV,GAAGZ,cAAcY,UAAU;oBAC3BkE,oBAAoBpH,QAAQoH,kBAAkB;oBAC9ChE,WAAWpD,QAAQK,OAAO,IAAIL;oBAC9BY,cAAcZ;oBACdqD,YAAYrD,QAAQqH,MAAM;oBAC1BtF;gBACF;YACF,GAEDuF,OAAO,CAAC;gBACP,IAAI,CAACJ,MAAM;gBAEXA,KAAKK,aAAa,CAAC;oBACjB,oBAAoBrC;oBACpB,YAAY;gBACd;gBAEA,MAAMsC,qBAAqBC,OAAOC,qBAAqB;gBACvD,iEAAiE;gBACjE,IAAI,CAACF,oBAAoB;oBACvB;gBACF;gBAEA,IACEA,mBAAmBG,GAAG,CAAC,sBACvB/H,eAAegI,aAAa,EAC5B;oBACAb,QAAQc,IAAI,CACV,CAAC,2BAA2B,EAAEL,mBAAmBG,GAAG,CAClD,kBACA,qEAAqE,CAAC;oBAE1E;gBACF;gBAEA,MAAMG,QAAQN,mBAAmBG,GAAG,CAAC;gBACrC,IAAIG,OAAO;oBACT,MAAMC,OAAO,GAAGjH,IAAIkH,MAAM,CAAC,CAAC,EAAEF,OAAO;oBAErCZ,KAAKK,aAAa,CAAC;wBACjB,cAAcO;wBACd,cAAcA;wBACd,kBAAkBC;oBACpB;oBACAb,KAAKe,UAAU,CAACF;gBAClB,OAAO;oBACLb,KAAKe,UAAU,CAAC,GAAGnH,IAAIkH,MAAM,CAAC,CAAC,EAAE/G,aAAa;gBAChD;YACF;YAEF,OAAOkE,uBAAuBC;QAChC,EAAE,OAAO0B,KAAK;YACZ,MAAMoB,YAAYzH,eAAeF;YACjC,MAAM4H,iBAAiBD,UAAUjI,WAAW;YAE5C,IAAIkI,eAAeC,KAAK,EAAE;gBACxB,MAAMtB;YACR;YAEA,MAAMqB,eAAeE,cAAc,CAAChH,SAASyF,KAAK;gBAChDwB,YAAY;gBACZC,WAAWvH;gBACXwH,WAAW;gBACXC,kBAAkB3F;YACpB;YAEA,MAAM4F,YAAY,MAAMP,eAAehB,MAAM,CAC3C,+CAA+C;YAC/C9F,SACA,IAAI5B,gBAAgBqD,YACpB;gBACE,GAAGR,aAAa;gBAChBC,MAAM9B,cAAc,SAAS;gBAC7ByC,YAAY;oBACV,GAAGZ,cAAcY,UAAU;oBAC3BkE,oBAAoBc,UAAUd,kBAAkB;oBAChDhE,WAAW8E,UAAU7H,OAAO,IAAI6H;oBAChCtH,cAAcsH;oBACd7E,YAAY6E,UAAUb,MAAM;gBAC9B;YACF;YAGF,OAAOlC,uBAAuBuD;QAChC;IACF;IAEA,MAAMjB,SAAS/H;IAEf,OAAO+H,OAAOkB,qBAAqB,CAAC7H,IAAI0E,OAAO,EAAE,IAC/CiC,OAAOmB,KAAK,CACVhJ,eAAegI,aAAa,EAC5B;YACEiB,UAAU,GAAG/H,IAAIkH,MAAM,CAAC,CAAC,EAAE/G,aAAa;YACxC6H,MAAMnJ,SAASoJ,MAAM;YACrBC,YAAY;gBACV,eAAelI,IAAIkH,MAAM;gBACzB,eAAe/G;gBACf,cAAcD;YAChB;QACF,GACAiG;AAGN;AAEA,eAAe,SAASgC,SAASC,IAA4C;IAC3E,OAAOlK,QAAQ;QACb,GAAGkK,IAAI;QACPjK;QACAkK,SAAStI;QACTuI;QACAC,eAAe;IACjB;AACF", "ignoreList": [0]}