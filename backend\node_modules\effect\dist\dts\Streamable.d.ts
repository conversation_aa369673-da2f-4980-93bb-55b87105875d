/**
 * @since 2.0.0
 */
import * as Stream from "./Stream.js";
/**
 * @since 2.0.0
 * @category constructors
 */
export declare abstract class Class<A, E = never, R = never> implements Stream.Stream<A, E, R> {
    /**
     * @since 2.0.0
     */
    readonly [Stream.StreamTypeId]: {
        _R: (_: never) => never;
        _E: (_: never) => never;
        _A: (_: never) => never;
    };
    /**
     * @since 2.0.0
     */
    pipe(): unknown;
    /**
     * @since 2.0.0
     */
    abstract toStream(): Stream.Stream<A, E, R>;
}
//# sourceMappingURL=Streamable.d.ts.map