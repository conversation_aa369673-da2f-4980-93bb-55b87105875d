{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/empty-projects/augment-small-project/frontend/src/services/api.ts"], "sourcesContent": ["import axios from 'axios';\n\nconst API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';\n\nconst api = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Add token to requests if available\napi.interceptors.request.use((config) => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n});\n\n// Handle token expiration\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response?.status === 401) {\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\nexport interface RegisterData {\n  firstName: string;\n  lastName: string;\n  email: string;\n  password: string;\n}\n\nexport interface LoginData {\n  email: string;\n  password: string;\n}\n\nexport interface UpdateUserData {\n  firstName?: string;\n  lastName?: string;\n  email?: string;\n  password?: string;\n  profilePicture?: string;\n}\n\nexport interface User {\n  id: string;\n  firstName: string;\n  lastName: string;\n  email: string;\n  profilePicture?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Quote {\n  quote: string;\n  timestamp: string;\n}\n\n// Auth API\nexport const authAPI = {\n  register: (data: RegisterData) => api.post('/auth/register', data),\n  login: (data: LoginData) => api.post('/auth/login', data),\n};\n\n// Users API\nexport const usersAPI = {\n  getProfile: () => api.get('/users/me'),\n  updateProfile: (data: UpdateUserData) => api.put('/users/update', data),\n};\n\n// Quotes API\nexport const quotesAPI = {\n  getRandomQuote: () => api.get<Quote>('/quotes/random'),\n};\n\nexport default api;\n"], "names": [], "mappings": ";;;;;;;;;;AAEgB;AAFhB;;AAEA,MAAM,UAAU,6DAAmC;AAEnD,MAAM,MAAM,mJAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,qCAAqC;AACrC,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAC5B,MAAM,QAAQ,aAAa,OAAO,CAAC;IACnC,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,AAAC,UAAe,OAAN;IAC3C;IACA,OAAO;AACT;AAEA,0BAA0B;AAC1B,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC3B,CAAC,WAAa,UACd,CAAC;QACK;IAAJ,IAAI,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,MAAM,MAAK,KAAK;QAClC,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAuCK,MAAM,UAAU;IACrB,UAAU,CAAC,OAAuB,IAAI,IAAI,CAAC,kBAAkB;IAC7D,OAAO,CAAC,OAAoB,IAAI,IAAI,CAAC,eAAe;AACtD;AAGO,MAAM,WAAW;IACtB,YAAY,IAAM,IAAI,GAAG,CAAC;IAC1B,eAAe,CAAC,OAAyB,IAAI,GAAG,CAAC,iBAAiB;AACpE;AAGO,MAAM,YAAY;IACvB,gBAAgB,IAAM,IAAI,GAAG,CAAQ;AACvC;uCAEe", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/empty-projects/augment-small-project/frontend/src/app/login/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { authAPI, LoginData } from '@/services/api';\n\nexport default function LoginPage() {\n  const router = useRouter();\n  const { login } = useAuth();\n  const [formData, setFormData] = useState<LoginData>({\n    email: '',\n    password: '',\n  });\n  const [errors, setErrors] = useState<Record<string, string>>({});\n  const [isLoading, setIsLoading] = useState(false);\n\n  const validateForm = () => {\n    const newErrors: Record<string, string> = {};\n\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email is invalid';\n    }\n\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!validateForm()) return;\n\n    setIsLoading(true);\n    try {\n      const response = await authAPI.login(formData);\n      const { access_token, user } = response.data;\n      \n      login(access_token, user);\n      router.push('/home');\n    } catch (error: any) {\n      const message = error.response?.data?.message || 'Login failed';\n      setErrors({ general: message });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({ ...prev, [name]: '' }));\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div>\n          <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\n            Sign in to your account\n          </h2>\n        </div>\n        <form className=\"mt-8 space-y-6\" onSubmit={handleSubmit}>\n          {errors.general && (\n            <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\">\n              {errors.general}\n            </div>\n          )}\n          \n          <div className=\"space-y-4\">\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\n                Email\n              </label>\n              <input\n                id=\"email\"\n                name=\"email\"\n                type=\"email\"\n                value={formData.email}\n                onChange={handleChange}\n                className=\"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm\"\n                placeholder=\"Email address\"\n              />\n              {errors.email && <p className=\"mt-1 text-sm text-red-600\">{errors.email}</p>}\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\n                Password\n              </label>\n              <input\n                id=\"password\"\n                name=\"password\"\n                type=\"password\"\n                value={formData.password}\n                onChange={handleChange}\n                className=\"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm\"\n                placeholder=\"Password\"\n              />\n              {errors.password && <p className=\"mt-1 text-sm text-red-600\">{errors.password}</p>}\n            </div>\n          </div>\n\n          <div>\n            <button\n              type=\"submit\"\n              disabled={isLoading}\n              className=\"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50\"\n            >\n              {isLoading ? 'Signing in...' : 'Sign in'}\n            </button>\n          </div>\n\n          <div className=\"text-center\">\n            <Link href=\"/register\" className=\"text-indigo-600 hover:text-indigo-500\">\n              Don't have an account? Sign up\n            </Link>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,SAAS,IAAA,kJAAS;IACxB,MAAM,EAAE,KAAK,EAAE,GAAG,IAAA,6IAAO;IACzB,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,yKAAQ,EAAY;QAClD,OAAO;QACP,UAAU;IACZ;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,yKAAQ,EAAyB,CAAC;IAC9D,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yKAAQ,EAAC;IAE3C,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,UAAU,KAAK,GAAG;QACpB,OAAO,IAAI,CAAC,eAAe,IAAI,CAAC,SAAS,KAAK,GAAG;YAC/C,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,CAAC,SAAS,QAAQ,EAAE;YACtB,UAAU,QAAQ,GAAG;QACvB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;QAErB,aAAa;QACb,IAAI;YACF,MAAM,WAAW,MAAM,oIAAO,CAAC,KAAK,CAAC;YACrC,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,SAAS,IAAI;YAE5C,MAAM,cAAc;YACpB,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAY;gBACH,sBAAA;YAAhB,MAAM,UAAU,EAAA,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,OAAO,KAAI;YACjD,UAAU;gBAAE,SAAS;YAAQ;QAC/B,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM,CAAC;QAC/C,sCAAsC;QACtC,IAAI,MAAM,CAAC,KAAK,EAAE;YAChB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,KAAK,EAAE;gBAAG,CAAC;QAC5C;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;8BACC,cAAA,6LAAC;wBAAG,WAAU;kCAAyD;;;;;;;;;;;8BAIzE,6LAAC;oBAAK,WAAU;oBAAiB,UAAU;;wBACxC,OAAO,OAAO,kBACb,6LAAC;4BAAI,WAAU;sCACZ,OAAO,OAAO;;;;;;sCAInB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA0C;;;;;;sDAG3E,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,KAAK;4CACrB,UAAU;4CACV,WAAU;4CACV,aAAY;;;;;;wCAEb,OAAO,KAAK,kBAAI,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,KAAK;;;;;;;;;;;;8CAGzE,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA0C;;;;;;sDAG9E,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,QAAQ;4CACxB,UAAU;4CACV,WAAU;4CACV,aAAY;;;;;;wCAEb,OAAO,QAAQ,kBAAI,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,QAAQ;;;;;;;;;;;;;;;;;;sCAIjF,6LAAC;sCACC,cAAA,6LAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAU;0CAET,YAAY,kBAAkB;;;;;;;;;;;sCAInC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,0KAAI;gCAAC,MAAK;gCAAY,WAAU;0CAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrF;GA5HwB;;QACP,kJAAS;QACN,6IAAO;;;KAFH", "debugId": null}}]}