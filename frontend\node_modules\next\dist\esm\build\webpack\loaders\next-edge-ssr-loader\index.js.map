{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-edge-ssr-loader/index.ts"], "sourcesContent": ["import type webpack from 'webpack'\nimport type { SizeLimit } from '../../../../types'\nimport type { PagesRouteModuleOptions } from '../../../../server/route-modules/pages/module'\nimport type { MiddlewareConfig } from '../../../analysis/get-page-static-info'\n\nimport { getModuleBuildInfo } from '../get-module-build-info'\nimport { WEBPACK_RESOURCE_QUERIES } from '../../../../lib/constants'\nimport { RouteKind } from '../../../../server/route-kind'\nimport { normalizePagePath } from '../../../../shared/lib/page-path/normalize-page-path'\nimport { loadEntrypoint } from '../../../load-entrypoint'\nimport type { PAGE_TYPES } from '../../../../lib/page-types'\n\nexport type EdgeSSRLoaderQuery = {\n  absolute500Path: string\n  absoluteAppPath: string\n  absoluteDocumentPath: string\n  absoluteErrorPath: string\n  absolutePagePath: string\n  dev: boolean\n  isServerComponent: boolean\n  page: string\n  stringifiedConfig: string\n  appDirLoader?: string\n  pagesType: PAGE_TYPES\n  sriEnabled: boolean\n  cacheHandler?: string\n  cacheHandlers?: string\n  preferredRegion: string | string[] | undefined\n  middlewareConfig: string\n  serverActions?: {\n    bodySizeLimit?: SizeLimit\n    allowedOrigins?: string[]\n  }\n}\n\n/*\nFor pages SSR'd at the edge, we bundle them with the ESM version of Next in order to\nbenefit from the better tree-shaking and thus, smaller bundle sizes.\n\nThe absolute paths for _app, _error and _document, used in this loader, link to the regular CJS modules.\nThey are generated in `createPagesMapping` where we don't have access to `isEdgeRuntime`,\nso we have to do it here. It's not that bad because it keeps all references to ESM modules magic in this place.\n*/\nfunction swapDistFolderWithEsmDistFolder(path: string) {\n  return path.replace('next/dist/pages', 'next/dist/esm/pages')\n}\n\nfunction getRouteModuleOptions(page: string) {\n  const options: Omit<PagesRouteModuleOptions, 'userland' | 'components'> = {\n    definition: {\n      kind: RouteKind.PAGES,\n      page: normalizePagePath(page),\n      pathname: page,\n      // The following aren't used in production.\n      bundlePath: '',\n      filename: '',\n    },\n    // edge runtime doesn't read from distDir or projectDir\n    distDir: '',\n    relativeProjectDir: '',\n  }\n\n  return options\n}\n\nconst edgeSSRLoader: webpack.LoaderDefinitionFunction<EdgeSSRLoaderQuery> =\n  async function edgeSSRLoader(this) {\n    const {\n      page,\n      absolutePagePath,\n      absoluteAppPath,\n      absoluteDocumentPath,\n      absolute500Path,\n      absoluteErrorPath,\n      isServerComponent,\n      stringifiedConfig: stringifiedConfigBase64,\n      appDirLoader: appDirLoaderBase64,\n      pagesType,\n      cacheHandler,\n      cacheHandlers: cacheHandlersStringified,\n      preferredRegion,\n      middlewareConfig: middlewareConfigBase64,\n    } = this.getOptions()\n\n    const cacheHandlers = JSON.parse(cacheHandlersStringified || '{}')\n\n    if (!cacheHandlers.default) {\n      cacheHandlers.default = require.resolve(\n        '../../../../server/lib/cache-handlers/default.external'\n      )\n    }\n\n    const middlewareConfig: MiddlewareConfig = JSON.parse(\n      Buffer.from(middlewareConfigBase64, 'base64').toString()\n    )\n\n    const stringifiedConfig = Buffer.from(\n      stringifiedConfigBase64 || '',\n      'base64'\n    ).toString()\n    const appDirLoader = Buffer.from(\n      appDirLoaderBase64 || '',\n      'base64'\n    ).toString()\n    const isAppDir = pagesType === 'app'\n\n    const buildInfo = getModuleBuildInfo(this._module as any)\n    buildInfo.nextEdgeSSR = {\n      isServerComponent,\n      page: page,\n      isAppDir,\n    }\n    buildInfo.route = {\n      page,\n      absolutePagePath,\n      preferredRegion,\n      middlewareConfig,\n    }\n\n    const pagePath = this.utils.contextify(\n      this.context || this.rootContext,\n      absolutePagePath\n    )\n    const appPath = this.utils.contextify(\n      this.context || this.rootContext,\n      swapDistFolderWithEsmDistFolder(absoluteAppPath)\n    )\n    const errorPath = this.utils.contextify(\n      this.context || this.rootContext,\n      swapDistFolderWithEsmDistFolder(absoluteErrorPath)\n    )\n    const documentPath = this.utils.contextify(\n      this.context || this.rootContext,\n      swapDistFolderWithEsmDistFolder(absoluteDocumentPath)\n    )\n    const userland500Path = absolute500Path\n      ? this.utils.contextify(\n          this.context || this.rootContext,\n          swapDistFolderWithEsmDistFolder(absolute500Path)\n        )\n      : null\n\n    const stringifiedPagePath = JSON.stringify(pagePath)\n\n    const pageModPath = `${appDirLoader}${stringifiedPagePath.substring(\n      1,\n      stringifiedPagePath.length - 1\n    )}${isAppDir ? `?${WEBPACK_RESOURCE_QUERIES.edgeSSREntry}` : ''}`\n\n    if (isAppDir) {\n      return await loadEntrypoint(\n        'edge-ssr-app',\n        {\n          VAR_USERLAND: pageModPath,\n          VAR_PAGE: page,\n        },\n        {\n          nextConfig: stringifiedConfig,\n        },\n        {\n          incrementalCacheHandler: cacheHandler ?? null,\n        }\n      )\n    } else {\n      return await loadEntrypoint(\n        'edge-ssr',\n        {\n          VAR_USERLAND: pageModPath,\n          VAR_PAGE: page,\n          VAR_MODULE_DOCUMENT: documentPath,\n          VAR_MODULE_APP: appPath,\n          VAR_MODULE_GLOBAL_ERROR: errorPath,\n        },\n        {\n          nextConfig: stringifiedConfig,\n          pageRouteModuleOptions: JSON.stringify(getRouteModuleOptions(page)),\n          errorRouteModuleOptions: JSON.stringify(\n            getRouteModuleOptions('/_error')\n          ),\n          user500RouteModuleOptions: JSON.stringify(\n            getRouteModuleOptions('/500')\n          ),\n        },\n        {\n          userland500Page: userland500Path,\n          incrementalCacheHandler: cacheHandler ?? null,\n        }\n      )\n    }\n  }\nexport default edgeSSRLoader\n"], "names": ["getModuleBuildInfo", "WEBPACK_RESOURCE_QUERIES", "RouteKind", "normalizePagePath", "loadEntrypoint", "swapDistFolderWithEsmDistFolder", "path", "replace", "getRouteModuleOptions", "page", "options", "definition", "kind", "PAGES", "pathname", "bundlePath", "filename", "distDir", "relativeProjectDir", "edgeSSRLoader", "absolutePagePath", "absoluteAppPath", "absoluteDocumentPath", "absolute500Path", "absoluteErrorPath", "isServerComponent", "stringifiedConfig", "stringifiedConfigBase64", "appDirLoader", "appDirLoaderBase64", "pagesType", "cache<PERSON><PERSON><PERSON>", "cacheHandlers", "cacheHandlersStringified", "preferredRegion", "middlewareConfig", "middlewareConfigBase64", "getOptions", "JSON", "parse", "default", "require", "resolve", "<PERSON><PERSON><PERSON>", "from", "toString", "isAppDir", "buildInfo", "_module", "nextEdgeSSR", "route", "pagePath", "utils", "contextify", "context", "rootContext", "appPath", "errorPath", "documentPath", "userland500Path", "stringifiedPagePath", "stringify", "pageModPath", "substring", "length", "edgeSSREntry", "VAR_USERLAND", "VAR_PAGE", "nextConfig", "incremental<PERSON>ache<PERSON><PERSON><PERSON>", "VAR_MODULE_DOCUMENT", "VAR_MODULE_APP", "VAR_MODULE_GLOBAL_ERROR", "pageRouteModuleOptions", "errorRouteModuleOptions", "user500RouteModuleOptions", "userland500Page"], "mappings": "AAKA,SAASA,kBAAkB,QAAQ,2BAA0B;AAC7D,SAASC,wBAAwB,QAAQ,4BAA2B;AACpE,SAASC,SAAS,QAAQ,gCAA+B;AACzD,SAASC,iBAAiB,QAAQ,uDAAsD;AACxF,SAASC,cAAc,QAAQ,2BAA0B;AA0BzD;;;;;;;AAOA,GACA,SAASC,gCAAgCC,IAAY;IACnD,OAAOA,KAAKC,OAAO,CAAC,mBAAmB;AACzC;AAEA,SAASC,sBAAsBC,IAAY;IACzC,MAAMC,UAAoE;QACxEC,YAAY;YACVC,MAAMV,UAAUW,KAAK;YACrBJ,MAAMN,kBAAkBM;YACxBK,UAAUL;YACV,2CAA2C;YAC3CM,YAAY;YACZC,UAAU;QACZ;QACA,uDAAuD;QACvDC,SAAS;QACTC,oBAAoB;IACtB;IAEA,OAAOR;AACT;AAEA,MAAMS,gBACJ,eAAeA;IACb,MAAM,EACJV,IAAI,EACJW,gBAAgB,EAChBC,eAAe,EACfC,oBAAoB,EACpBC,eAAe,EACfC,iBAAiB,EACjBC,iBAAiB,EACjBC,mBAAmBC,uBAAuB,EAC1CC,cAAcC,kBAAkB,EAChCC,SAAS,EACTC,YAAY,EACZC,eAAeC,wBAAwB,EACvCC,eAAe,EACfC,kBAAkBC,sBAAsB,EACzC,GAAG,IAAI,CAACC,UAAU;IAEnB,MAAML,gBAAgBM,KAAKC,KAAK,CAACN,4BAA4B;IAE7D,IAAI,CAACD,cAAcQ,OAAO,EAAE;QAC1BR,cAAcQ,OAAO,GAAGC,QAAQC,OAAO,CACrC;IAEJ;IAEA,MAAMP,mBAAqCG,KAAKC,KAAK,CACnDI,OAAOC,IAAI,CAACR,wBAAwB,UAAUS,QAAQ;IAGxD,MAAMnB,oBAAoBiB,OAAOC,IAAI,CACnCjB,2BAA2B,IAC3B,UACAkB,QAAQ;IACV,MAAMjB,eAAee,OAAOC,IAAI,CAC9Bf,sBAAsB,IACtB,UACAgB,QAAQ;IACV,MAAMC,WAAWhB,cAAc;IAE/B,MAAMiB,YAAY/C,mBAAmB,IAAI,CAACgD,OAAO;IACjDD,UAAUE,WAAW,GAAG;QACtBxB;QACAhB,MAAMA;QACNqC;IACF;IACAC,UAAUG,KAAK,GAAG;QAChBzC;QACAW;QACAc;QACAC;IACF;IAEA,MAAMgB,WAAW,IAAI,CAACC,KAAK,CAACC,UAAU,CACpC,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,WAAW,EAChCnC;IAEF,MAAMoC,UAAU,IAAI,CAACJ,KAAK,CAACC,UAAU,CACnC,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,WAAW,EAChClD,gCAAgCgB;IAElC,MAAMoC,YAAY,IAAI,CAACL,KAAK,CAACC,UAAU,CACrC,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,WAAW,EAChClD,gCAAgCmB;IAElC,MAAMkC,eAAe,IAAI,CAACN,KAAK,CAACC,UAAU,CACxC,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,WAAW,EAChClD,gCAAgCiB;IAElC,MAAMqC,kBAAkBpC,kBACpB,IAAI,CAAC6B,KAAK,CAACC,UAAU,CACnB,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,WAAW,EAChClD,gCAAgCkB,oBAElC;IAEJ,MAAMqC,sBAAsBtB,KAAKuB,SAAS,CAACV;IAE3C,MAAMW,cAAc,GAAGlC,eAAegC,oBAAoBG,SAAS,CACjE,GACAH,oBAAoBI,MAAM,GAAG,KAC3BlB,WAAW,CAAC,CAAC,EAAE7C,yBAAyBgE,YAAY,EAAE,GAAG,IAAI;IAEjE,IAAInB,UAAU;QACZ,OAAO,MAAM1C,eACX,gBACA;YACE8D,cAAcJ;YACdK,UAAU1D;QACZ,GACA;YACE2D,YAAY1C;QACd,GACA;YACE2C,yBAAyBtC,gBAAgB;QAC3C;IAEJ,OAAO;QACL,OAAO,MAAM3B,eACX,YACA;YACE8D,cAAcJ;YACdK,UAAU1D;YACV6D,qBAAqBZ;YACrBa,gBAAgBf;YAChBgB,yBAAyBf;QAC3B,GACA;YACEW,YAAY1C;YACZ+C,wBAAwBnC,KAAKuB,SAAS,CAACrD,sBAAsBC;YAC7DiE,yBAAyBpC,KAAKuB,SAAS,CACrCrD,sBAAsB;YAExBmE,2BAA2BrC,KAAKuB,SAAS,CACvCrD,sBAAsB;QAE1B,GACA;YACEoE,iBAAiBjB;YACjBU,yBAAyBtC,gBAAgB;QAC3C;IAEJ;AACF;AACF,eAAeZ,cAAa", "ignoreList": [0]}