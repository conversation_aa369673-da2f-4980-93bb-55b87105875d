{"version": 3, "sources": ["../../../src/client/components/navigation-untracked.ts"], "sourcesContent": ["import { useContext } from 'react'\nimport { PathnameContext } from '../../shared/lib/hooks-client-context.shared-runtime'\n\n/**\n * This checks to see if the current render has any unknown route parameters.\n * It's used to trigger a different render path in the error boundary.\n *\n * @returns true if there are any unknown route parameters, false otherwise\n */\nfunction hasFallbackRouteParams(): boolean {\n  if (typeof window === 'undefined') {\n    // AsyncLocalStorage should not be included in the client bundle.\n    const { workUnitAsyncStorage } =\n      require('../../server/app-render/work-unit-async-storage.external') as typeof import('../../server/app-render/work-unit-async-storage.external')\n\n    const workUnitStore = workUnitAsyncStorage.getStore()\n    if (!workUnitStore) return false\n\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n        const fallbackParams = workUnitStore.fallbackRouteParams\n        return fallbackParams ? fallbackParams.size > 0 : false\n      case 'prerender-legacy':\n      case 'request':\n      case 'prerender-runtime':\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n\n    return false\n  }\n\n  return false\n}\n\n/**\n * This returns a `null` value if there are any unknown route parameters, and\n * otherwise returns the pathname from the context. This is an alternative to\n * `usePathname` that is used in the error boundary to avoid rendering the\n * error boundary when there are unknown route parameters. This doesn't throw\n * when accessed with unknown route parameters.\n *\n * @returns\n *\n * @internal\n */\nexport function useUntrackedPathname(): string | null {\n  // If there are any unknown route parameters we would typically throw\n  // an error, but this internal method allows us to return a null value instead\n  // for components that do not propagate the pathname to the static shell (like\n  // the error boundary).\n  if (hasFallbackRouteParams()) {\n    return null\n  }\n\n  // This shouldn't cause any issues related to conditional rendering because\n  // the environment will be consistent for the render.\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return useContext(PathnameContext)\n}\n"], "names": ["useContext", "PathnameContext", "hasFallbackRouteParams", "window", "workUnitAsyncStorage", "require", "workUnitStore", "getStore", "type", "fallbackP<PERSON><PERSON>", "fallbackRouteParams", "size", "useUntrackedPathname"], "mappings": "AAAA,SAASA,UAAU,QAAQ,QAAO;AAClC,SAASC,eAAe,QAAQ,uDAAsD;AAEtF;;;;;CAKC,GACD,SAASC;IACP,IAAI,OAAOC,WAAW,aAAa;QACjC,iEAAiE;QACjE,MAAM,EAAEC,oBAAoB,EAAE,GAC5BC,QAAQ;QAEV,MAAMC,gBAAgBF,qBAAqBG,QAAQ;QACnD,IAAI,CAACD,eAAe,OAAO;QAE3B,OAAQA,cAAcE,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAMC,iBAAiBH,cAAcI,mBAAmB;gBACxD,OAAOD,iBAAiBA,eAAeE,IAAI,GAAG,IAAI;YACpD,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH;YACF;gBACEL;QACJ;QAEA,OAAO;IACT;IAEA,OAAO;AACT;AAEA;;;;;;;;;;CAUC,GACD,OAAO,SAASM;IACd,qEAAqE;IACrE,8EAA8E;IAC9E,8EAA8E;IAC9E,uBAAuB;IACvB,IAAIV,0BAA0B;QAC5B,OAAO;IACT;IAEA,2EAA2E;IAC3E,qDAAqD;IACrD,sDAAsD;IACtD,OAAOF,WAAWC;AACpB", "ignoreList": [0]}