{"version": 3, "sources": ["../../src/build/entries.ts"], "sourcesContent": ["import type { ClientPagesLoaderOptions } from './webpack/loaders/next-client-pages-loader'\nimport type { MiddlewareLoaderOptions } from './webpack/loaders/next-middleware-loader'\nimport type { EdgeSSRLoaderQuery } from './webpack/loaders/next-edge-ssr-loader'\nimport type { EdgeAppRouteLoaderQuery } from './webpack/loaders/next-edge-app-route-loader'\nimport type { NextConfigComplete } from '../server/config-shared'\nimport type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport type {\n  MiddlewareConfig,\n  MiddlewareMatcher,\n  PageStaticInfo,\n} from './analysis/get-page-static-info'\nimport type { LoadedEnvFiles } from '@next/env'\nimport type { AppLoaderOptions } from './webpack/loaders/next-app-loader'\n\nimport { posix, join, dirname, extname, normalize } from 'path'\nimport { stringify } from 'querystring'\nimport fs from 'fs'\nimport {\n  PAGES_DIR_ALIAS,\n  ROOT_DIR_ALIAS,\n  APP_DIR_ALIAS,\n  WEBPACK_LAYERS,\n  INSTRUMENTATION_HOOK_FILENAME,\n} from '../lib/constants'\nimport { isAPIRoute } from '../lib/is-api-route'\nimport { isEdgeRuntime } from '../lib/is-edge-runtime'\nimport {\n  APP_CLIENT_INTERNALS,\n  RSC_MODULE_TYPES,\n  UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n} from '../shared/lib/constants'\nimport {\n  CLIENT_STATIC_FILES_RUNTIME_AMP,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN_APP,\n  CLIENT_STATIC_FILES_RUNTIME_POLYFILLS,\n  CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH,\n  COMPILER_NAMES,\n  EDGE_RUNTIME_WEBPACK,\n} from '../shared/lib/constants'\nimport type { CompilerNameValues } from '../shared/lib/constants'\nimport type { __ApiPreviewProps } from '../server/api-utils'\nimport {\n  isMiddlewareFile,\n  isMiddlewareFilename,\n  isInstrumentationHookFile,\n  isInstrumentationHookFilename,\n  reduceAppConfig,\n} from './utils'\nimport {\n  getAppPageStaticInfo,\n  getPageStaticInfo,\n} from './analysis/get-page-static-info'\nimport { normalizePathSep } from '../shared/lib/page-path/normalize-path-sep'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport type { ServerRuntime } from '../types'\nimport { normalizeAppPath } from '../shared/lib/router/utils/app-paths'\nimport { encodeMatchers } from './webpack/loaders/next-middleware-loader'\nimport type { EdgeFunctionLoaderOptions } from './webpack/loaders/next-edge-function-loader'\nimport { isAppRouteRoute } from '../lib/is-app-route-route'\nimport {\n  normalizeMetadataPageToRoute,\n  normalizeMetadataRoute,\n} from '../lib/metadata/get-metadata-route'\nimport { getRouteLoaderEntry } from './webpack/loaders/next-route-loader'\nimport {\n  isInternalComponent,\n  isNonRoutePagesPage,\n} from '../lib/is-internal-component'\nimport { isMetadataRouteFile } from '../lib/metadata/is-metadata-route'\nimport { RouteKind } from '../server/route-kind'\nimport { encodeToBase64 } from './webpack/loaders/utils'\nimport { normalizeCatchAllRoutes } from './normalize-catchall-routes'\nimport type { PageExtensions } from './page-extensions-type'\nimport type { MappedPages } from './build-context'\nimport { PAGE_TYPES } from '../lib/page-types'\nimport { isAppPageRoute } from '../lib/is-app-page-route'\nimport { recursiveReadDir } from '../lib/recursive-readdir'\nimport type { createValidFileMatcher } from '../server/lib/find-page-file'\nimport { isReservedPage } from './utils'\nimport { isParallelRouteSegment } from '../shared/lib/segment'\nimport { ensureLeadingSlash } from '../shared/lib/page-path/ensure-leading-slash'\n\n/**\n * Collect app pages, layouts, and default files from the app directory\n * @param appDir - The app directory path\n * @param validFileMatcher - File matcher object\n * @returns Object containing appPaths, layoutPaths, and defaultPaths arrays\n */\nexport async function collectAppFiles(\n  appDir: string,\n  validFileMatcher: ReturnType<typeof createValidFileMatcher>\n): Promise<{\n  appPaths: string[]\n  layoutPaths: string[]\n  defaultPaths: string[]\n}> {\n  // Collect app pages, layouts, and default files in a single directory traversal\n  const allAppFiles = await recursiveReadDir(appDir, {\n    pathnameFilter: (absolutePath) =>\n      validFileMatcher.isAppRouterPage(absolutePath) ||\n      validFileMatcher.isRootNotFound(absolutePath) ||\n      validFileMatcher.isAppLayoutPage(absolutePath) ||\n      validFileMatcher.isAppDefaultPage(absolutePath),\n    ignorePartFilter: (part) => part.startsWith('_'),\n  })\n\n  // Separate app pages, layouts, and defaults\n  const appPaths = allAppFiles.filter(\n    (absolutePath) =>\n      validFileMatcher.isAppRouterPage(absolutePath) ||\n      validFileMatcher.isRootNotFound(absolutePath)\n  )\n  const layoutPaths = allAppFiles.filter((absolutePath) =>\n    validFileMatcher.isAppLayoutPage(absolutePath)\n  )\n  const defaultPaths = allAppFiles.filter((absolutePath) =>\n    validFileMatcher.isAppDefaultPage(absolutePath)\n  )\n\n  return { appPaths, layoutPaths, defaultPaths }\n}\n\n/**\n * Collect pages from the pages directory\n * @param pagesDir - The pages directory path\n * @param validFileMatcher - File matcher object\n * @returns Array of page file paths\n */\nexport async function collectPagesFiles(\n  pagesDir: string,\n  validFileMatcher: ReturnType<typeof createValidFileMatcher>\n): Promise<string[]> {\n  return recursiveReadDir(pagesDir, {\n    pathnameFilter: validFileMatcher.isPageFile,\n  })\n}\n\n// Types for route processing\nexport type RouteInfo = {\n  route: string\n  filePath: string\n}\n\nexport type SlotInfo = {\n  name: string\n  parent: string\n}\n\n/**\n * Create a relative file path from a mapped page path\n * @param baseDir - The base directory path\n * @param filePath - The mapped file path (with private prefix)\n * @param prefix - The directory prefix ('pages' or 'app')\n * @param isSrcDir - Whether the project uses src directory structure\n * @returns The relative file path\n */\nexport function createRelativeFilePath(\n  baseDir: string,\n  filePath: string,\n  prefix: 'pages' | 'app',\n  isSrcDir: boolean\n): string {\n  const privatePrefix =\n    prefix === 'pages' ? 'private-next-pages' : 'private-next-app-dir'\n  const srcPrefix = isSrcDir ? 'src/' : ''\n  return join(\n    baseDir,\n    filePath.replace(new RegExp(`^${privatePrefix}/`), `${srcPrefix}${prefix}/`)\n  )\n}\n\n/**\n * Process pages routes from mapped pages\n * @param mappedPages - The mapped pages object\n * @param baseDir - The base directory path\n * @param isSrcDir - Whether the project uses src directory structure\n * @returns Object containing pageRoutes and pageApiRoutes\n */\nexport function processPageRoutes(\n  mappedPages: { [page: string]: string },\n  baseDir: string,\n  isSrcDir: boolean\n): {\n  pageRoutes: RouteInfo[]\n  pageApiRoutes: RouteInfo[]\n} {\n  const pageRoutes: RouteInfo[] = []\n  const pageApiRoutes: RouteInfo[] = []\n\n  for (const [route, filePath] of Object.entries(mappedPages)) {\n    const relativeFilePath = createRelativeFilePath(\n      baseDir,\n      filePath,\n      'pages',\n      isSrcDir\n    )\n\n    if (route.startsWith('/api/')) {\n      pageApiRoutes.push({\n        route: normalizePathSep(route),\n        filePath: relativeFilePath,\n      })\n    } else {\n      // Filter out _app, _error, _document\n      if (isReservedPage(route)) continue\n\n      pageRoutes.push({\n        route: normalizePathSep(route),\n        filePath: relativeFilePath,\n      })\n    }\n  }\n\n  return { pageRoutes, pageApiRoutes }\n}\n\n/**\n * Extract slots from app routes\n * @param mappedAppPages - The mapped app pages object\n * @returns Array of slot information\n */\nexport function extractSlotsFromAppRoutes(mappedAppPages: {\n  [page: string]: string\n}): SlotInfo[] {\n  const slots: SlotInfo[] = []\n\n  for (const [route] of Object.entries(mappedAppPages)) {\n    if (route === '/_not-found/page') continue\n\n    const segments = route.split('/')\n    for (let i = segments.length - 1; i >= 0; i--) {\n      const segment = segments[i]\n      if (isParallelRouteSegment(segment)) {\n        const parentPath = normalizeAppPath(segments.slice(0, i).join('/'))\n        const slotName = segment.slice(1)\n\n        // Check if the slot already exists\n        if (slots.some((s) => s.name === slotName && s.parent === parentPath))\n          continue\n\n        slots.push({\n          name: slotName,\n          parent: parentPath,\n        })\n        break\n      }\n    }\n  }\n\n  return slots\n}\n\n/**\n * Extract slots from default files\n * @param mappedDefaultFiles - The mapped default files object\n * @returns Array of slot information\n */\nexport function extractSlotsFromDefaultFiles(mappedDefaultFiles: {\n  [page: string]: string\n}): SlotInfo[] {\n  const slots: SlotInfo[] = []\n\n  for (const [route] of Object.entries(mappedDefaultFiles)) {\n    const segments = route.split('/')\n    for (let i = segments.length - 1; i >= 0; i--) {\n      const segment = segments[i]\n      if (isParallelRouteSegment(segment)) {\n        const parentPath = normalizeAppPath(segments.slice(0, i).join('/'))\n        const slotName = segment.slice(1)\n\n        // Check if the slot already exists\n        if (slots.some((s) => s.name === slotName && s.parent === parentPath))\n          continue\n\n        slots.push({\n          name: slotName,\n          parent: parentPath,\n        })\n        break\n      }\n    }\n  }\n\n  return slots\n}\n\n/**\n * Combine and deduplicate slot arrays using a Set\n * @param slotArrays - Arrays of slot information to combine\n * @returns Deduplicated array of slots\n */\nexport function combineSlots(...slotArrays: SlotInfo[][]): SlotInfo[] {\n  const slotSet = new Set<string>()\n  const result: SlotInfo[] = []\n\n  for (const slots of slotArrays) {\n    for (const slot of slots) {\n      const key = `${slot.name}:${slot.parent}`\n      if (!slotSet.has(key)) {\n        slotSet.add(key)\n        result.push(slot)\n      }\n    }\n  }\n\n  return result\n}\n\n/**\n * Process app routes from mapped app pages\n * @param mappedAppPages - The mapped app pages object\n * @param validFileMatcher - File matcher object\n * @param baseDir - The base directory path\n * @param isSrcDir - Whether the project uses src directory structure\n * @returns Array of route information\n */\nexport function processAppRoutes(\n  mappedAppPages: { [page: string]: string },\n  validFileMatcher: ReturnType<typeof createValidFileMatcher>,\n  baseDir: string,\n  isSrcDir: boolean\n): {\n  appRoutes: RouteInfo[]\n  appRouteHandlers: RouteInfo[]\n} {\n  const appRoutes: RouteInfo[] = []\n  const appRouteHandlers: RouteInfo[] = []\n\n  for (const [route, filePath] of Object.entries(mappedAppPages)) {\n    if (route === '/_not-found/page') continue\n\n    const relativeFilePath = createRelativeFilePath(\n      baseDir,\n      filePath,\n      'app',\n      isSrcDir\n    )\n\n    if (validFileMatcher.isAppRouterRoute(filePath)) {\n      appRouteHandlers.push({\n        route: normalizeAppPath(normalizePathSep(route)),\n        filePath: relativeFilePath,\n      })\n    } else {\n      appRoutes.push({\n        route: normalizeAppPath(normalizePathSep(route)),\n        filePath: relativeFilePath,\n      })\n    }\n  }\n\n  return { appRoutes, appRouteHandlers }\n}\n\n/**\n * Process layout routes from mapped app layouts\n * @param mappedAppLayouts - The mapped app layouts object\n * @param baseDir - The base directory path\n * @param isSrcDir - Whether the project uses src directory structure\n * @returns Array of layout route information\n */\nexport function processLayoutRoutes(\n  mappedAppLayouts: { [page: string]: string },\n  baseDir: string,\n  isSrcDir: boolean\n): RouteInfo[] {\n  const layoutRoutes: RouteInfo[] = []\n\n  for (const [route, filePath] of Object.entries(mappedAppLayouts)) {\n    const relativeFilePath = createRelativeFilePath(\n      baseDir,\n      filePath,\n      'app',\n      isSrcDir\n    )\n    layoutRoutes.push({\n      route: ensureLeadingSlash(\n        normalizeAppPath(normalizePathSep(route)).replace(/\\/layout$/, '')\n      ),\n      filePath: relativeFilePath,\n    })\n  }\n\n  return layoutRoutes\n}\n\nexport function sortByPageExts(pageExtensions: PageExtensions) {\n  return (a: string, b: string) => {\n    // prioritize entries according to pageExtensions order\n    // for consistency as fs order can differ across systems\n    // NOTE: this is reversed so preferred comes last and\n    // overrides prior\n    const aExt = extname(a)\n    const bExt = extname(b)\n\n    const aNoExt = a.substring(0, a.length - aExt.length)\n    const bNoExt = a.substring(0, b.length - bExt.length)\n\n    if (aNoExt !== bNoExt) return 0\n\n    // find extension index (skip '.' as pageExtensions doesn't have it)\n    const aExtIndex = pageExtensions.indexOf(aExt.substring(1))\n    const bExtIndex = pageExtensions.indexOf(bExt.substring(1))\n\n    return bExtIndex - aExtIndex\n  }\n}\n\nexport async function getStaticInfoIncludingLayouts({\n  isInsideAppDir,\n  pageExtensions,\n  pageFilePath,\n  appDir,\n  config: nextConfig,\n  isDev,\n  page,\n}: {\n  isInsideAppDir: boolean\n  pageExtensions: PageExtensions\n  pageFilePath: string\n  appDir: string | undefined\n  config: NextConfigComplete\n  isDev: boolean | undefined\n  page: string\n}): Promise<PageStaticInfo> {\n  // TODO: sync types for pages: PAGE_TYPES, ROUTER_TYPE, 'app' | 'pages', etc.\n  const pageType = isInsideAppDir ? PAGE_TYPES.APP : PAGE_TYPES.PAGES\n\n  const pageStaticInfo = await getPageStaticInfo({\n    nextConfig,\n    pageFilePath,\n    isDev,\n    page,\n    pageType,\n  })\n\n  if (pageStaticInfo.type === PAGE_TYPES.PAGES || !appDir) {\n    return pageStaticInfo\n  }\n\n  const segments = [pageStaticInfo]\n\n  // inherit from layout files only if it's a page route\n  if (isAppPageRoute(page)) {\n    const layoutFiles = []\n    const potentialLayoutFiles = pageExtensions.map((ext) => 'layout.' + ext)\n    let dir = dirname(pageFilePath)\n\n    // Uses startsWith to not include directories further up.\n    while (dir.startsWith(appDir)) {\n      for (const potentialLayoutFile of potentialLayoutFiles) {\n        const layoutFile = join(dir, potentialLayoutFile)\n        if (!fs.existsSync(layoutFile)) {\n          continue\n        }\n        layoutFiles.push(layoutFile)\n      }\n      // Walk up the directory tree\n      dir = join(dir, '..')\n    }\n\n    for (const layoutFile of layoutFiles) {\n      const layoutStaticInfo = await getAppPageStaticInfo({\n        nextConfig,\n        pageFilePath: layoutFile,\n        isDev,\n        page,\n        pageType: isInsideAppDir ? PAGE_TYPES.APP : PAGE_TYPES.PAGES,\n      })\n\n      segments.unshift(layoutStaticInfo)\n    }\n  }\n\n  const config = reduceAppConfig(segments)\n\n  return {\n    ...pageStaticInfo,\n    config,\n    runtime: config.runtime,\n    preferredRegion: config.preferredRegion,\n    maxDuration: config.maxDuration,\n  }\n}\n\ntype ObjectValue<T> = T extends { [key: string]: infer V } ? V : never\n\n/**\n * For a given page path removes the provided extensions.\n */\nexport function getPageFromPath(\n  pagePath: string,\n  pageExtensions: PageExtensions\n) {\n  let page = normalizePathSep(\n    pagePath.replace(new RegExp(`\\\\.+(${pageExtensions.join('|')})$`), '')\n  )\n\n  page = page.replace(/\\/index$/, '')\n\n  return page === '' ? '/' : page\n}\n\nexport function getPageFilePath({\n  absolutePagePath,\n  pagesDir,\n  appDir,\n  rootDir,\n}: {\n  absolutePagePath: string\n  pagesDir: string | undefined\n  appDir: string | undefined\n  rootDir: string\n}) {\n  if (absolutePagePath.startsWith(PAGES_DIR_ALIAS) && pagesDir) {\n    return absolutePagePath.replace(PAGES_DIR_ALIAS, pagesDir)\n  }\n\n  if (absolutePagePath.startsWith(APP_DIR_ALIAS) && appDir) {\n    return absolutePagePath.replace(APP_DIR_ALIAS, appDir)\n  }\n\n  if (absolutePagePath.startsWith(ROOT_DIR_ALIAS)) {\n    return absolutePagePath.replace(ROOT_DIR_ALIAS, rootDir)\n  }\n\n  return require.resolve(absolutePagePath)\n}\n\n/**\n * Creates a mapping of route to page file path for a given list of page paths.\n * For example ['/middleware.ts'] is turned into  { '/middleware': `${ROOT_DIR_ALIAS}/middleware.ts` }\n */\nexport async function createPagesMapping({\n  isDev,\n  pageExtensions,\n  pagePaths,\n  pagesType,\n  pagesDir,\n  appDir,\n}: {\n  isDev: boolean\n  pageExtensions: PageExtensions\n  pagePaths: string[]\n  pagesType: PAGE_TYPES\n  pagesDir: string | undefined\n  appDir: string | undefined\n}): Promise<MappedPages> {\n  const isAppRoute = pagesType === 'app'\n  const pages: MappedPages = {}\n  const promises = pagePaths.map<Promise<void>>(async (pagePath) => {\n    // Do not process .d.ts files as routes\n    if (pagePath.endsWith('.d.ts') && pageExtensions.includes('ts')) {\n      return\n    }\n\n    let pageKey = getPageFromPath(pagePath, pageExtensions)\n    if (isAppRoute) {\n      pageKey = pageKey.replace(/%5F/g, '_')\n      if (pageKey === '/not-found') {\n        pageKey = UNDERSCORE_NOT_FOUND_ROUTE_ENTRY\n      }\n    }\n\n    const normalizedPath = normalizePathSep(\n      join(\n        pagesType === 'pages'\n          ? PAGES_DIR_ALIAS\n          : pagesType === 'app'\n            ? APP_DIR_ALIAS\n            : ROOT_DIR_ALIAS,\n        pagePath\n      )\n    )\n\n    let route = pagesType === 'app' ? normalizeMetadataRoute(pageKey) : pageKey\n\n    if (\n      pagesType === 'app' &&\n      isMetadataRouteFile(pagePath, pageExtensions, true)\n    ) {\n      const filePath = join(appDir!, pagePath)\n      const staticInfo = await getPageStaticInfo({\n        nextConfig: {},\n        pageFilePath: filePath,\n        isDev,\n        page: pageKey,\n        pageType: pagesType,\n      })\n\n      route = normalizeMetadataPageToRoute(\n        route,\n        !!(staticInfo.generateImageMetadata || staticInfo.generateSitemaps)\n      )\n    }\n\n    pages[route] = normalizedPath\n  })\n\n  await Promise.all(promises)\n\n  switch (pagesType) {\n    case PAGE_TYPES.ROOT: {\n      return pages\n    }\n    case PAGE_TYPES.APP: {\n      const hasAppPages = Object.keys(pages).some((page) =>\n        page.endsWith('/page')\n      )\n      return {\n        // If there's any app pages existed, add a default /_not-found route as 404.\n        // If there's any custom /_not-found page, it will override the default one.\n        ...(hasAppPages && {\n          [UNDERSCORE_NOT_FOUND_ROUTE_ENTRY]: require.resolve(\n            'next/dist/client/components/builtin/global-not-found'\n          ),\n        }),\n        ...pages,\n      }\n    }\n    case PAGE_TYPES.PAGES: {\n      if (isDev) {\n        delete pages['/_app']\n        delete pages['/_error']\n        delete pages['/_document']\n      }\n\n      // In development we always alias these to allow Webpack to fallback to\n      // the correct source file so that HMR can work properly when a file is\n      // added or removed.\n      const root = isDev && pagesDir ? PAGES_DIR_ALIAS : 'next/dist/pages'\n\n      return {\n        '/_app': `${root}/_app`,\n        '/_error': `${root}/_error`,\n        '/_document': `${root}/_document`,\n        ...pages,\n      }\n    }\n    default: {\n      return {}\n    }\n  }\n}\n\nexport interface CreateEntrypointsParams {\n  buildId: string\n  config: NextConfigComplete\n  envFiles: LoadedEnvFiles\n  isDev?: boolean\n  pages: MappedPages\n  pagesDir?: string\n  previewMode: __ApiPreviewProps\n  rootDir: string\n  rootPaths?: MappedPages\n  appDir?: string\n  appPaths?: MappedPages\n  pageExtensions: PageExtensions\n  hasInstrumentationHook?: boolean\n}\n\nexport function getEdgeServerEntry(opts: {\n  rootDir: string\n  absolutePagePath: string\n  buildId: string\n  bundlePath: string\n  config: NextConfigComplete\n  isDev: boolean\n  isServerComponent: boolean\n  page: string\n  pages: MappedPages\n  middleware?: Partial<MiddlewareConfig>\n  pagesType: PAGE_TYPES\n  appDirLoader?: string\n  hasInstrumentationHook?: boolean\n  preferredRegion: string | string[] | undefined\n  middlewareConfig?: MiddlewareConfig\n}) {\n  if (\n    opts.pagesType === 'app' &&\n    isAppRouteRoute(opts.page) &&\n    opts.appDirLoader\n  ) {\n    const loaderParams: EdgeAppRouteLoaderQuery = {\n      absolutePagePath: opts.absolutePagePath,\n      page: opts.page,\n      appDirLoader: Buffer.from(opts.appDirLoader || '').toString('base64'),\n      nextConfig: Buffer.from(JSON.stringify(opts.config)).toString('base64'),\n      preferredRegion: opts.preferredRegion,\n      middlewareConfig: Buffer.from(\n        JSON.stringify(opts.middlewareConfig || {})\n      ).toString('base64'),\n      cacheHandlers: JSON.stringify(\n        opts.config.experimental.cacheHandlers || {}\n      ),\n    }\n\n    return {\n      import: `next-edge-app-route-loader?${stringify(loaderParams)}!`,\n      layer: WEBPACK_LAYERS.reactServerComponents,\n    }\n  }\n\n  if (isMiddlewareFile(opts.page)) {\n    const loaderParams: MiddlewareLoaderOptions = {\n      absolutePagePath: opts.absolutePagePath,\n      page: opts.page,\n      rootDir: opts.rootDir,\n      matchers: opts.middleware?.matchers\n        ? encodeMatchers(opts.middleware.matchers)\n        : '',\n      preferredRegion: opts.preferredRegion,\n      middlewareConfig: Buffer.from(\n        JSON.stringify(opts.middlewareConfig || {})\n      ).toString('base64'),\n    }\n\n    return {\n      import: `next-middleware-loader?${stringify(loaderParams)}!`,\n      layer: WEBPACK_LAYERS.middleware,\n    }\n  }\n\n  if (isAPIRoute(opts.page)) {\n    const loaderParams: EdgeFunctionLoaderOptions = {\n      absolutePagePath: opts.absolutePagePath,\n      page: opts.page,\n      rootDir: opts.rootDir,\n      preferredRegion: opts.preferredRegion,\n      middlewareConfig: Buffer.from(\n        JSON.stringify(opts.middlewareConfig || {})\n      ).toString('base64'),\n    }\n\n    return {\n      import: `next-edge-function-loader?${stringify(loaderParams)}!`,\n      layer: WEBPACK_LAYERS.apiEdge,\n    }\n  }\n\n  const loaderParams: EdgeSSRLoaderQuery = {\n    absolute500Path: opts.pages['/500'] || '',\n    absoluteAppPath: opts.pages['/_app'],\n    absoluteDocumentPath: opts.pages['/_document'],\n    absoluteErrorPath: opts.pages['/_error'],\n    absolutePagePath: opts.absolutePagePath,\n    dev: opts.isDev,\n    isServerComponent: opts.isServerComponent,\n    page: opts.page,\n    stringifiedConfig: Buffer.from(JSON.stringify(opts.config)).toString(\n      'base64'\n    ),\n    pagesType: opts.pagesType,\n    appDirLoader: Buffer.from(opts.appDirLoader || '').toString('base64'),\n    sriEnabled: !opts.isDev && !!opts.config.experimental.sri?.algorithm,\n    cacheHandler: opts.config.cacheHandler,\n    preferredRegion: opts.preferredRegion,\n    middlewareConfig: Buffer.from(\n      JSON.stringify(opts.middlewareConfig || {})\n    ).toString('base64'),\n    serverActions: opts.config.experimental.serverActions,\n    cacheHandlers: JSON.stringify(opts.config.experimental.cacheHandlers || {}),\n  }\n\n  return {\n    import: `next-edge-ssr-loader?${JSON.stringify(loaderParams)}!`,\n    // The Edge bundle includes the server in its entrypoint, so it has to\n    // be in the SSR layer — we later convert the page request to the RSC layer\n    // via a webpack rule.\n    layer: opts.appDirLoader ? WEBPACK_LAYERS.serverSideRendering : undefined,\n  }\n}\n\nexport function getInstrumentationEntry(opts: {\n  absolutePagePath: string\n  isEdgeServer: boolean\n  isDev: boolean\n}) {\n  // the '../' is needed to make sure the file is not chunked\n  const filename = `${\n    opts.isEdgeServer ? 'edge-' : opts.isDev ? '' : '../'\n  }${INSTRUMENTATION_HOOK_FILENAME}.js`\n\n  return {\n    import: opts.absolutePagePath,\n    filename,\n    layer: WEBPACK_LAYERS.instrument,\n  }\n}\n\nexport function getAppLoader() {\n  return process.env.BUILTIN_APP_LOADER\n    ? `builtin:next-app-loader`\n    : 'next-app-loader'\n}\n\nexport function getAppEntry(opts: Readonly<AppLoaderOptions>) {\n  if (process.env.NEXT_RSPACK && process.env.BUILTIN_APP_LOADER) {\n    ;(opts as any).projectRoot = normalize(join(__dirname, '../../..'))\n  }\n  return {\n    import: `${getAppLoader()}?${stringify(opts)}!`,\n    layer: WEBPACK_LAYERS.reactServerComponents,\n  }\n}\n\nexport function getClientEntry(opts: {\n  absolutePagePath: string\n  page: string\n}) {\n  const loaderOptions: ClientPagesLoaderOptions = {\n    absolutePagePath: opts.absolutePagePath,\n    page: opts.page,\n  }\n\n  const pageLoader = `next-client-pages-loader?${stringify(loaderOptions)}!`\n\n  // Make sure next/router is a dependency of _app or else chunk splitting\n  // might cause the router to not be able to load causing hydration\n  // to fail\n  return opts.page === '/_app'\n    ? [pageLoader, require.resolve('../client/router')]\n    : pageLoader\n}\n\nexport function runDependingOnPageType<T>(params: {\n  onClient: () => T\n  onEdgeServer: () => T\n  onServer: () => T\n  page: string\n  pageRuntime: ServerRuntime\n  pageType?: PAGE_TYPES\n}): void {\n  if (\n    params.pageType === PAGE_TYPES.ROOT &&\n    isInstrumentationHookFile(params.page)\n  ) {\n    params.onServer()\n    params.onEdgeServer()\n    return\n  }\n\n  if (isMiddlewareFile(params.page)) {\n    if (params.pageRuntime === 'nodejs') {\n      params.onServer()\n      return\n    } else {\n      params.onEdgeServer()\n      return\n    }\n  }\n\n  if (isAPIRoute(params.page)) {\n    if (isEdgeRuntime(params.pageRuntime)) {\n      params.onEdgeServer()\n      return\n    }\n\n    params.onServer()\n    return\n  }\n  if (params.page === '/_document') {\n    params.onServer()\n    return\n  }\n  if (\n    params.page === '/_app' ||\n    params.page === '/_error' ||\n    params.page === '/404' ||\n    params.page === '/500'\n  ) {\n    params.onClient()\n    params.onServer()\n    return\n  }\n  if (isEdgeRuntime(params.pageRuntime)) {\n    params.onClient()\n    params.onEdgeServer()\n    return\n  }\n\n  params.onClient()\n  params.onServer()\n  return\n}\n\nexport async function createEntrypoints(\n  params: CreateEntrypointsParams\n): Promise<{\n  client: webpack.EntryObject\n  server: webpack.EntryObject\n  edgeServer: webpack.EntryObject\n  middlewareMatchers: undefined\n}> {\n  const {\n    config,\n    pages,\n    pagesDir,\n    isDev,\n    rootDir,\n    rootPaths,\n    appDir,\n    appPaths,\n    pageExtensions,\n  } = params\n  const edgeServer: webpack.EntryObject = {}\n  const server: webpack.EntryObject = {}\n  const client: webpack.EntryObject = {}\n  let middlewareMatchers: MiddlewareMatcher[] | undefined = undefined\n\n  let appPathsPerRoute: Record<string, string[]> = {}\n  if (appDir && appPaths) {\n    for (const pathname in appPaths) {\n      const normalizedPath = normalizeAppPath(pathname)\n      const actualPath = appPaths[pathname]\n      if (!appPathsPerRoute[normalizedPath]) {\n        appPathsPerRoute[normalizedPath] = []\n      }\n      appPathsPerRoute[normalizedPath].push(\n        // TODO-APP: refactor to pass the page path from createPagesMapping instead.\n        getPageFromPath(actualPath, pageExtensions).replace(APP_DIR_ALIAS, '')\n      )\n    }\n\n    // TODO: find a better place to do this\n    normalizeCatchAllRoutes(appPathsPerRoute)\n\n    // Make sure to sort parallel routes to make the result deterministic.\n    appPathsPerRoute = Object.fromEntries(\n      Object.entries(appPathsPerRoute).map(([k, v]) => [k, v.sort()])\n    )\n  }\n\n  const getEntryHandler =\n    (mappings: MappedPages, pagesType: PAGE_TYPES): ((page: string) => void) =>\n    async (page) => {\n      const bundleFile = normalizePagePath(page)\n      const clientBundlePath = posix.join(pagesType, bundleFile)\n      const serverBundlePath =\n        pagesType === PAGE_TYPES.PAGES\n          ? posix.join('pages', bundleFile)\n          : pagesType === PAGE_TYPES.APP\n            ? posix.join('app', bundleFile)\n            : bundleFile.slice(1)\n\n      const absolutePagePath = mappings[page]\n\n      // Handle paths that have aliases\n      const pageFilePath = getPageFilePath({\n        absolutePagePath,\n        pagesDir,\n        appDir,\n        rootDir,\n      })\n\n      const isInsideAppDir =\n        !!appDir &&\n        (absolutePagePath.startsWith(APP_DIR_ALIAS) ||\n          absolutePagePath.startsWith(appDir))\n\n      const staticInfo: PageStaticInfo = await getStaticInfoIncludingLayouts({\n        isInsideAppDir,\n        pageExtensions,\n        pageFilePath,\n        appDir,\n        config,\n        isDev,\n        page,\n      })\n\n      // TODO(timneutkens): remove this\n      const isServerComponent =\n        isInsideAppDir && staticInfo.rsc !== RSC_MODULE_TYPES.client\n\n      if (isMiddlewareFile(page)) {\n        middlewareMatchers = staticInfo.middleware?.matchers ?? [\n          { regexp: '.*', originalSource: '/:path*' },\n        ]\n      }\n\n      const isInstrumentation =\n        isInstrumentationHookFile(page) && pagesType === PAGE_TYPES.ROOT\n\n      runDependingOnPageType({\n        page,\n        pageRuntime: staticInfo.runtime,\n        pageType: pagesType,\n        onClient: () => {\n          if (isServerComponent || isInsideAppDir) {\n            // We skip the initial entries for server component pages and let the\n            // server compiler inject them instead.\n          } else {\n            client[clientBundlePath] = getClientEntry({\n              absolutePagePath,\n              page,\n            })\n          }\n        },\n        onServer: () => {\n          if (pagesType === 'app' && appDir) {\n            const matchedAppPaths = appPathsPerRoute[normalizeAppPath(page)]\n            server[serverBundlePath] = getAppEntry({\n              page,\n              name: serverBundlePath,\n              pagePath: absolutePagePath,\n              appDir,\n              appPaths: matchedAppPaths,\n              pageExtensions,\n              basePath: config.basePath,\n              assetPrefix: config.assetPrefix,\n              nextConfigOutput: config.output,\n              preferredRegion: staticInfo.preferredRegion,\n              middlewareConfig: encodeToBase64(staticInfo.middleware || {}),\n              isGlobalNotFoundEnabled: config.experimental.globalNotFound\n                ? true\n                : undefined,\n            })\n          } else if (isInstrumentation) {\n            server[serverBundlePath.replace('src/', '')] =\n              getInstrumentationEntry({\n                absolutePagePath,\n                isEdgeServer: false,\n                isDev: false,\n              })\n          } else if (isMiddlewareFile(page)) {\n            server[serverBundlePath.replace('src/', '')] = getEdgeServerEntry({\n              ...params,\n              rootDir,\n              absolutePagePath: absolutePagePath,\n              bundlePath: clientBundlePath,\n              isDev: false,\n              isServerComponent,\n              page,\n              middleware: staticInfo?.middleware,\n              pagesType,\n              preferredRegion: staticInfo.preferredRegion,\n              middlewareConfig: staticInfo.middleware,\n            })\n          } else if (isAPIRoute(page)) {\n            server[serverBundlePath] = [\n              getRouteLoaderEntry({\n                kind: RouteKind.PAGES_API,\n                page,\n                absolutePagePath,\n                preferredRegion: staticInfo.preferredRegion,\n                middlewareConfig: staticInfo.middleware || {},\n              }),\n            ]\n          } else if (\n            !isMiddlewareFile(page) &&\n            !isInternalComponent(absolutePagePath) &&\n            !isNonRoutePagesPage(page)\n          ) {\n            server[serverBundlePath] = [\n              getRouteLoaderEntry({\n                kind: RouteKind.PAGES,\n                page,\n                pages,\n                absolutePagePath,\n                preferredRegion: staticInfo.preferredRegion,\n                middlewareConfig: staticInfo.middleware ?? {},\n              }),\n            ]\n          } else {\n            server[serverBundlePath] = [absolutePagePath]\n          }\n        },\n        onEdgeServer: () => {\n          let appDirLoader: string = ''\n          if (isInstrumentation) {\n            edgeServer[serverBundlePath.replace('src/', '')] =\n              getInstrumentationEntry({\n                absolutePagePath,\n                isEdgeServer: true,\n                isDev: false,\n              })\n          } else {\n            if (pagesType === 'app') {\n              const matchedAppPaths = appPathsPerRoute[normalizeAppPath(page)]\n              appDirLoader = getAppEntry({\n                name: serverBundlePath,\n                page,\n                pagePath: absolutePagePath,\n                appDir: appDir!,\n                appPaths: matchedAppPaths,\n                pageExtensions,\n                basePath: config.basePath,\n                assetPrefix: config.assetPrefix,\n                nextConfigOutput: config.output,\n                // This isn't used with edge as it needs to be set on the entry module, which will be the `edgeServerEntry` instead.\n                // Still passing it here for consistency.\n                preferredRegion: staticInfo.preferredRegion,\n                middlewareConfig: Buffer.from(\n                  JSON.stringify(staticInfo.middleware || {})\n                ).toString('base64'),\n                isGlobalNotFoundEnabled: config.experimental.globalNotFound\n                  ? true\n                  : undefined,\n              }).import\n            }\n            edgeServer[serverBundlePath] = getEdgeServerEntry({\n              ...params,\n              rootDir,\n              absolutePagePath: absolutePagePath,\n              bundlePath: clientBundlePath,\n              isDev: false,\n              isServerComponent,\n              page,\n              middleware: staticInfo?.middleware,\n              pagesType,\n              appDirLoader,\n              preferredRegion: staticInfo.preferredRegion,\n              middlewareConfig: staticInfo.middleware,\n            })\n          }\n        },\n      })\n    }\n\n  const promises: Promise<void[]>[] = []\n\n  if (appPaths) {\n    const entryHandler = getEntryHandler(appPaths, PAGE_TYPES.APP)\n    promises.push(Promise.all(Object.keys(appPaths).map(entryHandler)))\n  }\n  if (rootPaths) {\n    promises.push(\n      Promise.all(\n        Object.keys(rootPaths).map(getEntryHandler(rootPaths, PAGE_TYPES.ROOT))\n      )\n    )\n  }\n  promises.push(\n    Promise.all(\n      Object.keys(pages).map(getEntryHandler(pages, PAGE_TYPES.PAGES))\n    )\n  )\n\n  await Promise.all(promises)\n\n  // Optimization: If there's only one instrumentation hook in edge compiler, which means there's no edge server entry.\n  // We remove the edge instrumentation entry from edge compiler as it can be pure server side.\n  if (edgeServer.instrumentation && Object.keys(edgeServer).length === 1) {\n    delete edgeServer.instrumentation\n  }\n\n  return {\n    client,\n    server,\n    edgeServer,\n    middlewareMatchers,\n  }\n}\n\nexport function finalizeEntrypoint({\n  name,\n  compilerType,\n  value,\n  isServerComponent,\n  hasAppDir,\n}: {\n  compilerType: CompilerNameValues\n  name: string\n  value: ObjectValue<webpack.EntryObject>\n  isServerComponent?: boolean\n  hasAppDir?: boolean\n}): ObjectValue<webpack.EntryObject> {\n  const entry =\n    typeof value !== 'object' || Array.isArray(value)\n      ? { import: value }\n      : value\n\n  const isApi = name.startsWith('pages/api/')\n  const isInstrumentation = isInstrumentationHookFilename(name)\n\n  switch (compilerType) {\n    case COMPILER_NAMES.server: {\n      const layer = isApi\n        ? WEBPACK_LAYERS.apiNode\n        : isInstrumentation\n          ? WEBPACK_LAYERS.instrument\n          : isServerComponent\n            ? WEBPACK_LAYERS.reactServerComponents\n            : name.startsWith('pages/')\n              ? WEBPACK_LAYERS.pagesDirNode\n              : undefined\n\n      return {\n        publicPath: isApi ? '' : undefined,\n        runtime: isApi ? 'webpack-api-runtime' : 'webpack-runtime',\n        layer,\n        ...entry,\n      }\n    }\n    case COMPILER_NAMES.edgeServer: {\n      return {\n        layer: isApi\n          ? WEBPACK_LAYERS.apiEdge\n          : isMiddlewareFilename(name) || isInstrumentation\n            ? WEBPACK_LAYERS.middleware\n            : name.startsWith('pages/')\n              ? WEBPACK_LAYERS.pagesDirEdge\n              : undefined,\n        library: { name: ['_ENTRIES', `middleware_[name]`], type: 'assign' },\n        runtime: EDGE_RUNTIME_WEBPACK,\n        asyncChunks: false,\n        ...entry,\n      }\n    }\n    case COMPILER_NAMES.client: {\n      const isAppLayer =\n        hasAppDir &&\n        (name === CLIENT_STATIC_FILES_RUNTIME_MAIN_APP ||\n          name === APP_CLIENT_INTERNALS ||\n          name.startsWith('app/'))\n\n      if (\n        // Client special cases\n        name !== CLIENT_STATIC_FILES_RUNTIME_POLYFILLS &&\n        name !== CLIENT_STATIC_FILES_RUNTIME_MAIN &&\n        name !== CLIENT_STATIC_FILES_RUNTIME_MAIN_APP &&\n        name !== CLIENT_STATIC_FILES_RUNTIME_AMP &&\n        name !== CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH\n      ) {\n        if (isAppLayer) {\n          return {\n            dependOn: CLIENT_STATIC_FILES_RUNTIME_MAIN_APP,\n            layer: WEBPACK_LAYERS.appPagesBrowser,\n            ...entry,\n          }\n        }\n\n        return {\n          dependOn:\n            name.startsWith('pages/') && name !== 'pages/_app'\n              ? 'pages/_app'\n              : CLIENT_STATIC_FILES_RUNTIME_MAIN,\n          layer: WEBPACK_LAYERS.pagesDirBrowser,\n          ...entry,\n        }\n      }\n\n      if (isAppLayer) {\n        return {\n          layer: WEBPACK_LAYERS.appPagesBrowser,\n          ...entry,\n        }\n      }\n\n      return {\n        layer: WEBPACK_LAYERS.pagesDirBrowser,\n        ...entry,\n      }\n    }\n    default:\n      return compilerType satisfies never\n  }\n}\n"], "names": ["collectAppFiles", "collectPagesFiles", "combineSlots", "createEntrypoints", "createPagesMapping", "createRelativeFilePath", "extractSlotsFromAppRoutes", "extractSlotsFromDefaultFiles", "finalizeEntrypoint", "getAppEntry", "getApp<PERSON><PERSON>der", "getClientEntry", "getEdgeServerEntry", "getInstrumentationEntry", "getPageFilePath", "getPageFromPath", "getStaticInfoIncludingLayouts", "processAppRoutes", "processLayoutRoutes", "processPageRoutes", "runDependingOnPageType", "sortByPageExts", "appDir", "validFile<PERSON><PERSON><PERSON>", "allAppFiles", "recursiveReadDir", "pathnameFilter", "absolutePath", "isAppRouterPage", "isRootNotFound", "isAppLayoutPage", "isAppDefaultPage", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "part", "startsWith", "appPaths", "filter", "layoutPaths", "defaultPaths", "pagesDir", "isPageFile", "baseDir", "filePath", "prefix", "isSrcDir", "privatePrefix", "srcPrefix", "join", "replace", "RegExp", "mappedPages", "pageRoutes", "pageApiRoutes", "route", "Object", "entries", "relativeFilePath", "push", "normalizePathSep", "isReservedPage", "mappedAppPages", "slots", "segments", "split", "i", "length", "segment", "isParallelRouteSegment", "parentPath", "normalizeAppPath", "slice", "slotName", "some", "s", "name", "parent", "mappedDefaultFiles", "slotArrays", "slotSet", "Set", "result", "slot", "key", "has", "add", "appRoutes", "appRouteHandlers", "isAppRouterRoute", "mappedAppLayouts", "layoutRoutes", "ensureLeadingSlash", "pageExtensions", "a", "b", "aExt", "extname", "bExt", "aNoExt", "substring", "bNoExt", "aExtIndex", "indexOf", "bExtIndex", "isInsideAppDir", "pageFilePath", "config", "nextConfig", "isDev", "page", "pageType", "PAGE_TYPES", "APP", "PAGES", "pageStaticInfo", "getPageStaticInfo", "type", "isAppPageRoute", "layoutFiles", "potentialLayoutFiles", "map", "ext", "dir", "dirname", "potentialLayoutFile", "layoutFile", "fs", "existsSync", "layoutStaticInfo", "getAppPageStaticInfo", "unshift", "reduceAppConfig", "runtime", "preferredRegion", "maxDuration", "pagePath", "absolutePagePath", "rootDir", "PAGES_DIR_ALIAS", "APP_DIR_ALIAS", "ROOT_DIR_ALIAS", "require", "resolve", "pagePaths", "pagesType", "isAppRoute", "pages", "promises", "endsWith", "includes", "page<PERSON><PERSON>", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "normalizedPath", "normalizeMetadataRoute", "isMetadataRouteFile", "staticInfo", "normalizeMetadataPageToRoute", "generateImageMetadata", "generateSitemaps", "Promise", "all", "ROOT", "hasAppPages", "keys", "root", "opts", "isAppRouteRoute", "appDirLoader", "loaderParams", "<PERSON><PERSON><PERSON>", "from", "toString", "JSON", "stringify", "middlewareConfig", "cacheHandlers", "experimental", "import", "layer", "WEBPACK_LAYERS", "reactServerComponents", "isMiddlewareFile", "matchers", "middleware", "encodeMatchers", "isAPIRoute", "apiEdge", "absolute500Path", "absoluteAppPath", "absoluteDocumentPath", "absoluteErrorPath", "dev", "isServerComponent", "stringifiedConfig", "sriEnabled", "sri", "algorithm", "cache<PERSON><PERSON><PERSON>", "serverActions", "serverSideRendering", "undefined", "filename", "isEdgeServer", "INSTRUMENTATION_HOOK_FILENAME", "instrument", "process", "env", "BUILTIN_APP_LOADER", "NEXT_RSPACK", "projectRoot", "normalize", "__dirname", "loaderOptions", "page<PERSON><PERSON>der", "params", "isInstrumentationHookFile", "onServer", "onEdgeServer", "pageRuntime", "isEdgeRuntime", "onClient", "rootPaths", "edgeServer", "server", "client", "middlewareMatchers", "appPathsPerRoute", "pathname", "actualPath", "normalizeCatchAllRoutes", "fromEntries", "k", "v", "sort", "getEntryHandler", "mappings", "bundleFile", "normalizePagePath", "clientBundlePath", "posix", "serverBundlePath", "rsc", "RSC_MODULE_TYPES", "regexp", "originalSource", "isInstrumentation", "matchedAppPaths", "basePath", "assetPrefix", "nextConfigOutput", "output", "encodeToBase64", "isGlobalNotFoundEnabled", "globalNotFound", "bundlePath", "getRouteLoaderEntry", "kind", "RouteKind", "PAGES_API", "isInternalComponent", "isNonRoutePagesPage", "<PERSON><PERSON><PERSON><PERSON>", "instrumentation", "compilerType", "value", "hasAppDir", "entry", "Array", "isArray", "isApi", "isInstrumentationHookFilename", "COMPILER_NAMES", "apiNode", "pagesDirNode", "publicPath", "isMiddlewareFilename", "pagesDirEdge", "library", "EDGE_RUNTIME_WEBPACK", "asyncChunks", "isApp<PERSON><PERSON>er", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "APP_CLIENT_INTERNALS", "CLIENT_STATIC_FILES_RUNTIME_POLYFILLS", "CLIENT_STATIC_FILES_RUNTIME_MAIN", "CLIENT_STATIC_FILES_RUNTIME_AMP", "CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH", "dependOn", "appPagesBrowser", "pagesDirBrowser"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAyFsBA,eAAe;eAAfA;;IAwCAC,iBAAiB;eAAjBA;;IAmKNC,YAAY;eAAZA;;IAmlBMC,iBAAiB;eAAjBA;;IAjWAC,kBAAkB;eAAlBA;;IAzXNC,sBAAsB;eAAtBA;;IAiEAC,yBAAyB;eAAzBA;;IAoCAC,4BAA4B;eAA5BA;;IAi4BAC,kBAAkB;eAAlBA;;IAtWAC,WAAW;eAAXA;;IANAC,YAAY;eAAZA;;IAgBAC,cAAc;eAAdA;;IAjJAC,kBAAkB;eAAlBA;;IAgHAC,uBAAuB;eAAvBA;;IA9QAC,eAAe;eAAfA;;IAbAC,eAAe;eAAfA;;IAlFMC,6BAA6B;eAA7BA;;IA5FNC,gBAAgB;eAAhBA;;IA6CAC,mBAAmB;eAAnBA;;IAvLAC,iBAAiB;eAAjBA;;IAuoBAC,sBAAsB;eAAtBA;;IAvbAC,cAAc;eAAdA;;;sBArXyC;6BAC/B;2DACX;2BAOR;4BACoB;+BACG;4BAKvB;uBAkBA;mCAIA;kCAC0B;mCACC;0BAED;sCACF;iCAEC;kCAIzB;iCAC6B;qCAI7B;iCAC6B;2BACV;wBACK;yCACS;2BAGb;gCACI;kCACE;yBAGM;oCACJ;;;;;;AAQ5B,eAAerB,gBACpBsB,MAAc,EACdC,gBAA2D;IAM3D,gFAAgF;IAChF,MAAMC,cAAc,MAAMC,IAAAA,kCAAgB,EAACH,QAAQ;QACjDI,gBAAgB,CAACC,eACfJ,iBAAiBK,eAAe,CAACD,iBACjCJ,iBAAiBM,cAAc,CAACF,iBAChCJ,iBAAiBO,eAAe,CAACH,iBACjCJ,iBAAiBQ,gBAAgB,CAACJ;QACpCK,kBAAkB,CAACC,OAASA,KAAKC,UAAU,CAAC;IAC9C;IAEA,4CAA4C;IAC5C,MAAMC,WAAWX,YAAYY,MAAM,CACjC,CAACT,eACCJ,iBAAiBK,eAAe,CAACD,iBACjCJ,iBAAiBM,cAAc,CAACF;IAEpC,MAAMU,cAAcb,YAAYY,MAAM,CAAC,CAACT,eACtCJ,iBAAiBO,eAAe,CAACH;IAEnC,MAAMW,eAAed,YAAYY,MAAM,CAAC,CAACT,eACvCJ,iBAAiBQ,gBAAgB,CAACJ;IAGpC,OAAO;QAAEQ;QAAUE;QAAaC;IAAa;AAC/C;AAQO,eAAerC,kBACpBsC,QAAgB,EAChBhB,gBAA2D;IAE3D,OAAOE,IAAAA,kCAAgB,EAACc,UAAU;QAChCb,gBAAgBH,iBAAiBiB,UAAU;IAC7C;AACF;AAqBO,SAASnC,uBACdoC,OAAe,EACfC,QAAgB,EAChBC,MAAuB,EACvBC,QAAiB;IAEjB,MAAMC,gBACJF,WAAW,UAAU,uBAAuB;IAC9C,MAAMG,YAAYF,WAAW,SAAS;IACtC,OAAOG,IAAAA,UAAI,EACTN,SACAC,SAASM,OAAO,CAAC,IAAIC,OAAO,CAAC,CAAC,EAAEJ,cAAc,CAAC,CAAC,GAAG,GAAGC,YAAYH,OAAO,CAAC,CAAC;AAE/E;AASO,SAASxB,kBACd+B,WAAuC,EACvCT,OAAe,EACfG,QAAiB;IAKjB,MAAMO,aAA0B,EAAE;IAClC,MAAMC,gBAA6B,EAAE;IAErC,KAAK,MAAM,CAACC,OAAOX,SAAS,IAAIY,OAAOC,OAAO,CAACL,aAAc;QAC3D,MAAMM,mBAAmBnD,uBACvBoC,SACAC,UACA,SACAE;QAGF,IAAIS,MAAMnB,UAAU,CAAC,UAAU;YAC7BkB,cAAcK,IAAI,CAAC;gBACjBJ,OAAOK,IAAAA,kCAAgB,EAACL;gBACxBX,UAAUc;YACZ;QACF,OAAO;YACL,qCAAqC;YACrC,IAAIG,IAAAA,qBAAc,EAACN,QAAQ;YAE3BF,WAAWM,IAAI,CAAC;gBACdJ,OAAOK,IAAAA,kCAAgB,EAACL;gBACxBX,UAAUc;YACZ;QACF;IACF;IAEA,OAAO;QAAEL;QAAYC;IAAc;AACrC;AAOO,SAAS9C,0BAA0BsD,cAEzC;IACC,MAAMC,QAAoB,EAAE;IAE5B,KAAK,MAAM,CAACR,MAAM,IAAIC,OAAOC,OAAO,CAACK,gBAAiB;QACpD,IAAIP,UAAU,oBAAoB;QAElC,MAAMS,WAAWT,MAAMU,KAAK,CAAC;QAC7B,IAAK,IAAIC,IAAIF,SAASG,MAAM,GAAG,GAAGD,KAAK,GAAGA,IAAK;YAC7C,MAAME,UAAUJ,QAAQ,CAACE,EAAE;YAC3B,IAAIG,IAAAA,+BAAsB,EAACD,UAAU;gBACnC,MAAME,aAAaC,IAAAA,0BAAgB,EAACP,SAASQ,KAAK,CAAC,GAAGN,GAAGjB,IAAI,CAAC;gBAC9D,MAAMwB,WAAWL,QAAQI,KAAK,CAAC;gBAE/B,mCAAmC;gBACnC,IAAIT,MAAMW,IAAI,CAAC,CAACC,IAAMA,EAAEC,IAAI,KAAKH,YAAYE,EAAEE,MAAM,KAAKP,aACxD;gBAEFP,MAAMJ,IAAI,CAAC;oBACTiB,MAAMH;oBACNI,QAAQP;gBACV;gBACA;YACF;QACF;IACF;IAEA,OAAOP;AACT;AAOO,SAAStD,6BAA6BqE,kBAE5C;IACC,MAAMf,QAAoB,EAAE;IAE5B,KAAK,MAAM,CAACR,MAAM,IAAIC,OAAOC,OAAO,CAACqB,oBAAqB;QACxD,MAAMd,WAAWT,MAAMU,KAAK,CAAC;QAC7B,IAAK,IAAIC,IAAIF,SAASG,MAAM,GAAG,GAAGD,KAAK,GAAGA,IAAK;YAC7C,MAAME,UAAUJ,QAAQ,CAACE,EAAE;YAC3B,IAAIG,IAAAA,+BAAsB,EAACD,UAAU;gBACnC,MAAME,aAAaC,IAAAA,0BAAgB,EAACP,SAASQ,KAAK,CAAC,GAAGN,GAAGjB,IAAI,CAAC;gBAC9D,MAAMwB,WAAWL,QAAQI,KAAK,CAAC;gBAE/B,mCAAmC;gBACnC,IAAIT,MAAMW,IAAI,CAAC,CAACC,IAAMA,EAAEC,IAAI,KAAKH,YAAYE,EAAEE,MAAM,KAAKP,aACxD;gBAEFP,MAAMJ,IAAI,CAAC;oBACTiB,MAAMH;oBACNI,QAAQP;gBACV;gBACA;YACF;QACF;IACF;IAEA,OAAOP;AACT;AAOO,SAAS3D,aAAa,GAAG2E,UAAwB;IACtD,MAAMC,UAAU,IAAIC;IACpB,MAAMC,SAAqB,EAAE;IAE7B,KAAK,MAAMnB,SAASgB,WAAY;QAC9B,KAAK,MAAMI,QAAQpB,MAAO;YACxB,MAAMqB,MAAM,GAAGD,KAAKP,IAAI,CAAC,CAAC,EAAEO,KAAKN,MAAM,EAAE;YACzC,IAAI,CAACG,QAAQK,GAAG,CAACD,MAAM;gBACrBJ,QAAQM,GAAG,CAACF;gBACZF,OAAOvB,IAAI,CAACwB;YACd;QACF;IACF;IAEA,OAAOD;AACT;AAUO,SAAS/D,iBACd2C,cAA0C,EAC1CrC,gBAA2D,EAC3DkB,OAAe,EACfG,QAAiB;IAKjB,MAAMyC,YAAyB,EAAE;IACjC,MAAMC,mBAAgC,EAAE;IAExC,KAAK,MAAM,CAACjC,OAAOX,SAAS,IAAIY,OAAOC,OAAO,CAACK,gBAAiB;QAC9D,IAAIP,UAAU,oBAAoB;QAElC,MAAMG,mBAAmBnD,uBACvBoC,SACAC,UACA,OACAE;QAGF,IAAIrB,iBAAiBgE,gBAAgB,CAAC7C,WAAW;YAC/C4C,iBAAiB7B,IAAI,CAAC;gBACpBJ,OAAOgB,IAAAA,0BAAgB,EAACX,IAAAA,kCAAgB,EAACL;gBACzCX,UAAUc;YACZ;QACF,OAAO;YACL6B,UAAU5B,IAAI,CAAC;gBACbJ,OAAOgB,IAAAA,0BAAgB,EAACX,IAAAA,kCAAgB,EAACL;gBACzCX,UAAUc;YACZ;QACF;IACF;IAEA,OAAO;QAAE6B;QAAWC;IAAiB;AACvC;AASO,SAASpE,oBACdsE,gBAA4C,EAC5C/C,OAAe,EACfG,QAAiB;IAEjB,MAAM6C,eAA4B,EAAE;IAEpC,KAAK,MAAM,CAACpC,OAAOX,SAAS,IAAIY,OAAOC,OAAO,CAACiC,kBAAmB;QAChE,MAAMhC,mBAAmBnD,uBACvBoC,SACAC,UACA,OACAE;QAEF6C,aAAahC,IAAI,CAAC;YAChBJ,OAAOqC,IAAAA,sCAAkB,EACvBrB,IAAAA,0BAAgB,EAACX,IAAAA,kCAAgB,EAACL,QAAQL,OAAO,CAAC,aAAa;YAEjEN,UAAUc;QACZ;IACF;IAEA,OAAOiC;AACT;AAEO,SAASpE,eAAesE,cAA8B;IAC3D,OAAO,CAACC,GAAWC;QACjB,uDAAuD;QACvD,wDAAwD;QACxD,qDAAqD;QACrD,kBAAkB;QAClB,MAAMC,OAAOC,IAAAA,aAAO,EAACH;QACrB,MAAMI,OAAOD,IAAAA,aAAO,EAACF;QAErB,MAAMI,SAASL,EAAEM,SAAS,CAAC,GAAGN,EAAE3B,MAAM,GAAG6B,KAAK7B,MAAM;QACpD,MAAMkC,SAASP,EAAEM,SAAS,CAAC,GAAGL,EAAE5B,MAAM,GAAG+B,KAAK/B,MAAM;QAEpD,IAAIgC,WAAWE,QAAQ,OAAO;QAE9B,oEAAoE;QACpE,MAAMC,YAAYT,eAAeU,OAAO,CAACP,KAAKI,SAAS,CAAC;QACxD,MAAMI,YAAYX,eAAeU,OAAO,CAACL,KAAKE,SAAS,CAAC;QAExD,OAAOI,YAAYF;IACrB;AACF;AAEO,eAAepF,8BAA8B,EAClDuF,cAAc,EACdZ,cAAc,EACda,YAAY,EACZlF,MAAM,EACNmF,QAAQC,UAAU,EAClBC,KAAK,EACLC,IAAI,EASL;IACC,6EAA6E;IAC7E,MAAMC,WAAWN,iBAAiBO,qBAAU,CAACC,GAAG,GAAGD,qBAAU,CAACE,KAAK;IAEnE,MAAMC,iBAAiB,MAAMC,IAAAA,oCAAiB,EAAC;QAC7CR;QACAF;QACAG;QACAC;QACAC;IACF;IAEA,IAAII,eAAeE,IAAI,KAAKL,qBAAU,CAACE,KAAK,IAAI,CAAC1F,QAAQ;QACvD,OAAO2F;IACT;IAEA,MAAMnD,WAAW;QAACmD;KAAe;IAEjC,sDAAsD;IACtD,IAAIG,IAAAA,8BAAc,EAACR,OAAO;QACxB,MAAMS,cAAc,EAAE;QACtB,MAAMC,uBAAuB3B,eAAe4B,GAAG,CAAC,CAACC,MAAQ,YAAYA;QACrE,IAAIC,MAAMC,IAAAA,aAAO,EAAClB;QAElB,yDAAyD;QACzD,MAAOiB,IAAIvF,UAAU,CAACZ,QAAS;YAC7B,KAAK,MAAMqG,uBAAuBL,qBAAsB;gBACtD,MAAMM,aAAa7E,IAAAA,UAAI,EAAC0E,KAAKE;gBAC7B,IAAI,CAACE,WAAE,CAACC,UAAU,CAACF,aAAa;oBAC9B;gBACF;gBACAP,YAAY5D,IAAI,CAACmE;YACnB;YACA,6BAA6B;YAC7BH,MAAM1E,IAAAA,UAAI,EAAC0E,KAAK;QAClB;QAEA,KAAK,MAAMG,cAAcP,YAAa;YACpC,MAAMU,mBAAmB,MAAMC,IAAAA,uCAAoB,EAAC;gBAClDtB;gBACAF,cAAcoB;gBACdjB;gBACAC;gBACAC,UAAUN,iBAAiBO,qBAAU,CAACC,GAAG,GAAGD,qBAAU,CAACE,KAAK;YAC9D;YAEAlD,SAASmE,OAAO,CAACF;QACnB;IACF;IAEA,MAAMtB,SAASyB,IAAAA,sBAAe,EAACpE;IAE/B,OAAO;QACL,GAAGmD,cAAc;QACjBR;QACA0B,SAAS1B,OAAO0B,OAAO;QACvBC,iBAAiB3B,OAAO2B,eAAe;QACvCC,aAAa5B,OAAO4B,WAAW;IACjC;AACF;AAOO,SAAStH,gBACduH,QAAgB,EAChB3C,cAA8B;IAE9B,IAAIiB,OAAOlD,IAAAA,kCAAgB,EACzB4E,SAAStF,OAAO,CAAC,IAAIC,OAAO,CAAC,KAAK,EAAE0C,eAAe5C,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG;IAGrE6D,OAAOA,KAAK5D,OAAO,CAAC,YAAY;IAEhC,OAAO4D,SAAS,KAAK,MAAMA;AAC7B;AAEO,SAAS9F,gBAAgB,EAC9ByH,gBAAgB,EAChBhG,QAAQ,EACRjB,MAAM,EACNkH,OAAO,EAMR;IACC,IAAID,iBAAiBrG,UAAU,CAACuG,0BAAe,KAAKlG,UAAU;QAC5D,OAAOgG,iBAAiBvF,OAAO,CAACyF,0BAAe,EAAElG;IACnD;IAEA,IAAIgG,iBAAiBrG,UAAU,CAACwG,wBAAa,KAAKpH,QAAQ;QACxD,OAAOiH,iBAAiBvF,OAAO,CAAC0F,wBAAa,EAAEpH;IACjD;IAEA,IAAIiH,iBAAiBrG,UAAU,CAACyG,yBAAc,GAAG;QAC/C,OAAOJ,iBAAiBvF,OAAO,CAAC2F,yBAAc,EAAEH;IAClD;IAEA,OAAOI,QAAQC,OAAO,CAACN;AACzB;AAMO,eAAenI,mBAAmB,EACvCuG,KAAK,EACLhB,cAAc,EACdmD,SAAS,EACTC,SAAS,EACTxG,QAAQ,EACRjB,MAAM,EAQP;IACC,MAAM0H,aAAaD,cAAc;IACjC,MAAME,QAAqB,CAAC;IAC5B,MAAMC,WAAWJ,UAAUvB,GAAG,CAAgB,OAAOe;QACnD,uCAAuC;QACvC,IAAIA,SAASa,QAAQ,CAAC,YAAYxD,eAAeyD,QAAQ,CAAC,OAAO;YAC/D;QACF;QAEA,IAAIC,UAAUtI,gBAAgBuH,UAAU3C;QACxC,IAAIqD,YAAY;YACdK,UAAUA,QAAQrG,OAAO,CAAC,QAAQ;YAClC,IAAIqG,YAAY,cAAc;gBAC5BA,UAAUC,4CAAgC;YAC5C;QACF;QAEA,MAAMC,iBAAiB7F,IAAAA,kCAAgB,EACrCX,IAAAA,UAAI,EACFgG,cAAc,UACVN,0BAAe,GACfM,cAAc,QACZL,wBAAa,GACbC,yBAAc,EACpBL;QAIJ,IAAIjF,QAAQ0F,cAAc,QAAQS,IAAAA,wCAAsB,EAACH,WAAWA;QAEpE,IACEN,cAAc,SACdU,IAAAA,oCAAmB,EAACnB,UAAU3C,gBAAgB,OAC9C;YACA,MAAMjD,WAAWK,IAAAA,UAAI,EAACzB,QAASgH;YAC/B,MAAMoB,aAAa,MAAMxC,IAAAA,oCAAiB,EAAC;gBACzCR,YAAY,CAAC;gBACbF,cAAc9D;gBACdiE;gBACAC,MAAMyC;gBACNxC,UAAUkC;YACZ;YAEA1F,QAAQsG,IAAAA,8CAA4B,EAClCtG,OACA,CAAC,CAAEqG,CAAAA,WAAWE,qBAAqB,IAAIF,WAAWG,gBAAgB,AAAD;QAErE;QAEAZ,KAAK,CAAC5F,MAAM,GAAGkG;IACjB;IAEA,MAAMO,QAAQC,GAAG,CAACb;IAElB,OAAQH;QACN,KAAKjC,qBAAU,CAACkD,IAAI;YAAE;gBACpB,OAAOf;YACT;QACA,KAAKnC,qBAAU,CAACC,GAAG;YAAE;gBACnB,MAAMkD,cAAc3G,OAAO4G,IAAI,CAACjB,OAAOzE,IAAI,CAAC,CAACoC,OAC3CA,KAAKuC,QAAQ,CAAC;gBAEhB,OAAO;oBACL,4EAA4E;oBAC5E,4EAA4E;oBAC5E,GAAIc,eAAe;wBACjB,CAACX,4CAAgC,CAAC,EAAEV,QAAQC,OAAO,CACjD;oBAEJ,CAAC;oBACD,GAAGI,KAAK;gBACV;YACF;QACA,KAAKnC,qBAAU,CAACE,KAAK;YAAE;gBACrB,IAAIL,OAAO;oBACT,OAAOsC,KAAK,CAAC,QAAQ;oBACrB,OAAOA,KAAK,CAAC,UAAU;oBACvB,OAAOA,KAAK,CAAC,aAAa;gBAC5B;gBAEA,uEAAuE;gBACvE,uEAAuE;gBACvE,oBAAoB;gBACpB,MAAMkB,OAAOxD,SAASpE,WAAWkG,0BAAe,GAAG;gBAEnD,OAAO;oBACL,SAAS,GAAG0B,KAAK,KAAK,CAAC;oBACvB,WAAW,GAAGA,KAAK,OAAO,CAAC;oBAC3B,cAAc,GAAGA,KAAK,UAAU,CAAC;oBACjC,GAAGlB,KAAK;gBACV;YACF;QACA;YAAS;gBACP,OAAO,CAAC;YACV;IACF;AACF;AAkBO,SAASrI,mBAAmBwJ,IAgBlC;QA6EgCA;IA5E/B,IACEA,KAAKrB,SAAS,KAAK,SACnBsB,IAAAA,gCAAe,EAACD,KAAKxD,IAAI,KACzBwD,KAAKE,YAAY,EACjB;QACA,MAAMC,eAAwC;YAC5ChC,kBAAkB6B,KAAK7B,gBAAgB;YACvC3B,MAAMwD,KAAKxD,IAAI;YACf0D,cAAcE,OAAOC,IAAI,CAACL,KAAKE,YAAY,IAAI,IAAII,QAAQ,CAAC;YAC5DhE,YAAY8D,OAAOC,IAAI,CAACE,KAAKC,SAAS,CAACR,KAAK3D,MAAM,GAAGiE,QAAQ,CAAC;YAC9DtC,iBAAiBgC,KAAKhC,eAAe;YACrCyC,kBAAkBL,OAAOC,IAAI,CAC3BE,KAAKC,SAAS,CAACR,KAAKS,gBAAgB,IAAI,CAAC,IACzCH,QAAQ,CAAC;YACXI,eAAeH,KAAKC,SAAS,CAC3BR,KAAK3D,MAAM,CAACsE,YAAY,CAACD,aAAa,IAAI,CAAC;QAE/C;QAEA,OAAO;YACLE,QAAQ,CAAC,2BAA2B,EAAEJ,IAAAA,sBAAS,EAACL,cAAc,CAAC,CAAC;YAChEU,OAAOC,yBAAc,CAACC,qBAAqB;QAC7C;IACF;IAEA,IAAIC,IAAAA,uBAAgB,EAAChB,KAAKxD,IAAI,GAAG;YAKnBwD;QAJZ,MAAMG,eAAwC;YAC5ChC,kBAAkB6B,KAAK7B,gBAAgB;YACvC3B,MAAMwD,KAAKxD,IAAI;YACf4B,SAAS4B,KAAK5B,OAAO;YACrB6C,UAAUjB,EAAAA,mBAAAA,KAAKkB,UAAU,qBAAflB,iBAAiBiB,QAAQ,IAC/BE,IAAAA,oCAAc,EAACnB,KAAKkB,UAAU,CAACD,QAAQ,IACvC;YACJjD,iBAAiBgC,KAAKhC,eAAe;YACrCyC,kBAAkBL,OAAOC,IAAI,CAC3BE,KAAKC,SAAS,CAACR,KAAKS,gBAAgB,IAAI,CAAC,IACzCH,QAAQ,CAAC;QACb;QAEA,OAAO;YACLM,QAAQ,CAAC,uBAAuB,EAAEJ,IAAAA,sBAAS,EAACL,cAAc,CAAC,CAAC;YAC5DU,OAAOC,yBAAc,CAACI,UAAU;QAClC;IACF;IAEA,IAAIE,IAAAA,sBAAU,EAACpB,KAAKxD,IAAI,GAAG;QACzB,MAAM2D,eAA0C;YAC9ChC,kBAAkB6B,KAAK7B,gBAAgB;YACvC3B,MAAMwD,KAAKxD,IAAI;YACf4B,SAAS4B,KAAK5B,OAAO;YACrBJ,iBAAiBgC,KAAKhC,eAAe;YACrCyC,kBAAkBL,OAAOC,IAAI,CAC3BE,KAAKC,SAAS,CAACR,KAAKS,gBAAgB,IAAI,CAAC,IACzCH,QAAQ,CAAC;QACb;QAEA,OAAO;YACLM,QAAQ,CAAC,0BAA0B,EAAEJ,IAAAA,sBAAS,EAACL,cAAc,CAAC,CAAC;YAC/DU,OAAOC,yBAAc,CAACO,OAAO;QAC/B;IACF;IAEA,MAAMlB,eAAmC;QACvCmB,iBAAiBtB,KAAKnB,KAAK,CAAC,OAAO,IAAI;QACvC0C,iBAAiBvB,KAAKnB,KAAK,CAAC,QAAQ;QACpC2C,sBAAsBxB,KAAKnB,KAAK,CAAC,aAAa;QAC9C4C,mBAAmBzB,KAAKnB,KAAK,CAAC,UAAU;QACxCV,kBAAkB6B,KAAK7B,gBAAgB;QACvCuD,KAAK1B,KAAKzD,KAAK;QACfoF,mBAAmB3B,KAAK2B,iBAAiB;QACzCnF,MAAMwD,KAAKxD,IAAI;QACfoF,mBAAmBxB,OAAOC,IAAI,CAACE,KAAKC,SAAS,CAACR,KAAK3D,MAAM,GAAGiE,QAAQ,CAClE;QAEF3B,WAAWqB,KAAKrB,SAAS;QACzBuB,cAAcE,OAAOC,IAAI,CAACL,KAAKE,YAAY,IAAI,IAAII,QAAQ,CAAC;QAC5DuB,YAAY,CAAC7B,KAAKzD,KAAK,IAAI,CAAC,GAACyD,gCAAAA,KAAK3D,MAAM,CAACsE,YAAY,CAACmB,GAAG,qBAA5B9B,8BAA8B+B,SAAS;QACpEC,cAAchC,KAAK3D,MAAM,CAAC2F,YAAY;QACtChE,iBAAiBgC,KAAKhC,eAAe;QACrCyC,kBAAkBL,OAAOC,IAAI,CAC3BE,KAAKC,SAAS,CAACR,KAAKS,gBAAgB,IAAI,CAAC,IACzCH,QAAQ,CAAC;QACX2B,eAAejC,KAAK3D,MAAM,CAACsE,YAAY,CAACsB,aAAa;QACrDvB,eAAeH,KAAKC,SAAS,CAACR,KAAK3D,MAAM,CAACsE,YAAY,CAACD,aAAa,IAAI,CAAC;IAC3E;IAEA,OAAO;QACLE,QAAQ,CAAC,qBAAqB,EAAEL,KAAKC,SAAS,CAACL,cAAc,CAAC,CAAC;QAC/D,sEAAsE;QACtE,2EAA2E;QAC3E,sBAAsB;QACtBU,OAAOb,KAAKE,YAAY,GAAGY,yBAAc,CAACoB,mBAAmB,GAAGC;IAClE;AACF;AAEO,SAAS1L,wBAAwBuJ,IAIvC;IACC,2DAA2D;IAC3D,MAAMoC,WAAW,GACfpC,KAAKqC,YAAY,GAAG,UAAUrC,KAAKzD,KAAK,GAAG,KAAK,QAC/C+F,wCAA6B,CAAC,GAAG,CAAC;IAErC,OAAO;QACL1B,QAAQZ,KAAK7B,gBAAgB;QAC7BiE;QACAvB,OAAOC,yBAAc,CAACyB,UAAU;IAClC;AACF;AAEO,SAASjM;IACd,OAAOkM,QAAQC,GAAG,CAACC,kBAAkB,GACjC,CAAC,uBAAuB,CAAC,GACzB;AACN;AAEO,SAASrM,YAAY2J,IAAgC;IAC1D,IAAIwC,QAAQC,GAAG,CAACE,WAAW,IAAIH,QAAQC,GAAG,CAACC,kBAAkB,EAAE;;QAC3D1C,KAAa4C,WAAW,GAAGC,IAAAA,eAAS,EAAClK,IAAAA,UAAI,EAACmK,WAAW;IACzD;IACA,OAAO;QACLlC,QAAQ,GAAGtK,eAAe,CAAC,EAAEkK,IAAAA,sBAAS,EAACR,MAAM,CAAC,CAAC;QAC/Ca,OAAOC,yBAAc,CAACC,qBAAqB;IAC7C;AACF;AAEO,SAASxK,eAAeyJ,IAG9B;IACC,MAAM+C,gBAA0C;QAC9C5E,kBAAkB6B,KAAK7B,gBAAgB;QACvC3B,MAAMwD,KAAKxD,IAAI;IACjB;IAEA,MAAMwG,aAAa,CAAC,yBAAyB,EAAExC,IAAAA,sBAAS,EAACuC,eAAe,CAAC,CAAC;IAE1E,wEAAwE;IACxE,kEAAkE;IAClE,UAAU;IACV,OAAO/C,KAAKxD,IAAI,KAAK,UACjB;QAACwG;QAAYxE,QAAQC,OAAO,CAAC;KAAoB,GACjDuE;AACN;AAEO,SAAShM,uBAA0BiM,MAOzC;IACC,IACEA,OAAOxG,QAAQ,KAAKC,qBAAU,CAACkD,IAAI,IACnCsD,IAAAA,gCAAyB,EAACD,OAAOzG,IAAI,GACrC;QACAyG,OAAOE,QAAQ;QACfF,OAAOG,YAAY;QACnB;IACF;IAEA,IAAIpC,IAAAA,uBAAgB,EAACiC,OAAOzG,IAAI,GAAG;QACjC,IAAIyG,OAAOI,WAAW,KAAK,UAAU;YACnCJ,OAAOE,QAAQ;YACf;QACF,OAAO;YACLF,OAAOG,YAAY;YACnB;QACF;IACF;IAEA,IAAIhC,IAAAA,sBAAU,EAAC6B,OAAOzG,IAAI,GAAG;QAC3B,IAAI8G,IAAAA,4BAAa,EAACL,OAAOI,WAAW,GAAG;YACrCJ,OAAOG,YAAY;YACnB;QACF;QAEAH,OAAOE,QAAQ;QACf;IACF;IACA,IAAIF,OAAOzG,IAAI,KAAK,cAAc;QAChCyG,OAAOE,QAAQ;QACf;IACF;IACA,IACEF,OAAOzG,IAAI,KAAK,WAChByG,OAAOzG,IAAI,KAAK,aAChByG,OAAOzG,IAAI,KAAK,UAChByG,OAAOzG,IAAI,KAAK,QAChB;QACAyG,OAAOM,QAAQ;QACfN,OAAOE,QAAQ;QACf;IACF;IACA,IAAIG,IAAAA,4BAAa,EAACL,OAAOI,WAAW,GAAG;QACrCJ,OAAOM,QAAQ;QACfN,OAAOG,YAAY;QACnB;IACF;IAEAH,OAAOM,QAAQ;IACfN,OAAOE,QAAQ;IACf;AACF;AAEO,eAAepN,kBACpBkN,MAA+B;IAO/B,MAAM,EACJ5G,MAAM,EACNwC,KAAK,EACL1G,QAAQ,EACRoE,KAAK,EACL6B,OAAO,EACPoF,SAAS,EACTtM,MAAM,EACNa,QAAQ,EACRwD,cAAc,EACf,GAAG0H;IACJ,MAAMQ,aAAkC,CAAC;IACzC,MAAMC,SAA8B,CAAC;IACrC,MAAMC,SAA8B,CAAC;IACrC,IAAIC,qBAAsDzB;IAE1D,IAAI0B,mBAA6C,CAAC;IAClD,IAAI3M,UAAUa,UAAU;QACtB,IAAK,MAAM+L,YAAY/L,SAAU;YAC/B,MAAMoH,iBAAiBlF,IAAAA,0BAAgB,EAAC6J;YACxC,MAAMC,aAAahM,QAAQ,CAAC+L,SAAS;YACrC,IAAI,CAACD,gBAAgB,CAAC1E,eAAe,EAAE;gBACrC0E,gBAAgB,CAAC1E,eAAe,GAAG,EAAE;YACvC;YACA0E,gBAAgB,CAAC1E,eAAe,CAAC9F,IAAI,CACnC,4EAA4E;YAC5E1C,gBAAgBoN,YAAYxI,gBAAgB3C,OAAO,CAAC0F,wBAAa,EAAE;QAEvE;QAEA,uCAAuC;QACvC0F,IAAAA,gDAAuB,EAACH;QAExB,sEAAsE;QACtEA,mBAAmB3K,OAAO+K,WAAW,CACnC/K,OAAOC,OAAO,CAAC0K,kBAAkB1G,GAAG,CAAC,CAAC,CAAC+G,GAAGC,EAAE,GAAK;gBAACD;gBAAGC,EAAEC,IAAI;aAAG;IAElE;IAEA,MAAMC,kBACJ,CAACC,UAAuB3F,YACxB,OAAOnC;YACL,MAAM+H,aAAaC,IAAAA,oCAAiB,EAAChI;YACrC,MAAMiI,mBAAmBC,WAAK,CAAC/L,IAAI,CAACgG,WAAW4F;YAC/C,MAAMI,mBACJhG,cAAcjC,qBAAU,CAACE,KAAK,GAC1B8H,WAAK,CAAC/L,IAAI,CAAC,SAAS4L,cACpB5F,cAAcjC,qBAAU,CAACC,GAAG,GAC1B+H,WAAK,CAAC/L,IAAI,CAAC,OAAO4L,cAClBA,WAAWrK,KAAK,CAAC;YAEzB,MAAMiE,mBAAmBmG,QAAQ,CAAC9H,KAAK;YAEvC,iCAAiC;YACjC,MAAMJ,eAAe1F,gBAAgB;gBACnCyH;gBACAhG;gBACAjB;gBACAkH;YACF;YAEA,MAAMjC,iBACJ,CAAC,CAACjF,UACDiH,CAAAA,iBAAiBrG,UAAU,CAACwG,wBAAa,KACxCH,iBAAiBrG,UAAU,CAACZ,OAAM;YAEtC,MAAMoI,aAA6B,MAAM1I,8BAA8B;gBACrEuF;gBACAZ;gBACAa;gBACAlF;gBACAmF;gBACAE;gBACAC;YACF;YAEA,iCAAiC;YACjC,MAAMmF,oBACJxF,kBAAkBmD,WAAWsF,GAAG,KAAKC,4BAAgB,CAAClB,MAAM;YAE9D,IAAI3C,IAAAA,uBAAgB,EAACxE,OAAO;oBACL8C;gBAArBsE,qBAAqBtE,EAAAA,yBAAAA,WAAW4B,UAAU,qBAArB5B,uBAAuB2B,QAAQ,KAAI;oBACtD;wBAAE6D,QAAQ;wBAAMC,gBAAgB;oBAAU;iBAC3C;YACH;YAEA,MAAMC,oBACJ9B,IAAAA,gCAAyB,EAAC1G,SAASmC,cAAcjC,qBAAU,CAACkD,IAAI;YAElE5I,uBAAuB;gBACrBwF;gBACA6G,aAAa/D,WAAWvB,OAAO;gBAC/BtB,UAAUkC;gBACV4E,UAAU;oBACR,IAAI5B,qBAAqBxF,gBAAgB;oBACvC,qEAAqE;oBACrE,uCAAuC;oBACzC,OAAO;wBACLwH,MAAM,CAACc,iBAAiB,GAAGlO,eAAe;4BACxC4H;4BACA3B;wBACF;oBACF;gBACF;gBACA2G,UAAU;oBACR,IAAIxE,cAAc,SAASzH,QAAQ;wBACjC,MAAM+N,kBAAkBpB,gBAAgB,CAAC5J,IAAAA,0BAAgB,EAACuC,MAAM;wBAChEkH,MAAM,CAACiB,iBAAiB,GAAGtO,YAAY;4BACrCmG;4BACAlC,MAAMqK;4BACNzG,UAAUC;4BACVjH;4BACAa,UAAUkN;4BACV1J;4BACA2J,UAAU7I,OAAO6I,QAAQ;4BACzBC,aAAa9I,OAAO8I,WAAW;4BAC/BC,kBAAkB/I,OAAOgJ,MAAM;4BAC/BrH,iBAAiBsB,WAAWtB,eAAe;4BAC3CyC,kBAAkB6E,IAAAA,sBAAc,EAAChG,WAAW4B,UAAU,IAAI,CAAC;4BAC3DqE,yBAAyBlJ,OAAOsE,YAAY,CAAC6E,cAAc,GACvD,OACArD;wBACN;oBACF,OAAO,IAAI6C,mBAAmB;wBAC5BtB,MAAM,CAACiB,iBAAiB/L,OAAO,CAAC,QAAQ,IAAI,GAC1CnC,wBAAwB;4BACtB0H;4BACAkE,cAAc;4BACd9F,OAAO;wBACT;oBACJ,OAAO,IAAIyE,IAAAA,uBAAgB,EAACxE,OAAO;wBACjCkH,MAAM,CAACiB,iBAAiB/L,OAAO,CAAC,QAAQ,IAAI,GAAGpC,mBAAmB;4BAChE,GAAGyM,MAAM;4BACT7E;4BACAD,kBAAkBA;4BAClBsH,YAAYhB;4BACZlI,OAAO;4BACPoF;4BACAnF;4BACA0E,UAAU,EAAE5B,8BAAAA,WAAY4B,UAAU;4BAClCvC;4BACAX,iBAAiBsB,WAAWtB,eAAe;4BAC3CyC,kBAAkBnB,WAAW4B,UAAU;wBACzC;oBACF,OAAO,IAAIE,IAAAA,sBAAU,EAAC5E,OAAO;wBAC3BkH,MAAM,CAACiB,iBAAiB,GAAG;4BACzBe,IAAAA,oCAAmB,EAAC;gCAClBC,MAAMC,oBAAS,CAACC,SAAS;gCACzBrJ;gCACA2B;gCACAH,iBAAiBsB,WAAWtB,eAAe;gCAC3CyC,kBAAkBnB,WAAW4B,UAAU,IAAI,CAAC;4BAC9C;yBACD;oBACH,OAAO,IACL,CAACF,IAAAA,uBAAgB,EAACxE,SAClB,CAACsJ,IAAAA,wCAAmB,EAAC3H,qBACrB,CAAC4H,IAAAA,wCAAmB,EAACvJ,OACrB;wBACAkH,MAAM,CAACiB,iBAAiB,GAAG;4BACzBe,IAAAA,oCAAmB,EAAC;gCAClBC,MAAMC,oBAAS,CAAChJ,KAAK;gCACrBJ;gCACAqC;gCACAV;gCACAH,iBAAiBsB,WAAWtB,eAAe;gCAC3CyC,kBAAkBnB,WAAW4B,UAAU,IAAI,CAAC;4BAC9C;yBACD;oBACH,OAAO;wBACLwC,MAAM,CAACiB,iBAAiB,GAAG;4BAACxG;yBAAiB;oBAC/C;gBACF;gBACAiF,cAAc;oBACZ,IAAIlD,eAAuB;oBAC3B,IAAI8E,mBAAmB;wBACrBvB,UAAU,CAACkB,iBAAiB/L,OAAO,CAAC,QAAQ,IAAI,GAC9CnC,wBAAwB;4BACtB0H;4BACAkE,cAAc;4BACd9F,OAAO;wBACT;oBACJ,OAAO;wBACL,IAAIoC,cAAc,OAAO;4BACvB,MAAMsG,kBAAkBpB,gBAAgB,CAAC5J,IAAAA,0BAAgB,EAACuC,MAAM;4BAChE0D,eAAe7J,YAAY;gCACzBiE,MAAMqK;gCACNnI;gCACA0B,UAAUC;gCACVjH,QAAQA;gCACRa,UAAUkN;gCACV1J;gCACA2J,UAAU7I,OAAO6I,QAAQ;gCACzBC,aAAa9I,OAAO8I,WAAW;gCAC/BC,kBAAkB/I,OAAOgJ,MAAM;gCAC/B,oHAAoH;gCACpH,yCAAyC;gCACzCrH,iBAAiBsB,WAAWtB,eAAe;gCAC3CyC,kBAAkBL,OAAOC,IAAI,CAC3BE,KAAKC,SAAS,CAAClB,WAAW4B,UAAU,IAAI,CAAC,IACzCZ,QAAQ,CAAC;gCACXiF,yBAAyBlJ,OAAOsE,YAAY,CAAC6E,cAAc,GACvD,OACArD;4BACN,GAAGvB,MAAM;wBACX;wBACA6C,UAAU,CAACkB,iBAAiB,GAAGnO,mBAAmB;4BAChD,GAAGyM,MAAM;4BACT7E;4BACAD,kBAAkBA;4BAClBsH,YAAYhB;4BACZlI,OAAO;4BACPoF;4BACAnF;4BACA0E,UAAU,EAAE5B,8BAAAA,WAAY4B,UAAU;4BAClCvC;4BACAuB;4BACAlC,iBAAiBsB,WAAWtB,eAAe;4BAC3CyC,kBAAkBnB,WAAW4B,UAAU;wBACzC;oBACF;gBACF;YACF;QACF;IAEF,MAAMpC,WAA8B,EAAE;IAEtC,IAAI/G,UAAU;QACZ,MAAMiO,eAAe3B,gBAAgBtM,UAAU2E,qBAAU,CAACC,GAAG;QAC7DmC,SAASzF,IAAI,CAACqG,QAAQC,GAAG,CAACzG,OAAO4G,IAAI,CAAC/H,UAAUoF,GAAG,CAAC6I;IACtD;IACA,IAAIxC,WAAW;QACb1E,SAASzF,IAAI,CACXqG,QAAQC,GAAG,CACTzG,OAAO4G,IAAI,CAAC0D,WAAWrG,GAAG,CAACkH,gBAAgBb,WAAW9G,qBAAU,CAACkD,IAAI;IAG3E;IACAd,SAASzF,IAAI,CACXqG,QAAQC,GAAG,CACTzG,OAAO4G,IAAI,CAACjB,OAAO1B,GAAG,CAACkH,gBAAgBxF,OAAOnC,qBAAU,CAACE,KAAK;IAIlE,MAAM8C,QAAQC,GAAG,CAACb;IAElB,qHAAqH;IACrH,6FAA6F;IAC7F,IAAI2E,WAAWwC,eAAe,IAAI/M,OAAO4G,IAAI,CAAC2D,YAAY5J,MAAM,KAAK,GAAG;QACtE,OAAO4J,WAAWwC,eAAe;IACnC;IAEA,OAAO;QACLtC;QACAD;QACAD;QACAG;IACF;AACF;AAEO,SAASxN,mBAAmB,EACjCkE,IAAI,EACJ4L,YAAY,EACZC,KAAK,EACLxE,iBAAiB,EACjByE,SAAS,EAOV;IACC,MAAMC,QACJ,OAAOF,UAAU,YAAYG,MAAMC,OAAO,CAACJ,SACvC;QAAEvF,QAAQuF;IAAM,IAChBA;IAEN,MAAMK,QAAQlM,KAAKxC,UAAU,CAAC;IAC9B,MAAMkN,oBAAoByB,IAAAA,oCAA6B,EAACnM;IAExD,OAAQ4L;QACN,KAAKQ,0BAAc,CAAChD,MAAM;YAAE;gBAC1B,MAAM7C,QAAQ2F,QACV1F,yBAAc,CAAC6F,OAAO,GACtB3B,oBACElE,yBAAc,CAACyB,UAAU,GACzBZ,oBACEb,yBAAc,CAACC,qBAAqB,GACpCzG,KAAKxC,UAAU,CAAC,YACdgJ,yBAAc,CAAC8F,YAAY,GAC3BzE;gBAEV,OAAO;oBACL0E,YAAYL,QAAQ,KAAKrE;oBACzBpE,SAASyI,QAAQ,wBAAwB;oBACzC3F;oBACA,GAAGwF,KAAK;gBACV;YACF;QACA,KAAKK,0BAAc,CAACjD,UAAU;YAAE;gBAC9B,OAAO;oBACL5C,OAAO2F,QACH1F,yBAAc,CAACO,OAAO,GACtByF,IAAAA,2BAAoB,EAACxM,SAAS0K,oBAC5BlE,yBAAc,CAACI,UAAU,GACzB5G,KAAKxC,UAAU,CAAC,YACdgJ,yBAAc,CAACiG,YAAY,GAC3B5E;oBACR6E,SAAS;wBAAE1M,MAAM;4BAAC;4BAAY,CAAC,iBAAiB,CAAC;yBAAC;wBAAEyC,MAAM;oBAAS;oBACnEgB,SAASkJ,gCAAoB;oBAC7BC,aAAa;oBACb,GAAGb,KAAK;gBACV;YACF;QACA,KAAKK,0BAAc,CAAC/C,MAAM;YAAE;gBAC1B,MAAMwD,aACJf,aACC9L,CAAAA,SAAS8M,gDAAoC,IAC5C9M,SAAS+M,gCAAoB,IAC7B/M,KAAKxC,UAAU,CAAC,OAAM;gBAE1B,IACE,uBAAuB;gBACvBwC,SAASgN,iDAAqC,IAC9ChN,SAASiN,4CAAgC,IACzCjN,SAAS8M,gDAAoC,IAC7C9M,SAASkN,2CAA+B,IACxClN,SAASmN,qDAAyC,EAClD;oBACA,IAAIN,YAAY;wBACd,OAAO;4BACLO,UAAUN,gDAAoC;4BAC9CvG,OAAOC,yBAAc,CAAC6G,eAAe;4BACrC,GAAGtB,KAAK;wBACV;oBACF;oBAEA,OAAO;wBACLqB,UACEpN,KAAKxC,UAAU,CAAC,aAAawC,SAAS,eAClC,eACAiN,4CAAgC;wBACtC1G,OAAOC,yBAAc,CAAC8G,eAAe;wBACrC,GAAGvB,KAAK;oBACV;gBACF;gBAEA,IAAIc,YAAY;oBACd,OAAO;wBACLtG,OAAOC,yBAAc,CAAC6G,eAAe;wBACrC,GAAGtB,KAAK;oBACV;gBACF;gBAEA,OAAO;oBACLxF,OAAOC,yBAAc,CAAC8G,eAAe;oBACrC,GAAGvB,KAAK;gBACV;YACF;QACA;YACE,OAAOH;IACX;AACF", "ignoreList": [0]}