{"version": 3, "sources": ["../../../../src/server/lib/router-utils/setup-dev-bundler.ts"], "sourcesContent": ["import type { NextConfigComplete } from '../../config-shared'\nimport type { FilesystemDynamicRoute } from './filesystem'\nimport type { UnwrapPromise } from '../../../lib/coalesced-function'\nimport {\n  getPageStaticInfo,\n  type MiddlewareMatcher,\n} from '../../../build/analysis/get-page-static-info'\nimport type { RoutesManifest } from '../../../build'\nimport type { MiddlewareRouteMatch } from '../../../shared/lib/router/utils/middleware-route-matcher'\nimport type { PropagateToWorkersField } from './types'\nimport type { NextJsHotReloaderInterface } from '../../dev/hot-reloader-types'\n\nimport { createDefineEnv } from '../../../build/swc'\nimport fs from 'fs'\nimport url from 'url'\nimport path from 'path'\nimport qs from 'querystring'\nimport Watchpack from 'next/dist/compiled/watchpack'\nimport { loadEnvConfig } from '@next/env'\nimport findUp from 'next/dist/compiled/find-up'\nimport { buildCustomRoute } from './filesystem'\nimport * as Log from '../../../build/output/log'\nimport HotReloaderWebpack from '../../dev/hot-reloader-webpack'\nimport { setGlobal } from '../../../trace/shared'\nimport type { Telemetry } from '../../../telemetry/storage'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport loadJsConfig from '../../../build/load-jsconfig'\nimport { createValidFileMatcher } from '../find-page-file'\nimport {\n  EVENT_BUILD_FEATURE_USAGE,\n  eventCliSession,\n} from '../../../telemetry/events'\nimport { getSortedRoutes } from '../../../shared/lib/router/utils'\nimport {\n  getStaticInfoIncludingLayouts,\n  sortByPageExts,\n} from '../../../build/entries'\nimport { verifyTypeScriptSetup } from '../../../lib/verify-typescript-setup'\nimport { verifyPartytownSetup } from '../../../lib/verify-partytown-setup'\nimport { getRouteRegex } from '../../../shared/lib/router/utils/route-regex'\nimport { normalizeAppPath } from '../../../shared/lib/router/utils/app-paths'\nimport { buildDataRoute } from './build-data-route'\nimport { getRouteMatcher } from '../../../shared/lib/router/utils/route-matcher'\nimport { normalizePathSep } from '../../../shared/lib/page-path/normalize-path-sep'\nimport { createClientRouterFilter } from '../../../lib/create-client-router-filter'\nimport { absolutePathToPage } from '../../../shared/lib/page-path/absolute-path-to-page'\nimport { generateInterceptionRoutesRewrites } from '../../../lib/generate-interception-routes-rewrites'\n\nimport {\n  CLIENT_STATIC_FILES_PATH,\n  DEV_CLIENT_PAGES_MANIFEST,\n  DEV_CLIENT_MIDDLEWARE_MANIFEST,\n  PHASE_DEVELOPMENT_SERVER,\n  TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST,\n  ROUTES_MANIFEST,\n  PRERENDER_MANIFEST,\n} from '../../../shared/lib/constants'\n\nimport { getMiddlewareRouteMatcher } from '../../../shared/lib/router/utils/middleware-route-matcher'\n\nimport {\n  isMiddlewareFile,\n  NestedMiddlewareError,\n  isInstrumentationHookFile,\n  getPossibleMiddlewareFilenames,\n  getPossibleInstrumentationHookFilenames,\n} from '../../../build/utils'\nimport { devPageFiles } from '../../../build/webpack/plugins/next-types-plugin/shared'\nimport type { LazyRenderServerInstance } from '../router-server'\nimport { HMR_ACTIONS_SENT_TO_BROWSER } from '../../dev/hot-reloader-types'\nimport { PAGE_TYPES } from '../../../lib/page-types'\nimport { createHotReloaderTurbopack } from '../../dev/hot-reloader-turbopack'\nimport { generateEncryptionKeyBase64 } from '../../app-render/encryption-utils-server'\nimport { isMetadataRouteFile } from '../../../lib/metadata/is-metadata-route'\nimport { normalizeMetadataPageToRoute } from '../../../lib/metadata/get-metadata-route'\nimport { createEnvDefinitions } from '../experimental/create-env-definitions'\nimport { JsConfigPathsPlugin } from '../../../build/webpack/plugins/jsconfig-paths-plugin'\nimport { store as consoleStore } from '../../../build/output/store'\nimport {\n  isPersistentCachingEnabled,\n  ModuleBuildError,\n} from '../../../shared/lib/turbopack/utils'\nimport { getDefineEnv } from '../../../build/define-env'\nimport { TurbopackInternalError } from '../../../shared/lib/turbopack/internal-error'\nimport { normalizePath } from '../../../lib/normalize-path'\nimport { JSON_CONTENT_TYPE_HEADER } from '../../../lib/constants'\nimport {\n  createRouteTypesManifest,\n  writeRouteTypesManifest,\n  writeValidatorFile,\n} from './route-types-utils'\nimport { isParallelRouteSegment } from '../../../shared/lib/segment'\nimport { ensureLeadingSlash } from '../../../shared/lib/page-path/ensure-leading-slash'\n\nexport type SetupOpts = {\n  renderServer: LazyRenderServerInstance\n  dir: string\n  turbo?: boolean\n  appDir?: string\n  pagesDir?: string\n  telemetry: Telemetry\n  isCustomServer?: boolean\n  fsChecker: UnwrapPromise<\n    ReturnType<typeof import('./filesystem').setupFsCheck>\n  >\n  nextConfig: NextConfigComplete\n  port: number\n  onDevServerCleanup: ((listener: () => Promise<void>) => void) | undefined\n  resetFetch: () => void\n}\n\nexport interface DevRoutesManifest {\n  version: number\n  caseSensitive: RoutesManifest['caseSensitive']\n  basePath: RoutesManifest['basePath']\n  rewrites: RoutesManifest['rewrites']\n  redirects: RoutesManifest['redirects']\n  headers: RoutesManifest['headers']\n  i18n: RoutesManifest['i18n']\n  skipMiddlewareUrlNormalize: RoutesManifest['skipMiddlewareUrlNormalize']\n}\n\nexport type ServerFields = {\n  actualMiddlewareFile?: string | undefined\n  actualInstrumentationHookFile?: string | undefined\n  appPathRoutes?: Record<string, string | string[]>\n  middleware?:\n    | {\n        page: string\n        match: MiddlewareRouteMatch\n        matchers?: MiddlewareMatcher[]\n      }\n    | undefined\n  hasAppNotFound?: boolean\n  interceptionRoutes?: ReturnType<\n    typeof import('./filesystem').buildCustomRoute\n  >[]\n  setIsrStatus?: (key: string, value: boolean) => void\n  resetFetch?: () => void\n}\n\nasync function verifyTypeScript(opts: SetupOpts) {\n  let usingTypeScript = false\n  const verifyResult = await verifyTypeScriptSetup({\n    dir: opts.dir,\n    distDir: opts.nextConfig.distDir,\n    intentDirs: [opts.pagesDir, opts.appDir].filter(Boolean) as string[],\n    typeCheckPreflight: false,\n    tsconfigPath: opts.nextConfig.typescript.tsconfigPath,\n    disableStaticImages: opts.nextConfig.images.disableStaticImages,\n    hasAppDir: !!opts.appDir,\n    hasPagesDir: !!opts.pagesDir,\n  })\n\n  if (verifyResult.version) {\n    usingTypeScript = true\n  }\n  return usingTypeScript\n}\n\nexport async function propagateServerField(\n  opts: SetupOpts,\n  field: PropagateToWorkersField,\n  args: any\n) {\n  await opts.renderServer?.instance?.propagateServerField(opts.dir, field, args)\n}\n\nasync function startWatcher(\n  opts: SetupOpts & {\n    isSrcDir: boolean\n  }\n) {\n  const { nextConfig, appDir, pagesDir, dir, resetFetch } = opts\n  const { useFileSystemPublicRoutes } = nextConfig\n\n  const distDir = path.join(opts.dir, opts.nextConfig.distDir)\n\n  setGlobal('distDir', distDir)\n  setGlobal('phase', PHASE_DEVELOPMENT_SERVER)\n\n  const validFileMatcher = createValidFileMatcher(\n    nextConfig.pageExtensions,\n    appDir\n  )\n\n  const serverFields: ServerFields = {}\n\n  // Update logging state once based on next.config.js when initializing\n  consoleStore.setState({\n    logging: nextConfig.logging !== false,\n  })\n\n  const hotReloader: NextJsHotReloaderInterface = opts.turbo\n    ? await createHotReloaderTurbopack(opts, serverFields, distDir, resetFetch)\n    : new HotReloaderWebpack(opts.dir, {\n        isSrcDir: opts.isSrcDir,\n        appDir,\n        pagesDir,\n        distDir,\n        config: opts.nextConfig,\n        buildId: 'development',\n        encryptionKey: await generateEncryptionKeyBase64({\n          isBuild: false,\n          distDir,\n        }),\n        telemetry: opts.telemetry,\n        rewrites: opts.fsChecker.rewrites,\n        previewProps: opts.fsChecker.prerenderManifest.preview,\n        resetFetch,\n      })\n\n  await hotReloader.start()\n\n  // have to write this after starting hot-reloader since that\n  // cleans the dist dir\n  const distTypesDir = path.join(distDir, 'types')\n  await writeRouteTypesManifest(\n    {\n      appRoutes: {},\n      pageRoutes: {},\n      layoutRoutes: {},\n      appRouteHandlerRoutes: {},\n      redirectRoutes: {},\n      rewriteRoutes: {},\n      appPagePaths: new Set(),\n      pagesRouterPagePaths: new Set(),\n      layoutPaths: new Set(),\n      appRouteHandlers: new Set(),\n      pageApiRoutes: new Set(),\n      filePathToRoute: new Map(),\n    },\n    path.join(distTypesDir, 'routes.d.ts'),\n    opts.nextConfig\n  )\n\n  const usingTypeScript = await verifyTypeScript(opts)\n\n  const routesManifestPath = path.join(distDir, ROUTES_MANIFEST)\n  const routesManifest: DevRoutesManifest = {\n    version: 3,\n    caseSensitive: !!nextConfig.experimental.caseSensitiveRoutes,\n    basePath: nextConfig.basePath,\n    rewrites: opts.fsChecker.rewrites,\n    redirects: opts.fsChecker.redirects,\n    headers: opts.fsChecker.headers,\n    i18n: nextConfig.i18n || undefined,\n    skipMiddlewareUrlNormalize: nextConfig.skipMiddlewareUrlNormalize,\n  }\n  await fs.promises.writeFile(\n    routesManifestPath,\n    JSON.stringify(routesManifest)\n  )\n\n  const prerenderManifestPath = path.join(distDir, PRERENDER_MANIFEST)\n  await fs.promises.writeFile(\n    prerenderManifestPath,\n    JSON.stringify(opts.fsChecker.prerenderManifest, null, 2)\n  )\n\n  if (opts.nextConfig.experimental.nextScriptWorkers) {\n    await verifyPartytownSetup(\n      opts.dir,\n      path.join(distDir, CLIENT_STATIC_FILES_PATH)\n    )\n  }\n\n  opts.fsChecker.ensureCallback(async function ensure(item) {\n    if (item.type === 'appFile' || item.type === 'pageFile') {\n      await hotReloader.ensurePage({\n        clientOnly: false,\n        page: item.itemPath,\n        isApp: item.type === 'appFile',\n        definition: undefined,\n      })\n    }\n  })\n\n  let resolved = false\n  let prevSortedRoutes: string[] = []\n\n  await new Promise<void>(async (resolve, reject) => {\n    if (pagesDir) {\n      // Watchpack doesn't emit an event for an empty directory\n      fs.readdir(pagesDir, (_, files) => {\n        if (files?.length) {\n          return\n        }\n\n        if (!resolved) {\n          resolve()\n          resolved = true\n        }\n      })\n    }\n\n    const pages = pagesDir ? [pagesDir] : []\n    const app = appDir ? [appDir] : []\n    const directories = [...pages, ...app]\n\n    const rootDir = pagesDir || appDir\n    const files = [\n      ...getPossibleMiddlewareFilenames(\n        path.join(rootDir!, '..'),\n        nextConfig.pageExtensions\n      ),\n      ...getPossibleInstrumentationHookFilenames(\n        path.join(rootDir!, '..'),\n        nextConfig.pageExtensions\n      ),\n    ]\n    let nestedMiddleware: string[] = []\n\n    const envFiles = [\n      '.env.development.local',\n      '.env.local',\n      '.env.development',\n      '.env',\n    ].map((file) => path.join(dir, file))\n\n    files.push(...envFiles)\n\n    // tsconfig/jsconfig paths hot-reloading\n    const tsconfigPaths = [\n      path.join(dir, 'tsconfig.json'),\n      path.join(dir, 'jsconfig.json'),\n    ] as const\n    files.push(...tsconfigPaths)\n\n    const wp = new Watchpack({\n      ignored: (pathname: string) => {\n        return (\n          !files.some((file) => file.startsWith(pathname)) &&\n          !directories.some(\n            (d) => pathname.startsWith(d) || d.startsWith(pathname)\n          )\n        )\n      },\n    })\n    const fileWatchTimes = new Map()\n    let enabledTypeScript = usingTypeScript\n    let previousClientRouterFilters: any\n    let previousConflictingPagePaths: Set<string> = new Set()\n\n    const routeTypesFilePath = path.join(distDir, 'types', 'routes.d.ts')\n    const validatorFilePath = path.join(distDir, 'types', 'validator.ts')\n\n    wp.on('aggregated', async () => {\n      let middlewareMatchers: MiddlewareMatcher[] | undefined\n      const routedPages: string[] = []\n      const knownFiles = wp.getTimeInfoEntries()\n      const appPaths: Record<string, string[]> = {}\n      const pageNameSet = new Set<string>()\n      const conflictingAppPagePaths = new Set<string>()\n      const appPageFilePaths = new Map<string, string>()\n      const pagesPageFilePaths = new Map<string, string>()\n      const appRouteHandlers: Array<{ route: string; filePath: string }> = []\n      const pageApiRoutes: Array<{ route: string; filePath: string }> = []\n\n      const pageRoutes: Array<{ route: string; filePath: string }> = []\n      const appRoutes: Array<{ route: string; filePath: string }> = []\n      const layoutRoutes: Array<{ route: string; filePath: string }> = []\n      const slots: Array<{ name: string; parent: string }> = []\n\n      let envChange = false\n      let tsconfigChange = false\n      let conflictingPageChange = 0\n      let hasRootAppNotFound = false\n\n      const { appFiles, pageFiles } = opts.fsChecker\n\n      appFiles.clear()\n      pageFiles.clear()\n      devPageFiles.clear()\n\n      const sortedKnownFiles: string[] = [...knownFiles.keys()].sort(\n        sortByPageExts(nextConfig.pageExtensions)\n      )\n\n      for (const fileName of sortedKnownFiles) {\n        if (\n          !files.includes(fileName) &&\n          !directories.some((d) => fileName.startsWith(d))\n        ) {\n          continue\n        }\n        const meta = knownFiles.get(fileName)\n\n        const watchTime = fileWatchTimes.get(fileName)\n        // If the file is showing up for the first time or the meta.timestamp is changed since last time\n        const watchTimeChange =\n          watchTime === undefined ||\n          (watchTime && watchTime !== meta?.timestamp)\n        fileWatchTimes.set(fileName, meta?.timestamp)\n\n        if (envFiles.includes(fileName)) {\n          if (watchTimeChange) {\n            envChange = true\n          }\n          continue\n        }\n\n        if (tsconfigPaths.includes(fileName)) {\n          if (fileName.endsWith('tsconfig.json')) {\n            enabledTypeScript = true\n          }\n          if (watchTimeChange) {\n            tsconfigChange = true\n          }\n          continue\n        }\n\n        if (\n          meta?.accuracy === undefined ||\n          !validFileMatcher.isPageFile(fileName)\n        ) {\n          continue\n        }\n\n        const isAppPath = Boolean(\n          appDir &&\n            normalizePathSep(fileName).startsWith(\n              normalizePathSep(appDir) + '/'\n            )\n        )\n        const isPagePath = Boolean(\n          pagesDir &&\n            normalizePathSep(fileName).startsWith(\n              normalizePathSep(pagesDir) + '/'\n            )\n        )\n\n        const rootFile = absolutePathToPage(fileName, {\n          dir: dir,\n          extensions: nextConfig.pageExtensions,\n          keepIndex: false,\n          pagesType: PAGE_TYPES.ROOT,\n        })\n\n        if (isMiddlewareFile(rootFile)) {\n          const staticInfo = await getStaticInfoIncludingLayouts({\n            pageFilePath: fileName,\n            config: nextConfig,\n            appDir: appDir,\n            page: rootFile,\n            isDev: true,\n            isInsideAppDir: isAppPath,\n            pageExtensions: nextConfig.pageExtensions,\n          })\n          if (nextConfig.output === 'export') {\n            Log.error(\n              'Middleware cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export'\n            )\n            continue\n          }\n          serverFields.actualMiddlewareFile = rootFile\n          await propagateServerField(\n            opts,\n            'actualMiddlewareFile',\n            serverFields.actualMiddlewareFile\n          )\n          middlewareMatchers = staticInfo.middleware?.matchers || [\n            { regexp: '.*', originalSource: '/:path*' },\n          ]\n          continue\n        }\n        if (isInstrumentationHookFile(rootFile)) {\n          serverFields.actualInstrumentationHookFile = rootFile\n          await propagateServerField(\n            opts,\n            'actualInstrumentationHookFile',\n            serverFields.actualInstrumentationHookFile\n          )\n          continue\n        }\n\n        if (fileName.endsWith('.ts') || fileName.endsWith('.tsx')) {\n          enabledTypeScript = true\n        }\n\n        if (!(isAppPath || isPagePath)) {\n          continue\n        }\n\n        // Collect all current filenames for the TS plugin to use\n        devPageFiles.add(fileName)\n\n        let pageName = absolutePathToPage(fileName, {\n          dir: isAppPath ? appDir! : pagesDir!,\n          extensions: nextConfig.pageExtensions,\n          keepIndex: isAppPath,\n          pagesType: isAppPath ? PAGE_TYPES.APP : PAGE_TYPES.PAGES,\n        })\n\n        if (\n          isAppPath &&\n          appDir &&\n          isMetadataRouteFile(\n            fileName.replace(appDir, ''),\n            nextConfig.pageExtensions,\n            true\n          )\n        ) {\n          const staticInfo = await getPageStaticInfo({\n            pageFilePath: fileName,\n            nextConfig: {},\n            page: pageName,\n            isDev: true,\n            pageType: PAGE_TYPES.APP,\n          })\n\n          pageName = normalizeMetadataPageToRoute(\n            pageName,\n            !!(staticInfo.generateSitemaps || staticInfo.generateImageMetadata)\n          )\n        }\n\n        if (\n          !isAppPath &&\n          pageName.startsWith('/api/') &&\n          nextConfig.output === 'export'\n        ) {\n          Log.error(\n            'API Routes cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export'\n          )\n          continue\n        }\n\n        if (isAppPath) {\n          const isRootNotFound = validFileMatcher.isRootNotFound(fileName)\n          hasRootAppNotFound = true\n\n          if (isRootNotFound) {\n            continue\n          }\n\n          // Ignore files/directories starting with `_` in the app directory\n          if (normalizePathSep(pageName).includes('/_')) {\n            continue\n          }\n\n          // Record parallel route slots for layout typing\n          // May run multiple times (e.g. if a parallel route\n          // has both a layout and a page, and children) but that's fine\n          const segments = normalizePathSep(pageName).split('/')\n          for (let i = segments.length - 1; i >= 0; i--) {\n            const segment = segments[i]\n            if (isParallelRouteSegment(segment)) {\n              const parentPath = normalizeAppPath(\n                segments.slice(0, i).join('/')\n              )\n\n              const slotName = segment.slice(1)\n              // check if the slot already exists\n              if (\n                slots.some(\n                  (s) => s.name === slotName && s.parent === parentPath\n                )\n              )\n                continue\n\n              slots.push({\n                name: slotName,\n                parent: parentPath,\n              })\n              break\n            }\n          }\n\n          // Record layouts\n          if (validFileMatcher.isAppLayoutPage(fileName)) {\n            layoutRoutes.push({\n              route: ensureLeadingSlash(\n                normalizeAppPath(normalizePathSep(pageName)).replace(\n                  /\\/layout$/,\n                  ''\n                )\n              ),\n              filePath: path.relative(\n                path.dirname(validatorFilePath),\n                fileName\n              ),\n            })\n          }\n\n          if (!validFileMatcher.isAppRouterPage(fileName)) {\n            continue\n          }\n\n          const originalPageName = pageName\n          pageName = normalizeAppPath(pageName).replace(/%5F/g, '_')\n          if (!appPaths[pageName]) {\n            appPaths[pageName] = []\n          }\n          appPaths[pageName].push(\n            opts.turbo\n              ? // Turbopack outputs the correct path which is normalized with the `_`.\n                originalPageName.replace(/%5F/g, '_')\n              : originalPageName\n          )\n\n          if (useFileSystemPublicRoutes) {\n            appFiles.add(pageName)\n          }\n\n          if (validFileMatcher.isAppRouterRoute(fileName)) {\n            appRouteHandlers.push({\n              route: normalizePathSep(pageName),\n              filePath: path.relative(\n                path.dirname(validatorFilePath),\n                fileName\n              ),\n            })\n          } else {\n            appRoutes.push({\n              route: normalizePathSep(pageName),\n              filePath: path.relative(\n                path.dirname(validatorFilePath),\n                fileName\n              ),\n            })\n          }\n\n          if (routedPages.includes(pageName)) {\n            continue\n          }\n        } else {\n          if (useFileSystemPublicRoutes) {\n            pageFiles.add(pageName)\n            // always add to nextDataRoutes for now but in future only add\n            // entries that actually use getStaticProps/getServerSideProps\n            opts.fsChecker.nextDataRoutes.add(pageName)\n          }\n\n          if (pageName.startsWith('/api/')) {\n            pageApiRoutes.push({\n              route: normalizePathSep(pageName),\n              filePath: path.relative(\n                path.dirname(validatorFilePath),\n                fileName\n              ),\n            })\n          } else {\n            pageRoutes.push({\n              route: normalizePathSep(pageName),\n              filePath: path.relative(\n                path.dirname(validatorFilePath),\n                fileName\n              ),\n            })\n          }\n        }\n\n        // Record pages\n        if (isAppPath) {\n          appPageFilePaths.set(pageName, fileName)\n        } else {\n          pagesPageFilePaths.set(pageName, fileName)\n        }\n\n        if (appDir && pageNameSet.has(pageName)) {\n          conflictingAppPagePaths.add(pageName)\n        } else {\n          pageNameSet.add(pageName)\n        }\n\n        /**\n         * If there is a middleware that is not declared in the root we will\n         * warn without adding it so it doesn't make its way into the system.\n         */\n        if (/[\\\\\\\\/]_middleware$/.test(pageName)) {\n          nestedMiddleware.push(pageName)\n          continue\n        }\n\n        routedPages.push(pageName)\n      }\n\n      const numConflicting = conflictingAppPagePaths.size\n      conflictingPageChange = numConflicting - previousConflictingPagePaths.size\n\n      if (conflictingPageChange !== 0) {\n        if (numConflicting > 0) {\n          let errorMessage = `Conflicting app and page file${\n            numConflicting === 1 ? ' was' : 's were'\n          } found, please remove the conflicting files to continue:\\n`\n\n          for (const p of conflictingAppPagePaths) {\n            const appPath = path.relative(dir, appPageFilePaths.get(p)!)\n            const pagesPath = path.relative(dir, pagesPageFilePaths.get(p)!)\n            errorMessage += `  \"${pagesPath}\" - \"${appPath}\"\\n`\n          }\n          hotReloader.setHmrServerError(new Error(errorMessage))\n        } else if (numConflicting === 0) {\n          hotReloader.clearHmrServerError()\n          await propagateServerField(opts, 'reloadMatchers', undefined)\n        }\n      }\n\n      previousConflictingPagePaths = conflictingAppPagePaths\n\n      let clientRouterFilters: any\n      if (nextConfig.experimental.clientRouterFilter) {\n        clientRouterFilters = createClientRouterFilter(\n          Object.keys(appPaths),\n          nextConfig.experimental.clientRouterFilterRedirects\n            ? ((nextConfig as any)._originalRedirects || []).filter(\n                (r: any) => !r.internal\n              )\n            : [],\n          nextConfig.experimental.clientRouterFilterAllowedRate\n        )\n\n        if (\n          !previousClientRouterFilters ||\n          JSON.stringify(previousClientRouterFilters) !==\n            JSON.stringify(clientRouterFilters)\n        ) {\n          envChange = true\n          previousClientRouterFilters = clientRouterFilters\n        }\n      }\n\n      if (!usingTypeScript && enabledTypeScript) {\n        // we tolerate the error here as this is best effort\n        // and the manual install command will be shown\n        await verifyTypeScript(opts)\n          .then(() => {\n            tsconfigChange = true\n          })\n          .catch(() => {})\n      }\n\n      if (envChange || tsconfigChange) {\n        if (envChange) {\n          const { loadedEnvFiles } = loadEnvConfig(\n            dir,\n            process.env.NODE_ENV === 'development',\n            Log,\n            true,\n            (envFilePath) => {\n              Log.info(`Reload env: ${envFilePath}`)\n            }\n          )\n\n          if (usingTypeScript && nextConfig.experimental?.typedEnv) {\n            // do not await, this is not essential for further process\n            createEnvDefinitions({\n              distDir,\n              loadedEnvFiles: [\n                ...loadedEnvFiles,\n                {\n                  path: nextConfig.configFileName,\n                  env: nextConfig.env,\n                  contents: '',\n                },\n              ],\n            })\n          }\n\n          await propagateServerField(opts, 'loadEnvConfig', [\n            { dev: true, forceReload: true, silent: true },\n          ])\n        }\n        let tsconfigResult:\n          | UnwrapPromise<ReturnType<typeof loadJsConfig>>\n          | undefined\n\n        if (tsconfigChange) {\n          try {\n            tsconfigResult = await loadJsConfig(dir, nextConfig)\n          } catch (_) {\n            /* do we want to log if there are syntax errors in tsconfig while editing? */\n          }\n        }\n\n        if (hotReloader.turbopackProject) {\n          const hasRewrites =\n            opts.fsChecker.rewrites.afterFiles.length > 0 ||\n            opts.fsChecker.rewrites.beforeFiles.length > 0 ||\n            opts.fsChecker.rewrites.fallback.length > 0\n\n          const rootPath =\n            opts.nextConfig.turbopack?.root ||\n            opts.nextConfig.outputFileTracingRoot ||\n            opts.dir\n          await hotReloader.turbopackProject.update({\n            defineEnv: createDefineEnv({\n              isTurbopack: true,\n              clientRouterFilters,\n              config: nextConfig,\n              dev: true,\n              distDir,\n              fetchCacheKeyPrefix:\n                opts.nextConfig.experimental.fetchCacheKeyPrefix,\n              hasRewrites,\n              // TODO: Implement\n              middlewareMatchers: undefined,\n              projectPath: opts.dir,\n              rewrites: opts.fsChecker.rewrites,\n            }),\n            rootPath,\n            projectPath: normalizePath(path.relative(rootPath, dir)),\n          })\n        }\n\n        hotReloader.activeWebpackConfigs?.forEach((config, idx) => {\n          const isClient = idx === 0\n          const isNodeServer = idx === 1\n          const isEdgeServer = idx === 2\n          const hasRewrites =\n            opts.fsChecker.rewrites.afterFiles.length > 0 ||\n            opts.fsChecker.rewrites.beforeFiles.length > 0 ||\n            opts.fsChecker.rewrites.fallback.length > 0\n\n          if (tsconfigChange) {\n            config.resolve?.plugins?.forEach((plugin: any) => {\n              // look for the JsConfigPathsPlugin and update with\n              // the latest paths/baseUrl config\n              if (plugin instanceof JsConfigPathsPlugin && tsconfigResult) {\n                const { resolvedBaseUrl, jsConfig } = tsconfigResult\n                const currentResolvedBaseUrl = plugin.resolvedBaseUrl\n                const resolvedUrlIndex = config.resolve?.modules?.findIndex(\n                  (item) => item === currentResolvedBaseUrl?.baseUrl\n                )\n\n                if (resolvedBaseUrl) {\n                  if (\n                    resolvedBaseUrl.baseUrl !== currentResolvedBaseUrl?.baseUrl\n                  ) {\n                    // remove old baseUrl and add new one\n                    if (resolvedUrlIndex && resolvedUrlIndex > -1) {\n                      config.resolve?.modules?.splice(resolvedUrlIndex, 1)\n                    }\n\n                    // If the resolvedBaseUrl is implicit we only remove the previous value.\n                    // Only add the baseUrl if it's explicitly set in tsconfig/jsconfig\n                    if (!resolvedBaseUrl.isImplicit) {\n                      config.resolve?.modules?.push(resolvedBaseUrl.baseUrl)\n                    }\n                  }\n                }\n\n                if (jsConfig?.compilerOptions?.paths && resolvedBaseUrl) {\n                  Object.keys(plugin.paths).forEach((key) => {\n                    delete plugin.paths[key]\n                  })\n                  Object.assign(plugin.paths, jsConfig.compilerOptions.paths)\n                  plugin.resolvedBaseUrl = resolvedBaseUrl\n                }\n              }\n            })\n          }\n\n          if (envChange) {\n            config.plugins?.forEach((plugin: any) => {\n              // we look for the DefinePlugin definitions so we can\n              // update them on the active compilers\n              if (\n                plugin &&\n                typeof plugin.definitions === 'object' &&\n                plugin.definitions.__NEXT_DEFINE_ENV\n              ) {\n                const newDefine = getDefineEnv({\n                  isTurbopack: false,\n                  clientRouterFilters,\n                  config: nextConfig,\n                  dev: true,\n                  distDir,\n                  fetchCacheKeyPrefix:\n                    opts.nextConfig.experimental.fetchCacheKeyPrefix,\n                  hasRewrites,\n                  isClient,\n                  isEdgeServer,\n                  isNodeServer,\n                  middlewareMatchers: undefined,\n                  projectPath: opts.dir,\n                  rewrites: opts.fsChecker.rewrites,\n                })\n\n                Object.keys(plugin.definitions).forEach((key) => {\n                  if (!(key in newDefine)) {\n                    delete plugin.definitions[key]\n                  }\n                })\n                Object.assign(plugin.definitions, newDefine)\n              }\n            })\n          }\n        })\n        await hotReloader.invalidate({\n          reloadAfterInvalidation: envChange,\n        })\n      }\n\n      if (nestedMiddleware.length > 0) {\n        Log.error(\n          new NestedMiddlewareError(\n            nestedMiddleware,\n            dir,\n            (pagesDir || appDir)!\n          ).message\n        )\n        nestedMiddleware = []\n      }\n\n      // Make sure to sort parallel routes to make the result deterministic.\n      serverFields.appPathRoutes = Object.fromEntries(\n        Object.entries(appPaths).map(([k, v]) => [k, v.sort()])\n      )\n      await propagateServerField(\n        opts,\n        'appPathRoutes',\n        serverFields.appPathRoutes\n      )\n\n      // TODO: pass this to fsChecker/next-dev-server?\n      serverFields.middleware = middlewareMatchers\n        ? {\n            match: null as any,\n            page: '/',\n            matchers: middlewareMatchers,\n          }\n        : undefined\n\n      await propagateServerField(opts, 'middleware', serverFields.middleware)\n      serverFields.hasAppNotFound = hasRootAppNotFound\n\n      opts.fsChecker.middlewareMatcher = serverFields.middleware?.matchers\n        ? getMiddlewareRouteMatcher(serverFields.middleware?.matchers)\n        : undefined\n\n      const interceptionRoutes = generateInterceptionRoutesRewrites(\n        Object.keys(appPaths),\n        opts.nextConfig.basePath\n      ).map((item) =>\n        buildCustomRoute(\n          'before_files_rewrite',\n          item,\n          opts.nextConfig.basePath,\n          opts.nextConfig.experimental.caseSensitiveRoutes\n        )\n      )\n\n      opts.fsChecker.rewrites.beforeFiles.push(...interceptionRoutes)\n\n      const exportPathMap =\n        (typeof nextConfig.exportPathMap === 'function' &&\n          (await nextConfig.exportPathMap?.(\n            {},\n            {\n              dev: true,\n              dir: opts.dir,\n              outDir: null,\n              distDir: distDir,\n              buildId: 'development',\n            }\n          ))) ||\n        {}\n\n      const exportPathMapEntries = Object.entries(exportPathMap || {})\n\n      if (exportPathMapEntries.length > 0) {\n        opts.fsChecker.exportPathMapRoutes = exportPathMapEntries.map(\n          ([key, value]) =>\n            buildCustomRoute(\n              'before_files_rewrite',\n              {\n                source: key,\n                destination: `${value.page}${\n                  value.query ? '?' : ''\n                }${qs.stringify(value.query)}`,\n              },\n              opts.nextConfig.basePath,\n              opts.nextConfig.experimental.caseSensitiveRoutes\n            )\n        )\n      }\n\n      try {\n        // we serve a separate manifest with all pages for the client in\n        // dev mode so that we can match a page after a rewrite on the client\n        // before it has been built and is populated in the _buildManifest\n        const sortedRoutes = getSortedRoutes(routedPages)\n\n        opts.fsChecker.dynamicRoutes = sortedRoutes.map(\n          (page): FilesystemDynamicRoute => {\n            const regex = getRouteRegex(page)\n            return {\n              regex: regex.re.toString(),\n              match: getRouteMatcher(regex),\n              page,\n            }\n          }\n        )\n\n        const dataRoutes: typeof opts.fsChecker.dynamicRoutes = []\n\n        for (const page of sortedRoutes) {\n          const route = buildDataRoute(page, 'development')\n          const routeRegex = getRouteRegex(route.page)\n          dataRoutes.push({\n            ...route,\n            regex: routeRegex.re.toString(),\n            match: getRouteMatcher({\n              // TODO: fix this in the manifest itself, must also be fixed in\n              // upstream builder that relies on this\n              re: opts.nextConfig.i18n\n                ? new RegExp(\n                    route.dataRouteRegex.replace(\n                      `/development/`,\n                      `/development/(?<nextLocale>[^/]+?)/`\n                    )\n                  )\n                : new RegExp(route.dataRouteRegex),\n              groups: routeRegex.groups,\n            }),\n          })\n        }\n        opts.fsChecker.dynamicRoutes.unshift(...dataRoutes)\n\n        if (!prevSortedRoutes?.every((val, idx) => val === sortedRoutes[idx])) {\n          const addedRoutes = sortedRoutes.filter(\n            (route) => !prevSortedRoutes.includes(route)\n          )\n          const removedRoutes = prevSortedRoutes.filter(\n            (route) => !sortedRoutes.includes(route)\n          )\n\n          // emit the change so clients fetch the update\n          hotReloader.send({\n            action: HMR_ACTIONS_SENT_TO_BROWSER.DEV_PAGES_MANIFEST_UPDATE,\n            data: [\n              {\n                devPagesManifest: true,\n              },\n            ],\n          })\n\n          addedRoutes.forEach((route) => {\n            hotReloader.send({\n              action: HMR_ACTIONS_SENT_TO_BROWSER.ADDED_PAGE,\n              data: [route],\n            })\n          })\n\n          removedRoutes.forEach((route) => {\n            hotReloader.send({\n              action: HMR_ACTIONS_SENT_TO_BROWSER.REMOVED_PAGE,\n              data: [route],\n            })\n          })\n        }\n        prevSortedRoutes = sortedRoutes\n\n        if (usingTypeScript) {\n          const routeTypesManifest = await createRouteTypesManifest({\n            dir,\n            pageRoutes,\n            appRoutes,\n            layoutRoutes,\n            slots,\n            redirects: opts.nextConfig.redirects,\n            rewrites: opts.nextConfig.rewrites,\n            appRouteHandlers,\n            pageApiRoutes,\n          })\n\n          await writeRouteTypesManifest(\n            routeTypesManifest,\n            routeTypesFilePath,\n            opts.nextConfig\n          )\n          await writeValidatorFile(routeTypesManifest, validatorFilePath)\n        }\n\n        if (!resolved) {\n          resolve()\n          resolved = true\n        }\n      } catch (e) {\n        if (!resolved) {\n          reject(e)\n          resolved = true\n        } else {\n          Log.warn('Failed to reload dynamic routes:', e)\n        }\n      } finally {\n        // Reload the matchers. The filesystem would have been written to,\n        // and the matchers need to re-scan it to update the router.\n        await propagateServerField(opts, 'reloadMatchers', undefined)\n      }\n    })\n\n    wp.watch({ directories: [dir], startTime: 0 })\n  })\n\n  const clientPagesManifestPath = `/_next/${CLIENT_STATIC_FILES_PATH}/development/${DEV_CLIENT_PAGES_MANIFEST}`\n  opts.fsChecker.devVirtualFsItems.add(clientPagesManifestPath)\n\n  const devMiddlewareManifestPath = `/_next/${CLIENT_STATIC_FILES_PATH}/development/${DEV_CLIENT_MIDDLEWARE_MANIFEST}`\n  opts.fsChecker.devVirtualFsItems.add(devMiddlewareManifestPath)\n\n  const devTurbopackMiddlewareManifestPath = `/_next/${CLIENT_STATIC_FILES_PATH}/development/${TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST}`\n  opts.fsChecker.devVirtualFsItems.add(devTurbopackMiddlewareManifestPath)\n\n  async function requestHandler(req: IncomingMessage, res: ServerResponse) {\n    const parsedUrl = url.parse(req.url || '/')\n\n    if (parsedUrl.pathname?.includes(clientPagesManifestPath)) {\n      res.statusCode = 200\n      res.setHeader('Content-Type', JSON_CONTENT_TYPE_HEADER)\n      res.end(\n        JSON.stringify({\n          pages: prevSortedRoutes.filter(\n            (route) => !opts.fsChecker.appFiles.has(route)\n          ),\n        })\n      )\n      return { finished: true }\n    }\n\n    if (\n      parsedUrl.pathname?.includes(devMiddlewareManifestPath) ||\n      parsedUrl.pathname?.includes(devTurbopackMiddlewareManifestPath)\n    ) {\n      res.statusCode = 200\n      res.setHeader('Content-Type', JSON_CONTENT_TYPE_HEADER)\n      res.end(JSON.stringify(serverFields.middleware?.matchers || []))\n      return { finished: true }\n    }\n    return { finished: false }\n  }\n\n  function logErrorWithOriginalStack(\n    err: unknown,\n    type?: 'unhandledRejection' | 'uncaughtException' | 'warning' | 'app-dir'\n  ) {\n    if (err instanceof ModuleBuildError) {\n      // Errors that may come from issues from the user's code\n      Log.error(err.message)\n    } else if (err instanceof TurbopackInternalError) {\n      // An internal Turbopack error that has been handled by next-swc, written\n      // to disk and a simplified message shown to user on the Rust side.\n    } else if (type === 'warning') {\n      Log.warn(err)\n    } else if (type === 'app-dir') {\n      Log.error(err)\n    } else if (type) {\n      Log.error(`${type}:`, err)\n    } else {\n      Log.error(err)\n    }\n  }\n\n  return {\n    serverFields,\n    hotReloader,\n    requestHandler,\n    logErrorWithOriginalStack,\n\n    async ensureMiddleware(requestUrl?: string) {\n      if (!serverFields.actualMiddlewareFile) return\n      return hotReloader.ensurePage({\n        page: serverFields.actualMiddlewareFile,\n        clientOnly: false,\n        definition: undefined,\n        url: requestUrl,\n      })\n    },\n  }\n}\n\nexport async function setupDevBundler(opts: SetupOpts) {\n  const isSrcDir = path\n    .relative(opts.dir, opts.pagesDir || opts.appDir || '')\n    .startsWith('src')\n\n  const result = await startWatcher({\n    ...opts,\n    isSrcDir,\n  })\n\n  opts.telemetry.record(\n    eventCliSession(\n      path.join(opts.dir, opts.nextConfig.distDir),\n      opts.nextConfig,\n      {\n        webpackVersion: 5,\n        isSrcDir,\n        turboFlag: !!opts.turbo,\n        cliCommand: 'dev',\n        appDir: !!opts.appDir,\n        pagesDir: !!opts.pagesDir,\n        isCustomServer: !!opts.isCustomServer,\n        hasNowJson: !!(await findUp('now.json', { cwd: opts.dir })),\n      }\n    )\n  )\n\n  // Track build features for dev server here:\n  opts.telemetry.record({\n    eventName: EVENT_BUILD_FEATURE_USAGE,\n    payload: {\n      featureName: 'turbopackPersistentCaching',\n      invocationCount: isPersistentCachingEnabled(opts.nextConfig) ? 1 : 0,\n    },\n  })\n\n  return result\n}\n\nexport type DevBundler = Awaited<ReturnType<typeof setupDevBundler>>\n\n// Returns a trace rewritten through Turbopack's sourcemaps\n"], "names": ["getPageStaticInfo", "createDefineEnv", "fs", "url", "path", "qs", "Watchpack", "loadEnvConfig", "findUp", "buildCustomRoute", "Log", "HotReloaderWebpack", "setGlobal", "loadJsConfig", "createValidFileMatcher", "EVENT_BUILD_FEATURE_USAGE", "eventCliSession", "getSortedRoutes", "getStaticInfoIncludingLayouts", "sortByPageExts", "verifyTypeScriptSetup", "verifyPartytownSetup", "getRouteRegex", "normalizeAppPath", "buildDataRoute", "getRouteMatcher", "normalizePathSep", "createClientRouterFilter", "absolutePathToPage", "generateInterceptionRoutesRewrites", "CLIENT_STATIC_FILES_PATH", "DEV_CLIENT_PAGES_MANIFEST", "DEV_CLIENT_MIDDLEWARE_MANIFEST", "PHASE_DEVELOPMENT_SERVER", "TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST", "ROUTES_MANIFEST", "PRERENDER_MANIFEST", "getMiddlewareRouteMatcher", "isMiddlewareFile", "NestedMiddlewareError", "isInstrumentationHookFile", "getPossibleMiddlewareFilenames", "getPossibleInstrumentationHookFilenames", "devPageFiles", "HMR_ACTIONS_SENT_TO_BROWSER", "PAGE_TYPES", "createHotReloaderTurbopack", "generateEncryptionKeyBase64", "isMetadataRouteFile", "normalizeMetadataPageToRoute", "createEnvDefinitions", "JsConfigPathsPlugin", "store", "consoleStore", "isPersistentCachingEnabled", "ModuleBuildError", "getDefineEnv", "TurbopackInternalError", "normalizePath", "JSON_CONTENT_TYPE_HEADER", "createRouteTypesManifest", "writeRouteTypesManifest", "writeValidatorFile", "isParallelRouteSegment", "ensureLeadingSlash", "verifyTypeScript", "opts", "usingTypeScript", "verifyResult", "dir", "distDir", "nextConfig", "intentDirs", "pagesDir", "appDir", "filter", "Boolean", "typeCheckPreflight", "tsconfigPath", "typescript", "disableStaticImages", "images", "hasAppDir", "hasPagesDir", "version", "propagateServerField", "field", "args", "renderServer", "instance", "startWatcher", "resetFetch", "useFileSystemPublicRoutes", "join", "validFile<PERSON><PERSON><PERSON>", "pageExtensions", "serverFields", "setState", "logging", "hotReloader", "turbo", "isSrcDir", "config", "buildId", "<PERSON><PERSON><PERSON>", "isBuild", "telemetry", "rewrites", "fs<PERSON><PERSON><PERSON>", "previewProps", "prerenderManifest", "preview", "start", "distTypesDir", "appRoutes", "pageRoutes", "layoutRoutes", "appRouteHandlerRoutes", "redirectRoutes", "rewriteRoutes", "appPagePaths", "Set", "pagesRouterPagePaths", "layoutPaths", "appRouteHandlers", "pageApiRoutes", "filePathToRoute", "Map", "routesManifestPath", "routesManifest", "caseSensitive", "experimental", "caseSensitiveRoutes", "basePath", "redirects", "headers", "i18n", "undefined", "skipMiddlewareUrlNormalize", "promises", "writeFile", "JSON", "stringify", "prerenderManifestPath", "nextScriptWorkers", "ensure<PERSON><PERSON>back", "ensure", "item", "type", "ensurePage", "clientOnly", "page", "itemPath", "isApp", "definition", "resolved", "prevSortedRoutes", "Promise", "resolve", "reject", "readdir", "_", "files", "length", "pages", "app", "directories", "rootDir", "nestedMiddleware", "envFiles", "map", "file", "push", "tsconfigPaths", "wp", "ignored", "pathname", "some", "startsWith", "d", "fileWatchTimes", "enabledTypeScript", "previousClientRouterFilters", "previousConflictingPagePaths", "routeTypesFilePath", "validatorFilePath", "on", "middlewareMatchers", "routedPages", "knownFiles", "getTimeInfoEntries", "appPaths", "pageNameSet", "conflictingAppPagePaths", "appPageFilePaths", "pagesPageFilePaths", "slots", "envChange", "tsconfigChange", "conflictingPageChange", "hasRootAppNotFound", "appFiles", "pageFiles", "clear", "sortedKnownFiles", "keys", "sort", "fileName", "includes", "meta", "get", "watchTime", "watchTimeChange", "timestamp", "set", "endsWith", "accuracy", "isPageFile", "isAppPath", "isPagePath", "rootFile", "extensions", "keepIndex", "pagesType", "ROOT", "staticInfo", "pageFilePath", "isDev", "isInsideAppDir", "output", "error", "actualMiddlewareFile", "middleware", "matchers", "regexp", "originalSource", "actualInstrumentationHookFile", "add", "pageName", "APP", "PAGES", "replace", "pageType", "generateSitemaps", "generateImageMetadata", "isRootNotFound", "segments", "split", "i", "segment", "parentPath", "slice", "slotName", "s", "name", "parent", "isAppLayoutPage", "route", "filePath", "relative", "dirname", "isAppRouterPage", "originalPageName", "isAppRouterRoute", "nextDataRoutes", "has", "test", "numConflicting", "size", "errorMessage", "p", "appPath", "pagesPath", "setHmrServerError", "Error", "clearHmrServerError", "clientRouterFilters", "clientRouterFilter", "Object", "clientRouterFilterRedirects", "_originalRedirects", "r", "internal", "clientRouterFilterAllowedRate", "then", "catch", "loadedEnvFiles", "process", "env", "NODE_ENV", "env<PERSON><PERSON><PERSON><PERSON>", "info", "typedEnv", "configFileName", "contents", "dev", "forceReload", "silent", "tsconfigResult", "turbopackProject", "hasRewrites", "afterFiles", "beforeFiles", "fallback", "rootPath", "turbopack", "root", "outputFileTracingRoot", "update", "defineEnv", "isTurbopack", "fetchCacheKeyPrefix", "projectPath", "activeWebpackConfigs", "for<PERSON>ach", "idx", "isClient", "isNodeServer", "isEdgeServer", "plugins", "plugin", "jsConfig", "resolvedBaseUrl", "currentResolvedBaseUrl", "resolvedUrlIndex", "modules", "findIndex", "baseUrl", "splice", "isImplicit", "compilerOptions", "paths", "key", "assign", "definitions", "__NEXT_DEFINE_ENV", "newDefine", "invalidate", "reloadAfterInvalidation", "message", "appPathRoutes", "fromEntries", "entries", "k", "v", "match", "hasAppNotFound", "middlewareMatcher", "interceptionRoutes", "exportPathMap", "outDir", "exportPathMapEntries", "exportPathMapRoutes", "value", "source", "destination", "query", "sortedRoutes", "dynamicRoutes", "regex", "re", "toString", "dataRoutes", "routeRegex", "RegExp", "dataRouteRegex", "groups", "unshift", "every", "val", "addedRoutes", "removedRoutes", "send", "action", "DEV_PAGES_MANIFEST_UPDATE", "data", "devPagesManifest", "ADDED_PAGE", "REMOVED_PAGE", "routeTypesManifest", "e", "warn", "watch", "startTime", "clientPagesManifestPath", "devVirtualFsItems", "devMiddlewareManifestPath", "devTurbopackMiddlewareManifestPath", "requestHandler", "req", "res", "parsedUrl", "parse", "statusCode", "<PERSON><PERSON><PERSON><PERSON>", "end", "finished", "logErrorWithOriginalStack", "err", "ensureMiddleware", "requestUrl", "setupDevBundler", "result", "record", "webpackVersion", "turboFlag", "cliCommand", "isCustomServer", "has<PERSON>ow<PERSON><PERSON>", "cwd", "eventName", "payload", "featureName", "invocationCount"], "mappings": "AAGA,SACEA,iBAAiB,QAEZ,+CAA8C;AAMrD,SAASC,eAAe,QAAQ,qBAAoB;AACpD,OAAOC,QAAQ,KAAI;AACnB,OAAOC,SAAS,MAAK;AACrB,OAAOC,UAAU,OAAM;AACvB,OAAOC,QAAQ,cAAa;AAC5B,OAAOC,eAAe,+BAA8B;AACpD,SAASC,aAAa,QAAQ,YAAW;AACzC,OAAOC,YAAY,6BAA4B;AAC/C,SAASC,gBAAgB,QAAQ,eAAc;AAC/C,YAAYC,SAAS,4BAA2B;AAChD,OAAOC,wBAAwB,iCAAgC;AAC/D,SAASC,SAAS,QAAQ,wBAAuB;AAGjD,OAAOC,kBAAkB,+BAA8B;AACvD,SAASC,sBAAsB,QAAQ,oBAAmB;AAC1D,SACEC,yBAAyB,EACzBC,eAAe,QACV,4BAA2B;AAClC,SAASC,eAAe,QAAQ,mCAAkC;AAClE,SACEC,6BAA6B,EAC7BC,cAAc,QACT,yBAAwB;AAC/B,SAASC,qBAAqB,QAAQ,uCAAsC;AAC5E,SAASC,oBAAoB,QAAQ,sCAAqC;AAC1E,SAASC,aAAa,QAAQ,+CAA8C;AAC5E,SAASC,gBAAgB,QAAQ,6CAA4C;AAC7E,SAASC,cAAc,QAAQ,qBAAoB;AACnD,SAASC,eAAe,QAAQ,iDAAgD;AAChF,SAASC,gBAAgB,QAAQ,mDAAkD;AACnF,SAASC,wBAAwB,QAAQ,2CAA0C;AACnF,SAASC,kBAAkB,QAAQ,sDAAqD;AACxF,SAASC,kCAAkC,QAAQ,qDAAoD;AAEvG,SACEC,wBAAwB,EACxBC,yBAAyB,EACzBC,8BAA8B,EAC9BC,wBAAwB,EACxBC,oCAAoC,EACpCC,eAAe,EACfC,kBAAkB,QACb,gCAA+B;AAEtC,SAASC,yBAAyB,QAAQ,4DAA2D;AAErG,SACEC,gBAAgB,EAChBC,qBAAqB,EACrBC,yBAAyB,EACzBC,8BAA8B,EAC9BC,uCAAuC,QAClC,uBAAsB;AAC7B,SAASC,YAAY,QAAQ,0DAAyD;AAEtF,SAASC,2BAA2B,QAAQ,+BAA8B;AAC1E,SAASC,UAAU,QAAQ,0BAAyB;AACpD,SAASC,0BAA0B,QAAQ,mCAAkC;AAC7E,SAASC,2BAA2B,QAAQ,2CAA0C;AACtF,SAASC,mBAAmB,QAAQ,0CAAyC;AAC7E,SAASC,4BAA4B,QAAQ,2CAA0C;AACvF,SAASC,oBAAoB,QAAQ,yCAAwC;AAC7E,SAASC,mBAAmB,QAAQ,uDAAsD;AAC1F,SAASC,SAASC,YAAY,QAAQ,8BAA6B;AACnE,SACEC,0BAA0B,EAC1BC,gBAAgB,QACX,sCAAqC;AAC5C,SAASC,YAAY,QAAQ,4BAA2B;AACxD,SAASC,sBAAsB,QAAQ,+CAA8C;AACrF,SAASC,aAAa,QAAQ,8BAA6B;AAC3D,SAASC,wBAAwB,QAAQ,yBAAwB;AACjE,SACEC,wBAAwB,EACxBC,uBAAuB,EACvBC,kBAAkB,QACb,sBAAqB;AAC5B,SAASC,sBAAsB,QAAQ,8BAA6B;AACpE,SAASC,kBAAkB,QAAQ,qDAAoD;AAiDvF,eAAeC,iBAAiBC,IAAe;IAC7C,IAAIC,kBAAkB;IACtB,MAAMC,eAAe,MAAMhD,sBAAsB;QAC/CiD,KAAKH,KAAKG,GAAG;QACbC,SAASJ,KAAKK,UAAU,CAACD,OAAO;QAChCE,YAAY;YAACN,KAAKO,QAAQ;YAAEP,KAAKQ,MAAM;SAAC,CAACC,MAAM,CAACC;QAChDC,oBAAoB;QACpBC,cAAcZ,KAAKK,UAAU,CAACQ,UAAU,CAACD,YAAY;QACrDE,qBAAqBd,KAAKK,UAAU,CAACU,MAAM,CAACD,mBAAmB;QAC/DE,WAAW,CAAC,CAAChB,KAAKQ,MAAM;QACxBS,aAAa,CAAC,CAACjB,KAAKO,QAAQ;IAC9B;IAEA,IAAIL,aAAagB,OAAO,EAAE;QACxBjB,kBAAkB;IACpB;IACA,OAAOA;AACT;AAEA,OAAO,eAAekB,qBACpBnB,IAAe,EACfoB,KAA8B,EAC9BC,IAAS;QAEHrB,6BAAAA;IAAN,QAAMA,qBAAAA,KAAKsB,YAAY,sBAAjBtB,8BAAAA,mBAAmBuB,QAAQ,qBAA3BvB,4BAA6BmB,oBAAoB,CAACnB,KAAKG,GAAG,EAAEiB,OAAOC;AAC3E;AAEA,eAAeG,aACbxB,IAEC;IAED,MAAM,EAAEK,UAAU,EAAEG,MAAM,EAAED,QAAQ,EAAEJ,GAAG,EAAEsB,UAAU,EAAE,GAAGzB;IAC1D,MAAM,EAAE0B,yBAAyB,EAAE,GAAGrB;IAEtC,MAAMD,UAAUlE,KAAKyF,IAAI,CAAC3B,KAAKG,GAAG,EAAEH,KAAKK,UAAU,CAACD,OAAO;IAE3D1D,UAAU,WAAW0D;IACrB1D,UAAU,SAASqB;IAEnB,MAAM6D,mBAAmBhF,uBACvByD,WAAWwB,cAAc,EACzBrB;IAGF,MAAMsB,eAA6B,CAAC;IAEpC,sEAAsE;IACtE3C,aAAa4C,QAAQ,CAAC;QACpBC,SAAS3B,WAAW2B,OAAO,KAAK;IAClC;IAEA,MAAMC,cAA0CjC,KAAKkC,KAAK,GACtD,MAAMtD,2BAA2BoB,MAAM8B,cAAc1B,SAASqB,cAC9D,IAAIhF,mBAAmBuD,KAAKG,GAAG,EAAE;QAC/BgC,UAAUnC,KAAKmC,QAAQ;QACvB3B;QACAD;QACAH;QACAgC,QAAQpC,KAAKK,UAAU;QACvBgC,SAAS;QACTC,eAAe,MAAMzD,4BAA4B;YAC/C0D,SAAS;YACTnC;QACF;QACAoC,WAAWxC,KAAKwC,SAAS;QACzBC,UAAUzC,KAAK0C,SAAS,CAACD,QAAQ;QACjCE,cAAc3C,KAAK0C,SAAS,CAACE,iBAAiB,CAACC,OAAO;QACtDpB;IACF;IAEJ,MAAMQ,YAAYa,KAAK;IAEvB,4DAA4D;IAC5D,sBAAsB;IACtB,MAAMC,eAAe7G,KAAKyF,IAAI,CAACvB,SAAS;IACxC,MAAMT,wBACJ;QACEqD,WAAW,CAAC;QACZC,YAAY,CAAC;QACbC,cAAc,CAAC;QACfC,uBAAuB,CAAC;QACxBC,gBAAgB,CAAC;QACjBC,eAAe,CAAC;QAChBC,cAAc,IAAIC;QAClBC,sBAAsB,IAAID;QAC1BE,aAAa,IAAIF;QACjBG,kBAAkB,IAAIH;QACtBI,eAAe,IAAIJ;QACnBK,iBAAiB,IAAIC;IACvB,GACA3H,KAAKyF,IAAI,CAACoB,cAAc,gBACxB/C,KAAKK,UAAU;IAGjB,MAAMJ,kBAAkB,MAAMF,iBAAiBC;IAE/C,MAAM8D,qBAAqB5H,KAAKyF,IAAI,CAACvB,SAASnC;IAC9C,MAAM8F,iBAAoC;QACxC7C,SAAS;QACT8C,eAAe,CAAC,CAAC3D,WAAW4D,YAAY,CAACC,mBAAmB;QAC5DC,UAAU9D,WAAW8D,QAAQ;QAC7B1B,UAAUzC,KAAK0C,SAAS,CAACD,QAAQ;QACjC2B,WAAWpE,KAAK0C,SAAS,CAAC0B,SAAS;QACnCC,SAASrE,KAAK0C,SAAS,CAAC2B,OAAO;QAC/BC,MAAMjE,WAAWiE,IAAI,IAAIC;QACzBC,4BAA4BnE,WAAWmE,0BAA0B;IACnE;IACA,MAAMxI,GAAGyI,QAAQ,CAACC,SAAS,CACzBZ,oBACAa,KAAKC,SAAS,CAACb;IAGjB,MAAMc,wBAAwB3I,KAAKyF,IAAI,CAACvB,SAASlC;IACjD,MAAMlC,GAAGyI,QAAQ,CAACC,SAAS,CACzBG,uBACAF,KAAKC,SAAS,CAAC5E,KAAK0C,SAAS,CAACE,iBAAiB,EAAE,MAAM;IAGzD,IAAI5C,KAAKK,UAAU,CAAC4D,YAAY,CAACa,iBAAiB,EAAE;QAClD,MAAM3H,qBACJ6C,KAAKG,GAAG,EACRjE,KAAKyF,IAAI,CAACvB,SAASxC;IAEvB;IAEAoC,KAAK0C,SAAS,CAACqC,cAAc,CAAC,eAAeC,OAAOC,IAAI;QACtD,IAAIA,KAAKC,IAAI,KAAK,aAAaD,KAAKC,IAAI,KAAK,YAAY;YACvD,MAAMjD,YAAYkD,UAAU,CAAC;gBAC3BC,YAAY;gBACZC,MAAMJ,KAAKK,QAAQ;gBACnBC,OAAON,KAAKC,IAAI,KAAK;gBACrBM,YAAYjB;YACd;QACF;IACF;IAEA,IAAIkB,WAAW;IACf,IAAIC,mBAA6B,EAAE;IAEnC,MAAM,IAAIC,QAAc,OAAOC,SAASC;QACtC,IAAItF,UAAU;YACZ,yDAAyD;YACzDvE,GAAG8J,OAAO,CAACvF,UAAU,CAACwF,GAAGC;gBACvB,IAAIA,yBAAAA,MAAOC,MAAM,EAAE;oBACjB;gBACF;gBAEA,IAAI,CAACR,UAAU;oBACbG;oBACAH,WAAW;gBACb;YACF;QACF;QAEA,MAAMS,QAAQ3F,WAAW;YAACA;SAAS,GAAG,EAAE;QACxC,MAAM4F,MAAM3F,SAAS;YAACA;SAAO,GAAG,EAAE;QAClC,MAAM4F,cAAc;eAAIF;eAAUC;SAAI;QAEtC,MAAME,UAAU9F,YAAYC;QAC5B,MAAMwF,QAAQ;eACTzH,+BACDrC,KAAKyF,IAAI,CAAC0E,SAAU,OACpBhG,WAAWwB,cAAc;eAExBrD,wCACDtC,KAAKyF,IAAI,CAAC0E,SAAU,OACpBhG,WAAWwB,cAAc;SAE5B;QACD,IAAIyE,mBAA6B,EAAE;QAEnC,MAAMC,WAAW;YACf;YACA;YACA;YACA;SACD,CAACC,GAAG,CAAC,CAACC,OAASvK,KAAKyF,IAAI,CAACxB,KAAKsG;QAE/BT,MAAMU,IAAI,IAAIH;QAEd,wCAAwC;QACxC,MAAMI,gBAAgB;YACpBzK,KAAKyF,IAAI,CAACxB,KAAK;YACfjE,KAAKyF,IAAI,CAACxB,KAAK;SAChB;QACD6F,MAAMU,IAAI,IAAIC;QAEd,MAAMC,KAAK,IAAIxK,UAAU;YACvByK,SAAS,CAACC;gBACR,OACE,CAACd,MAAMe,IAAI,CAAC,CAACN,OAASA,KAAKO,UAAU,CAACF,cACtC,CAACV,YAAYW,IAAI,CACf,CAACE,IAAMH,SAASE,UAAU,CAACC,MAAMA,EAAED,UAAU,CAACF;YAGpD;QACF;QACA,MAAMI,iBAAiB,IAAIrD;QAC3B,IAAIsD,oBAAoBlH;QACxB,IAAImH;QACJ,IAAIC,+BAA4C,IAAI9D;QAEpD,MAAM+D,qBAAqBpL,KAAKyF,IAAI,CAACvB,SAAS,SAAS;QACvD,MAAMmH,oBAAoBrL,KAAKyF,IAAI,CAACvB,SAAS,SAAS;QAEtDwG,GAAGY,EAAE,CAAC,cAAc;gBAqkBiB1F,0BACLA;YArkB9B,IAAI2F;YACJ,MAAMC,cAAwB,EAAE;YAChC,MAAMC,aAAaf,GAAGgB,kBAAkB;YACxC,MAAMC,WAAqC,CAAC;YAC5C,MAAMC,cAAc,IAAIvE;YACxB,MAAMwE,0BAA0B,IAAIxE;YACpC,MAAMyE,mBAAmB,IAAInE;YAC7B,MAAMoE,qBAAqB,IAAIpE;YAC/B,MAAMH,mBAA+D,EAAE;YACvE,MAAMC,gBAA4D,EAAE;YAEpE,MAAMV,aAAyD,EAAE;YACjE,MAAMD,YAAwD,EAAE;YAChE,MAAME,eAA2D,EAAE;YACnE,MAAMgF,QAAiD,EAAE;YAEzD,IAAIC,YAAY;YAChB,IAAIC,iBAAiB;YACrB,IAAIC,wBAAwB;YAC5B,IAAIC,qBAAqB;YAEzB,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAE,GAAGxI,KAAK0C,SAAS;YAE9C6F,SAASE,KAAK;YACdD,UAAUC,KAAK;YACfhK,aAAagK,KAAK;YAElB,MAAMC,mBAA6B;mBAAIf,WAAWgB,IAAI;aAAG,CAACC,IAAI,CAC5D3L,eAAeoD,WAAWwB,cAAc;YAG1C,KAAK,MAAMgH,YAAYH,iBAAkB;gBACvC,IACE,CAAC1C,MAAM8C,QAAQ,CAACD,aAChB,CAACzC,YAAYW,IAAI,CAAC,CAACE,IAAM4B,SAAS7B,UAAU,CAACC,KAC7C;oBACA;gBACF;gBACA,MAAM8B,OAAOpB,WAAWqB,GAAG,CAACH;gBAE5B,MAAMI,YAAY/B,eAAe8B,GAAG,CAACH;gBACrC,gGAAgG;gBAChG,MAAMK,kBACJD,cAAc1E,aACb0E,aAAaA,eAAcF,wBAAAA,KAAMI,SAAS;gBAC7CjC,eAAekC,GAAG,CAACP,UAAUE,wBAAAA,KAAMI,SAAS;gBAE5C,IAAI5C,SAASuC,QAAQ,CAACD,WAAW;oBAC/B,IAAIK,iBAAiB;wBACnBf,YAAY;oBACd;oBACA;gBACF;gBAEA,IAAIxB,cAAcmC,QAAQ,CAACD,WAAW;oBACpC,IAAIA,SAASQ,QAAQ,CAAC,kBAAkB;wBACtClC,oBAAoB;oBACtB;oBACA,IAAI+B,iBAAiB;wBACnBd,iBAAiB;oBACnB;oBACA;gBACF;gBAEA,IACEW,CAAAA,wBAAAA,KAAMO,QAAQ,MAAK/E,aACnB,CAAC3C,iBAAiB2H,UAAU,CAACV,WAC7B;oBACA;gBACF;gBAEA,MAAMW,YAAY9I,QAChBF,UACEhD,iBAAiBqL,UAAU7B,UAAU,CACnCxJ,iBAAiBgD,UAAU;gBAGjC,MAAMiJ,aAAa/I,QACjBH,YACE/C,iBAAiBqL,UAAU7B,UAAU,CACnCxJ,iBAAiB+C,YAAY;gBAInC,MAAMmJ,WAAWhM,mBAAmBmL,UAAU;oBAC5C1I,KAAKA;oBACLwJ,YAAYtJ,WAAWwB,cAAc;oBACrC+H,WAAW;oBACXC,WAAWlL,WAAWmL,IAAI;gBAC5B;gBAEA,IAAI1L,iBAAiBsL,WAAW;wBAsBTK;oBArBrB,MAAMA,aAAa,MAAM/M,8BAA8B;wBACrDgN,cAAcnB;wBACdzG,QAAQ/B;wBACRG,QAAQA;wBACR6E,MAAMqE;wBACNO,OAAO;wBACPC,gBAAgBV;wBAChB3H,gBAAgBxB,WAAWwB,cAAc;oBAC3C;oBACA,IAAIxB,WAAW8J,MAAM,KAAK,UAAU;wBAClC3N,IAAI4N,KAAK,CACP;wBAEF;oBACF;oBACAtI,aAAauI,oBAAoB,GAAGX;oBACpC,MAAMvI,qBACJnB,MACA,wBACA8B,aAAauI,oBAAoB;oBAEnC5C,qBAAqBsC,EAAAA,yBAAAA,WAAWO,UAAU,qBAArBP,uBAAuBQ,QAAQ,KAAI;wBACtD;4BAAEC,QAAQ;4BAAMC,gBAAgB;wBAAU;qBAC3C;oBACD;gBACF;gBACA,IAAInM,0BAA0BoL,WAAW;oBACvC5H,aAAa4I,6BAA6B,GAAGhB;oBAC7C,MAAMvI,qBACJnB,MACA,iCACA8B,aAAa4I,6BAA6B;oBAE5C;gBACF;gBAEA,IAAI7B,SAASQ,QAAQ,CAAC,UAAUR,SAASQ,QAAQ,CAAC,SAAS;oBACzDlC,oBAAoB;gBACtB;gBAEA,IAAI,CAAEqC,CAAAA,aAAaC,UAAS,GAAI;oBAC9B;gBACF;gBAEA,yDAAyD;gBACzDhL,aAAakM,GAAG,CAAC9B;gBAEjB,IAAI+B,WAAWlN,mBAAmBmL,UAAU;oBAC1C1I,KAAKqJ,YAAYhJ,SAAUD;oBAC3BoJ,YAAYtJ,WAAWwB,cAAc;oBACrC+H,WAAWJ;oBACXK,WAAWL,YAAY7K,WAAWkM,GAAG,GAAGlM,WAAWmM,KAAK;gBAC1D;gBAEA,IACEtB,aACAhJ,UACA1B,oBACE+J,SAASkC,OAAO,CAACvK,QAAQ,KACzBH,WAAWwB,cAAc,EACzB,OAEF;oBACA,MAAMkI,aAAa,MAAMjO,kBAAkB;wBACzCkO,cAAcnB;wBACdxI,YAAY,CAAC;wBACbgF,MAAMuF;wBACNX,OAAO;wBACPe,UAAUrM,WAAWkM,GAAG;oBAC1B;oBAEAD,WAAW7L,6BACT6L,UACA,CAAC,CAAEb,CAAAA,WAAWkB,gBAAgB,IAAIlB,WAAWmB,qBAAqB,AAAD;gBAErE;gBAEA,IACE,CAAC1B,aACDoB,SAAS5D,UAAU,CAAC,YACpB3G,WAAW8J,MAAM,KAAK,UACtB;oBACA3N,IAAI4N,KAAK,CACP;oBAEF;gBACF;gBAEA,IAAIZ,WAAW;oBACb,MAAM2B,iBAAiBvJ,iBAAiBuJ,cAAc,CAACtC;oBACvDP,qBAAqB;oBAErB,IAAI6C,gBAAgB;wBAClB;oBACF;oBAEA,kEAAkE;oBAClE,IAAI3N,iBAAiBoN,UAAU9B,QAAQ,CAAC,OAAO;wBAC7C;oBACF;oBAEA,gDAAgD;oBAChD,mDAAmD;oBACnD,8DAA8D;oBAC9D,MAAMsC,WAAW5N,iBAAiBoN,UAAUS,KAAK,CAAC;oBAClD,IAAK,IAAIC,IAAIF,SAASnF,MAAM,GAAG,GAAGqF,KAAK,GAAGA,IAAK;wBAC7C,MAAMC,UAAUH,QAAQ,CAACE,EAAE;wBAC3B,IAAIzL,uBAAuB0L,UAAU;4BACnC,MAAMC,aAAanO,iBACjB+N,SAASK,KAAK,CAAC,GAAGH,GAAG3J,IAAI,CAAC;4BAG5B,MAAM+J,WAAWH,QAAQE,KAAK,CAAC;4BAC/B,mCAAmC;4BACnC,IACEvD,MAAMnB,IAAI,CACR,CAAC4E,IAAMA,EAAEC,IAAI,KAAKF,YAAYC,EAAEE,MAAM,KAAKL,aAG7C;4BAEFtD,MAAMxB,IAAI,CAAC;gCACTkF,MAAMF;gCACNG,QAAQL;4BACV;4BACA;wBACF;oBACF;oBAEA,iBAAiB;oBACjB,IAAI5J,iBAAiBkK,eAAe,CAACjD,WAAW;wBAC9C3F,aAAawD,IAAI,CAAC;4BAChBqF,OAAOjM,mBACLzC,iBAAiBG,iBAAiBoN,WAAWG,OAAO,CAClD,aACA;4BAGJiB,UAAU9P,KAAK+P,QAAQ,CACrB/P,KAAKgQ,OAAO,CAAC3E,oBACbsB;wBAEJ;oBACF;oBAEA,IAAI,CAACjH,iBAAiBuK,eAAe,CAACtD,WAAW;wBAC/C;oBACF;oBAEA,MAAMuD,mBAAmBxB;oBACzBA,WAAWvN,iBAAiBuN,UAAUG,OAAO,CAAC,QAAQ;oBACtD,IAAI,CAAClD,QAAQ,CAAC+C,SAAS,EAAE;wBACvB/C,QAAQ,CAAC+C,SAAS,GAAG,EAAE;oBACzB;oBACA/C,QAAQ,CAAC+C,SAAS,CAAClE,IAAI,CACrB1G,KAAKkC,KAAK,GAENkK,iBAAiBrB,OAAO,CAAC,QAAQ,OACjCqB;oBAGN,IAAI1K,2BAA2B;wBAC7B6G,SAASoC,GAAG,CAACC;oBACf;oBAEA,IAAIhJ,iBAAiByK,gBAAgB,CAACxD,WAAW;wBAC/CnF,iBAAiBgD,IAAI,CAAC;4BACpBqF,OAAOvO,iBAAiBoN;4BACxBoB,UAAU9P,KAAK+P,QAAQ,CACrB/P,KAAKgQ,OAAO,CAAC3E,oBACbsB;wBAEJ;oBACF,OAAO;wBACL7F,UAAU0D,IAAI,CAAC;4BACbqF,OAAOvO,iBAAiBoN;4BACxBoB,UAAU9P,KAAK+P,QAAQ,CACrB/P,KAAKgQ,OAAO,CAAC3E,oBACbsB;wBAEJ;oBACF;oBAEA,IAAInB,YAAYoB,QAAQ,CAAC8B,WAAW;wBAClC;oBACF;gBACF,OAAO;oBACL,IAAIlJ,2BAA2B;wBAC7B8G,UAAUmC,GAAG,CAACC;wBACd,8DAA8D;wBAC9D,8DAA8D;wBAC9D5K,KAAK0C,SAAS,CAAC4J,cAAc,CAAC3B,GAAG,CAACC;oBACpC;oBAEA,IAAIA,SAAS5D,UAAU,CAAC,UAAU;wBAChCrD,cAAc+C,IAAI,CAAC;4BACjBqF,OAAOvO,iBAAiBoN;4BACxBoB,UAAU9P,KAAK+P,QAAQ,CACrB/P,KAAKgQ,OAAO,CAAC3E,oBACbsB;wBAEJ;oBACF,OAAO;wBACL5F,WAAWyD,IAAI,CAAC;4BACdqF,OAAOvO,iBAAiBoN;4BACxBoB,UAAU9P,KAAK+P,QAAQ,CACrB/P,KAAKgQ,OAAO,CAAC3E,oBACbsB;wBAEJ;oBACF;gBACF;gBAEA,eAAe;gBACf,IAAIW,WAAW;oBACbxB,iBAAiBoB,GAAG,CAACwB,UAAU/B;gBACjC,OAAO;oBACLZ,mBAAmBmB,GAAG,CAACwB,UAAU/B;gBACnC;gBAEA,IAAIrI,UAAUsH,YAAYyE,GAAG,CAAC3B,WAAW;oBACvC7C,wBAAwB4C,GAAG,CAACC;gBAC9B,OAAO;oBACL9C,YAAY6C,GAAG,CAACC;gBAClB;gBAEA;;;SAGC,GACD,IAAI,sBAAsB4B,IAAI,CAAC5B,WAAW;oBACxCtE,iBAAiBI,IAAI,CAACkE;oBACtB;gBACF;gBAEAlD,YAAYhB,IAAI,CAACkE;YACnB;YAEA,MAAM6B,iBAAiB1E,wBAAwB2E,IAAI;YACnDrE,wBAAwBoE,iBAAiBpF,6BAA6BqF,IAAI;YAE1E,IAAIrE,0BAA0B,GAAG;gBAC/B,IAAIoE,iBAAiB,GAAG;oBACtB,IAAIE,eAAe,CAAC,6BAA6B,EAC/CF,mBAAmB,IAAI,SAAS,SACjC,0DAA0D,CAAC;oBAE5D,KAAK,MAAMG,KAAK7E,wBAAyB;wBACvC,MAAM8E,UAAU3Q,KAAK+P,QAAQ,CAAC9L,KAAK6H,iBAAiBgB,GAAG,CAAC4D;wBACxD,MAAME,YAAY5Q,KAAK+P,QAAQ,CAAC9L,KAAK8H,mBAAmBe,GAAG,CAAC4D;wBAC5DD,gBAAgB,CAAC,GAAG,EAAEG,UAAU,KAAK,EAAED,QAAQ,GAAG,CAAC;oBACrD;oBACA5K,YAAY8K,iBAAiB,CAAC,qBAAuB,CAAvB,IAAIC,MAAML,eAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAsB;gBACtD,OAAO,IAAIF,mBAAmB,GAAG;oBAC/BxK,YAAYgL,mBAAmB;oBAC/B,MAAM9L,qBAAqBnB,MAAM,kBAAkBuE;gBACrD;YACF;YAEA8C,+BAA+BU;YAE/B,IAAImF;YACJ,IAAI7M,WAAW4D,YAAY,CAACkJ,kBAAkB,EAAE;gBAC9CD,sBAAsBzP,yBACpB2P,OAAOzE,IAAI,CAACd,WACZxH,WAAW4D,YAAY,CAACoJ,2BAA2B,GAC/C,AAAC,CAAA,AAAChN,WAAmBiN,kBAAkB,IAAI,EAAE,AAAD,EAAG7M,MAAM,CACnD,CAAC8M,IAAW,CAACA,EAAEC,QAAQ,IAEzB,EAAE,EACNnN,WAAW4D,YAAY,CAACwJ,6BAA6B;gBAGvD,IACE,CAACrG,+BACDzC,KAAKC,SAAS,CAACwC,iCACbzC,KAAKC,SAAS,CAACsI,sBACjB;oBACA/E,YAAY;oBACZf,8BAA8B8F;gBAChC;YACF;YAEA,IAAI,CAACjN,mBAAmBkH,mBAAmB;gBACzC,oDAAoD;gBACpD,+CAA+C;gBAC/C,MAAMpH,iBAAiBC,MACpB0N,IAAI,CAAC;oBACJtF,iBAAiB;gBACnB,GACCuF,KAAK,CAAC,KAAO;YAClB;YAEA,IAAIxF,aAAaC,gBAAgB;oBAyE/BnG;gBAxEA,IAAIkG,WAAW;wBAWU9H;oBAVvB,MAAM,EAAEuN,cAAc,EAAE,GAAGvR,cACzB8D,KACA0N,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACzBvR,KACA,MACA,CAACwR;wBACCxR,IAAIyR,IAAI,CAAC,CAAC,YAAY,EAAED,aAAa;oBACvC;oBAGF,IAAI/N,qBAAmBI,2BAAAA,WAAW4D,YAAY,qBAAvB5D,yBAAyB6N,QAAQ,GAAE;wBACxD,0DAA0D;wBAC1DlP,qBAAqB;4BACnBoB;4BACAwN,gBAAgB;mCACXA;gCACH;oCACE1R,MAAMmE,WAAW8N,cAAc;oCAC/BL,KAAKzN,WAAWyN,GAAG;oCACnBM,UAAU;gCACZ;6BACD;wBACH;oBACF;oBAEA,MAAMjN,qBAAqBnB,MAAM,iBAAiB;wBAChD;4BAAEqO,KAAK;4BAAMC,aAAa;4BAAMC,QAAQ;wBAAK;qBAC9C;gBACH;gBACA,IAAIC;gBAIJ,IAAIpG,gBAAgB;oBAClB,IAAI;wBACFoG,iBAAiB,MAAM7R,aAAawD,KAAKE;oBAC3C,EAAE,OAAO0F,GAAG;oBACV,2EAA2E,GAC7E;gBACF;gBAEA,IAAI9D,YAAYwM,gBAAgB,EAAE;wBAO9BzO;oBANF,MAAM0O,cACJ1O,KAAK0C,SAAS,CAACD,QAAQ,CAACkM,UAAU,CAAC1I,MAAM,GAAG,KAC5CjG,KAAK0C,SAAS,CAACD,QAAQ,CAACmM,WAAW,CAAC3I,MAAM,GAAG,KAC7CjG,KAAK0C,SAAS,CAACD,QAAQ,CAACoM,QAAQ,CAAC5I,MAAM,GAAG;oBAE5C,MAAM6I,WACJ9O,EAAAA,6BAAAA,KAAKK,UAAU,CAAC0O,SAAS,qBAAzB/O,2BAA2BgP,IAAI,KAC/BhP,KAAKK,UAAU,CAAC4O,qBAAqB,IACrCjP,KAAKG,GAAG;oBACV,MAAM8B,YAAYwM,gBAAgB,CAACS,MAAM,CAAC;wBACxCC,WAAWpT,gBAAgB;4BACzBqT,aAAa;4BACblC;4BACA9K,QAAQ/B;4BACRgO,KAAK;4BACLjO;4BACAiP,qBACErP,KAAKK,UAAU,CAAC4D,YAAY,CAACoL,mBAAmB;4BAClDX;4BACA,kBAAkB;4BAClBjH,oBAAoBlD;4BACpB+K,aAAatP,KAAKG,GAAG;4BACrBsC,UAAUzC,KAAK0C,SAAS,CAACD,QAAQ;wBACnC;wBACAqM;wBACAQ,aAAa9P,cAActD,KAAK+P,QAAQ,CAAC6C,UAAU3O;oBACrD;gBACF;iBAEA8B,oCAAAA,YAAYsN,oBAAoB,qBAAhCtN,kCAAkCuN,OAAO,CAAC,CAACpN,QAAQqN;oBACjD,MAAMC,WAAWD,QAAQ;oBACzB,MAAME,eAAeF,QAAQ;oBAC7B,MAAMG,eAAeH,QAAQ;oBAC7B,MAAMf,cACJ1O,KAAK0C,SAAS,CAACD,QAAQ,CAACkM,UAAU,CAAC1I,MAAM,GAAG,KAC5CjG,KAAK0C,SAAS,CAACD,QAAQ,CAACmM,WAAW,CAAC3I,MAAM,GAAG,KAC7CjG,KAAK0C,SAAS,CAACD,QAAQ,CAACoM,QAAQ,CAAC5I,MAAM,GAAG;oBAE5C,IAAImC,gBAAgB;4BAClBhG,yBAAAA;yBAAAA,kBAAAA,OAAOwD,OAAO,sBAAdxD,0BAAAA,gBAAgByN,OAAO,qBAAvBzN,wBAAyBoN,OAAO,CAAC,CAACM;4BAChC,mDAAmD;4BACnD,kCAAkC;4BAClC,IAAIA,kBAAkB7Q,uBAAuBuP,gBAAgB;oCAGlCpM,yBAAAA,iBAqBrB2N;gCAvBJ,MAAM,EAAEC,eAAe,EAAED,QAAQ,EAAE,GAAGvB;gCACtC,MAAMyB,yBAAyBH,OAAOE,eAAe;gCACrD,MAAME,oBAAmB9N,kBAAAA,OAAOwD,OAAO,sBAAdxD,0BAAAA,gBAAgB+N,OAAO,qBAAvB/N,wBAAyBgO,SAAS,CACzD,CAACnL,OAASA,UAASgL,0CAAAA,uBAAwBI,OAAO;gCAGpD,IAAIL,iBAAiB;oCACnB,IACEA,gBAAgBK,OAAO,MAAKJ,0CAAAA,uBAAwBI,OAAO,GAC3D;wCACA,qCAAqC;wCACrC,IAAIH,oBAAoBA,mBAAmB,CAAC,GAAG;gDAC7C9N,0BAAAA;6CAAAA,mBAAAA,OAAOwD,OAAO,sBAAdxD,2BAAAA,iBAAgB+N,OAAO,qBAAvB/N,yBAAyBkO,MAAM,CAACJ,kBAAkB;wCACpD;wCAEA,wEAAwE;wCACxE,mEAAmE;wCACnE,IAAI,CAACF,gBAAgBO,UAAU,EAAE;gDAC/BnO,0BAAAA;6CAAAA,mBAAAA,OAAOwD,OAAO,sBAAdxD,2BAAAA,iBAAgB+N,OAAO,qBAAvB/N,yBAAyBsE,IAAI,CAACsJ,gBAAgBK,OAAO;wCACvD;oCACF;gCACF;gCAEA,IAAIN,CAAAA,6BAAAA,4BAAAA,SAAUS,eAAe,qBAAzBT,0BAA2BU,KAAK,KAAIT,iBAAiB;oCACvD5C,OAAOzE,IAAI,CAACmH,OAAOW,KAAK,EAAEjB,OAAO,CAAC,CAACkB;wCACjC,OAAOZ,OAAOW,KAAK,CAACC,IAAI;oCAC1B;oCACAtD,OAAOuD,MAAM,CAACb,OAAOW,KAAK,EAAEV,SAASS,eAAe,CAACC,KAAK;oCAC1DX,OAAOE,eAAe,GAAGA;gCAC3B;4BACF;wBACF;oBACF;oBAEA,IAAI7H,WAAW;4BACb/F;yBAAAA,kBAAAA,OAAOyN,OAAO,qBAAdzN,gBAAgBoN,OAAO,CAAC,CAACM;4BACvB,qDAAqD;4BACrD,sCAAsC;4BACtC,IACEA,UACA,OAAOA,OAAOc,WAAW,KAAK,YAC9Bd,OAAOc,WAAW,CAACC,iBAAiB,EACpC;gCACA,MAAMC,YAAYxR,aAAa;oCAC7B8P,aAAa;oCACblC;oCACA9K,QAAQ/B;oCACRgO,KAAK;oCACLjO;oCACAiP,qBACErP,KAAKK,UAAU,CAAC4D,YAAY,CAACoL,mBAAmB;oCAClDX;oCACAgB;oCACAE;oCACAD;oCACAlI,oBAAoBlD;oCACpB+K,aAAatP,KAAKG,GAAG;oCACrBsC,UAAUzC,KAAK0C,SAAS,CAACD,QAAQ;gCACnC;gCAEA2K,OAAOzE,IAAI,CAACmH,OAAOc,WAAW,EAAEpB,OAAO,CAAC,CAACkB;oCACvC,IAAI,CAAEA,CAAAA,OAAOI,SAAQ,GAAI;wCACvB,OAAOhB,OAAOc,WAAW,CAACF,IAAI;oCAChC;gCACF;gCACAtD,OAAOuD,MAAM,CAACb,OAAOc,WAAW,EAAEE;4BACpC;wBACF;oBACF;gBACF;gBACA,MAAM7O,YAAY8O,UAAU,CAAC;oBAC3BC,yBAAyB7I;gBAC3B;YACF;YAEA,IAAI7B,iBAAiBL,MAAM,GAAG,GAAG;gBAC/BzJ,IAAI4N,KAAK,CACP,qBAIC,CAJD,IAAI/L,sBACFiI,kBACAnG,KACCI,YAAYC,SAHf,qBAAA;2BAAA;gCAAA;kCAAA;gBAIA,GAAEyQ,OAAO;gBAEX3K,mBAAmB,EAAE;YACvB;YAEA,sEAAsE;YACtExE,aAAaoP,aAAa,GAAG9D,OAAO+D,WAAW,CAC7C/D,OAAOgE,OAAO,CAACvJ,UAAUrB,GAAG,CAAC,CAAC,CAAC6K,GAAGC,EAAE,GAAK;oBAACD;oBAAGC,EAAE1I,IAAI;iBAAG;YAExD,MAAMzH,qBACJnB,MACA,iBACA8B,aAAaoP,aAAa;YAG5B,gDAAgD;YAChDpP,aAAawI,UAAU,GAAG7C,qBACtB;gBACE8J,OAAO;gBACPlM,MAAM;gBACNkF,UAAU9C;YACZ,IACAlD;YAEJ,MAAMpD,qBAAqBnB,MAAM,cAAc8B,aAAawI,UAAU;YACtExI,aAAa0P,cAAc,GAAGlJ;YAE9BtI,KAAK0C,SAAS,CAAC+O,iBAAiB,GAAG3P,EAAAA,2BAAAA,aAAawI,UAAU,qBAAvBxI,yBAAyByI,QAAQ,IAChEpM,2BAA0B2D,4BAAAA,aAAawI,UAAU,qBAAvBxI,0BAAyByI,QAAQ,IAC3DhG;YAEJ,MAAMmN,qBAAqB/T,mCACzByP,OAAOzE,IAAI,CAACd,WACZ7H,KAAKK,UAAU,CAAC8D,QAAQ,EACxBqC,GAAG,CAAC,CAACvB,OACL1I,iBACE,wBACA0I,MACAjF,KAAKK,UAAU,CAAC8D,QAAQ,EACxBnE,KAAKK,UAAU,CAAC4D,YAAY,CAACC,mBAAmB;YAIpDlE,KAAK0C,SAAS,CAACD,QAAQ,CAACmM,WAAW,CAAClI,IAAI,IAAIgL;YAE5C,MAAMC,gBACJ,AAAC,OAAOtR,WAAWsR,aAAa,KAAK,cAClC,OAAMtR,WAAWsR,aAAa,oBAAxBtR,WAAWsR,aAAa,MAAxBtR,YACL,CAAC,GACD;gBACEgO,KAAK;gBACLlO,KAAKH,KAAKG,GAAG;gBACbyR,QAAQ;gBACRxR,SAASA;gBACTiC,SAAS;YACX,OAEJ,CAAC;YAEH,MAAMwP,uBAAuBzE,OAAOgE,OAAO,CAACO,iBAAiB,CAAC;YAE9D,IAAIE,qBAAqB5L,MAAM,GAAG,GAAG;gBACnCjG,KAAK0C,SAAS,CAACoP,mBAAmB,GAAGD,qBAAqBrL,GAAG,CAC3D,CAAC,CAACkK,KAAKqB,MAAM,GACXxV,iBACE,wBACA;wBACEyV,QAAQtB;wBACRuB,aAAa,GAAGF,MAAM1M,IAAI,GACxB0M,MAAMG,KAAK,GAAG,MAAM,KACnB/V,GAAGyI,SAAS,CAACmN,MAAMG,KAAK,GAAG;oBAChC,GACAlS,KAAKK,UAAU,CAAC8D,QAAQ,EACxBnE,KAAKK,UAAU,CAAC4D,YAAY,CAACC,mBAAmB;YAGxD;YAEA,IAAI;gBACF,gEAAgE;gBAChE,qEAAqE;gBACrE,kEAAkE;gBAClE,MAAMiO,eAAepV,gBAAgB2K;gBAErC1H,KAAK0C,SAAS,CAAC0P,aAAa,GAAGD,aAAa3L,GAAG,CAC7C,CAACnB;oBACC,MAAMgN,QAAQjV,cAAciI;oBAC5B,OAAO;wBACLgN,OAAOA,MAAMC,EAAE,CAACC,QAAQ;wBACxBhB,OAAOhU,gBAAgB8U;wBACvBhN;oBACF;gBACF;gBAGF,MAAMmN,aAAkD,EAAE;gBAE1D,KAAK,MAAMnN,QAAQ8M,aAAc;oBAC/B,MAAMpG,QAAQzO,eAAe+H,MAAM;oBACnC,MAAMoN,aAAarV,cAAc2O,MAAM1G,IAAI;oBAC3CmN,WAAW9L,IAAI,CAAC;wBACd,GAAGqF,KAAK;wBACRsG,OAAOI,WAAWH,EAAE,CAACC,QAAQ;wBAC7BhB,OAAOhU,gBAAgB;4BACrB,+DAA+D;4BAC/D,uCAAuC;4BACvC+U,IAAItS,KAAKK,UAAU,CAACiE,IAAI,GACpB,IAAIoO,OACF3G,MAAM4G,cAAc,CAAC5H,OAAO,CAC1B,CAAC,aAAa,CAAC,EACf,CAAC,mCAAmC,CAAC,KAGzC,IAAI2H,OAAO3G,MAAM4G,cAAc;4BACnCC,QAAQH,WAAWG,MAAM;wBAC3B;oBACF;gBACF;gBACA5S,KAAK0C,SAAS,CAAC0P,aAAa,CAACS,OAAO,IAAIL;gBAExC,IAAI,EAAC9M,oCAAAA,iBAAkBoN,KAAK,CAAC,CAACC,KAAKtD,MAAQsD,QAAQZ,YAAY,CAAC1C,IAAI,IAAG;oBACrE,MAAMuD,cAAcb,aAAa1R,MAAM,CACrC,CAACsL,QAAU,CAACrG,iBAAiBoD,QAAQ,CAACiD;oBAExC,MAAMkH,gBAAgBvN,iBAAiBjF,MAAM,CAC3C,CAACsL,QAAU,CAACoG,aAAarJ,QAAQ,CAACiD;oBAGpC,8CAA8C;oBAC9C9J,YAAYiR,IAAI,CAAC;wBACfC,QAAQzU,4BAA4B0U,yBAAyB;wBAC7DC,MAAM;4BACJ;gCACEC,kBAAkB;4BACpB;yBACD;oBACH;oBAEAN,YAAYxD,OAAO,CAAC,CAACzD;wBACnB9J,YAAYiR,IAAI,CAAC;4BACfC,QAAQzU,4BAA4B6U,UAAU;4BAC9CF,MAAM;gCAACtH;6BAAM;wBACf;oBACF;oBAEAkH,cAAczD,OAAO,CAAC,CAACzD;wBACrB9J,YAAYiR,IAAI,CAAC;4BACfC,QAAQzU,4BAA4B8U,YAAY;4BAChDH,MAAM;gCAACtH;6BAAM;wBACf;oBACF;gBACF;gBACArG,mBAAmByM;gBAEnB,IAAIlS,iBAAiB;oBACnB,MAAMwT,qBAAqB,MAAM/T,yBAAyB;wBACxDS;wBACA8C;wBACAD;wBACAE;wBACAgF;wBACA9D,WAAWpE,KAAKK,UAAU,CAAC+D,SAAS;wBACpC3B,UAAUzC,KAAKK,UAAU,CAACoC,QAAQ;wBAClCiB;wBACAC;oBACF;oBAEA,MAAMhE,wBACJ8T,oBACAnM,oBACAtH,KAAKK,UAAU;oBAEjB,MAAMT,mBAAmB6T,oBAAoBlM;gBAC/C;gBAEA,IAAI,CAAC9B,UAAU;oBACbG;oBACAH,WAAW;gBACb;YACF,EAAE,OAAOiO,GAAG;gBACV,IAAI,CAACjO,UAAU;oBACbI,OAAO6N;oBACPjO,WAAW;gBACb,OAAO;oBACLjJ,IAAImX,IAAI,CAAC,oCAAoCD;gBAC/C;YACF,SAAU;gBACR,kEAAkE;gBAClE,4DAA4D;gBAC5D,MAAMvS,qBAAqBnB,MAAM,kBAAkBuE;YACrD;QACF;QAEAqC,GAAGgN,KAAK,CAAC;YAAExN,aAAa;gBAACjG;aAAI;YAAE0T,WAAW;QAAE;IAC9C;IAEA,MAAMC,0BAA0B,CAAC,OAAO,EAAElW,yBAAyB,aAAa,EAAEC,2BAA2B;IAC7GmC,KAAK0C,SAAS,CAACqR,iBAAiB,CAACpJ,GAAG,CAACmJ;IAErC,MAAME,4BAA4B,CAAC,OAAO,EAAEpW,yBAAyB,aAAa,EAAEE,gCAAgC;IACpHkC,KAAK0C,SAAS,CAACqR,iBAAiB,CAACpJ,GAAG,CAACqJ;IAErC,MAAMC,qCAAqC,CAAC,OAAO,EAAErW,yBAAyB,aAAa,EAAEI,sCAAsC;IACnIgC,KAAK0C,SAAS,CAACqR,iBAAiB,CAACpJ,GAAG,CAACsJ;IAErC,eAAeC,eAAeC,GAAoB,EAAEC,GAAmB;YAGjEC,qBAcFA,sBACAA;QAjBF,MAAMA,YAAYpY,IAAIqY,KAAK,CAACH,IAAIlY,GAAG,IAAI;QAEvC,KAAIoY,sBAAAA,UAAUvN,QAAQ,qBAAlBuN,oBAAoBvL,QAAQ,CAACgL,0BAA0B;YACzDM,IAAIG,UAAU,GAAG;YACjBH,IAAII,SAAS,CAAC,gBAAgB/U;YAC9B2U,IAAIK,GAAG,CACL9P,KAAKC,SAAS,CAAC;gBACbsB,OAAOR,iBAAiBjF,MAAM,CAC5B,CAACsL,QAAU,CAAC/L,KAAK0C,SAAS,CAAC6F,QAAQ,CAACgE,GAAG,CAACR;YAE5C;YAEF,OAAO;gBAAE2I,UAAU;YAAK;QAC1B;QAEA,IACEL,EAAAA,uBAAAA,UAAUvN,QAAQ,qBAAlBuN,qBAAoBvL,QAAQ,CAACkL,iCAC7BK,uBAAAA,UAAUvN,QAAQ,qBAAlBuN,qBAAoBvL,QAAQ,CAACmL,sCAC7B;gBAGuBnS;YAFvBsS,IAAIG,UAAU,GAAG;YACjBH,IAAII,SAAS,CAAC,gBAAgB/U;YAC9B2U,IAAIK,GAAG,CAAC9P,KAAKC,SAAS,CAAC9C,EAAAA,2BAAAA,aAAawI,UAAU,qBAAvBxI,yBAAyByI,QAAQ,KAAI,EAAE;YAC9D,OAAO;gBAAEmK,UAAU;YAAK;QAC1B;QACA,OAAO;YAAEA,UAAU;QAAM;IAC3B;IAEA,SAASC,0BACPC,GAAY,EACZ1P,IAAyE;QAEzE,IAAI0P,eAAevV,kBAAkB;YACnC,wDAAwD;YACxD7C,IAAI4N,KAAK,CAACwK,IAAI3D,OAAO;QACvB,OAAO,IAAI2D,eAAerV,wBAAwB;QAChD,yEAAyE;QACzE,mEAAmE;QACrE,OAAO,IAAI2F,SAAS,WAAW;YAC7B1I,IAAImX,IAAI,CAACiB;QACX,OAAO,IAAI1P,SAAS,WAAW;YAC7B1I,IAAI4N,KAAK,CAACwK;QACZ,OAAO,IAAI1P,MAAM;YACf1I,IAAI4N,KAAK,CAAC,GAAGlF,KAAK,CAAC,CAAC,EAAE0P;QACxB,OAAO;YACLpY,IAAI4N,KAAK,CAACwK;QACZ;IACF;IAEA,OAAO;QACL9S;QACAG;QACAiS;QACAS;QAEA,MAAME,kBAAiBC,UAAmB;YACxC,IAAI,CAAChT,aAAauI,oBAAoB,EAAE;YACxC,OAAOpI,YAAYkD,UAAU,CAAC;gBAC5BE,MAAMvD,aAAauI,oBAAoB;gBACvCjF,YAAY;gBACZI,YAAYjB;gBACZtI,KAAK6Y;YACP;QACF;IACF;AACF;AAEA,OAAO,eAAeC,gBAAgB/U,IAAe;IACnD,MAAMmC,WAAWjG,KACd+P,QAAQ,CAACjM,KAAKG,GAAG,EAAEH,KAAKO,QAAQ,IAAIP,KAAKQ,MAAM,IAAI,IACnDwG,UAAU,CAAC;IAEd,MAAMgO,SAAS,MAAMxT,aAAa;QAChC,GAAGxB,IAAI;QACPmC;IACF;IAEAnC,KAAKwC,SAAS,CAACyS,MAAM,CACnBnY,gBACEZ,KAAKyF,IAAI,CAAC3B,KAAKG,GAAG,EAAEH,KAAKK,UAAU,CAACD,OAAO,GAC3CJ,KAAKK,UAAU,EACf;QACE6U,gBAAgB;QAChB/S;QACAgT,WAAW,CAAC,CAACnV,KAAKkC,KAAK;QACvBkT,YAAY;QACZ5U,QAAQ,CAAC,CAACR,KAAKQ,MAAM;QACrBD,UAAU,CAAC,CAACP,KAAKO,QAAQ;QACzB8U,gBAAgB,CAAC,CAACrV,KAAKqV,cAAc;QACrCC,YAAY,CAAC,CAAE,MAAMhZ,OAAO,YAAY;YAAEiZ,KAAKvV,KAAKG,GAAG;QAAC;IAC1D;IAIJ,4CAA4C;IAC5CH,KAAKwC,SAAS,CAACyS,MAAM,CAAC;QACpBO,WAAW3Y;QACX4Y,SAAS;YACPC,aAAa;YACbC,iBAAiBvW,2BAA2BY,KAAKK,UAAU,IAAI,IAAI;QACrE;IACF;IAEA,OAAO2U;AACT;CAIA,2DAA2D", "ignoreList": [0]}