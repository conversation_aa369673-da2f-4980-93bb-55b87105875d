{"version": 3, "sources": ["../../../src/telemetry/events/build.ts"], "sourcesContent": ["import type { TelemetryPlugin } from '../../build/webpack/plugins/telemetry-plugin/telemetry-plugin'\nimport type { SWC_TARGET_TRIPLE } from '../../build/webpack/plugins/telemetry-plugin/telemetry-plugin'\nimport type { UseCacheTrackerKey } from '../../build/webpack/plugins/telemetry-plugin/use-cache-tracker-utils'\nimport { extractNextErrorCode } from '../../lib/error-telemetry-utils'\n\nconst REGEXP_DIRECTORY_DUNDER =\n  /[\\\\/]__[^\\\\/]+(?<![\\\\/]__(?:tests|mocks))__[\\\\/]/i\nconst REGEXP_DIRECTORY_TESTS = /[\\\\/]__(tests|mocks)__[\\\\/]/i\nconst REGEXP_FILE_TEST = /\\.(?:spec|test)\\.[^.]+$/i\n\nconst EVENT_TYPE_CHECK_COMPLETED = 'NEXT_TYPE_CHECK_COMPLETED'\ntype EventTypeCheckCompleted = {\n  durationInSeconds: number\n  typescriptVersion: string | null\n  inputFilesCount?: number\n  totalFilesCount?: number\n  incremental?: boolean\n}\n\nexport function eventTypeCheckCompleted(event: EventTypeCheckCompleted): {\n  eventName: string\n  payload: EventTypeCheckCompleted\n} {\n  return {\n    eventName: EVENT_TYPE_CHECK_COMPLETED,\n    payload: event,\n  }\n}\n\nconst EVENT_LINT_CHECK_COMPLETED = 'NEXT_LINT_CHECK_COMPLETED'\nexport type EventLintCheckCompleted = {\n  durationInSeconds: number\n  eslintVersion: string | null\n  lintedFilesCount?: number\n  lintFix?: boolean\n  buildLint?: boolean\n  nextEslintPluginVersion?: string | null\n  nextEslintPluginErrorsCount?: number\n  nextEslintPluginWarningsCount?: number\n  nextRulesEnabled: {\n    [ruleName: `@next/next/${string}`]: 'off' | 'warn' | 'error'\n  }\n}\n\nexport function eventLintCheckCompleted(event: EventLintCheckCompleted): {\n  eventName: string\n  payload: EventLintCheckCompleted\n} {\n  return {\n    eventName: EVENT_LINT_CHECK_COMPLETED,\n    payload: event,\n  }\n}\n\nconst EVENT_BUILD_COMPLETED = 'NEXT_BUILD_COMPLETED'\ntype EventBuildCompleted = {\n  bundler: 'webpack' | 'rspack' | 'turbopack'\n  durationInSeconds: number\n  totalPageCount: number\n  hasDunderPages: boolean\n  hasTestPages: boolean\n  totalAppPagesCount?: number\n}\n\nexport function eventBuildCompleted(\n  pagePaths: string[],\n  event: Omit<\n    EventBuildCompleted,\n    'totalPageCount' | 'hasDunderPages' | 'hasTestPages'\n  >\n): { eventName: string; payload: EventBuildCompleted } {\n  return {\n    eventName: EVENT_BUILD_COMPLETED,\n    payload: {\n      ...event,\n      totalPageCount: pagePaths.length,\n      hasDunderPages: pagePaths.some((path) =>\n        REGEXP_DIRECTORY_DUNDER.test(path)\n      ),\n      hasTestPages: pagePaths.some(\n        (path) =>\n          REGEXP_DIRECTORY_TESTS.test(path) || REGEXP_FILE_TEST.test(path)\n      ),\n      totalAppPagesCount: event.totalAppPagesCount,\n    },\n  }\n}\n\nconst EVENT_BUILD_FAILED = 'NEXT_BUILD_FAILED'\ntype EventBuildFailed = {\n  bundler: 'webpack' | 'rspack' | 'turbopack'\n  errorCode: string\n  durationInSeconds: number\n}\n\nexport function eventBuildFailed(event: EventBuildFailed) {\n  return {\n    eventName: EVENT_BUILD_FAILED,\n    payload: event,\n  }\n}\n\nconst EVENT_BUILD_OPTIMIZED = 'NEXT_BUILD_OPTIMIZED'\ntype EventBuildOptimized = {\n  durationInSeconds: number\n  totalPageCount: number\n  staticPageCount: number\n  staticPropsPageCount: number\n  serverPropsPageCount: number\n  ssrPageCount: number\n  hasDunderPages: boolean\n  hasTestPages: boolean\n  hasStatic404: boolean\n  hasReportWebVitals: boolean\n  headersCount: number\n  rewritesCount: number\n  redirectsCount: number\n  headersWithHasCount: number\n  rewritesWithHasCount: number\n  redirectsWithHasCount: number\n  middlewareCount: number\n  isRspack: boolean\n  totalAppPagesCount?: number\n  staticAppPagesCount?: number\n  serverAppPagesCount?: number\n  edgeRuntimeAppCount?: number\n  edgeRuntimePagesCount?: number\n}\n\nexport function eventBuildOptimize(\n  pagePaths: string[],\n  event: Omit<\n    EventBuildOptimized,\n    'totalPageCount' | 'hasDunderPages' | 'hasTestPages' | 'isRspack'\n  >\n): { eventName: string; payload: EventBuildOptimized } {\n  return {\n    eventName: EVENT_BUILD_OPTIMIZED,\n    payload: {\n      ...event,\n      totalPageCount: pagePaths.length,\n      hasDunderPages: pagePaths.some((path) =>\n        REGEXP_DIRECTORY_DUNDER.test(path)\n      ),\n      hasTestPages: pagePaths.some(\n        (path) =>\n          REGEXP_DIRECTORY_TESTS.test(path) || REGEXP_FILE_TEST.test(path)\n      ),\n      totalAppPagesCount: event.totalAppPagesCount,\n      staticAppPagesCount: event.staticAppPagesCount,\n      serverAppPagesCount: event.serverAppPagesCount,\n      edgeRuntimeAppCount: event.edgeRuntimeAppCount,\n      edgeRuntimePagesCount: event.edgeRuntimePagesCount,\n      isRspack: process.env.NEXT_RSPACK !== undefined,\n    },\n  }\n}\n\nexport const EVENT_BUILD_FEATURE_USAGE = 'NEXT_BUILD_FEATURE_USAGE'\nexport type EventBuildFeatureUsage = {\n  // NOTE: If you are adding features, make sure to update the `enum` field\n  // for `featureName` in https://github.com/vercel/next-telemetry/blob/master/events/v1/featureUsage.ts\n  // *before* you make changes here.\n  featureName:\n    | 'next/image'\n    | 'next/legacy/image'\n    | 'next/future/image'\n    | 'next/script'\n    | 'next/dynamic'\n    | '@next/font/google'\n    | '@next/font/local'\n    | 'next/font/google'\n    | 'next/font/local'\n    | 'experimental/nextScriptWorkers'\n    | 'experimental/cacheComponents'\n    | 'experimental/optimizeCss'\n    | 'experimental/ppr'\n    | 'swcLoader'\n    | 'swcRelay'\n    | 'swcStyledComponents'\n    | 'swcReactRemoveProperties'\n    | 'swcExperimentalDecorators'\n    | 'swcRemoveConsole'\n    | 'swcImportSource'\n    | 'swcEmotion'\n    | `swc/target/${SWC_TARGET_TRIPLE}`\n    | 'turbotrace'\n    | 'build-lint'\n    | 'vercelImageGeneration'\n    | 'transpilePackages'\n    | 'skipMiddlewareUrlNormalize'\n    | 'skipTrailingSlashRedirect'\n    | 'modularizeImports'\n    | 'esmExternals'\n    | 'webpackPlugins'\n    | UseCacheTrackerKey\n    | 'turbopackPersistentCaching'\n    | 'runAfterProductionCompile'\n  invocationCount: number\n}\nexport function eventBuildFeatureUsage(\n  usages: ReturnType<TelemetryPlugin['usages']>\n): Array<{ eventName: string; payload: EventBuildFeatureUsage }> {\n  return usages.map(({ featureName, invocationCount }) => ({\n    eventName: EVENT_BUILD_FEATURE_USAGE,\n    payload: {\n      featureName,\n      invocationCount,\n    },\n  }))\n}\n\nexport const EVENT_NAME_PACKAGE_USED_IN_GET_SERVER_SIDE_PROPS =\n  'NEXT_PACKAGE_USED_IN_GET_SERVER_SIDE_PROPS'\n\nexport type EventPackageUsedInGetServerSideProps = {\n  package: string\n}\n\nexport function eventPackageUsedInGetServerSideProps(\n  packagesUsedInServerSideProps: ReturnType<\n    TelemetryPlugin['packagesUsedInServerSideProps']\n  >\n): Array<{ eventName: string; payload: EventPackageUsedInGetServerSideProps }> {\n  return packagesUsedInServerSideProps.map((packageName) => ({\n    eventName: EVENT_NAME_PACKAGE_USED_IN_GET_SERVER_SIDE_PROPS,\n    payload: {\n      package: packageName,\n    },\n  }))\n}\n\nexport const ERROR_THROWN_EVENT = 'NEXT_ERROR_THROWN'\ntype ErrorThrownEvent = {\n  eventName: typeof ERROR_THROWN_EVENT\n  payload: {\n    errorCode: string | undefined\n    location: string | undefined\n  }\n}\n\n// Creates a Telemetry event for errors. For privacy, only includes the error code and not the error\n// message.\n//\n// `location` may be included if it's a location internal to the next.js source tree (i.e. a\n// non-absolute path).\nexport function eventErrorThrown(\n  error: Error,\n  anonymizedLocation: string | undefined\n): ErrorThrownEvent {\n  return {\n    eventName: ERROR_THROWN_EVENT,\n    payload: {\n      errorCode: extractNextErrorCode(error) || 'Unknown',\n      location: anonymizedLocation,\n    },\n  }\n}\n"], "names": ["ERROR_THROWN_EVENT", "EVENT_BUILD_FEATURE_USAGE", "EVENT_NAME_PACKAGE_USED_IN_GET_SERVER_SIDE_PROPS", "eventBuildCompleted", "eventBuildFailed", "eventBuildFeatureUsage", "eventBuildOptimize", "eventErrorThrown", "eventLintCheckCompleted", "eventPackageUsedInGetServerSideProps", "eventTypeCheckCompleted", "REGEXP_DIRECTORY_DUNDER", "REGEXP_DIRECTORY_TESTS", "REGEXP_FILE_TEST", "EVENT_TYPE_CHECK_COMPLETED", "event", "eventName", "payload", "EVENT_LINT_CHECK_COMPLETED", "EVENT_BUILD_COMPLETED", "pagePaths", "totalPageCount", "length", "hasDunderPages", "some", "path", "test", "hasTestPages", "totalAppPagesCount", "EVENT_BUILD_FAILED", "EVENT_BUILD_OPTIMIZED", "staticAppPagesCount", "serverAppPagesCount", "edgeRuntimeAppCount", "edgeRuntimePagesCount", "isRspack", "process", "env", "NEXT_RSPACK", "undefined", "usages", "map", "featureName", "invocationCount", "packagesUsedInServerSideProps", "packageName", "package", "error", "anonymizedLocation", "errorCode", "extractNextErrorCode", "location"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;IAwOaA,kBAAkB;eAAlBA;;IA1EAC,yBAAyB;eAAzBA;;IAsDAC,gDAAgD;eAAhDA;;IApJGC,mBAAmB;eAAnBA;;IA+BAC,gBAAgB;eAAhBA;;IAyGAC,sBAAsB;eAAtBA;;IAvEAC,kBAAkB;eAAlBA;;IAqHAC,gBAAgB;eAAhBA;;IA1MAC,uBAAuB;eAAvBA;;IA+KAC,oCAAoC;eAApCA;;IAxMAC,uBAAuB;eAAvBA;;;qCAhBqB;AAErC,MAAMC,0BACJ;AACF,MAAMC,yBAAyB;AAC/B,MAAMC,mBAAmB;AAEzB,MAAMC,6BAA6B;AAS5B,SAASJ,wBAAwBK,KAA8B;IAIpE,OAAO;QACLC,WAAWF;QACXG,SAASF;IACX;AACF;AAEA,MAAMG,6BAA6B;AAe5B,SAASV,wBAAwBO,KAA8B;IAIpE,OAAO;QACLC,WAAWE;QACXD,SAASF;IACX;AACF;AAEA,MAAMI,wBAAwB;AAUvB,SAAShB,oBACdiB,SAAmB,EACnBL,KAGC;IAED,OAAO;QACLC,WAAWG;QACXF,SAAS;YACP,GAAGF,KAAK;YACRM,gBAAgBD,UAAUE,MAAM;YAChCC,gBAAgBH,UAAUI,IAAI,CAAC,CAACC,OAC9Bd,wBAAwBe,IAAI,CAACD;YAE/BE,cAAcP,UAAUI,IAAI,CAC1B,CAACC,OACCb,uBAAuBc,IAAI,CAACD,SAASZ,iBAAiBa,IAAI,CAACD;YAE/DG,oBAAoBb,MAAMa,kBAAkB;QAC9C;IACF;AACF;AAEA,MAAMC,qBAAqB;AAOpB,SAASzB,iBAAiBW,KAAuB;IACtD,OAAO;QACLC,WAAWa;QACXZ,SAASF;IACX;AACF;AAEA,MAAMe,wBAAwB;AA2BvB,SAASxB,mBACdc,SAAmB,EACnBL,KAGC;IAED,OAAO;QACLC,WAAWc;QACXb,SAAS;YACP,GAAGF,KAAK;YACRM,gBAAgBD,UAAUE,MAAM;YAChCC,gBAAgBH,UAAUI,IAAI,CAAC,CAACC,OAC9Bd,wBAAwBe,IAAI,CAACD;YAE/BE,cAAcP,UAAUI,IAAI,CAC1B,CAACC,OACCb,uBAAuBc,IAAI,CAACD,SAASZ,iBAAiBa,IAAI,CAACD;YAE/DG,oBAAoBb,MAAMa,kBAAkB;YAC5CG,qBAAqBhB,MAAMgB,mBAAmB;YAC9CC,qBAAqBjB,MAAMiB,mBAAmB;YAC9CC,qBAAqBlB,MAAMkB,mBAAmB;YAC9CC,uBAAuBnB,MAAMmB,qBAAqB;YAClDC,UAAUC,QAAQC,GAAG,CAACC,WAAW,KAAKC;QACxC;IACF;AACF;AAEO,MAAMtC,4BAA4B;AA0ClC,SAASI,uBACdmC,MAA6C;IAE7C,OAAOA,OAAOC,GAAG,CAAC,CAAC,EAAEC,WAAW,EAAEC,eAAe,EAAE,GAAM,CAAA;YACvD3B,WAAWf;YACXgB,SAAS;gBACPyB;gBACAC;YACF;QACF,CAAA;AACF;AAEO,MAAMzC,mDACX;AAMK,SAASO,qCACdmC,6BAEC;IAED,OAAOA,8BAA8BH,GAAG,CAAC,CAACI,cAAiB,CAAA;YACzD7B,WAAWd;YACXe,SAAS;gBACP6B,SAASD;YACX;QACF,CAAA;AACF;AAEO,MAAM7C,qBAAqB;AAc3B,SAASO,iBACdwC,KAAY,EACZC,kBAAsC;IAEtC,OAAO;QACLhC,WAAWhB;QACXiB,SAAS;YACPgC,WAAWC,IAAAA,yCAAoB,EAACH,UAAU;YAC1CI,UAAUH;QACZ;IACF;AACF", "ignoreList": [0]}