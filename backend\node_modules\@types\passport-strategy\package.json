{"name": "@types/passport-strategy", "version": "0.2.38", "description": "TypeScript definitions for passport-strategy", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/passport-strategy", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "liorm", "url": "https://github.com/liorm"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/passport-strategy"}, "scripts": {}, "dependencies": {"@types/express": "*", "@types/passport": "*"}, "typesPublisherContentHash": "b9c360136a0cb44f7d54f7b2d72071dbb3d3f60f27755beb039724a1d00d74da", "typeScriptVersion": "4.5"}