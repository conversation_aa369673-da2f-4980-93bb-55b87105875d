{"version": 3, "sources": ["../../../src/server/request/pathname.ts"], "sourcesContent": ["import type { WorkStore } from '../app-render/work-async-storage.external'\n\nimport {\n  delayUntilRuntimeStage,\n  postponeWithTracking,\n  type DynamicTrackingState,\n} from '../app-render/dynamic-rendering'\n\nimport {\n  throwInvariantForMissingStore,\n  workUnitAsyncStorage,\n  type StaticPrerenderStore,\n} from '../app-render/work-unit-async-storage.external'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nexport function createServerPathnameForMetadata(\n  underlyingPathname: string,\n  workStore: WorkStore\n): Promise<string> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy': {\n        return createPrerenderPathname(\n          underlyingPathname,\n          workStore,\n          workUnitStore\n        )\n      }\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        throw new InvariantError(\n          'createServerPathnameForMetadata should not be called in cache contexts.'\n        )\n\n      case 'prerender-runtime':\n        return delayUntilRuntimeStage(\n          workUnitStore,\n          createRenderPathname(underlyingPathname)\n        )\n      case 'request':\n        return createRenderPathname(underlyingPathname)\n      default:\n        workUnitStore satisfies never\n    }\n  }\n  throwInvariantForMissingStore()\n}\n\nfunction createPrerenderPathname(\n  underlyingPathname: string,\n  workStore: WorkStore,\n  prerenderStore: StaticPrerenderStore\n): Promise<string> {\n  switch (prerenderStore.type) {\n    case 'prerender-client':\n      throw new InvariantError(\n        'createPrerenderPathname was called inside a client component scope.'\n      )\n    case 'prerender': {\n      const fallbackParams = prerenderStore.fallbackRouteParams\n      if (fallbackParams && fallbackParams.size > 0) {\n        return makeHangingPromise<string>(\n          prerenderStore.renderSignal,\n          workStore.route,\n          '`pathname`'\n        )\n      }\n      break\n    }\n    case 'prerender-ppr': {\n      const fallbackParams = prerenderStore.fallbackRouteParams\n      if (fallbackParams && fallbackParams.size > 0) {\n        return makeErroringPathname(workStore, prerenderStore.dynamicTracking)\n      }\n      break\n    }\n    case 'prerender-legacy':\n      break\n    default:\n      prerenderStore satisfies never\n  }\n\n  // We don't have any fallback params so we have an entirely static safe params object\n  return Promise.resolve(underlyingPathname)\n}\n\nfunction makeErroringPathname<T>(\n  workStore: WorkStore,\n  dynamicTracking: null | DynamicTrackingState\n): Promise<T> {\n  let reject: null | ((reason: unknown) => void) = null\n  const promise = new Promise<T>((_, re) => {\n    reject = re\n  })\n\n  const originalThen = promise.then.bind(promise)\n\n  // We instrument .then so that we can generate a tracking event only if you actually\n  // await this promise, not just that it is created.\n  promise.then = (onfulfilled, onrejected) => {\n    if (reject) {\n      try {\n        postponeWithTracking(\n          workStore.route,\n          'metadata relative url resolving',\n          dynamicTracking\n        )\n      } catch (error) {\n        reject(error)\n        reject = null\n      }\n    }\n    return originalThen(onfulfilled, onrejected)\n  }\n\n  // We wrap in a noop proxy to trick the runtime into thinking it\n  // isn't a native promise (it's not really). This is so that awaiting\n  // the promise will call the `then` property triggering the lazy postpone\n  return new Proxy(promise, {})\n}\n\nfunction createRenderPathname(underlyingPathname: string): Promise<string> {\n  return Promise.resolve(underlyingPathname)\n}\n"], "names": ["createServerPathnameForMetadata", "underlyingPathname", "workStore", "workUnitStore", "workUnitAsyncStorage", "getStore", "type", "createPrerenderPathname", "InvariantError", "delayUntilRuntimeStage", "createRenderPathname", "throwInvariantForMissingStore", "prerenderStore", "fallbackP<PERSON><PERSON>", "fallbackRouteParams", "size", "makeHangingPromise", "renderSignal", "route", "makeErroringPathname", "dynamicTracking", "Promise", "resolve", "reject", "promise", "_", "re", "originalThen", "then", "bind", "onfulfilled", "onrejected", "postponeWithTracking", "error", "Proxy"], "mappings": ";;;;+BAgBgBA;;;eAAAA;;;kCAVT;8CAMA;uCAC4B;gCACJ;AAExB,SAASA,gCACdC,kBAA0B,EAC1BC,SAAoB;IAEpB,MAAMC,gBAAgBC,kDAAoB,CAACC,QAAQ;IACnD,IAAIF,eAAe;QACjB,OAAQA,cAAcG,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBAAoB;oBACvB,OAAOC,wBACLN,oBACAC,WACAC;gBAEJ;YACA,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,qBAEL,CAFK,IAAIK,8BAAc,CACtB,4EADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YAEF,KAAK;gBACH,OAAOC,IAAAA,wCAAsB,EAC3BN,eACAO,qBAAqBT;YAEzB,KAAK;gBACH,OAAOS,qBAAqBT;YAC9B;gBACEE;QACJ;IACF;IACAQ,IAAAA,2DAA6B;AAC/B;AAEA,SAASJ,wBACPN,kBAA0B,EAC1BC,SAAoB,EACpBU,cAAoC;IAEpC,OAAQA,eAAeN,IAAI;QACzB,KAAK;YACH,MAAM,qBAEL,CAFK,IAAIE,8BAAc,CACtB,wEADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,KAAK;YAAa;gBAChB,MAAMK,iBAAiBD,eAAeE,mBAAmB;gBACzD,IAAID,kBAAkBA,eAAeE,IAAI,GAAG,GAAG;oBAC7C,OAAOC,IAAAA,yCAAkB,EACvBJ,eAAeK,YAAY,EAC3Bf,UAAUgB,KAAK,EACf;gBAEJ;gBACA;YACF;QACA,KAAK;YAAiB;gBACpB,MAAML,iBAAiBD,eAAeE,mBAAmB;gBACzD,IAAID,kBAAkBA,eAAeE,IAAI,GAAG,GAAG;oBAC7C,OAAOI,qBAAqBjB,WAAWU,eAAeQ,eAAe;gBACvE;gBACA;YACF;QACA,KAAK;YACH;QACF;YACER;IACJ;IAEA,qFAAqF;IACrF,OAAOS,QAAQC,OAAO,CAACrB;AACzB;AAEA,SAASkB,qBACPjB,SAAoB,EACpBkB,eAA4C;IAE5C,IAAIG,SAA6C;IACjD,MAAMC,UAAU,IAAIH,QAAW,CAACI,GAAGC;QACjCH,SAASG;IACX;IAEA,MAAMC,eAAeH,QAAQI,IAAI,CAACC,IAAI,CAACL;IAEvC,oFAAoF;IACpF,mDAAmD;IACnDA,QAAQI,IAAI,GAAG,CAACE,aAAaC;QAC3B,IAAIR,QAAQ;YACV,IAAI;gBACFS,IAAAA,sCAAoB,EAClB9B,UAAUgB,KAAK,EACf,mCACAE;YAEJ,EAAE,OAAOa,OAAO;gBACdV,OAAOU;gBACPV,SAAS;YACX;QACF;QACA,OAAOI,aAAaG,aAAaC;IACnC;IAEA,gEAAgE;IAChE,qEAAqE;IACrE,yEAAyE;IACzE,OAAO,IAAIG,MAAMV,SAAS,CAAC;AAC7B;AAEA,SAASd,qBAAqBT,kBAA0B;IACtD,OAAOoB,QAAQC,OAAO,CAACrB;AACzB", "ignoreList": [0]}