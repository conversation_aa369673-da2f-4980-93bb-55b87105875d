{"version": 3, "sources": ["../../../src/server/dev/middleware-turbopack.ts"], "sourcesContent": ["import type { IncomingMessage, ServerResponse } from 'http'\nimport {\n  getOriginalCodeFrame,\n  ignoreListAnonymousStackFramesIfSandwiched,\n  type IgnorableStackFrame,\n  type OriginalStackFrameResponse,\n  type OriginalStackFramesRequest,\n  type OriginalStackFramesResponse,\n  type StackFrame,\n} from '../../next-devtools/server/shared'\nimport { middlewareResponse } from '../../next-devtools/server/middleware-response'\nimport path from 'path'\nimport { openFileInEditor } from '../../next-devtools/server/launch-editor'\nimport {\n  SourceMapConsumer,\n  type NullableMappedPosition,\n} from 'next/dist/compiled/source-map08'\nimport type { Project, TurbopackStackFrame } from '../../build/swc/types'\nimport {\n  type ModernSourceMapPayload,\n  devirtualizeReactServerURL,\n  findApplicableSourceMapPayload,\n} from '../lib/source-maps'\nimport { findSourceMap, type SourceMap } from 'node:module'\nimport { fileURLToPath, pathToFileURL } from 'node:url'\nimport { inspect } from 'node:util'\n\nfunction shouldIgnorePath(modulePath: string): boolean {\n  return (\n    modulePath.includes('node_modules') ||\n    // Only relevant for when Next.js is symlinked e.g. in the Next.js monorepo\n    modulePath.includes('next/dist') ||\n    modulePath.startsWith('node:')\n  )\n}\n\nconst currentSourcesByFile: Map<string, Promise<string | null>> = new Map()\n/**\n * @returns 1-based lines and 1-based columns\n */\nasync function batchedTraceSource(\n  project: Project,\n  frame: TurbopackStackFrame\n): Promise<{ frame: IgnorableStackFrame; source: string | null } | undefined> {\n  const file = frame.file\n    ? // TODO(veil): Why are the frames sent encoded?\n      decodeURIComponent(frame.file)\n    : undefined\n\n  if (!file) return\n\n  // For node internals they cannot traced the actual source code with project.traceSource,\n  // we need an early return to indicate it's ignored to avoid the unknown scheme error from `project.traceSource`.\n  if (file.startsWith('node:')) {\n    return {\n      frame: {\n        file,\n        line1: frame.line ?? null,\n        column1: frame.column ?? null,\n        methodName: frame.methodName ?? '<unknown>',\n        ignored: true,\n        arguments: [],\n      },\n      source: null,\n    }\n  }\n\n  const currentDirectoryFileUrl = pathToFileURL(process.cwd()).href\n\n  const sourceFrame = await project.traceSource(frame, currentDirectoryFileUrl)\n  if (!sourceFrame) {\n    return {\n      frame: {\n        file,\n        line1: frame.line ?? null,\n        column1: frame.column ?? null,\n        methodName: frame.methodName ?? '<unknown>',\n        ignored: shouldIgnorePath(file),\n        arguments: [],\n      },\n      source: null,\n    }\n  }\n\n  let source = null\n  const originalFile = sourceFrame.originalFile\n\n  // Don't look up source for node_modules or internals. These can often be large bundled files.\n  const ignored =\n    shouldIgnorePath(originalFile ?? sourceFrame.file) ||\n    // isInternal means resource starts with turbopack:///[turbopack]\n    !!sourceFrame.isInternal\n  if (originalFile && !ignored) {\n    let sourcePromise = currentSourcesByFile.get(originalFile)\n    if (!sourcePromise) {\n      sourcePromise = project.getSourceForAsset(originalFile)\n      currentSourcesByFile.set(originalFile, sourcePromise)\n      setTimeout(() => {\n        // Cache file reads for 100ms, as frames will often reference the same\n        // files and can be large.\n        currentSourcesByFile.delete(originalFile!)\n      }, 100)\n    }\n    source = await sourcePromise\n  }\n\n  // TODO: get ignoredList from turbopack source map\n  const ignorableFrame: IgnorableStackFrame = {\n    file: sourceFrame.file,\n    line1: sourceFrame.line ?? null,\n    column1: sourceFrame.column ?? null,\n    methodName:\n      // We ignore the sourcemapped name since it won't be the correct name.\n      // The callsite will point to the column of the variable name instead of the\n      // name of the enclosing function.\n      // TODO(NDX-531): Spy on prepareStackTrace to get the enclosing line number for method name mapping.\n      frame.methodName ?? '<unknown>',\n    ignored,\n    arguments: [],\n  }\n\n  return {\n    frame: ignorableFrame,\n    source,\n  }\n}\n\nfunction parseFile(fileParam: string | null): string | undefined {\n  if (!fileParam) {\n    return undefined\n  }\n\n  return devirtualizeReactServerURL(fileParam)\n}\n\nfunction createStackFrames(\n  body: OriginalStackFramesRequest\n): TurbopackStackFrame[] {\n  const { frames, isServer } = body\n\n  return frames\n    .map((frame): TurbopackStackFrame | undefined => {\n      const file = parseFile(frame.file)\n\n      if (!file) {\n        return undefined\n      }\n\n      return {\n        file,\n        methodName: frame.methodName ?? '<unknown>',\n        line: frame.line1 ?? undefined,\n        column: frame.column1 ?? undefined,\n        isServer,\n      }\n    })\n    .filter((f): f is TurbopackStackFrame => f !== undefined)\n}\n\nfunction createStackFrame(\n  searchParams: URLSearchParams\n): TurbopackStackFrame | undefined {\n  const file = parseFile(searchParams.get('file'))\n\n  if (!file) {\n    return undefined\n  }\n\n  return {\n    file,\n    methodName: searchParams.get('methodName') ?? '<unknown>',\n    line: parseInt(searchParams.get('line1') ?? '0', 10) || undefined,\n    column: parseInt(searchParams.get('column1') ?? '0', 10) || undefined,\n    isServer: searchParams.get('isServer') === 'true',\n  }\n}\n\n/**\n * @returns 1-based lines and 1-based columns\n */\nasync function nativeTraceSource(\n  frame: TurbopackStackFrame\n): Promise<{ frame: IgnorableStackFrame; source: string | null } | undefined> {\n  const sourceURL = frame.file\n  let sourceMapPayload: ModernSourceMapPayload | undefined\n  try {\n    sourceMapPayload = findSourceMap(sourceURL)?.payload\n  } catch (cause) {\n    throw new Error(\n      `${sourceURL}: Invalid source map. Only conformant source maps can be used to find the original code.`,\n      { cause }\n    )\n  }\n\n  if (sourceMapPayload !== undefined) {\n    let consumer: SourceMapConsumer\n    try {\n      consumer = await new SourceMapConsumer(sourceMapPayload)\n    } catch (cause) {\n      throw new Error(\n        `${sourceURL}: Invalid source map. Only conformant source maps can be used to find the original code.`,\n        { cause }\n      )\n    }\n    let traced: {\n      originalPosition: NullableMappedPosition\n      sourceContent: string | null\n    } | null\n    try {\n      const originalPosition = consumer.originalPositionFor({\n        line: frame.line ?? 1,\n        // 0-based columns out requires 0-based columns in.\n        column: (frame.column ?? 1) - 1,\n      })\n\n      if (originalPosition.source === null) {\n        traced = null\n      } else {\n        const sourceContent: string | null =\n          consumer.sourceContentFor(\n            originalPosition.source,\n            /* returnNullOnMissing */ true\n          ) ?? null\n\n        traced = { originalPosition, sourceContent }\n      }\n    } finally {\n      consumer.destroy()\n    }\n\n    if (traced !== null) {\n      const { originalPosition, sourceContent } = traced\n      const applicableSourceMap = findApplicableSourceMapPayload(\n        (frame.line ?? 1) - 1,\n        (frame.column ?? 1) - 1,\n        sourceMapPayload\n      )\n\n      // TODO(veil): Upstream a method to sourcemap consumer that immediately says if a frame is ignored or not.\n      let ignored = false\n      if (applicableSourceMap === undefined) {\n        console.error(\n          'No applicable source map found in sections for frame',\n          frame\n        )\n      } else {\n        // TODO: O(n^2). Consider moving `ignoreList` into a Set\n        const sourceIndex = applicableSourceMap.sources.indexOf(\n          originalPosition.source!\n        )\n        ignored =\n          applicableSourceMap.ignoreList?.includes(sourceIndex) ??\n          // When sourcemap is not available, fallback to checking `frame.file`.\n          // e.g. In pages router, nextjs server code is not bundled into the page.\n          shouldIgnorePath(frame.file)\n      }\n\n      const originalStackFrame: IgnorableStackFrame = {\n        methodName:\n          // We ignore the sourcemapped name since it won't be the correct name.\n          // The callsite will point to the column of the variable name instead of the\n          // name of the enclosing function.\n          // TODO(NDX-531): Spy on prepareStackTrace to get the enclosing line number for method name mapping.\n          frame.methodName\n            ?.replace('__WEBPACK_DEFAULT_EXPORT__', 'default')\n            ?.replace('__webpack_exports__.', '') || '<unknown>',\n        file: originalPosition.source,\n        line1: originalPosition.line,\n        column1:\n          originalPosition.column === null ? null : originalPosition.column + 1,\n        // TODO: c&p from async createOriginalStackFrame but why not frame.arguments?\n        arguments: [],\n        ignored,\n      }\n\n      return {\n        frame: originalStackFrame,\n        source: sourceContent,\n      }\n    }\n  }\n\n  return undefined\n}\n\nasync function createOriginalStackFrame(\n  project: Project,\n  projectPath: string,\n  frame: TurbopackStackFrame\n): Promise<OriginalStackFrameResponse | null> {\n  const traced =\n    (await nativeTraceSource(frame)) ??\n    // TODO(veil): When would the bundler know more than native?\n    // If it's faster, try the bundler first and fall back to native later.\n    (await batchedTraceSource(project, frame))\n  if (!traced) {\n    return null\n  }\n\n  let normalizedStackFrameLocation = traced.frame.file\n  if (\n    normalizedStackFrameLocation !== null &&\n    normalizedStackFrameLocation.startsWith('file://')\n  ) {\n    normalizedStackFrameLocation = path.relative(\n      projectPath,\n      fileURLToPath(normalizedStackFrameLocation)\n    )\n  }\n\n  return {\n    originalStackFrame: {\n      arguments: traced.frame.arguments,\n      file: normalizedStackFrameLocation,\n      line1: traced.frame.line1,\n      column1: traced.frame.column1,\n      ignored: traced.frame.ignored,\n      methodName: traced.frame.methodName,\n    },\n    originalCodeFrame: getOriginalCodeFrame(traced.frame, traced.source),\n  }\n}\n\nexport function getOverlayMiddleware({\n  project,\n  projectPath,\n  isSrcDir,\n}: {\n  project: Project\n  projectPath: string\n  isSrcDir: boolean\n}) {\n  return async function (\n    req: IncomingMessage,\n    res: ServerResponse,\n    next: () => void\n  ): Promise<void> {\n    const { pathname, searchParams } = new URL(req.url!, 'http://n')\n\n    if (pathname === '/__nextjs_original-stack-frames') {\n      if (req.method !== 'POST') {\n        return middlewareResponse.badRequest(res)\n      }\n\n      const body = await new Promise<string>((resolve, reject) => {\n        let data = ''\n        req.on('data', (chunk) => {\n          data += chunk\n        })\n        req.on('end', () => resolve(data))\n        req.on('error', reject)\n      })\n\n      const request = JSON.parse(body) as OriginalStackFramesRequest\n      const result = await getOriginalStackFrames({\n        project,\n        projectPath,\n        frames: request.frames,\n        isServer: request.isServer,\n        isEdgeServer: request.isEdgeServer,\n        isAppDirectory: request.isAppDirectory,\n      })\n\n      ignoreListAnonymousStackFramesIfSandwiched(result)\n\n      return middlewareResponse.json(res, result)\n    } else if (pathname === '/__nextjs_launch-editor') {\n      const isAppRelativePath = searchParams.get('isAppRelativePath') === '1'\n\n      let openEditorResult\n      if (isAppRelativePath) {\n        const relativeFilePath = searchParams.get('file') || ''\n        const appPath = path.join(\n          'app',\n          isSrcDir ? 'src' : '',\n          relativeFilePath\n        )\n        openEditorResult = await openFileInEditor(appPath, 1, 1, projectPath)\n      } else {\n        const frame = createStackFrame(searchParams)\n        if (!frame) return middlewareResponse.badRequest(res)\n        openEditorResult = await openFileInEditor(\n          frame.file,\n          frame.line ?? 1,\n          frame.column ?? 1,\n          projectPath\n        )\n      }\n\n      if (openEditorResult.error) {\n        return middlewareResponse.internalServerError(\n          res,\n          openEditorResult.error\n        )\n      }\n      if (!openEditorResult.found) {\n        return middlewareResponse.notFound(res)\n      }\n      return middlewareResponse.noContent(res)\n    }\n\n    return next()\n  }\n}\n\nexport function getSourceMapMiddleware(project: Project) {\n  return async function (\n    req: IncomingMessage,\n    res: ServerResponse,\n    next: () => void\n  ): Promise<void> {\n    const { pathname, searchParams } = new URL(req.url!, 'http://n')\n\n    if (pathname !== '/__nextjs_source-map') {\n      return next()\n    }\n\n    let filename = searchParams.get('filename')\n\n    if (!filename) {\n      return middlewareResponse.badRequest(res)\n    }\n\n    let nativeSourceMap: SourceMap | undefined\n    try {\n      nativeSourceMap = findSourceMap(filename)\n    } catch (cause) {\n      return middlewareResponse.internalServerError(\n        res,\n        new Error(\n          `${filename}: Invalid source map. Only conformant source maps can be used to find the original code.`,\n          { cause }\n        )\n      )\n    }\n\n    if (nativeSourceMap !== undefined) {\n      const sourceMapPayload = nativeSourceMap.payload\n      return middlewareResponse.json(res, sourceMapPayload)\n    }\n\n    try {\n      // Turbopack chunk filenames might be URL-encoded.\n      filename = decodeURI(filename)\n    } catch {\n      return middlewareResponse.badRequest(res)\n    }\n\n    if (path.isAbsolute(filename)) {\n      filename = pathToFileURL(filename).href\n    }\n\n    try {\n      const sourceMapString = await project.getSourceMap(filename)\n\n      if (sourceMapString) {\n        return middlewareResponse.jsonString(res, sourceMapString)\n      }\n    } catch (cause) {\n      return middlewareResponse.internalServerError(\n        res,\n        new Error(\n          `Failed to get source map for '${filename}'. This is a bug in Next.js`,\n          {\n            cause,\n          }\n        )\n      )\n    }\n\n    middlewareResponse.noContent(res)\n  }\n}\n\nexport async function getOriginalStackFrames({\n  project,\n  projectPath,\n  frames,\n  isServer,\n  isEdgeServer,\n  isAppDirectory,\n}: {\n  project: Project\n  projectPath: string\n  frames: readonly StackFrame[]\n  isServer: boolean\n  isEdgeServer: boolean\n  isAppDirectory: boolean\n}): Promise<OriginalStackFramesResponse> {\n  const stackFrames = createStackFrames({\n    frames,\n    isServer,\n    isEdgeServer,\n    isAppDirectory,\n  })\n\n  return Promise.all(\n    stackFrames.map(async (frame) => {\n      try {\n        const stackFrame = await createOriginalStackFrame(\n          project,\n          projectPath,\n          frame\n        )\n        if (stackFrame === null) {\n          return {\n            status: 'rejected',\n            reason: 'Failed to create original stack frame',\n          }\n        }\n        return { status: 'fulfilled', value: stackFrame }\n      } catch (error) {\n        return {\n          status: 'rejected',\n          reason: inspect(error, { colors: false }),\n        }\n      }\n    })\n  )\n}\n"], "names": ["getOriginalStackFrames", "getOverlayMiddleware", "getSourceMapMiddleware", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modulePath", "includes", "startsWith", "currentSourcesByFile", "Map", "batchedTraceSource", "project", "frame", "file", "decodeURIComponent", "undefined", "line1", "line", "column1", "column", "methodName", "ignored", "arguments", "source", "currentDirectoryFileUrl", "pathToFileURL", "process", "cwd", "href", "sourceFrame", "traceSource", "originalFile", "isInternal", "sourcePromise", "get", "getSourceForAsset", "set", "setTimeout", "delete", "ignorableFrame", "parseFile", "fileParam", "devirtualizeReactServerURL", "createStackFrames", "body", "frames", "isServer", "map", "filter", "f", "createStackFrame", "searchParams", "parseInt", "nativeTraceSource", "sourceURL", "sourceMapPayload", "findSourceMap", "payload", "cause", "Error", "consumer", "SourceMapConsumer", "traced", "originalPosition", "originalPositionFor", "sourceContent", "sourceContentFor", "destroy", "applicableSourceMap", "findApplicableSourceMapPayload", "console", "error", "sourceIndex", "sources", "indexOf", "ignoreList", "originalStackFrame", "replace", "createOriginalStackFrame", "projectPath", "normalizedStackFrameLocation", "path", "relative", "fileURLToPath", "originalCodeFrame", "getOriginalCodeFrame", "isSrcDir", "req", "res", "next", "pathname", "URL", "url", "method", "middlewareResponse", "badRequest", "Promise", "resolve", "reject", "data", "on", "chunk", "request", "JSON", "parse", "result", "isEdgeServer", "isAppDirectory", "ignoreListAnonymousStackFramesIfSandwiched", "json", "isAppRelativePath", "openEditorResult", "relativeFilePath", "appPath", "join", "openFileInEditor", "internalServerError", "found", "notFound", "noContent", "filename", "nativeSourceMap", "decodeURI", "isAbsolute", "sourceMapString", "getSourceMap", "jsonString", "stackFrames", "all", "stackFrame", "status", "reason", "value", "inspect", "colors"], "mappings": ";;;;;;;;;;;;;;;;IA0dsBA,sBAAsB;eAAtBA;;IAvJNC,oBAAoB;eAApBA;;IAkFAC,sBAAsB;eAAtBA;;;wBA5YT;oCAC4B;6DAClB;8BACgB;6BAI1B;4BAMA;4BACuC;yBACD;0BACrB;;;;;;AAExB,SAASC,iBAAiBC,UAAkB;IAC1C,OACEA,WAAWC,QAAQ,CAAC,mBACpB,2EAA2E;IAC3ED,WAAWC,QAAQ,CAAC,gBACpBD,WAAWE,UAAU,CAAC;AAE1B;AAEA,MAAMC,uBAA4D,IAAIC;AACtE;;CAEC,GACD,eAAeC,mBACbC,OAAgB,EAChBC,KAA0B;IAE1B,MAAMC,OAAOD,MAAMC,IAAI,GAEnBC,mBAAmBF,MAAMC,IAAI,IAC7BE;IAEJ,IAAI,CAACF,MAAM;IAEX,yFAAyF;IACzF,iHAAiH;IACjH,IAAIA,KAAKN,UAAU,CAAC,UAAU;QAC5B,OAAO;YACLK,OAAO;gBACLC;gBACAG,OAAOJ,MAAMK,IAAI,IAAI;gBACrBC,SAASN,MAAMO,MAAM,IAAI;gBACzBC,YAAYR,MAAMQ,UAAU,IAAI;gBAChCC,SAAS;gBACTC,WAAW,EAAE;YACf;YACAC,QAAQ;QACV;IACF;IAEA,MAAMC,0BAA0BC,IAAAA,sBAAa,EAACC,QAAQC,GAAG,IAAIC,IAAI;IAEjE,MAAMC,cAAc,MAAMlB,QAAQmB,WAAW,CAAClB,OAAOY;IACrD,IAAI,CAACK,aAAa;QAChB,OAAO;YACLjB,OAAO;gBACLC;gBACAG,OAAOJ,MAAMK,IAAI,IAAI;gBACrBC,SAASN,MAAMO,MAAM,IAAI;gBACzBC,YAAYR,MAAMQ,UAAU,IAAI;gBAChCC,SAASjB,iBAAiBS;gBAC1BS,WAAW,EAAE;YACf;YACAC,QAAQ;QACV;IACF;IAEA,IAAIA,SAAS;IACb,MAAMQ,eAAeF,YAAYE,YAAY;IAE7C,8FAA8F;IAC9F,MAAMV,UACJjB,iBAAiB2B,gBAAgBF,YAAYhB,IAAI,KACjD,iEAAiE;IACjE,CAAC,CAACgB,YAAYG,UAAU;IAC1B,IAAID,gBAAgB,CAACV,SAAS;QAC5B,IAAIY,gBAAgBzB,qBAAqB0B,GAAG,CAACH;QAC7C,IAAI,CAACE,eAAe;YAClBA,gBAAgBtB,QAAQwB,iBAAiB,CAACJ;YAC1CvB,qBAAqB4B,GAAG,CAACL,cAAcE;YACvCI,WAAW;gBACT,sEAAsE;gBACtE,0BAA0B;gBAC1B7B,qBAAqB8B,MAAM,CAACP;YAC9B,GAAG;QACL;QACAR,SAAS,MAAMU;IACjB;IAEA,kDAAkD;IAClD,MAAMM,iBAAsC;QAC1C1B,MAAMgB,YAAYhB,IAAI;QACtBG,OAAOa,YAAYZ,IAAI,IAAI;QAC3BC,SAASW,YAAYV,MAAM,IAAI;QAC/BC,YACE,sEAAsE;QACtE,4EAA4E;QAC5E,kCAAkC;QAClC,oGAAoG;QACpGR,MAAMQ,UAAU,IAAI;QACtBC;QACAC,WAAW,EAAE;IACf;IAEA,OAAO;QACLV,OAAO2B;QACPhB;IACF;AACF;AAEA,SAASiB,UAAUC,SAAwB;IACzC,IAAI,CAACA,WAAW;QACd,OAAO1B;IACT;IAEA,OAAO2B,IAAAA,sCAA0B,EAACD;AACpC;AAEA,SAASE,kBACPC,IAAgC;IAEhC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAE,GAAGF;IAE7B,OAAOC,OACJE,GAAG,CAAC,CAACnC;QACJ,MAAMC,OAAO2B,UAAU5B,MAAMC,IAAI;QAEjC,IAAI,CAACA,MAAM;YACT,OAAOE;QACT;QAEA,OAAO;YACLF;YACAO,YAAYR,MAAMQ,UAAU,IAAI;YAChCH,MAAML,MAAMI,KAAK,IAAID;YACrBI,QAAQP,MAAMM,OAAO,IAAIH;YACzB+B;QACF;IACF,GACCE,MAAM,CAAC,CAACC,IAAgCA,MAAMlC;AACnD;AAEA,SAASmC,iBACPC,YAA6B;IAE7B,MAAMtC,OAAO2B,UAAUW,aAAajB,GAAG,CAAC;IAExC,IAAI,CAACrB,MAAM;QACT,OAAOE;IACT;IAEA,OAAO;QACLF;QACAO,YAAY+B,aAAajB,GAAG,CAAC,iBAAiB;QAC9CjB,MAAMmC,SAASD,aAAajB,GAAG,CAAC,YAAY,KAAK,OAAOnB;QACxDI,QAAQiC,SAASD,aAAajB,GAAG,CAAC,cAAc,KAAK,OAAOnB;QAC5D+B,UAAUK,aAAajB,GAAG,CAAC,gBAAgB;IAC7C;AACF;AAEA;;CAEC,GACD,eAAemB,kBACbzC,KAA0B;IAE1B,MAAM0C,YAAY1C,MAAMC,IAAI;IAC5B,IAAI0C;IACJ,IAAI;YACiBC;QAAnBD,oBAAmBC,iBAAAA,IAAAA,yBAAa,EAACF,+BAAdE,eAA0BC,OAAO;IACtD,EAAE,OAAOC,OAAO;QACd,MAAM,qBAGL,CAHK,IAAIC,MACR,GAAGL,UAAU,wFAAwF,CAAC,EACtG;YAAEI;QAAM,IAFJ,qBAAA;mBAAA;wBAAA;0BAAA;QAGN;IACF;IAEA,IAAIH,qBAAqBxC,WAAW;QAClC,IAAI6C;QACJ,IAAI;YACFA,WAAW,MAAM,IAAIC,8BAAiB,CAACN;QACzC,EAAE,OAAOG,OAAO;YACd,MAAM,qBAGL,CAHK,IAAIC,MACR,GAAGL,UAAU,wFAAwF,CAAC,EACtG;gBAAEI;YAAM,IAFJ,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QACA,IAAII;QAIJ,IAAI;YACF,MAAMC,mBAAmBH,SAASI,mBAAmB,CAAC;gBACpD/C,MAAML,MAAMK,IAAI,IAAI;gBACpB,mDAAmD;gBACnDE,QAAQ,AAACP,CAAAA,MAAMO,MAAM,IAAI,CAAA,IAAK;YAChC;YAEA,IAAI4C,iBAAiBxC,MAAM,KAAK,MAAM;gBACpCuC,SAAS;YACX,OAAO;gBACL,MAAMG,gBACJL,SAASM,gBAAgB,CACvBH,iBAAiBxC,MAAM,EACvB,uBAAuB,GAAG,SACvB;gBAEPuC,SAAS;oBAAEC;oBAAkBE;gBAAc;YAC7C;QACF,SAAU;YACRL,SAASO,OAAO;QAClB;QAEA,IAAIL,WAAW,MAAM;gBA6Bf,sEAAsE;YACtE,4EAA4E;YAC5E,kCAAkC;YAClC,oGAAoG;YACpGlD,2BAAAA;YAhCJ,MAAM,EAAEmD,gBAAgB,EAAEE,aAAa,EAAE,GAAGH;YAC5C,MAAMM,sBAAsBC,IAAAA,0CAA8B,EACxD,AAACzD,CAAAA,MAAMK,IAAI,IAAI,CAAA,IAAK,GACpB,AAACL,CAAAA,MAAMO,MAAM,IAAI,CAAA,IAAK,GACtBoC;YAGF,0GAA0G;YAC1G,IAAIlC,UAAU;YACd,IAAI+C,wBAAwBrD,WAAW;gBACrCuD,QAAQC,KAAK,CACX,wDACA3D;YAEJ,OAAO;oBAMHwD;gBALF,wDAAwD;gBACxD,MAAMI,cAAcJ,oBAAoBK,OAAO,CAACC,OAAO,CACrDX,iBAAiBxC,MAAM;gBAEzBF,UACE+C,EAAAA,kCAAAA,oBAAoBO,UAAU,qBAA9BP,gCAAgC9D,QAAQ,CAACkE,iBACzC,sEAAsE;gBACtE,yEAAyE;gBACzEpE,iBAAiBQ,MAAMC,IAAI;YAC/B;YAEA,MAAM+D,qBAA0C;gBAC9CxD,YAKER,EAAAA,oBAAAA,MAAMQ,UAAU,sBAAhBR,4BAAAA,kBACIiE,OAAO,CAAC,8BAA8B,+BAD1CjE,0BAEIiE,OAAO,CAAC,wBAAwB,QAAO;gBAC7ChE,MAAMkD,iBAAiBxC,MAAM;gBAC7BP,OAAO+C,iBAAiB9C,IAAI;gBAC5BC,SACE6C,iBAAiB5C,MAAM,KAAK,OAAO,OAAO4C,iBAAiB5C,MAAM,GAAG;gBACtE,6EAA6E;gBAC7EG,WAAW,EAAE;gBACbD;YACF;YAEA,OAAO;gBACLT,OAAOgE;gBACPrD,QAAQ0C;YACV;QACF;IACF;IAEA,OAAOlD;AACT;AAEA,eAAe+D,yBACbnE,OAAgB,EAChBoE,WAAmB,EACnBnE,KAA0B;IAE1B,MAAMkD,SACJ,AAAC,MAAMT,kBAAkBzC,UACzB,4DAA4D;IAC5D,uEAAuE;IACtE,MAAMF,mBAAmBC,SAASC;IACrC,IAAI,CAACkD,QAAQ;QACX,OAAO;IACT;IAEA,IAAIkB,+BAA+BlB,OAAOlD,KAAK,CAACC,IAAI;IACpD,IACEmE,iCAAiC,QACjCA,6BAA6BzE,UAAU,CAAC,YACxC;QACAyE,+BAA+BC,aAAI,CAACC,QAAQ,CAC1CH,aACAI,IAAAA,sBAAa,EAACH;IAElB;IAEA,OAAO;QACLJ,oBAAoB;YAClBtD,WAAWwC,OAAOlD,KAAK,CAACU,SAAS;YACjCT,MAAMmE;YACNhE,OAAO8C,OAAOlD,KAAK,CAACI,KAAK;YACzBE,SAAS4C,OAAOlD,KAAK,CAACM,OAAO;YAC7BG,SAASyC,OAAOlD,KAAK,CAACS,OAAO;YAC7BD,YAAY0C,OAAOlD,KAAK,CAACQ,UAAU;QACrC;QACAgE,mBAAmBC,IAAAA,4BAAoB,EAACvB,OAAOlD,KAAK,EAAEkD,OAAOvC,MAAM;IACrE;AACF;AAEO,SAASrB,qBAAqB,EACnCS,OAAO,EACPoE,WAAW,EACXO,QAAQ,EAKT;IACC,OAAO,eACLC,GAAoB,EACpBC,GAAmB,EACnBC,IAAgB;QAEhB,MAAM,EAAEC,QAAQ,EAAEvC,YAAY,EAAE,GAAG,IAAIwC,IAAIJ,IAAIK,GAAG,EAAG;QAErD,IAAIF,aAAa,mCAAmC;YAClD,IAAIH,IAAIM,MAAM,KAAK,QAAQ;gBACzB,OAAOC,sCAAkB,CAACC,UAAU,CAACP;YACvC;YAEA,MAAM5C,OAAO,MAAM,IAAIoD,QAAgB,CAACC,SAASC;gBAC/C,IAAIC,OAAO;gBACXZ,IAAIa,EAAE,CAAC,QAAQ,CAACC;oBACdF,QAAQE;gBACV;gBACAd,IAAIa,EAAE,CAAC,OAAO,IAAMH,QAAQE;gBAC5BZ,IAAIa,EAAE,CAAC,SAASF;YAClB;YAEA,MAAMI,UAAUC,KAAKC,KAAK,CAAC5D;YAC3B,MAAM6D,SAAS,MAAMxG,uBAAuB;gBAC1CU;gBACAoE;gBACAlC,QAAQyD,QAAQzD,MAAM;gBACtBC,UAAUwD,QAAQxD,QAAQ;gBAC1B4D,cAAcJ,QAAQI,YAAY;gBAClCC,gBAAgBL,QAAQK,cAAc;YACxC;YAEAC,IAAAA,kDAA0C,EAACH;YAE3C,OAAOX,sCAAkB,CAACe,IAAI,CAACrB,KAAKiB;QACtC,OAAO,IAAIf,aAAa,2BAA2B;YACjD,MAAMoB,oBAAoB3D,aAAajB,GAAG,CAAC,yBAAyB;YAEpE,IAAI6E;YACJ,IAAID,mBAAmB;gBACrB,MAAME,mBAAmB7D,aAAajB,GAAG,CAAC,WAAW;gBACrD,MAAM+E,UAAUhC,aAAI,CAACiC,IAAI,CACvB,OACA5B,WAAW,QAAQ,IACnB0B;gBAEFD,mBAAmB,MAAMI,IAAAA,8BAAgB,EAACF,SAAS,GAAG,GAAGlC;YAC3D,OAAO;gBACL,MAAMnE,QAAQsC,iBAAiBC;gBAC/B,IAAI,CAACvC,OAAO,OAAOkF,sCAAkB,CAACC,UAAU,CAACP;gBACjDuB,mBAAmB,MAAMI,IAAAA,8BAAgB,EACvCvG,MAAMC,IAAI,EACVD,MAAMK,IAAI,IAAI,GACdL,MAAMO,MAAM,IAAI,GAChB4D;YAEJ;YAEA,IAAIgC,iBAAiBxC,KAAK,EAAE;gBAC1B,OAAOuB,sCAAkB,CAACsB,mBAAmB,CAC3C5B,KACAuB,iBAAiBxC,KAAK;YAE1B;YACA,IAAI,CAACwC,iBAAiBM,KAAK,EAAE;gBAC3B,OAAOvB,sCAAkB,CAACwB,QAAQ,CAAC9B;YACrC;YACA,OAAOM,sCAAkB,CAACyB,SAAS,CAAC/B;QACtC;QAEA,OAAOC;IACT;AACF;AAEO,SAAStF,uBAAuBQ,OAAgB;IACrD,OAAO,eACL4E,GAAoB,EACpBC,GAAmB,EACnBC,IAAgB;QAEhB,MAAM,EAAEC,QAAQ,EAAEvC,YAAY,EAAE,GAAG,IAAIwC,IAAIJ,IAAIK,GAAG,EAAG;QAErD,IAAIF,aAAa,wBAAwB;YACvC,OAAOD;QACT;QAEA,IAAI+B,WAAWrE,aAAajB,GAAG,CAAC;QAEhC,IAAI,CAACsF,UAAU;YACb,OAAO1B,sCAAkB,CAACC,UAAU,CAACP;QACvC;QAEA,IAAIiC;QACJ,IAAI;YACFA,kBAAkBjE,IAAAA,yBAAa,EAACgE;QAClC,EAAE,OAAO9D,OAAO;YACd,OAAOoC,sCAAkB,CAACsB,mBAAmB,CAC3C5B,KACA,qBAGC,CAHD,IAAI7B,MACF,GAAG6D,SAAS,wFAAwF,CAAC,EACrG;gBAAE9D;YAAM,IAFV,qBAAA;uBAAA;4BAAA;8BAAA;YAGA;QAEJ;QAEA,IAAI+D,oBAAoB1G,WAAW;YACjC,MAAMwC,mBAAmBkE,gBAAgBhE,OAAO;YAChD,OAAOqC,sCAAkB,CAACe,IAAI,CAACrB,KAAKjC;QACtC;QAEA,IAAI;YACF,kDAAkD;YAClDiE,WAAWE,UAAUF;QACvB,EAAE,OAAM;YACN,OAAO1B,sCAAkB,CAACC,UAAU,CAACP;QACvC;QAEA,IAAIP,aAAI,CAAC0C,UAAU,CAACH,WAAW;YAC7BA,WAAW/F,IAAAA,sBAAa,EAAC+F,UAAU5F,IAAI;QACzC;QAEA,IAAI;YACF,MAAMgG,kBAAkB,MAAMjH,QAAQkH,YAAY,CAACL;YAEnD,IAAII,iBAAiB;gBACnB,OAAO9B,sCAAkB,CAACgC,UAAU,CAACtC,KAAKoC;YAC5C;QACF,EAAE,OAAOlE,OAAO;YACd,OAAOoC,sCAAkB,CAACsB,mBAAmB,CAC3C5B,KACA,qBAKC,CALD,IAAI7B,MACF,CAAC,8BAA8B,EAAE6D,SAAS,2BAA2B,CAAC,EACtE;gBACE9D;YACF,IAJF,qBAAA;uBAAA;4BAAA;8BAAA;YAKA;QAEJ;QAEAoC,sCAAkB,CAACyB,SAAS,CAAC/B;IAC/B;AACF;AAEO,eAAevF,uBAAuB,EAC3CU,OAAO,EACPoE,WAAW,EACXlC,MAAM,EACNC,QAAQ,EACR4D,YAAY,EACZC,cAAc,EAQf;IACC,MAAMoB,cAAcpF,kBAAkB;QACpCE;QACAC;QACA4D;QACAC;IACF;IAEA,OAAOX,QAAQgC,GAAG,CAChBD,YAAYhF,GAAG,CAAC,OAAOnC;QACrB,IAAI;YACF,MAAMqH,aAAa,MAAMnD,yBACvBnE,SACAoE,aACAnE;YAEF,IAAIqH,eAAe,MAAM;gBACvB,OAAO;oBACLC,QAAQ;oBACRC,QAAQ;gBACV;YACF;YACA,OAAO;gBAAED,QAAQ;gBAAaE,OAAOH;YAAW;QAClD,EAAE,OAAO1D,OAAO;YACd,OAAO;gBACL2D,QAAQ;gBACRC,QAAQE,IAAAA,iBAAO,EAAC9D,OAAO;oBAAE+D,QAAQ;gBAAM;YACzC;QACF;IACF;AAEJ", "ignoreList": [0]}